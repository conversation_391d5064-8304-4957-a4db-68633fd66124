/*------------------------------------------------------*/
/*                   修改控件的配置信息                                                                    */
/*                   version:4.0.1                      */
/*                                                      */
/*------------------------------------------------------*/
//64位控件的calssid
var classidx64="A64E3073-2016-4baf-A89D-FFE1FAA10EE0";
//32位控件的classid
var classid="A64E3073-2016-4baf-A89D-FFE1FAA10EC0";
//32位控件包的路径
//var codebase="officecontrol/OfficeControl.cab#version=6.0.3.1";
//64位控件包的路径
//var codebase64="officecontrol/OfficeControlx64.cab#version=6.0.3.1";
//设置高度
var height="1000px";
//设置宽度
var width="100%";
//买断授权密钥如果不是买断可以不用写
var MakerCaption="";
//买断授权密钥如果不是买断可以不用写
var MakerKey="";
//密钥
var ProductCaption="北京众驰伟业科技发展有限公司";
//密钥
var ProductKey="79AF4B9C95D73DD35A96E330076C045E1424C1DF";
//解除时间密钥
var NoExpireKey="";

/*
谷歌浏览器事件接管但此接管事件只支持低版本的谷歌（45以下版本不包含45）和火狐（52以下版本不包含52）
*/
function ntkosavetourl(type,code,html)
{
  //alert("SaveToURL成功回调");
}


function ntkopublishashtml(type,code,html){
  //alert("Onpublishashtmltourl成功回调");
}
function ntkopublishaspdf(type,code,html){
//alert("Onpublishaspdftourl成功回调");
}
function ntkosaveasotherurl(type,code,html){
//alert("SaveAsOtherformattourl成功回调");
}

function ntkoondocumentopened(type,code){
  if(cmd==5){
    if(datavalue==1){
      ntko.ShowTipMessage("注意","已经接收到父页面传过来的值,请注意文档中内容的变化");
      ntkoDataToChild(datatext);
    }
  }
}
/*------------------------------------------------------*/
/*             以下内容 请勿修改，否则可能出错                                                              */
/*------------------------------------------------------*/

var userAgent = navigator.userAgent,
  rMsie = /(msie\s|trident.*rv:)([\w.]+)/,
  rFirefox = /(firefox)\/([\w.]+)/,
  rOpera = /(opera).+version\/([\w.]+)/,
  rChrome = /(chrome)\/([\w.]+)/,
  rSafari = /version\/([\w.]+).*(safari)/;
var browser;
var version;
var ua = userAgent.toLowerCase();
function uaMatch(ua) {
  var match = rMsie.exec(ua);
  if (match != null) {
    return { browser : "IE", version : match[2] || "0" };
  }
  var match = rFirefox.exec(ua);
  if (match != null) {
    return { browser : match[1] || "", version : match[2] || "0" };
  }
  var match = rOpera.exec(ua);
  if (match != null) {
    return { browser : match[1] || "", version : match[2] || "0" };
  }
  var match = rChrome.exec(ua);
  if (match != null) {
    return { browser : match[1] || "", version : match[2] || "0" };
  }
  var match = rSafari.exec(ua);
  if (match != null) {
    return { browser : match[2] || "", version : match[1] || "0" };
  }
  if (match != null) {
    return { browser : "", version : "0" };
  }
}
var browserMatch = uaMatch(userAgent.toLowerCase());
if (browserMatch.browser) {
  browser = browserMatch.browser;
  version = browserMatch.version;
}


if (browser=="IE"){
  if(window.navigator.platform=="Win32"){

    document.write('<!-- 用来产生编辑状态的ActiveX控件的JS脚本-->   ');
    document.write('<!-- 因为微软的ActiveX新机制，需要一个外部引入的js-->   ');
    document.write('<object id="TANGER_OCX" classid="clsid:'+classid+'"');
    document.write(' width="'+width+'" height="'+height+'">   ');
    document.write('<param name="MakerCaption" value="'+MakerCaption+'">   ');
    document.write('<param name="MakerKey" value="'+MakerKey+'">   ');
    document.write('<param name="ProductCaption" value="'+ProductCaption+'">   ');
    document.write('<param name="ProductKey" value="'+ProductKey+'">   ');
    document.write('<param name="NoExpireKey" value="'+NoExpireKey+'">   ');
    document.write('<param name="IsUseUTF8URL" value="-1">   ');
    document.write('<param name="IsUseUTF8Data" value="-1">   ');
    document.write('<param name="Caption" value="OFFICE文档在线编辑">   ');
    document.write('<SPAN STYLE="color:red">不能装载文档控件。请在检查浏览器的选项中检查浏览器的安全设置。</SPAN>   ');
    document.write('</object>');
  }
  if(window.navigator.platform=="Win64"){

    document.write('<!-- 用来产生编辑状态的ActiveX控件的JS脚本-->   ');
    document.write('<!-- 因为微软的ActiveX新机制，需要一个外部引入的js-->   ');
    document.write('<object id="TANGER_OCX" classid="clsid:'+classidx64+'"');
    document.write(' width="'+width+'" height="'+height+'">   ');
    document.write('<param name="MakerCaption" value="'+MakerCaption+'">   ');
    document.write('<param name="MakerKey" value="'+MakerKey+'">   ');
    document.write('<param name="ProductCaption" value="'+ProductCaption+'">   ');
    document.write('<param name="ProductKey" value="'+ProductKey+'">   ');
    document.write('<param name="NoExpireKey" value="'+NoExpireKey+'">   ');
    document.write('<param name="IsUseUTF8URL" value="-1">   ');
    document.write('<param name="IsUseUTF8Data" value="-1">   ');
    document.write('<param name="Caption" value="OFFICE文档在线编辑">   ');
    document.write('<SPAN STYLE="color:red">不能装载文档控件。请在检查浏览器的选项中检查浏览器的安全设置。</SPAN>   ');
    document.write('</object>');
  }
}
else if (browser=="firefox"||browser=="chrome"){
  document.write('<object id="TANGER_OCX" type="application/ntko-plug"   width="'+width+'" height="'+height+'" ForOnSaveToURL="ntkosavetourl"  ForOndocumentopened="ntkoondocumentopened"');
  document.write('ForOnpublishAshtmltourl="ntkopublishashtml"');
  document.write('ForOnpublishAspdftourl="ntkopublishaspdf"');
  document.write('ForOnSaveAsOtherFormatToUrl="ntkosaveasotherurl"');
  document.write('_MakerCaption="'+MakerCaption+'"  ');
  document.write('_MakerKey="'+MakerKey+'"  ');
  document.write('_ProductCaption="'+ProductCaption+'"  ');
  document.write('_ProductKey="'+ProductKey+'"   ');
  document.write('_NoExpireKey="'+NoExpireKey+'"   ');
  document.write('clsid="{'+classid+'}" >');
  document.write('<SPAN STYLE="color:red">尚未安装跨浏览器插件</SPAN>   ');
  document.write('</object>   ');
}else if (Sys.opera){
  alert("sorry,ntko web印章暂时不支持opera!");
}else if (Sys.safari){
  alert("sorry,ntko web印章暂时不支持safari!");
}

window.onunload =function(){
  var ntkojb= ntkoBrowser.NtkoJudgingBrowsers();
  if(ntkojb){
    window.opener.ntkoCloseEvent();
  }
}
//ie,谷歌，火狐传值
var ntkoBrowser={
  ntkoSetReturnValueToParentPage:function(data1,text1){
    var ntkojb= ntkoBrowser.NtkoJudgingBrowsers();
    if(ntkojb){
      window.opener.ieattachEventntko(data1,text1);
    }else{
      window.external.SetReturnValueToParentPage(data1,text1);
    }
  },
  NtkoJudgingBrowsers:function(){
    if("SetReturnValueToParentPage" in window.external){
      return false;
    }else{
      return true;
    }
  }
}
//触发父页面传值的功能

var NtkoUrlTest = window.location.href;
if(NtkoUrlTest!=null&&NtkoUrlTest!=""){
  var ntkoarry=new Array();
  ntkoarry.push(NtkoUrlTest);
  ntkoBrowser.ntkoSetReturnValueToParentPage("ntkoSendDataToChildtext",ntkoarry);
}
