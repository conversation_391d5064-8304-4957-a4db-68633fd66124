body{
	text-align:center;
	/*font-family:"微软雅黑";*/
	letter-spacing:0.05em;
	color:#575757;
	font-size:16px;
	margin:0px;
}
a{
	text-decoration:none;
	cursor:pointer;
	color:#299ceb;
}
a:hover{
	text-decoration:underline;
	color:#c60000;
}
hr{	
	border:0px;
	border-bottom:1px #b0b0b0 solid;
	margin:50px 0px 30px 0px;
}
p{
	text-align:left;
	line-height:1.6;
	text-indent:2em;
}

.divTop{
	width:100%;
	height:70px;
	text-align:center;
	background-color:#000000;
	top:0;
}
.divTopInner{
	width:1200px;
	text-align:left;
	color:#ffffff;
	line-height:70px;
	margin:auto;
}
.divTopInnerCaption{
	width:1150px;
	font-size:20px;	
	float:left;
}
.divTopInnerNtko{
	width:50px;
	font-size:14px;
	text-align:center;
	float:left;
	cursor:pointer;
}
.divTopInnerNtko:hover{
	background-color:#c60000;
}
.divTopInner a{
	color:#ffffff;
	text-decoration:none;
}

.divTitle{
	width:100%;
	height:80px;
	line-height:80px;
	border-bottom:1px #bababa solid;
	margin-bottom:10px;
}
.divTitle_body{
	width:1200px;
	text-align:left;
	margin:auto;
}
.divTitle_caption{
	width:100%;
	float:left;
	color:#c60000;
	font-size:36px;
	font-weight:bold;

}
.divTitle_caption_small{
	width:100%;
	float:left;
	color:#c60000;
	font-size:24px;
	font-weight:bold;
    height:50px;
    line-height:50px;
}
.divTitle_Information{
	width:100%;
	float:left;
	color:#999999;
	font-size:14px;
    height:auto;
    line-height:24px;
	margin:20px 0px 30px 0px;
}
.divTitle_Information_Left{
	width:40px;
	height:40px;
	line-height:40px;
	background-color:#ffde00;
	float:left;
	text-align:center;
	font-size:20px;
	font-weight:bolder;
	color:#ffffff;
	margin:auto 20px auto auto;
}
.divTitle_Information_Right{
	width:1140px;
	height:40px;
	line-height:40px;
	float:left;
	font-size:16px;
}
.divTitle_introduction{
	width:100%;
	float:left;
	color:#999999;
	font-size:14px;
    height:auto;
    line-height:24px;
}

.divBody{
    width:1200px;
	margin:10px auto;
    text-align:left;
}
.divBody_Title{
	width:100%;
	height:40px;
	font-size:16px;
	margin:40px 0px 0px 0px;
}
.divBody_Title_Flag{
	width:5px;
	height:40px;
	font-size:16px;
	float:left;
	background-color:#a10000;
	margin:0px 20px 10px 0px;
}
.divBody_Table_Caption{
	width:1175px;
	height:40px;
	line-height:40px;
	font-size:24px;
	font-weight:bolder;
	color:#999999;
	float:left;
}
table{
	border-collapse:collapse;
	width:100%;
}
td{
	border:1px #e0e0e0 solid;
	height:60px;
	text-align:center;
	color:#999999;
	font-size:14px;
	padding:20px;
}
.divBody_Table_RowTitle{
	font-size:18px;
	font-weight:bolder;
	color:#ffffff;
	background-color:#e0e0e0;
}
   