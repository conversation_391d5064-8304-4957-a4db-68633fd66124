var ntko//控件对象
window.onbeforeunload = function() {
  if (ntkoBrowser.NtkoJudgingBrowsers()) {
    var ntkoconfirm = ntko.ShowConfirmMessage('提示', '是否关闭窗口？')
    if (ntkoconfirm) {
      ntkocloseparentpage()
    } else {
      return '是否关闭窗口？'
    }
  }
}

//初始化去打开文档
function init(cmd, fileUrl, readOnly, type,webUserName,protectType) {
  ntko = document.getElementById('TANGER_OCX')
  ntko.ToolBars = true
  ntko.Menubar = true
  ntko.TitleBar = false
  //禁止文件菜单的新建项
  ntko.FileNew = false
  //禁止文件菜单的打开项
  ntko.FileOpen = false
  //禁止文件菜单的另存为项
  ntko.FileSaveAs = false
  //禁止文件菜单的打印项
  ntko.FilePrint = false
  if (null != webUserName && typeof webUserName != 'undefined') {
    ntko.WebUserName = webUserName
  }
  if (Number(type)===2) {
    //禁止文件菜单的保存项
    ntko.FileSave = false
  }
  if (window.navigator.platform == 'Win32') {
    ntko.AddDocTypePlugin('.pdf', 'PDF.NtkoDocument', '4.0.1.0', './officecontrol/ntkooledocall.cab', 51, true)
  }
  if (window.navigator.platform == 'Win64') {
    ntko.AddDocTypePlugin('.pdf', 'PDF.NtkoDocument', '4.0.1.0', './officecontrol/ntkooledocallx64.cab', 51, true)
  }
  // 添加认证标头
  // var token =  window.localStorage.getItem('MDM-Token')
  var token = sessionStorage.getItem('Admin-Token')
  ntko.AddHTTPHeader('Authorization:Bearer ' + token)
  if (cmd == 1) {
    ntko.openFromURL('./test.docx')
  } else {
    let isReadOnly = false
    if (null != readOnly && typeof readOnly != 'undefined' && readOnly === 'true') {
      isReadOnly = true
    }
    //isReadOnly
    ntko.OpenFromURL(fileUrl, false)
    if(protectType!=null){
      ntko.SetReadOnly(isReadOnly, Number(protectType)>0?'Dms@2025':null, protectType==null?false:Number(protectType),Number(protectType))
      var count=ntko.GetSheetsCount();
      if(count){
        for(var i=1;i<=count;i++){
          var sheet=ntko.ActiveDocument.Sheets(i);
          if(sheet){
            sheet.Protect('Dms@2025',false,true,true);
          }
        }
      }
    }else{
      ntko.SetReadOnly(false,'Dms@2025')
      ntko.TrackRevisions(false)
      var count=ntko.GetSheetsCount();
      if(count){
        for(var i=1;i<=count;i++){
          var sheet=ntko.ActiveDocument.Sheets(i);
          if(sheet){
            ssheet.unprotect("Dms@2025");
          }
        }
      }

    }
    //  ntko.WebUserName = "自定义用户名"
  }
}

function ntkoWebBrowseCloseEvent() {
  var ntkoconfirm = ntko.ShowConfirmMessage('提示', '是否关闭窗口？')
  if (ntkoconfirm) {
    ntkocloseparentpage()
    return true
  } else {
    return false
  }
}

