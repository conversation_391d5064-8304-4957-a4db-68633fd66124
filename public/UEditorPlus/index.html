<!doctype html>
<html>
<head>
  <title>UEditor Plus 完整演示</title>
  <meta name="viewport" content="width=device-width, minimum-scale=0.5, maximum-scale=5, user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
  <script type="text/javascript" charset="utf-8" src="ueditor.config.js"></script>
  <script type="text/javascript" charset="utf-8" src="ueditor.all.js"></script>
  <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
  <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
  <script type="text/javascript" charset="utf-8" src="lang/zh-cn/zh-cn.js"></script>
  <script src="./plugins/demo/demo.js"></script>
  <script>
    // window.UEDITOR_CONFIG.toolbars[0].push('undo');
  </script>
  <script>var _hmt = _hmt || [];
  (function () {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?5f8b495b287894a451c761c963e6e34a";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
  })();</script>
  <link rel="stylesheet" href="./style.css"/>
</head>
<body class="demo-editor-page">
<div>
  <h1>完整示例</h1>
  <div>
    <script id="editor" type="text/plain" style="height:300px;">
<h1>
    你好 UEditor Plus
</h1>
<p>
    UEditor Plus 是基于 UEditor 二次开发的富文本编辑器，让 UEditor <span style="color: #E36C09;">焕<span style="color: #0070C0;">然</span><span style="color: #31859B;"><span style="color: #00B050;">一</span><span style="color: #FF0000;">新</span></span></span>
</p>

<table data-sort="sortDisabled">
    <tbody>
        <tr class="firstRow">
            <td valign="top" style="word-break: break-all;" rowspan="1" colspan="3">
                我是表格
            </td>
        </tr>
        <tr>
            <td width="273" valign="top" style="word-break: break-all;">
                如果
            </td>
            <td width="273" valign="top" style="word-break: break-all;">
                有一天
            </td>
            <td width="273" valign="top" style="word-break: break-all;">
                我离开了
            </td>
        </tr>
        <tr>
            <td valign="top" colspan="1" rowspan="1" style="word-break: break-all;">
                怎么才能
            </td>
            <td valign="top" colspan="1" rowspan="1" style="word-break: break-all;">
                证明我
            </td>
            <td valign="top" colspan="1" rowspan="1" style="word-break: break-all;">
                曾经来过
            </td>
        </tr>
    </tbody>
</table>
<pre class="brush:html;toolbar:false">&lt;div&gt;
  &lt;span&gt;这里是HTML标签&lt;/span&gt;
&lt;/div&gt;</pre>
<h2>
公式支持
</h2>
<p>
    <img src="https://latex.codecogs.com/svg.image?f(a)%20%3D%20%5Cfrac%7B1%7D%7B2%5Cpi%20i%7D%20%5Coint%5Cfrac%7Bf(z)%7D%7Bz-a%7Ddz" data-formula-image="f(a)%20%3D%20%5Cfrac%7B1%7D%7B2%5Cpi%20i%7D%20%5Coint%5Cfrac%7Bf(z)%7D%7Bz-a%7Ddz"/>
</p>
<p>
    <br/>
</p>
    </script>
  </div>
  <div id="btns" style="margin-top:20px;">
    <div>
      <button onclick="getAllHtml()">获得整个html的内容</button>
      <button onclick="getContent()">获得内容</button>
      <button onclick="setContent()">写入内容</button>
      <button onclick="setContent(true)">追加内容</button>
      <button onclick="getContentTxt()">获得纯文本</button>
      <button onclick="getPlainTxt()">获得带格式的纯文本</button>
      <button onclick="hasContent()">判断是否有内容</button>
      <button onclick="setFocus()">使编辑器获得焦点</button>
      <button onmousedown="isFocus(event)">编辑器是否获得焦点</button>
      <button onmousedown="setblur(event)">编辑器失去焦点</button>
      <button onclick="getText()">获得当前选中的文本</button>
      <button onclick="insertHtml()">插入给定的内容</button>
      <button id="enable" onclick="setEnabled()">可以编辑</button>
      <button onclick="setDisabled()">不可编辑</button>
      <button onclick=" UE.getEditor('editor').setHide()">隐藏编辑器</button>
      <button onclick=" UE.getEditor('editor').setShow()">显示编辑器</button>
      <button onclick=" UE.getEditor('editor').setHeight(300)">设置高度为300默认关闭了自动长高</button>
      <button onclick="getLocalData()">获取草稿箱内容</button>
      <button onclick="clearLocalData()">清空草稿箱</button>
      <button onclick="createEditor()">
        创建编辑器
      </button>
      <button onclick="deleteEditor()">
        删除编辑器
      </button>
    </div>
  </div>

  <script type="text/javascript">
    //实例化编辑器
    //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
    var ue = UE.getEditor('editor');

    function isFocus(e) {
      alert(UE.getEditor('editor').isFocus());
      UE.dom.domUtils.preventDefault(e)
    }

    function setblur(e) {
      UE.getEditor('editor').blur();
      UE.dom.domUtils.preventDefault(e)
    }

    function insertHtml() {
      var value = prompt('插入html代码', '');
      UE.getEditor('editor').execCommand('insertHtml', value)
    }

    function createEditor() {
      enableBtn();
      UE.getEditor('editor');
    }

    function getAllHtml() {
      alert(UE.getEditor('editor').getAllHtml())
    }

    function getContent() {
      var arr = [];
      arr.push("使用editor.getContent()方法可以获得编辑器的内容");
      arr.push("内容为：");
      arr.push(UE.getEditor('editor').getContent());
      alert(arr.join("\n"));
    }

    function getPlainTxt() {
      var arr = [];
      arr.push("使用editor.getPlainTxt()方法可以获得编辑器的带格式的纯文本内容");
      arr.push("内容为：");
      arr.push(UE.getEditor('editor').getPlainTxt());
      alert(arr.join('\n'))
    }

    function setContent(isAppendTo) {
      var arr = [];
      arr.push("使用editor.setContent('欢迎使用ueditor')方法可以设置编辑器的内容");
      UE.getEditor('editor').setContent('欢迎使用ueditor', isAppendTo);
      alert(arr.join("\n"));
    }

    function setDisabled() {
      UE.getEditor('editor').setDisabled('fullscreen');
      disableBtn("enable");
    }

    function setEnabled() {
      UE.getEditor('editor').setEnabled();
      enableBtn();
    }

    function getText() {
      //当你点击按钮时编辑区域已经失去了焦点，如果直接用getText将不会得到内容，所以要在选回来，然后取得内容
      var range = UE.getEditor('editor').selection.getRange();
      range.select();
      var txt = UE.getEditor('editor').selection.getText();
      alert(txt)
    }

    function getContentTxt() {
      var arr = [];
      arr.push("使用editor.getContentTxt()方法可以获得编辑器的纯文本内容");
      arr.push("编辑器的纯文本内容为：");
      arr.push(UE.getEditor('editor').getContentTxt());
      alert(arr.join("\n"));
    }

    function hasContent() {
      var arr = [];
      arr.push("使用editor.hasContents()方法判断编辑器里是否有内容");
      arr.push("判断结果为：");
      arr.push(UE.getEditor('editor').hasContents());
      alert(arr.join("\n"));
    }

    function setFocus() {
      UE.getEditor('editor').focus();
    }

    function deleteEditor() {
      disableBtn();
      UE.getEditor('editor').destroy();
    }

    function disableBtn(str) {
      var div = document.getElementById('btns');
      var btns = UE.dom.domUtils.getElementsByTagName(div, "button");
      for (var i = 0, btn; btn = btns[i++];) {
        if (btn.id == str) {
          UE.dom.domUtils.removeAttributes(btn, ["disabled"]);
        } else {
          btn.setAttribute("disabled", "true");
        }
      }
    }

    function enableBtn() {
      var div = document.getElementById('btns');
      var btns = UE.dom.domUtils.getElementsByTagName(div, "button");
      for (var i = 0, btn; btn = btns[i++];) {
        UE.dom.domUtils.removeAttributes(btn, ["disabled"]);
      }
    }

    function getLocalData() {
      alert(UE.getEditor('editor').execCommand("getlocaldata"));
    }

    function clearLocalData() {
      UE.getEditor('editor').execCommand("clearlocaldata");
      alert("已清空草稿箱")
    }
  </script>
</div>
</body>
</html>
