.hiddentext{
  overflow:hidden ;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:100%
}
.rz-button-handel-60 {
  position: absolute;
  top: 15px;
  right: 60px;
}
.fr{
    float:right;
    min-height: 20px;
  }
  .top10{
    top:10px
  }
  .fl{
    float: left;
  }
  .global-ser .ser-text .cell-input .el-row .el-form-item .inputlength{
    margin:0;
    width: 72%;
    }
  .w-p18{
      width: 18%;
      display: inline-block;
      margin-bottom: 18px;

  }
  .miancss{
    margin-left: 28px;
    margin-right: 28px;
  }
  .mainItem{
    margin-bottom: 22px;
  }
  .mr-10{
    margin-right: 10px;
  }
  .mr-20{
    margin-right: 20px;
  }
  .mr-30{
    margin-right: 30px;
  }
  .ml-10{
    margin-left: 10px;
  }
  .ml-30{
    margin-left: 30px;
  }
  .ml-50{
    margin-left: 50px;
  }
  .mt-10{
    margin-top: 10px;
  }
  .mt--20{
    margin-top: -20px;
  }
  .mt-20{
    margin-top: 20px;
  }
  .mt-30{
      margin-top: 30px;
  }
  .mt-50{
    margin-top: 50px;
}
  .mt-100{
    margin-top: 100px;
  }
  .mt{
    margin-top: 0;
  }
  .mb-10{
    margin-bottom: 10px;
  }
  .mb-30{
    margin-bottom: 30px;
  }
  .mb-70{
    margin-bottom: 70px;
  }
  .mh40{
    min-height: 40px;
  }
  .mh257{
    min-height: 257px;
  }
  .mh413{
    min-height: 413px;
  }
  .mh500{
    min-height: 500px;
  }
  .mh200{
    min-height: 200px;
  }

  .img-deviceInfo{
    height:200px;

  }
  .infoCard div{
    margin: 10px 0 10px 0;
  }
  .textcenter{
    text-align: center;
  }
  .searchbox input{
    width: 100% !important;
    height: 30px !important;
  }
  .searchbox  .el-col{
    margin-right: 35px;
  }
  /* 表格页面*/
  .clickable{
    cursor: pointer;
  }
  .tab-box, .tab-box td{
    border:1px solid rgb(213, 213, 213);
    border-collapse:collapse;/* 让边框合并，不让出现双边框*/
  }
  .tab-box{
    width: 100%;
  }
  .tab-box tr{
    display: flex;/*flex布局*/
  }
  .tab-box td {
    flex: 4;
    height:30px;
    padding-left:10px;
    display: flex;/*flex布局*/
    align-items: center;/*让单元格文字垂直居中*/
    color: #606266;
  }
  .tab-box tr>td:first-child{
    background-color: rgb(242, 242, 242);
  }
  .tab-box tr td:nth-child(3){
    background-color: rgb(242, 242, 242);
  }
  tr .widthTd{

    flex: 1.3;
  }
  .verdivider{
    height: 50px;
    left: 24px;
    bottom: 28px;
  }
  /* 仪器视图*/
  .deviceList .dtitle{
    background: #FFFFFF;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.16);
    opacity: 1;
    border-radius: 10px 10px 0px 0px;
  }

  .deviceList .colImg{
      min-height: 133px;
    }
  .deviceList  .circlesymbol{
      width: 16px;
      height: 16px;
      background: #71EE97;
      border-radius: 50%;
      opacity: 1;
    }
    .deviceList  .machineInfo{
      width: 197px;
      height: 51px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      line-height: 18px;
      color: #5E5E5E;
      opacity: 1;
    }
    .deviceList  .devicename{
      width: 197px;
      height: 51px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      line-height: 18px;
      color: #5E5E5E;
      opacity: 1;
    }
    .deviceList .choose input{
      height: 39px;
      background: #EFF0F3;
      opacity: 1;
      border-radius: 12px;
    }
    .deviceList .btn{
      font-size: 20px;
      font-family: Source Han Sans CN;
      line-height: 0px;
      color: #013288;
      opacity: 1;
    }
    /*信息卡片  */
    .infoCard .fno{
      height: 18px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      line-height: 30px;
      color: #A7A7A7;
      opacity: 1;
    }

    /* 操作日志 */
    .operlog .btn{
      height: 39px;
      border: 1px solid #013288;
      opacity: 1;
      border-radius: 20px;
      font-size: 20px;
      font-family: Source Han Sans CN;
      line-height: 0px;
      color: #013288;
      opacity: 1;
    }
    /* 注册 */
    .registerbox{
      margin: 10px 50px;
      padding-top: 30px;
      min-height: 80%;
      opacity: 1;
      border-radius: 10px;
    }
    .registerbox .form{
      margin: 10px 0;
    }

    .registerbox .el-input{
      width: 210px;
      opacity: 1;
	  max-width:100%;
    }
    .registerbox .el-select .el-input{
      width: 210px;
      max-width:100%;
      opacity: 1;
      border-radius: 8px;
    }
    .registerbox  label{
      width:120px;
      /* text-align:right; */
    }
	.registerbox .vue-treeselect{
		width:210px !important;
		}

    .registerbox .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .registerbox .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }
    .registerbox .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 108px;
      height: 108px;
      line-height: 108px;
      text-align: center;
    }
    .registerbox .avatar {
      width: 108px;
      height: 108px;
      display: block;
    }
    .registerbox .textarea{
      width: 70%;
    }
    .detailbox .result{
      min-height: 501px;
      background: #F7F7F9;
      opacity: 1;
      border-radius: 10px;
    }
    .detailbox .checkTable{
      background: #F7F7F9;
    }
    .bgitem{
     width: 1614px;
      height: 920px;
      background: #EFF0F3;
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
      opacity: 1;
      border-radius: 0px 0px 10px 10px;

    }
    .quailtybox .el-col div{
      border:1px solid rgb(213, 213, 213);
      border-collapse:collapse;/* 让边框合并，不让出现双边框*/
      min-height: 30px;
      text-align: center;
      line-height: 30px;
    }
    .chartInfo{
      padding-left: 20px;
      border-radius: 10px ;

    }
    .chartInfo #title{
      padding-top: 4px;
      font-size: 13px;
      border: 0px solid rgba(163, 158, 158, 0.3);
    }
    .chartInfo .info div{
      width: 82px;
      padding-top: 1px;
      text-align: center;
      min-height: 22px;
      background: #FFFFFF;
      border: 1px solid rgba(163, 158, 158, 0.3);
      opacity: 1;
      margin-top: 10px !important;
      border-radius: 4px;
      font-size: 12px;
      overflow: hidden;
    }
    .chartInfo .info .first{
      font-size: 15px;
    }
    .chartInfo .info div i{
      font-size: 16px;
    }



      .el-drawer.rtl  .details-foot{
        width:65%;
        left:35%;
        }
  .addForm .el-input{
    width: 210px;
  }
  .addForm   .vue-treeselect__control{
    width: 210px;
  }
 .addForm .textarea{
   width: 400px;
 }
 .addForm label{
  width: 120px;
}
.addForm .el-textarea{
  width: 75%;
}
.add-card{
  border: none !important;
}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item.is-active{
	color: #013288;
    font-size: 22px;
	font-weight:normal;
	}


/*add-card 首页-其他卡片*/

.add-card .el-card__body .morenbtn{
	display: inline-block;
    position: absolute;
    right: 20px;
    top: 18px;
	z-index:9;
	}
.add-card .el-card__body .morenbtn .el-icon-arrow-right{
	color: #013288;
    font-size: 22px;
    font-weight: bold;
    cursor: pointer;
	}
.add-card .el-card__body  .el-tabs{
	}
.add-card .el-card__body  .el-tabs .el-tabs__header {
	margin: 0;
    padding: 12.5px 0;
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap{
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap::after{
	content:none;
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll{
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav{
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__active-bar{
	display:none;
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item{
	color: #a7a7a7;
    font-size: 18px;
    padding: 0 !important;
	font-weight: normal;
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item:hover{
	color: #013288;
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item+.el-tabs__item{
	margin-left:25px;
	}
.add-card .el-card__body  .el-tabs .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item.is-active{
	color: #013288;
    font-size: 22px;
	font-weight:normal;
	}


/*el-tabs-1 仪器详情页签*/
.el-tabs-1{
	}
.add-card .el-card__body .el-tabs.el-tabs-1 .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item{
    padding: 0 20px 0 0 !important;
    margin: 0;
	position:relative;
}
.add-card .el-card__body .el-tabs.el-tabs-1 .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item+.el-tabs__item {
    padding: 0 20px !important;
    margin: 0;
}
.add-card .el-card__body .el-tabs.el-tabs-1 .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item+.el-tabs__item:before{
	content: "";
    display: block;
    position: absolute;
    width: 1px;
    height: 17px;
    background: #cfcfcf;
    top: 11px;
    left: 0;
	}
  .tips i{
    margin-right: 5px;
  }
  .rulesTitle .el-checkbox {
    color: #141414;
    font-weight:bold;
  }
  .details-foot-detail{
    width: 100% !important;
    position:static !important;
    background: #fff;
    box-shadow:none !important
  }
