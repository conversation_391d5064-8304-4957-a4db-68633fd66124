<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <link rel="icon" href="../../demo/favicon.ico" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta name="theme-color" content="#000000" />
        <meta name="description" content="Web site created using create-react-app" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="anyshare-sdk-public-path" content="../../" />
        <link rel="apple-touch-icon" href="../../demo/logo192.png" />
        <link rel="manifest" href="../../demo/manifest.json" />
        <title></title>
        <link href="../../css/anyshare.sdk.css" rel="stylesheet" />
        <script src="../../js/anyshare.sdk.js"></script>
    </head>
    <body>
        <div id="root"></div>
        <script type="text/javascript">
            function mainSdk() {
                AnyShareSDKFactory.create({
                    apiBase: "http://127.0.0.1:3000",
                    rootElement: document.getElementById("root"),
                    token: "0s6gNXDUGPtTDq9JM2xpg9n9Mj3E4U8E4eqELp9AfZI.yXWX_hklV504uEfS6NjbmR3Tk9cfv5-FfCrZuciZzPM",
                    locale: "zh-cn",
                })
                    .then(function resolve(sdk) {
                        sdk.download(
                            [
                                {
                                    docid: "gns://BB9E985652F448BDA64565DFB21B8356/2657CC71DAE048A8AFB3350E7E3C75DB",
                                    name: "截屏2021-01-21 上午10.32.18.png",
                                    rev: "FB01BF330F5E4DEBB567AF2F85C1030A",
                                    size: 141970,
                                },
                            ],
                            {
                                docid: "gns://BB9E985652F448BDA64565DFB21B8356",
                                name: "rgy",
                                rev: "1AE91E5BD105440E84D211E2CCCEE9FC",
                                size: -1,
                                type: "user_doc_lib",
                            },
                            undefined,
                            undefined,
                            "user_doc_lib",
                            undefined
                            /*具体参数说明可查看组件说明书*/
                        );
                    })
                    .catch(function reject(err) {
                        console.error(err);
                    });
            }
            mainSdk();
        </script>
    </body>
</html>
