<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="icon" href="../../demo/favicon.ico" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site created using create-react-app" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="anyshare-sdk-public-path" content="../../" />
    <link rel="apple-touch-icon" href="../../demo/logo192.png" />
    <link rel="manifest" href="../../demo/manifest.json" />
    <title></title>
</head>
<style>
    #root {
        text-align: center;
        margin-top: 400px;
    }
</style>

<body>
    <div id="root">

        <button id="location">打开文件所在位置</button>

    </div>

    <script type="text/javascript">
        function locationFn() {
            const url = "https://**********/anyshare/location?docid=gns://D7DE62E62EF34743A78D77F11336A41E/3AAD6CC9D913466AB74CAD04F202A6EE&tokenid=QMMDiTXSvx5mJyF0Oap3qx2X0v8MvHWA7GEpNtOjryM._7Osuj5h4YTEovHirjYSwSNx-uqkqxT7SMNPoY5lRWQ"
            window.open(url)
        }
        document.getElementById("location").onclick = locationFn



    </script>
</body>

</html>