<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="icon" href="../../demo/favicon.ico" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site created using create-react-app" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="anyshare-sdk-public-path" content="../../" />
    <link rel="apple-touch-icon" href="../../demo/logo192.png" />
    <link rel="manifest" href="../../demo/manifest.json" />
    <title></title>
    <link href="../../css/anyshare.sdk.css" rel="stylesheet" />
    <script src="../../js/anyshare.sdk.js"></script>
</head>

<body>
    <div id="root"></div>
    <script type="text/javascript">
        function mainSdk() {
            AnyShareSDKFactory.create({
                apiBase: "http://127.0.0.1:3000",
                rootElement: document.getElementById("root"),
                token: "hr6s765nPNqQJ4coWP6pi2Px4xNdANmzlgAeSngL7dk.Rr1iLQ8Gyk24lczjO96LzV1Ols9O7zPLtrOK1E5i31M",
                locale: "zh-cn",
            })
                .then(function resolve(sdk) {
                    sdk.preview(
                        {
                            docid:
                                "gns://5EF26633E8E740D3AFBB14022CC575CA/A70EA2A769BF4A1DA7F5381AC7EA9830",
                            name: "out.xls",
                            size: 9750,
                            rev: "F0DD8185299340A7B8B4BA268A3A0803",
                        },
                        false,
                    );
                })
                .catch(function reject(err) {
                    console.error(err);
                });
        }
        mainSdk();
    </script>
</body>

</html>