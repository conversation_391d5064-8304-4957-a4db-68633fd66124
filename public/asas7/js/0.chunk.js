(this["webpackJsonp@anyshare/sdk"]=this["webpackJsonp@anyshare/sdk"]||[]).push([[0],{2361:function(t,r){},2362:function(t,r,n){(function(t){function n(t,r){for(var n=0,e=t.length-1;e>=0;e--){var i=t[e];"."===i?t.splice(e,1):".."===i?(t.splice(e,1),n++):n&&(t.splice(e,1),n--)}if(r)for(;n--;n)t.unshift("..");return t}function e(t,r){if(t.filter)return t.filter(r);for(var n=[],e=0;e<t.length;e++)r(t[e],e,t)&&n.push(t[e]);return n}r.resolve=function(){for(var r="",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var s=o>=0?arguments[o]:t.cwd();if("string"!==typeof s)throw new TypeError("Arguments to path.resolve must be strings");s&&(r=s+"/"+r,i="/"===s.charAt(0))}return(i?"/":"")+(r=n(e(r.split("/"),(function(t){return!!t})),!i).join("/"))||"."},r.normalize=function(t){var o=r.isAbsolute(t),s="/"===i(t,-1);return(t=n(e(t.split("/"),(function(t){return!!t})),!o).join("/"))||o||(t="."),t&&s&&(t+="/"),(o?"/":"")+t},r.isAbsolute=function(t){return"/"===t.charAt(0)},r.join=function(){var t=Array.prototype.slice.call(arguments,0);return r.normalize(e(t,(function(t,r){if("string"!==typeof t)throw new TypeError("Arguments to path.join must be strings");return t})).join("/"))},r.relative=function(t,n){function e(t){for(var r=0;r<t.length&&""===t[r];r++);for(var n=t.length-1;n>=0&&""===t[n];n--);return r>n?[]:t.slice(r,n-r+1)}t=r.resolve(t).substr(1),n=r.resolve(n).substr(1);for(var i=e(t.split("/")),o=e(n.split("/")),s=Math.min(i.length,o.length),u=s,l=0;l<s;l++)if(i[l]!==o[l]){u=l;break}var a=[];for(l=u;l<i.length;l++)a.push("..");return(a=a.concat(o.slice(u))).join("/")},r.sep="/",r.delimiter=":",r.dirname=function(t){if("string"!==typeof t&&(t+=""),0===t.length)return".";for(var r=t.charCodeAt(0),n=47===r,e=-1,i=!0,o=t.length-1;o>=1;--o)if(47===(r=t.charCodeAt(o))){if(!i){e=o;break}}else i=!1;return-1===e?n?"/":".":n&&1===e?"/":t.slice(0,e)},r.basename=function(t,r){var n=function(t){"string"!==typeof t&&(t+="");var r,n=0,e=-1,i=!0;for(r=t.length-1;r>=0;--r)if(47===t.charCodeAt(r)){if(!i){n=r+1;break}}else-1===e&&(i=!1,e=r+1);return-1===e?"":t.slice(n,e)}(t);return r&&n.substr(-1*r.length)===r&&(n=n.substr(0,n.length-r.length)),n},r.extname=function(t){"string"!==typeof t&&(t+="");for(var r=-1,n=0,e=-1,i=!0,o=0,s=t.length-1;s>=0;--s){var u=t.charCodeAt(s);if(47!==u)-1===e&&(i=!1,e=s+1),46===u?-1===r?r=s:1!==o&&(o=1):-1!==r&&(o=-1);else if(!i){n=s+1;break}}return-1===r||-1===e||0===o||1===o&&r===e-1&&r===n+1?"":t.slice(r,e)};var i="b"==="ab".substr(-1)?function(t,r,n){return t.substr(r,n)}:function(t,r,n){return r<0&&(r=t.length+r),t.substr(r,n)}}).call(this,n(136))},2363:function(t,r,n){(function(r){var e=n(2361),i=n(2362),o=i.join(r,"path.txt");t.exports=function(){if(e.existsSync(o)){var t=e.readFileSync(o,"utf-8");return Object({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0}).ELECTRON_OVERRIDE_DIST_PATH?i.join(Object({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0}).ELECTRON_OVERRIDE_DIST_PATH,t):i.join(r,"dist",t)}throw new Error("Electron failed to install correctly, please delete node_modules/electron and try installing again")}()}).call(this,"/")}}]);