<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>首页 - OFFICE文档控件演示示例</title>
  <link href="./officecontrol/ntkoStyle.css" rel="stylesheet" type="text/css" />

  <script type="text/javascript" src="./officecontrol/ntko.js"></script>
  <script type="text/javascript" src="./officecontrol/json2.js"></script>

  <script type="text/javascript">
    var cmd;	//命令类型
    var datatext;
    var datavalue;
    function getQueryString(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      var r = decodeURIComponent(window.location.search.substr(1)).match(reg);
      if (r != null) return unescape(r[2]);

      return null;

    }

    function CurentTime()
    {
      var now = new Date();

      var year = now.getFullYear();       //年
      var month = now.getMonth() + 1;     //月
      var day = now.getDate();            //日

      var hh = now.getHours();            //时
      var mm = now.getMinutes();          //分
      var ss = now.getSeconds();          //分

      var clock = year + "-";

      if(month < 10)
        clock += "0";

      clock += month + "-";

      if(day < 10)
        clock += "0";

      clock += day + " ";

      if(hh < 10)
        clock += "0";

      clock += hh + ":";
      if (mm < 10) clock += '0';
      clock += mm + ":";
      if (ss < 10) clock += '0';
      clock += ss;
      return(clock);
    }

    //在子页面定义的向父页面回传值的方法，方法名可以自定义
    function ntkoSendDataToParentPage()
    {
      var protoFileId = getQueryString('protoFileId')
      const cbType = getQueryString('cbType')
      var varData = new Array();
      varData.push(CurentTime());
      varData.push(protoFileId);
      varData.push("closeEditing");
      varData.push("关闭编辑");
      ntkoBrowser.ntkoSetReturnValueToParentPage(cbType,varData);
    }
    function ntkoDataToChild(data){
      ntko.SetBookmarkValue("ntko",data);
      var ntkoc= ntko.ActiveDocument.bookMarks.count;
      for(var i=1;i<=ntkoc;i++){
        var ntkoname= ntko.ActiveDocument.bookMarks.item(i).name;
        if(ntkoname=="ntko"){
          ntko.ActiveDocument.bookMarks.item(i).select();
          ntko.ActiveDocument.Application.Selection.Font.Color=255;
          ntko.ActiveDocument.Application.Selection.Font.Bold=9999998;
          ntko.ActiveDocument.Application.Selection.Font.Size = 16;
        }
      }
    }
    function ntkoGetParentData(data){
      //data=decodeURIComponent(data);
      datatext=data;
    }
    function ntkocloseparentpage(){
      if(cmd==4 || cmd==2){
        ntkoSendDataToParentPage();
      }
    }

  </script>
</head>

<body onload="cmd=getQueryString('cmd');init(cmd, getQueryString('fileUrl'),getQueryString('readOnly'),getQueryString('type'),getQueryString('webUserName'),getQueryString('protectType'));">

<input id="newOssId" hidden="" value=""/>
<div id="divMask"  style="background: transparent;display: none">
  <div class="solpopu">
    <div class="popu">
      <div class="popuh">文档数据对比不一致，是否确认覆盖更新？</div>
      <div class="popub" id="msgContent"></div>
      <div class="popuf">
        <button id="affirm" class="primary">确定</button>
        <button id="cancel">取消</button>
      </div>
    </div>
  </div>
  <br />
  <iframe id="iframeStyle" src="#" allowtransparency="true"></iframe>
</div>



<script type="text/javascript" for="TANGER_OCX" event="AfterOpenFromURL(doc, statusCode)">
  // window.loading?.close()
</script>

<script type="text/javascript" for="TANGER_OCX" event="OnDocumentOpened(File, Document)" >
  if(cmd==5){
    ntko.ShowTipMessage("注意","已经接收到父页面传过来的值,请注意文档中内容的变化");
    ntkoDataToChild(datatext);
  }
</script>
<script type="text/javascript">


  /**
   * cbPath 回调地址接口 url
   * *newOssId 新的 oss文档id
   *  此方法会组装 打开文档url后所有参数 + 新的oss文档id 传输给cbPath 后端取需要的即可
   * POST 请求
   **/
  function callbackRequest(cbPath,param){
    ntko.ShowUIMessage("正在执行文档信息更新，请稍候...")
    var xhr = new XMLHttpRequest();
    var url = getQueryString('baseApi') +cbPath; // 替换为你的POST请求的URL "/product/documentLog/savePackagingLog"
    xhr.open("POST", url, true); // 第一个参数是请求类型，第二个是URL，第三个指示是否异步发送请求
    // 设置请求头，如果需要发送JSON数据，设置Content-Type
    xhr.setRequestHeader("Content-Type", "application/json");
    //   var Clientid =  window.localStorage.getItem('MDM-Client')
    //    var token =  window.localStorage.getItem('MDM-Token')
    var token = sessionStorage.getItem('Admin-Token')
    xhr.setRequestHeader("Authorization","Bearer " + token)
    // xhr.setRequestHeader("Clientid",Clientid)
    // 发送数据
    // const paramsObj = urlParamsToObject(decodeURIComponent(window.location.href));
    // paramsObj.fileId = fileId
    xhr.onreadystatechange = function () {
      ntko.CloseUIMessage()
      if(xhr.readyState === XMLHttpRequest.DONE){
        if ( xhr.status === 200) {
          // 请求成功
          var response = JSON.parse(xhr.responseText);
          if(response.code ==200){
            ntko.ShowTipMessage("信息","文档信息更新成功！");
          }else if(null != response.msg && "" != response.msg){
            ntko.ShowTipMessage("信息",response.msg,true);
          }else{
            ntko.ShowTipMessage("信息","文档信息更新失败！",true);
          }
        } else {
          // 请求出错
          ntko.ShowTipMessage("信息","文档信息更新失败！",true);
        }
      }

    };

    xhr.send(JSON.stringify(param));
  }



</script>

<script type="text/javascript">
  function showDivMsg(msg){
    if(msg !=="" && typeof msg !== "undefined"){
      document.getElementById("msgContent").innerHTML="<p>" +  msg + "</p>";
    }
    if (document.getElementById("divMask").style.display=="block")
    {
      document.documentElement.style.overflowY = "auto"
      document.getElementById("divMask").style.display="none";
    }
    else
    {
      document.documentElement.style.overflowY = "hidden"
      document.getElementById("divMask").style.display="block";
    }
  }

  document.getElementById("cancel").addEventListener("click", function() {
    // 点击事件处理逻辑
    showDivMsg("")
  });

  // document.getElementById("affirm").addEventListener("click", function() {
  //   // 点击事件处理逻辑
  //    showDivMsg("")
  //    var newOssId = document.getElementById("newOssId").value;
  //    callbackRequest(getQueryString('cbPath'),newOssId)
  // });
</script>

<script type="text/javascript">

  function urlParamsToObject(url) {
    // 获取URL中的查询字符串
    let queryString = url ? url.split('?')[1] : window.location.search.slice(1);
    // 解析查询字符串为键值对
    let obj = {};
    if (queryString) {
      queryString = queryString.split('#')[0]; // 去除URL中的hash
      let arr = queryString.split('&');
      for (let i = 0; i < arr.length; i++) {
        let a = arr[i].split('=');
        let paramName = decodeURIComponent(a[0]);
        let paramValue = typeof (a[1]) === 'undefined' ? undefined : decodeURIComponent(a[1]);
        paramValue = paramValue === 'true' ? true : paramValue === 'false' ? false : paramValue;
        if (paramName in obj) {
          if (typeof obj[paramName] === 'string') {
            obj[paramName] = [obj[paramName]];
          }
          if (typeof obj[paramName] === 'object') {
            obj[paramName].push(paramValue);
          }
        } else {
          obj[paramName] = paramValue;
        }
      }
    }
    return obj;
  }

</script>

<script type="text/javascript" for="TANGER_OCX" event="OnFileCommand(cmd2, canceled)" >
  const cbType = getQueryString('cbType')
  if (cmd2 == 3 && cbType) {
    // 拦截office本身操作
    ntko.CancelLastCommand = true;
    // 保存
    var isAccept = ntko.ShowConfirmMessage("提示","是否确认保存文档？",false,true);
    if (isAccept){
      // var result = "{\"code\":200,\"msg\":\"操作成功\",\"data\":{\"path\":\"process\\\\20250228112653907\\\\test1.docx\",\"fileName\":\"test1.docx\",\"fileId\":\"20250228112653907\"}}"
      var result = ntko.SaveToURL(getQueryString('baseApi')  + "/process/file/local_upload","file","",getQueryString('fileName'),0);
      var res = JSON.parse(result);
      var protoFileId = getQueryString('protoFileId')
      if (res.code == 200) {
        if(typeof cbType !== "undefined" &&  null != cbType){
          var varData = new Array();
          varData.push(res.data.fileId);
          varData.push(res.data.fileName);
          varData.push(protoFileId);
          ntkoBrowser.ntkoSetReturnValueToParentPage(cbType,varData);
          ntko.ShowTipMessage("注意","保存成功！");
        }
        const cbPath = getQueryString('cbPath')
        if(typeof cbPath !== "undefined" &&  null != cbPath){
          var param = {
            applyId: getQueryString('applyId'),
            fileId:res.data.fileId,
            fileName:res.data.fileName,
            protoFileId: protoFileId
          }
          callbackRequest(cbPath,param)
        }

      } else {
        ntko.ShowTipMessage("注意","保存失败，请联系管理员",true);
      }

    }
  }
</script>


<div>
  <script type="text/javascript" src="./officecontrol/ntkoofficecontrol.min.js"></script>
</div>

</body>
<style type="text/css">
  /*solpopu*/
  .solpopu{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9;
    background: transparent;
  }
  .solpopu .popu{
    display: block;
    width: 500px;
    background: #f0f0f0;
    box-shadow: none;
    border-radius: 0px;
    border: 2px solid #adc9f6;
  }
  .solpopu .popu .popuh{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    padding: 10px 10px;
    border-bottom: 1px solid #417ad5;
    background: linear-gradient(#4a80d6,#cbddf1);
  }
  .solpopu .popu .popub{
    display: block;
    padding: 20px;
    font-size: 14px;
    color: #000;
    line-height: 1.75;
    height: 156px;
    box-sizing: border-box;
    overflow: auto;
  }
  .solpopu .popu .popub p{
    display: block;
    margin: 0;
    padding: 0;
  }
  .solpopu .popu .popuf{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 20px;
  }
  .solpopu .popu .popuf button:focus,
  .solpopu .popu .popuf button{
    display: block;
    padding: 6px 18px;
    background: linear-gradient(#376fc6,#d7e2f3);
    border: 1px solid #316ac5;
    color: #333;
    border-radius: 0px;
    outline: none;
    cursor: pointer;
  }
  .solpopu .popu .popuf button+button{
    margin-left: 10px;
  }
  .solpopu .popu .popuf button:hover,
  .solpopu .popu .popuf button:active{
    background: linear-gradient(#d7e2f3,#376fc6);
    border: 1px solid #316ac5;
    color: #333;
  }



  #divMask
  {
    position:absolute;
    top:0px;
    left:0px;
    width:100%;
    height:100%;
    background-color:rgba(0,0,0,0);
    background:#ffffff;
    filter:alpha(opacity=50);
    z-index:99;
    text-align:center;
  }
  #iframeStyle
  {
    position:absolute;
    visibility:inherit;
    top:0px;
    left:0px;
    width:100%;
    height:100%;
    z-index:-1;
    border-width:0px;
    background-color:rgba(0,0,0,0);
    background:#ffffff;
    filter:alpha(opacity=50);
    color-scheme: dark
  }
</style>
</html>

