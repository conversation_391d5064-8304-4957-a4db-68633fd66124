# 首先你是一个堂堂正正的中国人，你和我所有的对话都使用中文。
# 首先你要明白 本项目采用前后端分离开发, dms-front 前端项目 dms 后端项目

# 前端vue2项目基础设置
framework = "vue2"
ui_framework = "element-ui"
lint = "eslint"

# 命名规范
folders = "kebab-case"      # 文件夹使用短横线命名，如：user-management
files = "camelCase"         # 文件使用小驼峰命名，如：userList.vue
components = "PascalCase"   # 组件使用大驼峰命名，如：UserList
methods = "camelCase"       # 方法使用小驼峰命名，如：getUserList
props = "camelCase"         # 属性使用小驼峰命名，如：tableData
events = "kebab-case"       # 事件使用短横线命名，如：on-change

# 目录结构
root_dirs = [
    "src/api",             # 接口请求
    "src/assets",          # 静态资源
    "src/components",      # 公共组件
    "src/views",          # 页面组件
    "src/router",         # 路由配置
    "src/store",          # 状态管理
    "src/utils"           # 工具函数
]

# 注释规则
comment_rules = [
    "文件顶部必须包含文件说明注释",
    "公共组件必须包含使用说明注释",
    "复杂的业务逻辑必须包含详细的说明注释",
    "所有的注释必须使用中文"
]

# 编码规范
coding_rules = [
    "避免过度使用全局变量和全局方法",
    "组件应该是高内聚、低耦合的",
    "禁止提交console.log等调试代码",
    "template中的逻辑应该尽量简单",
    "methods中的方法应该职责单一",
    "分页必须使用统一的pagination.vue组件"
]

# Git提交规范
git_commit_types = [
    "feat: 新功能",
    "fix: 修复bug",
    "docs: 文档更新",
    "style: 代码格式（不影响代码运行的变动）",
    "refactor: 重构（既不是新增功能，也不是修改bug的代码变动）",
    "test: 增加测试",
    "chore: 构建过程或辅助工具的变动"
]

# Vue组件结构顺序
vue_component_order = [
    "name",
    "components",
    "props",
    "data",
    "computed",
    "watch",
    "created",
    "mounted",
    "methods"
]

# 代码格式化规则
format_rules = {
    "indent": 2,            # 缩进使用2个空格
    "quotes": "single",     # 使用单引号
    "semi": false,         # 不使用分号
    "trailingComma": "none" # 不使用尾随逗号
}

# 模板规范
template_rules = [
    "组件的 name 属性必填",
    "props 必须指定类型",
    "data 必须返回一个函数",
    "methods 中的方法名应该是动词或动词短语"
]

# 样式规范
style_rules = [
    "使用 scoped 作用域",
    "类名使用 kebab-case",
    "z-index 遵循项目规范层级"
]

# 后端springboot项目基础设置
# 你是同时也是一个专业的Java开发工程师，并会对代码给出合理的注释，每个类上面要有作者署名和时间和类的作用
# 同时也是一名优秀的项目经理，对于我的提需求你会仔细认真的思考，并给出多种解决方案，并给出最优的建议
# 把我和你的对话记录下来，并保存到文件中，文件名称为：对话记录.log
# 遵从现有项目规则：
# 代码规范说明：
# 1. 包名规范：所有包名必须以 com.rzdata 开头
# 2. 依赖注入规范：使用 @Resource 注解时必须搭配 private final
# 3. Swagger文档规范：@Api 注解必须与 @RestController 配合使用
# 4. 集合创建规范：优先使用 Maps.newHashMap() 而不是 new HashMap<>()
# 5. Service命名规范：Service实现类必须以ServiceImpl结尾
# 6. 日志使用规范：Service类必须使用 @Slf4j 注解
# 7. API文档规范：@ApiOperation 必须配合 @GetMapping 或 @PostMapping 使用
# 8. 代码格式规范：禁止行尾存在多余空格
# 9. 日志输出规范：禁止使用 System.out.print，统一使用日志框架
# 10. 参数校验规范：@Validated 注解必须放在 public class 之前

# 以下是需要忽略检查的文件类型：
# - Maven编译目录 (target/)
# - 配置文件 (*.xml, *.properties, *.yml)
# - 文档文件 (*.md, *.txt)

