INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES
                                                                                                                        (UUID(), 'file_set.please_select_file_class', '请选择文件分类', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_company', '请选择下推公司', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_company_first', '请先选择下推公司', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_doc_class', '请选择下推文件分类', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select', '请选择', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.submit', '提交', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.cancel', '取消', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.get_subsidiary_doc_class_failed', '获取下推文件分类失败', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.get_detail_data_failed', '获取详情数据失败', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.save_success', '保存成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_connector', '请选择接收人', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.subsidiary_doc_file', '下推文件', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_doc_file', '请选择下推文件', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.get_subsidiary_doc_file_failed', '获取下推文件列表失败', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),

-- 英文国际化条目
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES
                                                                                                                        (UUID(), 'file_set.please_select_file_class', 'Please select file class', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_company', 'Please select subsidiary company', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_company_first', 'Please select subsidiary company first', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_doc_class', 'Please select subsidiary document class', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select', 'Please select', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.submit', 'Submit', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.cancel', 'Cancel', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.get_subsidiary_doc_class_failed', 'Failed to get subsidiary document class', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.get_detail_data_failed', 'Failed to get detail data', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.save_success', 'Save successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_connector', 'Please select connector', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.subsidiary_doc_file', 'Subsidiary Document File', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.please_select_subsidiary_doc_file', 'Please select subsidiary document file', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.get_subsidiary_doc_file_failed', 'Failed to obtain the list of pushed files', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());


-- 文件推送配置组件国际化 SQL
-- 基于模板: INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES ('001aeb58033a4f8e96d88b72f5e412b9', 'menus.1005', '用户导出', 'zh', 'front', 'CAM', 'luob', '2024-05-24 17:23:52', 'luob', '2024-05-24 17:23:52');

-- 中文国际化条目
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES
                                                                                                                        (UUID(), 'file_set.file_push', '文件推送', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.is_push_file', '是否下推文件', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.yes', '是', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.no', '否', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                        (UUID(), 'file_set.save_failed', '保存失败', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());


-- 英文国际化条目
    INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES
    (UUID(), 'file_set.file_push', 'File Push', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                            (UUID(), 'file_set.is_push_file', 'Push File', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                            (UUID(), 'file_set.yes', 'Yes', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                            (UUID(), 'file_set.no', 'No', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());
    INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '文件推送', 'file_push', 'flow_node_fun_list', null, 'default', 'N', '0', 'admin', '2025-08-06 16:33:08', 'admin', '2025-08-06 16:33:08', '文件推送', null, 'dc41618350206272c0b3271ccb9c3c76')
(UUID(), 'file_set.save_failed', 'Save failed', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 下推文件相关国际化配置
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES
                                                                                                                      (UUID(), 'file_set.push_file', '下推文件', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                      (UUID(), 'file_set.push_file', 'Push File', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),

                                                                                                                      (UUID(), 'file_set.please_select_push_class_first', '请先选择下推分类', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                      (UUID(), 'file_set.please_select_push_class_first', 'Please select push class first', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),

                                                                                                                      (UUID(), 'file_set.please_select_push_file', '请选择下推文件', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                      (UUID(), 'file_set.please_select_push_file', 'Please select push file', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),

                                                                                                                      (UUID(), 'file_set.get_push_file_failed', '获取下推文件列表失败', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                      (UUID(), 'file_set.get_push_file_failed', 'Failed to get push file list', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());



INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES
                                                                                                                      (UUID(), 'file_set.receipt_status', '回执状态', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
                                                                                                                      (UUID(), 'file_set.receipt_status', 'Receipt Status', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

