import request from '@/utils/request'

// 查询文件变更操作申请列表
export function listModifyApply(query) {
  return request({
    url: '/process/modifyApply/list',
    method: 'get',
    params: query
  })
}

export function getInfoByBpmnId(bpmnId) {
  return request({
    url: '/process/modifyApply/bpmnId/'+bpmnId,
    method: 'get',
  })
}

export function getBatchInfoByBpmnId(bpmnId) {
  return request({
    url: '/process/modifyApply/batch/bpmnId/'+bpmnId,
    method: 'get',
  })
}

export function linkLoglistlink(query) {
  return request({
    url: '/process/modifyApplyLink/list/link',
    method: 'get',
    params: query
  })
}

export function queryModifyApplyTrain(query) {
  return request({
    url: '/process/modifyApplyTrain/queryModifyApplyTrain',
    method: 'get',
    params: query
  })
}

export function getDocNoByApplyId(id) {
  return request({
    url: '/process/modifyApply/getDocNoByApplyId/' + id,
    method: 'get',
  })
}
export function getRecordDocNoByLinkId(data) {
  return request({
    url: '/process/modifyApply/getRecordDocNoByLinkId' ,
    method: 'post',
    data: data
  })
}

// 查询文件变更操作申请详细
export function getModifyApply(id) {
  return request({
    url: '/process/modifyApply/' + id,
    method: 'get'
  })
}

// 新增文件变更操作申请
export function addModifyApply(data) {
  return request({
    url: '/process/modifyApply',
    method: 'post',
    data: data,
    timeout: 30*60*1000
  })
}
export function addModifyApplyBatch(data) {
  return request({
    url: '/process/modifyApply/batch',
    method: 'post',
    data: data,
    timeout: 30*60*1000
  })
}

// 修改文件变更操作申请
export function updateModifyApply(data) {
  return request({
    url: '/process/modifyApply',
    method: 'put',
    data: data,
    timeout: 30*60*1000
  })
}
export function updateModifyApplyBatch(data) {
  return request({
    url: '/process/modifyApply/batch',
    method: 'put',
    data: data,
    timeout: 30*60*1000
  })
}

export function updateModifyById(data) {
  return request({
    url: '/process/modifyApply/updateById',
    method: 'post',
    data: data,
    timeout: 5*60*1000
  })
}

// 删除文件变更操作申请
export function delModifyApply(id) {
  return request({
    url: '/process/modifyApply/' + id,
    method: 'delete'
  })
}

export function updateDocId(data) {
  return request({
    url: '/process/modifyApply/update/docId',
    method: 'post',
    data: data,
  })
}

export function updateRecordDocId(data) {
  return request({
    url: '/process/modifyApply/update/record/docId',
    method: 'post',
    data: data,
  })
}

export function updateById(data) {
  return request({
    url: '/process/modifyApply/updateById',
    method: 'post',
    data: data,
  })
}

export function getDocNoListByApplyId(applyIdList) {
  return request({
    url: '/process/modifyApply/getDocNoListByApplyId',
    method: 'post',
    data: applyIdList
  })
}

export function updateDocIdList(data) {
  return request({
    url: '/process/modifyApply/update/docId/list',
    method: 'post',
    data: data,
  })
}

export function modifyApplyUpload(data) {
  return request({
    url: '/process/modifyApply/batch/upload',
    method: 'post',
    data: data
  })
}

export function modifyApplyImport(data) {
  return request({
    url: '/process/modifyApply/batch/import',
    method: 'post',
    data: data,
    timeout: 30*60*1000
  })
}

export function trainValidateRequired(data) {
  return request({
    url: '/process/modifyApplyTrain/validateRequired',
    method: 'post',
    data: data,
  })
}

export function onlyEditModifyApply(data) {
  return request({
    url: '/process/modifyApply/onlyEdit',
    method: 'post',
    data: data,
  })
}

export function compareFile(data) {
  return request({
    url: '/process/modifyApply/handleCompareFile',
    method: 'post',
    data: data,
  })
}

export function compareFileByFileId(data) {
  return request({
    url: '/process/filePdf/compare',
    method: 'get',
    data: data,
  })
}
