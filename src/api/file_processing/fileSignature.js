import request from '@/utils/request'

// 签章生效和执行发布
export function signEffective(applyId,type) {
  return request({
    url: '/process/file-signature/signEffective?applyId='+applyId+'&type='+type,
    method: 'get',
    timeout: 30*60*1000
  })
}

export function signEffectiveBatch(batchId,type) {
  return request({
    url: '/process/file-signature/signEffective/batch?batchId='+batchId+'&type='+type,
    method: 'get',
    timeout: 30*60*1000
  })
}

export function coverDownload(applyId) {
  return request({
    url: '/process/file-signature/cover/download?applyId='+applyId,
    method: 'get',
    timeout: 30*60*1000,
    responseType: 'blob'
  })
}

// 签章生效和执行发布
export function coverEffective(applyId,type) {
  return request({
    url: '/process/file-signature/coverEffective?applyId='+applyId+'&type='+type,
    method: 'get',
    timeout: 30*60*1000
  })
}

export function coverEffectiveBatch(batchId,type) {
  return request({
    url: '/process/file-signature/coverEffective/batch?batchId='+batchId+'&type='+type,
    method: 'get',
    timeout: 30*60*1000
  })
}

// 签章生效分发 （用于文件分发-生成文件）
export function signEffectiveDis(docDistributeId,distributeChar) {
  return request({
    url: '/process/file-signature/signEffectiveDis?docDistributeId='+docDistributeId+'&distributeChar='+distributeChar,
    method: 'get',
    timeout: 30*60*1000
  })
}
// 无分发
export function signEffectiveDisByVersionId(versionId) {
  return request({
    url: '/process/file-signature/signEffectiveDis/versionId?versionId='+versionId,
    method: 'get',
    timeout: 30*60*1000
  })
}


// 预览PDF文件增加水印
export function previewPdfWatermark(pdfFileId) {
  return request({
    url: '/process/file-signature/previewPdfWatermark?pdfFileId='+pdfFileId,
    method: 'get',
    timeout: 30*60*1000,
    responseType: 'blob'
  })
}
