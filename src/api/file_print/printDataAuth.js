import request from '@/utils/request'

// 查询文件打印数据权限列表
export function listPrintDataAuth(query) {
  return request({
    url: '/system/printDataAuth/list',
    method: 'get',
    params: query
  })
}

// 查询文件打印数据权限详细
export function getPrintDataAuth(id) {
  return request({
    url: '/system/printDataAuth/' + id,
    method: 'get'
  })
}

// 新增文件打印数据权限
export function addPrintDataAuth(data) {
  return request({
    url: '/system/printDataAuth',
    method: 'post',
    data: data
  })
}

// 修改文件打印数据权限
export function updatePrintDataAuth(data) {
  return request({
    url: '/system/printDataAuth',
    method: 'put',
    data: data
  })
}

// 删除文件打印数据权限
export function delPrintDataAuth(id) {
  return request({
    url: '/system/printDataAuth/' + id,
    method: 'delete'
  })
}


// 获取当前用户是否有打印全新
export function getCurInfoIsPrintAuth() {
  return request({
    url: '/system/printDataAuth/getCurInfoIsPrintAuth',
    method: 'get'
  })
}
