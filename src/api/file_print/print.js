import request from '@/utils/request'

// 增加打印记录
export function addPrintTask(data) {
  return request({
    url: '/process/printTask',
    method: 'post',
    data
  })
}

export function addPrintTaskCallBack(data) {
  return request({
    url: '/process/printTask/callback',
    method: 'post',
    data
  })
}

// 获取打印任务列表
export function getPrintTaskList(params) {
  return request({
    url: '/process/printTask/list',
    method: 'get',
    params
  })
}

// 获取打印任务详情
export function getPrintTaskDetail(taskId) {
  return request({
    url: `/process/printTask/detail/${taskId}`,
    method: 'get'
  })
}

// 批量删除打印任务
export function deletePrintTasks(data) {
  return request({
    url: '/process/printTask/delete',
    method: 'post',
    data
  })
}

// 重新打印任务
export function reprintTask(data) {
  return request({
    url: '/process/printTask/reprint',
    method: 'post',
    data
  })
}

// 导出打印任务列表
export function exportPrintTasks(params) {
  return request({
    url: '/process/printTask/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取打印机列表
export function getPrinterList() {
  return request({
    url: '/print/printer/list',
    method: 'get'
  })
}

// 修改打印任务状态
export function updatePrintTaskStatus(data) {
  return request({
    url: '/process/printTask/status/update',
    method: 'post',
    data
  })
}

// 获取打印任务统计数据
export function getPrintTaskStats(params) {
  return request({
    url: '/process/printTask/stats',
    method: 'get',
    params
  })
}

// 批量重新打印任务
export function batchReprintTasks(data) {
  return request({
    url: '/process/printTask/batch-reprint',
    method: 'post',
    data
  })
}

// 取消打印任务
export function cancelPrintTask(taskId) {
  return request({
    url: `/process/printTask/cancel/${taskId}`,
    method: 'post'
  })
}
