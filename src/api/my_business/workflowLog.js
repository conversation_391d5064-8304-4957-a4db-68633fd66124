import request from '@/utils/request'

// 查询流程审批记录列表
export function listWorkflowLog(query) {
  return request({
    url: '/process/workflowLog/list',
    method: 'get',
    params: query
  })
}

// 查询流程审批记录详细
export function getWorkflowLog(id) {
  return request({
    url: '/process/workflowLog/' + id,
    method: 'get'
  })
}

// 新增流程审批记录
export function addWorkflowLog(data) {
  return request({
    url: '/process/workflowLog',
    method: 'post',
    data: data
  })
}

// 修改流程审批记录
export function updateWorkflowLog(data) {
  return request({
    url: '/process/workflowLog',
    method: 'put',
    data: data
  })
}

// 删除流程审批记录
export function delWorkflowLog(id) {
  return request({
    url: '/process/workflowLog/' + id,
    method: 'delete'
  })
}

// 根据业务Id获取流程记录
export function selectLogByBusinessId(id) {
  return request({
    url: '/process/workflowLog/selectLogByBusinessId?businessId=' + id,
    method: 'get'
  })
}


// 流程日志
export function workflowGetLog(data) {
  return request({
    url: '/workflow/getLog',
    method: 'post',
    data: data
  })
}