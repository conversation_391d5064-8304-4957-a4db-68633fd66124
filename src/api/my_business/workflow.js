import request from '@/utils/request'


// 审批记录
export function workflowGetLog(data) {
  return request({
    url: '/workflow/getLog',
    method: 'post',
    data: data
  })
}
//流程实例撤回到拟稿人环节
export function workflowbacktostart(data) {
  return request({
    url: '/workflow/backtostart',
    method: 'post',
    data: data,
    timeout: 30*60*1000
  })
}
// 获取流程环节定义信息
export function activitydefinition(procDefId, actDefId) {
  return request({
    url: '/workflow/activitydefinition/' + procDefId + '/' + actDefId,
    method: 'get',

  })
}
// 查询列表
export function workflowToDoList(data) {
  return request({
    url: '/workflow/toDoList',
    method: 'post',
    data: data
  })
}

export function normalizeUrlToCurrentOrigin(targetUrl) {
  try {
    // 空值处理
    if (!targetUrl) return '';

    const current = new URL(window.location.href);
    const target = new URL(targetUrl, window.location.href);

    // 不处理非HTTP(S)协议
    if (!['http:', 'https:'].includes(target.protocol)) {
      return target.toString();
    }

    // 如果origin不同则替换
    if (current.origin !== target.origin) {
      return new URL(
        target.pathname + target.search + target.hash,
        current.origin
      ).toString();
    }

    return target.toString();
  } catch (e) {
    console.error('URL处理失败:', e);
    // 解析失败时尝试返回相对路径或原URL
    return targetUrl.startsWith('/')
      ? new URL(targetUrl, window.location.origin).toString()
      : targetUrl;
  }
}

// 新建流程，获取下一环节流程定义信息
export function workflowNextactsNew(data) {
  return request({
    url: '/workflow/nextacts/new',
    method: 'post',
    data: data
  })
}

// 新建流程，获取下一环节流程定义信息
// 获取下一环节集合，并可以标识已办和自动将未办理环节优先排序
export function workflowNextactsNew2(data) {
  return request({
    url: '/workflow/nextacts/new2',
    method: 'post',
    data: data
  })
}

// 查询流程审批记录列表
export function processWorkflowGetList(businessId) {
  return request({
    url: '/process/workflowLog/list' + '?pageNum=1&pageSize=100&businessId=' + businessId,
    method: 'get',

  })
}

export function getNextactuserByPending(query) {
  return request({
    url: '/workflow/nextactuser/pending',
    method: 'get',
    params: query,
    timeout: 30*60*1000
  })
}

export function getNextActUsersByNew(query) {
  return request({
    url: '/workflow/nextactuser/new',
    method: 'get',
    params: query,
    timeout: 30*60*1000
  })
}

export function workflowSubmit(data) {
  return request({
    url: '/workflow/submit',
    method: 'post',
    data: data
  })
}
export function workflowCancel(data) {
  return request({
    url: '/workflow/cancel',
    method: 'post',
    data: data
  })
}
export function workflowReject(data) {
  return request({
    url: '/workflow/reject',
    method: 'post',
    data: data
  })
}

export function workflowBacktoprev(data) {
  return request({
    url: '/workflow/backtoprev',
    method: 'post',
    data: data
  })
}

export function getHistAskLogUrl(procInstId) {
  return request({
    url: '/workflow/histasklogurl/' + procInstId,
    method: 'get',
  })
}

export function getExtAttributeModel(procDefId, actDefId) {
  return request({
    url: '/workflow/getExtAttributeModel?procDefId=' + procDefId + "&actDefId=" + actDefId,
    method: 'get',
  })
}
export function selectLogByBusinessId(query) {
  return request({
    url: '/process/workflowLog/selectLogByBusinessId?businessId' + query,
    method: 'get',

  })
}
export function workflowprocesskey(query) {
  return request({
    url: '/workflow/process/key/' + query,
    method: 'get',

  })
}

export function getStartActdef(procDefId) {
  return request({
    url: '/workflow/startactdef/' + procDefId,
    method: 'get',
  })
}

// 根据流程实例id获取流程实例信息和流程状态
export function procInstInfoAndStatus(procInstId) {
  return request({
    url: '/workflow/procInstInfoAndStatus/' + procInstId,
    method: 'get',
  })
}

// 根据流程实例id获取流程实例信息
export function getProcInstInfo(procInstId) {
  return request({
    url: '/workflow/procInstInfo/' + procInstId,
    method: 'get',
  })
}

export function shlkSelectLogByProcInstId(procInstId) {
  return request({
    url: '/process/workflowLog/shlkSelectLogByProcInstId?procInstId=' + procInstId,
    method: 'get',
  })
}

// 获取流程待选人员展示方式
export function selectUserStyle() {
  return request({
    url: '/workflow/selectUserStyle',
    method: 'get',
  })
}

// 根据流程实例id获取流程实例信息
export function getRecordbyPorcInstId(procInstId) {
  return request({
    url: '/workflow/getRecord/' + procInstId,
    method: 'get',
  })
}

export function getRedirectDefId(query) {
  return request({
    url: '/process/workflowLog/getRedirectDefId',
    method: 'get',
    params: query
  })
}

export function backFlowToOne(data) {
  return request({
    url: '/workflow/back/one',
    method: 'post',
    data: data
  })
}

export function workflowTransfer(data) {
  return request({
    url: '/workflow/transfer',
    method: 'post',
    data: data
  })
}

export function getDoing(data) {
  return request({
    url: '/workflow/doing',
    method: 'post',
    data: data
  })
}



