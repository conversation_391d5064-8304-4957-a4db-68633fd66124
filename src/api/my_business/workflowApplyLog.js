import request from '@/utils/request'

// 查询流程申请记录列表
export function listWorkflowApplyLog(query) {
  return request({
    url: '/process/workflowApplyLog/list',
    method: 'get',
    params: query
  })
}

// 查询流程申请记录详细
export function getWorkflowApplyLog(id) {
  return request({
    url: '/process/workflowApplyLog/' + id,
    method: 'get'
  })
}

// 新增流程申请记录
export function addWorkflowApplyLog(data) {
  return request({
    url: '/process/workflowApplyLog',
    method: 'post',
    data: data
  })
}

// 修改流程申请记录
export function updateWorkflowApplyLog(data) {
  return request({
    url: '/process/workflowApplyLog',
    method: 'put',
    data: data
  })
}

// 删除流程申请记录
export function delWorkflowApplyLog(id) {
  return request({
    url: '/process/workflowApplyLog/' + id,
    method: 'delete'
  })
}

// 根据文件id获取流程状态
export function selectStatusByDocId(query) {
  return request({
    url: '/process/workflowApplyLog/selectStatusByDocId',
    method: 'get',
    params: query
  })
}

// 根据文件id获取流程状态
export function selectStatusRecord(query) {
  return request({
    url: '/process/workflowApplyLog/selectStatusRecord',
    method: 'get',
    params: query
  })
}

// 根据流程类型查询流水号
export function selectApplySerial(applyClass) {
  return request({
    url: '/process/workflowApplyLog/apply/serial/' + applyClass,
    method: 'get'
  })
}


