import request from '@/utils/request'


export function queryNews(data) {
  return request({
    url: '/process/doc-message/query',
    method: 'post',
    data: data
  })
}

export function readNews(data) {
  return request({
      url: '/process/doc-message/read',
      data: data,
      method: 'post'
  })
}

export function getNewsNum(data) {
  return request({
    url: '/process/doc-message/unread-num',
    method: 'post',
    data: data
  })
}
