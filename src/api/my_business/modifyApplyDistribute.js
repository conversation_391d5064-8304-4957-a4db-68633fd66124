import request from '@/utils/request'

// 查询文件变更操作申请培训记录列表
export function listModifyApplyDistribute(query) {
  return request({
    url: '/process/modifyApplyDistribute/list',
    method: 'get',
    params: query
  })
}

// 查询文件变更操作申请培训记录详细
export function getModifyApplyDistribute(id) {
  return request({
    url: '/process/modifyApplyDistribute/' + id,
    method: 'get'
  })
}

// 新增文件变更操作申请培训记录
export function addModifyApplyDistribute(data) {
  return request({
    url: '/process/modifyApplyDistribute',
    method: 'post',
    data: data
  })
}

// 新增文件变更操作申请培训记录
export function updateModifyApplyDistributeList(data,versionId) {
  return request({
    url: '/process/modifyApplyDistribute/update/list/'+versionId,
    method: 'post',
    data: data
  })
}

export function getModifyApplyDistributeList(versionId) {
  return request({
    url: '/process/modifyApplyDistribute/list/'+versionId,
    method: 'post',
  })
}

// 修改文件变更操作申请培训记录
export function updateModifyApplyDistribute(data) {
  return request({
    url: '/process/modifyApplyDistribute',
    method: 'put',
    data: data
  })
}

// 删除文件变更操作申请培训记录
export function delModifyApplyDistribute(id) {
  return request({
    url: '/process/modifyApplyDistribute/' + id,
    method: 'delete'
  })
}
