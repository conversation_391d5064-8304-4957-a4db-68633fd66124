import PreviewPdf from "./previewPdf"
import { fileLocalDownload } from "@/api/commmon/file";
import { previewPdfWatermark } from "@/api/file_processing/fileSignature.js";

// PDF在线预览且弹出新的页签展示
export function fileLocalPdfView(pdfFileId) {
  let isPreviewWaterMark = true

  if(isPreviewWaterMark) {
    // 原生PDF文件在线预览
    return previewPdfWatermark(pdfFileId).then(res=>{
      new PreviewPdf({
          blob: res,
          docTitle: '文件预览',
          isAddWatermark: false,
          watermark: {
            type: 'canvas',
            text: 'WFT-CANVAS'
          }
      })
    })
  } else {
    // 原生PDF文件在线预览
    return fileLocalDownload(pdfFileId).then(res=>{
      new PreviewPdf({
          blob: res,
          docTitle: '文件预览',
          isAddWatermark: false,
          watermark: {
            type: 'canvas',
            text: 'WFT-CANVAS'
          }
      })
    })
  }
    
}