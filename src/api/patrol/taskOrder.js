import request from '@/utils/request'

// 查询巡检工单列表
export function listTaskOrder(query) {
  return request({
    url: '/patrol/task/order/list',
    method: 'get',
    params: query
  })
}

// 查询巡检工单列表
export function listAllTaskOrder(query) {
  return request({
    url: '/patrol/task/order/list/all',
    method: 'get',
    params: query
  })
}

// 查询巡检工单详细
export function getTaskOrder(id) {
  return request({
    url: '/patrol/task/order/' + id,
    method: 'get'
  })
}

// 新增巡检工单
export function addTaskOrder(data) {
  return request({
    url: '/patrol/task/order',
    method: 'post',
    data: data
  })
}

// 修改巡检工单
export function updateTaskOrder(data) {
  return request({
    url: '/patrol/task/order',
    method: 'put',
    data: data
  })
}

// 删除巡检工单
export function delTaskOrder(id) {
  return request({
    url: '/patrol/task/order/' + id,
    method: 'delete'
  })
}
