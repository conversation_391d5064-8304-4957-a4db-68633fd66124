import request from '@/utils/request'

// 查询巡检工单-记录列表
export function listTaskOrderItem(query) {
  return request({
    url: '/patrol/task/order/item/list',
    method: 'get',
    params: query
  })
}

// 根据工单id获取巡检工单-记录列表
export function listTaskOrderItemByOrderId(orderId) {
  return request({
    url: '/patrol/task/order/item/list/'+orderId,
    method: 'get'
  })
}

// 查询巡检工单-记录详细
export function getTaskOrderItem(id) {
  return request({
    url: '/patrol/task/order/item/' + id,
    method: 'get'
  })
}

// 新增巡检工单-记录
export function addTaskOrderItem(data) {
  return request({
    url: '/patrol/task/order/item',
    method: 'post',
    data: data
  })
}

// 修改巡检工单-记录
export function updateTaskOrderItem(data) {
  return request({
    url: '/patrol/task/order/item',
    method: 'put',
    data: data
  })
}

// 删除巡检工单-记录
export function delTaskOrderItem(id) {
  return request({
    url: '/patrol/task/order/item/' + id,
    method: 'delete'
  })
}
