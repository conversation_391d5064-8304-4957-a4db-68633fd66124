import request from '@/utils/request'

// 查询记录列表
export function listVersion(query) {
  return request({
    url: '/process/external/list',
    method: 'get',
    params: query
  })
}
// 删除记录
export function delVersion(id) {
  return request({
    url: '/process/external/' + id,
    method: 'delete'
  })
}
//导入
export function standardimport(data) {
  return request({
      url: '/process/external/import',
      method: 'post',
      data: data,
      timeout: 10*60*1000
  })
}
//修改
export function updateVersion(data) {
  return request({
    url: '/process/external',
    method: 'put',
    data: data
  })
}
//修改
export function deleteFile(data) {
  return request({
    url: '/process/external/deleteFile',
    method: 'put',
    data: data
  })
}


