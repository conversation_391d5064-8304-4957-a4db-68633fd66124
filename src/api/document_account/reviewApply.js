import request from '@/utils/request'

// 查询文件复审申请列表
export function listReviewApply(query) {
  return request({
    url: '/process/reviewApply/list',
    method: 'get',
    params: query
  })
}

// 查询文件复审申请详细
export function getReviewApply(id) {
  return request({
    url: '/process/reviewApply/' + id,
    method: 'get'
  })
}

export function getReviewApplyByBpmnId(bpmnId) {
  return request({
    url: '/process/reviewApply/bpmnId/' + bpmnId,
    method: 'get'
  })
}

// 新增文件复审申请
export function addReviewApply(data) {
  return request({
    url: '/process/reviewApply',
    method: 'post',
    data: data,
    timeout: 30*60*1000
  })
}

// 修改文件复审申请
export function updateReviewApply(data) {
  return request({
    url: '/process/reviewApply',
    method: 'put',
    data: data
  })
}

// 删除文件复审申请
export function delReviewApply(id) {
  return request({
    url: '/process/reviewApply/' + id,
    method: 'delete'
  })
}
