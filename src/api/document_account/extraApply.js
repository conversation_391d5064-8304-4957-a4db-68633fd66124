import request from '@/utils/request'

// 查询文件增发申请列表
export function listExtraApply(query) {
  return request({
    url: '/process/extraApply/list',
    method: 'get',
    params: query
  })
}

// 查询文件增发申请详细
export function getExtraApply(id) {
  return request({
    url: '/process/extraApply/' + id,
    method: 'get'
  })
}

export function addExtraApplyByBpmnId(bpmnId) {
  return request({
    url: '/process/extraApply/bpmnId/' + bpmnId,
    method: 'get'
  })
}

// 新增文件增发申请
export function addExtraApply(data) {
  return request({
    url: '/process/extraApply',
    method: 'post',
    data: data,
    timeout: 30*60*1000
  })
}

// 修改文件增发申请
export function updateExtraApply(data) {
  return request({
    url: '/process/extraApply',
    method: 'put',
    data: data,
    timeout: 30*60*1000
  })
}

// 删除文件增发申请
export function delExtraApply(id) {
  return request({
    url: '/process/extraApply/' + id,
    method: 'delete'
  })
}

export function listExtraApplyItem(query) {
  return request({
    url: '/process/extraApply/item/list',
    method: 'get',
    params: query
  })
}
