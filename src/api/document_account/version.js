import request from '@/utils/request'
import { param } from '@/utils'

// 查询文件版本记录列表
export function listVersion(data) {
  return request({
    url: '/process/version/list',
    method: 'post',
    data: data
  })
}

export function listVersionAll(data) {
  return request({
    url: '/process/version/list/all',
    method: 'post',
    data: data
  })
}

// 查询文件版本外部门列表
export function listOtherDept(data) {
  return request({
    url: '/process/version/list/other/dept',
    method: 'post',
    data: data
  })
}


// 查询文件版本记录列表
export function validateUniqueness(query) {
  return request({
    url: '/process/version/validateUniqueness',
    method: 'get',
    params: query
  })
}

export function distributeLogcheckSign(query) {
  return request({
    url: '/process/distributeLog/checkSign',
    method: 'get',
    params: query
  })
}

// 查询活跃文件版本列表
export function listActiveVersion(query) {
  return request({
    url: '/process/version/active',
    method: 'get',
    params: query
  })
}

// 查询文件版本记录详细
export function getVersion(id) {
  return request({
    url: '/process/version/' + id,
    method: 'get'
  })
}

// 新增文件版本记录
export function addVersion(data) {
  return request({
    url: '/process/version',
    method: 'post',
    data: data
  })
}

// 修改文件版本记录
export function updateVersion(data) {
  return request({
    url: '/process/version',
    method: 'put',
    data: data
  })
}

// 删除文件版本记录
export function delVersion(id) {
  return request({
    url: '/process/version/' + id,
    method: 'delete'
  })
}
