import request from '@/utils/request'

// 查询文件丢失申请列表
export function listLostApply(query) {
  return request({
    url: '/process/lostApply/list',
    method: 'get',
    params: query
  })
}

// 查询文件丢失申请详细
export function getLostApply(id) {
  return request({
    url: '/process/lostApply/' + id,
    method: 'get'
  })
}

export function getLostApplyByBpmnId(bpmnId) {
  return request({
    url: '/process/lostApply/bpmnId/' + bpmnId,
    method: 'get'
  })
}

// 新增文件丢失申请
export function addLostApply(data) {
  return request({
    url: '/process/lostApply',
    method: 'post',
    data: data
  })
}

// 修改文件丢失申请
export function updateLostApply(data) {
  return request({
    url: '/process/lostApply',
    method: 'put',
    data: data
  })
}

// 删除文件丢失申请
export function delLostApply(id) {
  return request({
    url: '/process/lostApply/' + id,
    method: 'delete'
  })
}
