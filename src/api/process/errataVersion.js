import request from '@/utils/request'

// 查询勘误文件版本记录列表
export function listErrataVersion(query) {
  return request({
    url: '/process/errataVersion/list',
    method: 'get',
    params: query
  })
}

// 查询勘误文件版本记录详细
export function getErrataVersion(id) {
  return request({
    url: '/process/errataVersion/' + id,
    method: 'get'
  })
}

export function queryPrev(data) {
  return request({
    url: '/process/errataVersion/prev',
    method: 'post',
    data: data
  })
}

// 新增勘误文件版本记录
export function addErrataVersion(data) {
  return request({
    url: '/process/errataVersion',
    method: 'post',
    data: data,
    timeout: 30*60*1000,
  })
}

// 修改勘误文件版本记录
export function updateErrataVersion(data) {
  return request({
    url: '/process/errataVersion',
    method: 'put',
    data: data
  })
}

// 删除勘误文件版本记录
export function delErrataVersion(id) {
  return request({
    url: '/process/errataVersion/' + id,
    method: 'delete'
  })
}
