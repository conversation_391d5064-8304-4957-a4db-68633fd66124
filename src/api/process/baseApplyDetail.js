import request from '@/utils/request'

// 查询项目文件基线申请明细列表
export function listBaseApplyDetail(query) {
  return request({
    url: '/process/baseApplyDetail/list',
    method: 'get',
    params: query
  })
}

export function getInfoByStandardId(standardId) {
  return request({
    url: '/process/baseApplyDetail/standardId/' + standardId,
    method: 'get'
  })
}

// 查询项目文件基线申请明细详细
export function getBaseApplyDetail(id) {
  return request({
    url: '/process/baseApplyDetail/' + id,
    method: 'get'
  })
}

// 新增项目文件基线申请明细
export function addBaseApplyDetail(data) {
  return request({
    url: '/process/baseApplyDetail',
    method: 'post',
    data: data
  })
}

// 修改项目文件基线申请明细
export function updateBaseApplyDetail(data) {
  return request({
    url: '/process/baseApplyDetail',
    method: 'put',
    data: data
  })
}

// 删除项目文件基线申请明细
export function delBaseApplyDetail(id) {
  return request({
    url: '/process/baseApplyDetail/' + id,
    method: 'delete'
  })
}

export function selectStatusByVersionId(ids) {
  return request({
    url: '/process/baseApplyDetail/selectStatusByVersionId/' + ids,
    method: 'get'
  })
}
