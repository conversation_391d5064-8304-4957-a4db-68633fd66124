import request from '@/utils/request'

// 查询文件丢失申请详情列表
export function listLostApplyItem(query) {
  return request({
    url: '/process/lostApplyItem/list',
    method: 'get',
    params: query
  })
}

// 查询文件丢失申请详情详细
export function getLostApplyItem(id) {
  return request({
    url: '/process/lostApplyItem/' + id,
    method: 'get'
  })
}

// 新增文件丢失申请详情
export function addLostApplyItem(data) {
  return request({
    url: '/process/lostApplyItem',
    method: 'post',
    data: data
  })
}

// 修改文件丢失申请详情
export function updateLostApplyItem(data) {
  return request({
    url: '/process/lostApplyItem',
    method: 'put',
    data: data
  })
}

// 删除文件丢失申请详情
export function delLostApplyItem(id) {
  return request({
    url: '/process/lostApplyItem/' + id,
    method: 'delete'
  })
}
