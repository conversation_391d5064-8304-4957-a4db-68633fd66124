import request from '@/utils/request'

// 查询文件编辑日志列表
export function listEditLog(query) {
  return request({
    url: '/process/editLog/list',
    method: 'get',
    params: query
  })
}

// 查询文件编辑日志详细
export function getEditLog(id) {
  return request({
    url: '/process/editLog/' + id,
    method: 'get'
  })
}

// 新增文件编辑日志
export function addEditLog(data) {
  return request({
    url: '/process/editLog',
    method: 'post',
    data: data
  })
}

// 修改文件编辑日志
export function updateEditLog(data) {
  return request({
    url: '/process/editLog',
    method: 'put',
    data: data
  })
}

// 删除文件编辑日志
export function delEditLog(id) {
  return request({
    url: '/process/editLog/' + id,
    method: 'delete'
  })
}
