import request from '@/utils/request'

// 查询文件补发申请详情列表
export function listReissueApplyItem(query) {
  return request({
    url: '/process/reissueApplyItem/list',
    method: 'get',
    params: query
  })
}

// 查询文件补发申请详情详细
export function getReissueApplyItem(id) {
  return request({
    url: '/process/reissueApplyItem/' + id,
    method: 'get'
  })
}

// 新增文件补发申请详情
export function addReissueApplyItem(data) {
  return request({
    url: '/process/reissueApplyItem',
    method: 'post',
    data: data
  })
}

// 修改文件补发申请详情
export function updateReissueApplyItem(data) {
  return request({
    url: '/process/reissueApplyItem',
    method: 'put',
    data: data
  })
}

// 删除文件补发申请详情
export function delReissueApplyItem(id) {
  return request({
    url: '/process/reissueApplyItem/' + id,
    method: 'delete'
  })
}
