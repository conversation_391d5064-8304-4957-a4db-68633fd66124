import request from '@/utils/request'

// 查询文件补发申请列表
export function listReissueApply(query) {
  return request({
    url: '/process/reissueApply/list',
    method: 'get',
    params: query
  })
}

// 查询文件补发申请详细
export function getReissueApply(id) {
  return request({
    url: '/process/reissueApply/' + id,
    method: 'get'
  })
}

export function getReissueApplyByBpmnId(bpmnId) {
  return request({
    url: '/process/reissueApply/bpmnId/' + bpmnId,
    method: 'get'
  })
}

// 新增文件补发申请
export function addReissueApply(data) {
  return request({
    url: '/process/reissueApply',
    method: 'post',
    data: data
  })
}

// 修改文件补发申请
export function updateReissueApply(data) {
  return request({
    url: '/process/reissueApply',
    method: 'put',
    data: data
  })
}

// 删除文件补发申请
export function delReissueApply(id) {
  return request({
    url: '/process/reissueApply/' + id,
    method: 'delete'
  })
}
