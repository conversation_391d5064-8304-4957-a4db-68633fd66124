import request from '@/utils/request'

// 查询文件关联勘误记录列表
export function listErrataLink(query) {
  return request({
    url: '/process/errataLink/list',
    method: 'get',
    params: query
  })
}

// 查询文件关联勘误记录详细
export function getErrataLink(id) {
  return request({
    url: '/process/errataLink/' + id,
    method: 'get'
  })
}

// 新增文件关联勘误记录
export function addErrataLink(data) {
  return request({
    url: '/process/errataLink',
    method: 'post',
    data: data
  })
}

// 修改文件关联勘误记录
export function updateErrataLink(data) {
  return request({
    url: '/process/errataLink',
    method: 'put',
    data: data
  })
}

// 删除文件关联勘误记录
export function delErrataLink(id) {
  return request({
    url: '/process/errataLink/' + id,
    method: 'delete'
  })
}
