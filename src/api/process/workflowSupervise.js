import request from '@/utils/request'

// 查询流程督办配置列表
export function listWorkflowSupervise(query) {
  return request({
    url: '/process/workflow/supervise/list',
    method: 'get',
    params: query
  })
}

// 同步流程督办配置
export function syncWorkflowSupervise() {
  return request({
    url: '/process/workflow/supervise/sync',
    method: 'post'
  })
}

export function updateWorkflowSupervise(data) {
  return request({
    url: '/process/workflow/supervise',
    method: 'put',
    data: data
  })
}

// 删除流程督办配置
export function delWorkflowSupervise(id) {
  return request({
    url: '/process/workflow/supervise/' + id,
    method: 'delete'
  })
}

