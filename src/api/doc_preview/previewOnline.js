import request from '@/utils/request'


export function view(id, linkType, mode = 'view') {
  console.log('REF_DOC ', linkType);

  // if (linkType == 'REF_DOC') {
    // return request({
    //   url: '/process/doc-preview/view-by-version?id=' + id + '&mode=' + mode,
    //   method: 'get'
    // })
  // } else {
    return request({
      url: '/process/doc-preview/view?id=' + id + '&mode=' + mode,
      method: 'get'
    })
  // }

}
export function docPreviewViewByVersion(id) {
  return request({
    url: '/process/doc-preview/view-by-version?id=' + id,
    method: 'get'
  })
}

export function personalPreview(query) {
  return request({
    url: '/process/doc-preview/personal-preview',
    method: 'get',
    params: query
  })
}
export function edit(id) {
  return request({
    url: '/process/doc-preview/edit/' + id,
    method: 'get'
  })
}
