import request from '@/utils/request'

// 查询合稿文件分类列表
export function listFileClass(query) {
  return request({
    url: '/combined/fileClass/list',
    method: 'get',
    params: query
  })
}

// 查询合稿文件分类详细
export function getFileClass(id) {
  return request({
    url: '/combined/fileClass/' + id,
    method: 'get'
  })
}

// 新增合稿文件分类
export function addFileClass(data) {
  return request({
    url: '/combined/fileClass',
    method: 'post',
    data: data
  })
}

// 修改合稿文件分类
export function updateFileClass(data) {
  return request({
    url: '/combined/fileClass',
    method: 'put',
    data: data
  })
}

// 删除合稿文件分类
export function delFileClass(id) {
  return request({
    url: '/combined/fileClass/' + id,
    method: 'delete'
  })
}
