import request from '@/utils/request'

// 与流程平台比较流程环节数据
export function compareFlowPlatNodeList(flowKey,id) {
  return request({
    url: `/setting/docClassFlowNode/compareFlowPlatNodeList/${flowKey}/${id}`,
    method: 'get',
  })
}

// 与流程平台同步流程环节数据
export function syncFlowPlatNodeList(docClass,bizType,flowKey,id) {
  return request({
    url: `/setting/docClassFlowNode/syncFlowPlatNodeList/${docClass}/${bizType}/${flowKey}/${id}`,
    method: 'get',
  })
}

// 根据流程KEY获取流程节点清单
export function getByFlowKey(docClass,bizType,flowKey) {
  return request({
    url: `/setting/docClassFlowNode/getByFlowKey/${docClass}/${bizType}/${flowKey}`,
    method: 'get',
  })
}

// 查询文件分类设置-流程节点设置列表
export function listDocClassFlowNode(query) {
  return request({
    url: '/setting/docClassFlowNode/list',
    method: 'get',
    params: query
  })
}

// 查询文件分类设置-流程节点设置详细
export function getDocClassFlowNode(id) {
  return request({
    url: '/setting/docClassFlowNode/' + id,
    method: 'get'
  })
}

// 新增文件分类设置-流程节点设置
export function addDocClassFlowNode(data) {
  return request({
    url: '/setting/docClassFlowNode',
    method: 'post',
    data: data
  })
}

// 修改文件分类设置-流程节点设置
export function updateDocClassFlowNode(data) {
  return request({
    url: '/setting/docClassFlowNode',
    method: 'put',
    data: data
  })
}

// 删除文件分类设置-流程节点设置
export function delDocClassFlowNode(id) {
  return request({
    url: '/setting/docClassFlowNode/' + id,
    method: 'delete'
  })
}

export function getNodeList(params) {
  return request({
    url: `/setting/docClassFlowNode/getNodeList`,
    method: 'get',
    params: params
  })
}
