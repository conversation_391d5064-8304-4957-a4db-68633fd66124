import request from '@/utils/request'

// 查询合稿管理列表
export function listCodraft(query) {
  return request({
    url: '/setting/codraft/list',
    method: 'get',
    params: query
  })
}

// 查询合稿管理详细
export function getCodraft(id) {
  return request({
    url: '/setting/codraft/' + id,
    method: 'get'
  })
}

// 新增合稿管理
export function addCodraft(data) {
  return request({
    url: '/setting/codraft',
    method: 'post',
    data: data
  })
}

// 修改合稿管理
export function updateCodraft(data) {
  return request({
    url: '/setting/codraft',
    method: 'put',
    data: data
  })
}

// 删除合稿管理
export function delCodraft(id) {
  return request({
    url: '/setting/codraft/' + id,
    method: 'delete'
  })
}
