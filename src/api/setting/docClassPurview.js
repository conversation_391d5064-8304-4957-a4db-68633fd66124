import request from '@/utils/request'

// 查询文件类型权限列表
export function listDocClassPurview(query) {
  return request({
    url: '/setting/docClassPurview/list',
    method: 'get',
    params: query
  })
}

// 查询文件类型权限详细
export function getDocClassPurview(id) {
  return request({
    url: '/setting/docClassPurview/' + id,
    method: 'get'
  })
}

// 新增文件类型权限
export function addDocClassPurview(data) {
  return request({
    url: '/setting/docClassPurview',
    method: 'post',
    data: data
  })
}

// 修改文件类型权限
export function updateDocClassPurview(data) {
  return request({
    url: '/setting/docClassPurview',
    method: 'put',
    data: data
  })
}

// 删除文件类型权限
export function delDocClassPurview(id) {
  return request({
    url: '/setting/docClassPurview/' + id,
    method: 'delete'
  })
}
