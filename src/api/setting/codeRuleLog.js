import request from '@/utils/request'

// 查询编号规则日志列表
export function listCodeRuleLog(query) {
  return request({
    url: '/setting/codeRuleLog/list',
    method: 'get',
    params: query
  })
}

// 查询编号规则日志详细
export function getCodeRuleLog(id) {
  return request({
    url: '/setting/codeRuleLog/' + id,
    method: 'get'
  })
}

// 新增编号规则日志
export function addCodeRuleLog(data) {
  return request({
    url: '/setting/codeRuleLog',
    method: 'post',
    data: data
  })
}

// 修改编号规则日志
export function updateCodeRuleLog(data) {
  return request({
    url: '/setting/codeRuleLog',
    method: 'put',
    data: data
  })
}

// 删除编号规则日志
export function delCodeRuleLog(id) {
  return request({
    url: '/setting/codeRuleLog/' + id,
    method: 'delete'
  })
}
