import request from '@/utils/request'

// 查询水印规则列表
export function listWatermarkRule(query) {
  return request({
    url: '/setting/watermarkRule/list',
    method: 'get',
    params: query
  })
}
// 查询所有水印规则
export function listAllWatermarkRule(query) {
  return request({
    url: '/setting/watermarkRule/listAll',
    method: 'get',
    params: query
  })
}

// 查询水印规则详细
export function getWatermarkRule(id) {
  return request({
    url: '/setting/watermarkRule/' + id,
    method: 'get'
  })
}


// 新增水印规则
export function addWatermarkRule(data) {
  return request({
    url: '/setting/watermarkRule',
    method: 'post',
    data: data
  })
}

// 修改水印规则
export function updateWatermarkRule(data) {
  return request({
    url: '/setting/watermarkRule',
    method: 'put',
    data: data
  })
}

// 删除水印规则
export function delWatermarkRule(id) {
  return request({
    url: '/setting/watermarkRule/' + id,
    method: 'delete'
  })
}

// 检查水印规则是否被使用
export function checkWatermarkRuleUsage(id) {
  return request({
    url: '/setting/watermarkRule/checkUsage/' + id,
    method: 'get'
  })
}

// 移除水印规则应用
export function removeWatermarkRuleUsage(data) {
  return request({
    url: '/setting/watermarkRule/removeUsage',
    method: 'post',
    data: data
  })
}

// 新增水印规则应用
export function addWatermarkRuleUsage(data) {
  return request({
    url: '/setting/watermarkRule/addUsage',
    method: 'post',
    data: data
  })
}

// 获取文件类型列表
export function listDocClass() {
  return request({
    url: '/setting/docClass/listAll',
    method: 'get'
  })
}
// 预览pdf
export function previewPdf(id) {
  return request({
    url: '/setting/watermarkRule/preview',
    method: 'get',
    params: { id }
  })
}