import request from '@/utils/request'

// 查询文件类型设置列表
export function listDocClassSetting(query) {
  return request({
    url: '/setting/docClassSetting/list',
    method: 'get',
    params: query
  })
}

export function getInfoBy(query) {
  return request({
    url: '/setting/docClassSetting/info',
    method: 'get',
    params: query
  })
}

// 查询文件类型设置详细
export function getDocClassSetting(id) {
  return request({
    url: '/setting/docClassSetting/' + id,
    method: 'get'
  })
}

// 新增文件类型设置
export function addDocClassSetting(data) {
  return request({
    url: '/setting/docClassSetting',
    method: 'post',
    data: data
  })
}

// 修改文件类型设置
export function updateDocClassSetting(data) {
  return request({
    url: '/setting/docClassSetting',
    method: 'put',
    data: data
  })
}

// 删除文件类型设置
export function delDocClassSetting(id) {
  return request({
    url: '/setting/docClassSetting/' + id,
    method: 'delete'
  })
}
