import request from '@/utils/request'

// 查询合稿管理列表
export function listMgr(query) {
  return request({
    url: '/combined/mgr/list',
    method: 'get',
    params: query
  })
}

// 查询合稿管理详细
export function getMgr(id) {
  return request({
    url: '/combined/mgr/' + id,
    method: 'get'
  })
}

// 新增合稿管理
export function addMgr(data) {
  return request({
    url: '/combined/mgr',
    method: 'post',
    data: data
  })
}

// 修改合稿管理
export function updateMgr(data) {
  return request({
    url: '/combined/mgr',
    method: 'put',
    data: data
  })
}

// 删除合稿管理
export function delMgr(id) {
  return request({
    url: '/combined/mgr/' + id,
    method: 'delete'
  })
}
