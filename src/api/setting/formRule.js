import request from '@/utils/request'

// 查询单规则列表
export function listFormRule(query) {
  return request({
    url: '/setting/formRule/list',
    method: 'get',
    params: query
  })
}

// 查询单规则详细
export function getFormRule(id) {
  return request({
    url: '/setting/formRule/' + id,
    method: 'get'
  })
}

// 查询单规则详细通过递归查找
export function getFormRuleRecursive(docClass) {
  return request({
    url: '/setting/formRule/recursive/' + docClass,
    method: 'get'
  })
}
// 新增单规则
export function addFormRule(data) {
  return request({
    url: '/setting/formRule',
    method: 'post',
    data: data
  })
}

// 修改单规则
export function updateFormRule(data) {
  return request({
    url: '/setting/formRule',
    method: 'put',
    data: data
  })
}

// 删除单规则
export function delFormRule(id) {
  return request({
    url: '/setting/formRule/' + id,
    method: 'delete'
  })
}
