import request from '@/utils/request'

// 查询预选环节用户列表
export function listPresetUser(query) {
  return request({
    url: '/setting/presetUser/list',
    method: 'get',
    params: query
  })
}
export function pagePresetUser(query) {
  return request({
    url: '/setting/presetUser/page',
    method: 'get',
    params: query
  })
}

// 查询预选环节用户详细
export function getPresetUser(id) {
  return request({
    url: '/setting/presetUser/' + id,
    method: 'get'
  })
}

// 新增预选环节用户
export function addPresetUser(data) {
  return request({
    url: '/setting/presetUser',
    method: 'post',
    data: data
  })
}

// 修改预选环节用户
export function updatePresetUser(data) {
  return request({
    url: '/setting/presetUser',
    method: 'put',
    data: data
  })
}

// 删除预选环节用户
export function delPresetUser(id) {
  return request({
    url: '/setting/presetUser/' + id,
    method: 'delete'
  })
}
