import request from '@/utils/request'

// 下载
export function downloadFileId(query, id) {
  return request({
    url: '/as/file/api/downloadFileId?fileId=' + id,
    responseType: 'blob',
    method: 'post',
    params: query
  })
}

// 预览
export function previewFileId(query) {
  return request({
    url: '/as/file/api/previewFileId',
    method: 'post',
    params: query
  })
}

// 复制文件 爱数
export function copyFile(docId) {
  return request({
    url: '/as/file/api/copyFile?docid=' + docId,
    method: 'get'
  })
}

export function downloadFileDocId(id, type) {
  return request({
    url: '/as/file/api/downloadFileDocId?fileId=' + id + "&fileType=" + type,
    responseType: 'blob',
    method: 'post'
  })
}
