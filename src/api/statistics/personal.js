import request from '@/utils/request'

export function changeFactor(query) {
  return request({
    url: '/process/doc-statistics/change-factor',
    method: 'post',
    data: query
  })
}

export function changeType(query) {
    return request({
        url: '/process/doc-statistics/change-type',
        method: 'post',
        data: query
    })
}

export function changeTypeSum(query) {
  return request({
      url: '/process/doc-statistics/change-type-sum',
      method: 'post',
      data: query
  })
}

export function training(query) {
  return request({
    url: '/process/doc-statistics/training',
    method: 'POST',
    data: query,
  })
}
