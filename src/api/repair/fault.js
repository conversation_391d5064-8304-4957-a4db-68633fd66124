import request from '@/utils/request'

// 查询异常故障列表
export function listFault(query) {
  return request({
    url: '/repair/fault/list',
    method: 'get',
    params: query
  })
}

// 查询异常故障详细
export function getFault(id) {
  return request({
    url: '/repair/fault/' + id,
    method: 'get'
  })
}

// 新增异常故障
export function addFault(data) {
  return request({
    url: '/repair/fault',
    method: 'post',
    data: data
  })
}

// 修改异常故障
export function updateFault(data) {
  return request({
    url: '/repair/fault',
    method: 'put',
    data: data
  })
}

// 删除异常故障
export function delFault(id) {
  return request({
    url: '/repair/fault/' + id,
    method: 'delete'
  })
}

/**
 * 合并相同项
 * @param data 与该接口参数一直addFault，多加一个ids -- id主键集合
 * @returns {*}
 * @constructor
 */
export function MergeSameItems(data) {
  return request({
    url: '/repair/fault/MergeSameItems',
    method: 'post',
    data: data
  })
}

/**
 * 故障误报
 * @param data   id --主键,reason--原因
 * @returns {*}
 * @constructor
 */
export function FailureFalseAlarm(data) {
  return request({
    url: '/repair/fault/FailureFalseAlarm',
    method: 'post',
    data: data
  })
}

/**
 * 加入工单
 * @param data ids --id集合,orderNo --订单号
 * @returns {*}
 * @constructor
 */
export function JoinRepairOrder(data) {
  return request({
    url: '/repair/fault/JoinRepairOrder',
    method: 'post',
    data: data
  })
}

/**
 * 导出
 * @param data
 * @returns {*}
 */
export function exportFailure(data) {
  return request({
    url: '/repair/fault/export',
    method: 'get',
    params: data
  })
}

/**
 * 查询排班信息
 * groupId 项目组Id
 * shiftId 班次id
 * userId 人员id
 * startTime 查询开始时间 yyyy-MM-dd HH:mm:ss
 * endTime 查询结束时间 yyyy-MM-dd HH:mm:ss
 * @param data
 * @returns {*}
 */
export function selectWorkPlanList(data) {
  return request({
    url: '/repair/fault/selectWorkPlanList',
    method: 'post',
    data: data
  })
}
