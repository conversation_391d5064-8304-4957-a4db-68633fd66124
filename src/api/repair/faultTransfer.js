import request from '@/utils/request'

// 查询异常转班记录列表
export function listFaultTransfer(query) {
  return request({
    url: '/order/faultTransfer/list',
    method: 'get',
    params: query
  })
}

// 查询异常转班记录详细
export function getFaultTransfer(id) {
  return request({
    url: '/order/faultTransfer/' + id,
    method: 'get'
  })
}

// 新增异常转班记录
export function addFaultTransfer(data) {
  return request({
    url: '/order/faultTransfer',
    method: 'post',
    data: data
  })
}

// 修改异常转班记录
export function updateFaultTransfer(data) {
  return request({
    url: '/order/faultTransfer',
    method: 'put',
    data: data
  })
}

// 删除异常转班记录
export function delFaultTransfer(id) {
  return request({
    url: '/order/faultTransfer/' + id,
    method: 'delete'
  })
}
