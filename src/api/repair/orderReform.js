import request from '@/utils/request'

// 查询维修工单-改造列表
export function listOrderReform(query) {
  return request({
    url: '/order/orderReform/list',
    method: 'get',
    params: query
  })
}

// 查询维修工单-改造详细
export function getOrderReform(id) {
  return request({
    url: '/order/orderReform/' + id,
    method: 'get'
  })
}

// 新增维修工单-改造
export function addOrderReform(data) {
  return request({
    url: '/order/orderReform',
    method: 'post',
    data: data
  })
}

// 修改维修工单-改造
export function updateOrderReform(data) {
  return request({
    url: '/order/orderReform',
    method: 'put',
    data: data
  })
}

// 删除维修工单-改造
export function delOrderReform(id) {
  return request({
    url: '/order/orderReform/' + id,
    method: 'delete'
  })
}
