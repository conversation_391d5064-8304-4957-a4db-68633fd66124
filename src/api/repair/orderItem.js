import request from '@/utils/request'

// 查询维修工单-明细列表
export function listOrderItem(query) {
  return request({
    url: '/order/orderItem/list',
    method: 'get',
    params: query
  })
}

// 查询维修工单-明细详细
export function getOrderItem(id) {
  return request({
    url: '/order/orderItem/' + id,
    method: 'get'
  })
}

// 新增维修工单-明细
export function addOrderItem(data) {
  return request({
    url: '/order/orderItem',
    method: 'post',
    data: data
  })
}

// 修改维修工单-明细
export function updateOrderItem(data) {
  return request({
    url: '/order/orderItem',
    method: 'put',
    data: data
  })
}

// 删除维修工单-明细
export function delOrderItem(id) {
  return request({
    url: '/order/orderItem/' + id,
    method: 'delete'
  })
}
