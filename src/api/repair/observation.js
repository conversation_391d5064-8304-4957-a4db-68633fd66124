import request from '@/utils/request'

// 查询观察清单列表
export function listObservation(query) {
  return request({
    url: '/order/observation/list',
    method: 'get',
    params: query
  })
}

// 查询观察清单详细
export function getObservation(id) {
  return request({
    url: '/order/observation/' + id,
    method: 'get'
  })
}

// 新增观察清单
export function addObservation(data) {
  return request({
    url: '/order/observation',
    method: 'post',
    data: data
  })
}

// 修改观察清单
export function updateObservation(data) {
  return request({
    url: '/order/observation',
    method: 'put',
    data: data
  })
}

// 删除观察清单
export function delObservation(id) {
  return request({
    url: '/order/observation/' + id,
    method: 'delete'
  })
}
