import request from '@/utils/request'

// SSO检查是否有效
export function ssoCheck(client,token) {
  const data = {
    'client':client
  }
  return request({
    url: '/as/sso/check?client='+client+'&token='+token,
    method: 'post',
    headers: {
      isToken: false
    },
    data: data
  })
}
// SSO登录
export function ssoLogin(data) {
  return request({
    url: '/as/sso/login',
    method: 'post',
    data
  })
}

// 登录方法
export function login(username, password, code, uuid, type, TenantId,longinUrl) {
  const data = {
    username,
    password,
    code,
    uuid,
    type,
    TenantId
  }
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
