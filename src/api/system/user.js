import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}
// 查询用户列表
export function listByPerms(query) {
  return request({
    url: '/system/user/listByPerms',
    method: 'get',
    params: query
  })
}

export function listUserAll(query) {
  return request({
    url: '/system/user/list/all',
    method: 'get',
    params: query
  })
}

export function listUserByRoleKey(roleKey) {
  return request({
    url: '/system/user/list/'+roleKey,
    method: 'get',
  })
}

export function listUserForGroup(query) {
  return request({
    url: '/system/user/listForGroup',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + praseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get',
    timeout: 10*60*1000
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}


// 获取部门人员树形结构
export function treeselect(query) {
  return request({
    url: '/system/user/treeselect',
    method: 'get',
    params: query
  })
}

// 获取部门人员树形结构
export function queryCompanyList(userName) {
  return request({
    url: '/system/user/queryCompanyList/'+userName,
    method: 'get',
  })
}

export function getCompanyList(data) {
  return request({
    url: '/basic/tenant/listNoPage',
    method: 'post',
    data: data
  })
}

export function getNumByDeptId(deptId) {
  return request({
    url: '/system/user/getNumByDeptId/'+deptId,
    method: 'get',
  })
}

export function getNumByTenantId(tenantId) {
  return request({
    url: '/system/user/getNumByTenantId/'+tenantId,
    method: 'get',
  })
}

export function getFullPathRoleUserList(query){
  return request({
    url: '/system/user/getFullPathRoleUserList',
    method: 'get',
    params: query
  })
}
export function get3Token(){
  return request({
    url: '/as/sso/get3Token',
    method: 'get'
  })
}

export function getLeader(userName,deptId) {
  return request({
    url: '/system/user/leader',
    method: 'get',
    params: {userName:userName,deptId:deptId}
  })
}

export function getDivisionLeader(deptId) {
  return request({
    url: '/system/user/divisionLeader',
    method: 'get',
    params: {deptId:deptId}
  })
}



export function getDocManagersByDeptId(deptId) {
  return request({
    url: '/system/user/docManagers',
    method: 'get',
    params: {deptId:deptId}
  })
}

export function getDeptByUserName(userName) {
  return request({
    url: '/system/user/getDeptByUserName',
    method: 'get',
    params: {userName:userName}
  })
}

export function getUserByUserNames(userNames) {
  return request({
    url: '/system/user/getUserByUserNames',
    method: 'get',
    params: {userNames:userNames}
  })
}export function getActiveUsers(dbName) {
  return request({
    url: '/system/user/active',
    method: 'get',
    params: {dbName:dbName}
  })
}

// 验证身份
export function verifyIdentity(data) {
  return request({
    url: '/system/user/verifyIdentity',
    method: 'post',
    data: data
  })
}
