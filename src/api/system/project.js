import request from '@/utils/request'

// 查询项目信息列表
export function listInfo(query) {
  return request({
    url: '/system/project/list',
    method: 'get',
    params: query
  })
}

// 查询项目信息详细
export function getInfo(id) {
  return request({
    url: '/system/project/' + id,
    method: 'get'
  })
}

// 新增项目信息
export function addInfo(data) {
  return request({
    url: '/system/project',
    method: 'post',
    data: data
  })
}

// 修改项目信息
export function updateInfo(data) {
  return request({
    url: '/system/project',
    method: 'put',
    data: data
  })
}

// 删除项目信息
export function delInfo(id) {
  return request({
    url: '/system/project/' + id,
    method: 'delete'
  })
}

// 获取项目相关的文件类型树结构
export function treeSelect() {
  return request({
    url: '/system/project/treeSelect',
    method: 'get'
  })
}

// 查询人员所属项目清单接口
export function queryUserProjectList() {
  return request({
    url: '/system/project/queryUserProjectList',
    method: 'get'
  })
}
