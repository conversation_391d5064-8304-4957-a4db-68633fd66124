import request from '@/utils/request'

// 查询参数列表
export function listInternationals(query) {
  return request({
    url: '/system/i18n/list',
    method: 'get',
    params: query
  })
}


// 国家化多语言后台初始化接口配置
export function initI18nBack() {
  return request({
    url: '/system/i18n/initRedis',
    method: 'get'
  })
}


// 保存数据
export function saveI18nData(dataForm) {
  return request({
    url: '/system/i18n/save',
    method: 'post',
    data: dataForm
  })
}


// 更新数据
export function updateI18nData(dataForm) {
  return request({
    url: '/system/i18n/update',
    method: 'post',
    data: dataForm
  })
}

// 国际化多语言删除
export function removeI18nData(dataForm) {
  return request({
    url: '/system/i18n/remove',
    method: 'post',
    data: dataForm
  })
}

// 国际化多语言下载模板
export function downloadTemplate() {
  return request({
    url: '/system/i18n/downloadTemplate',
    method: 'get',
  })
}


// 国际化多语言导入数据
export function importI18nData(dataForm) {
  return request({
    url: '/system/i18n/import',
    method: 'post',
    data: dataForm
  })
}


// 一键翻译
export function translateI18nData(query) {
  return request({
    url: '/system/i18n/translate',
    method: 'get',
    params: query
  })
}
