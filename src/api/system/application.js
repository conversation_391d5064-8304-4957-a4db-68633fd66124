import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/ruoyi";

// 查询应用账号列表
export function listApplicaition(query) {
  return request({
    url: '/system/application/list',
    method: 'get',
    params: query
  })
}

// 查询应用账号详细
export function getApplicaition(userId) {
  return request({
    url: '/system/application/' + praseStrEmpty(userId),
    method: 'get'
  })
}

// 新增应用账号
export function addApplicaition(data) {
  return request({
    url: '/system/application',
    method: 'post',
    data: data
  })
}

// 修改应用账号
export function updateApplicaition(data) {
  return request({
    url: '/system/application',
    method: 'put',
    data: data
  })
}

// 密码重置
export function resetApplicaitionUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/application/resetPwd',
    method: 'put',
    data: data
  })
}

// 状态修改
export function changeApplicaitionUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/application/changeStatus',
    method: 'put',
    data: data
  })
}
