import request from '@/utils/request'

// 查询签章管理列表
export function listUserSignature(query) {
  return request({
    url: '/system/userSignature/getPageList',
    method: 'get',
    params: query
  })
}

// 查询签章管理详细
export function getUserSignature(id) {
  return request({
    url: '/system/userSignature/' + id,
    method: 'get'
  })
}

// 新增签名
export function addUserSignature(data) {
  return request({
    url: '/system/userSignature',
    method: 'post',
    data: data
  })
}

// 修改签名
export function updateUserSignature(data) {
  return request({
    url: '/system/userSignature',
    method: 'put',
    data: data
  })
}

// 删除签章管理
export function delUserSignature(id) {
  return request({
    url: '/system/userSignature/' + id,
    method: 'delete'
  })
}

export function validateByUserName(query) {
  return request({
    url: '/system/userSignature/validate',
    method: 'get',
    params: query
  })
}
