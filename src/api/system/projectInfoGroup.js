import request from '@/utils/request'

// 查询项目与群组关系列表
export function listInfoGroup(query) {
  return request({
    url: '/system/infoGroup/list',
    method: 'get',
    params: query
  })
}

// 查询项目与群组关系详细
export function getInfoGroup(projectId) {
  return request({
    url: '/system/infoGroup/' + projectId,
    method: 'get'
  })
}

// 新增项目与群组关系
export function addInfoGroup(data) {
  return request({
    url: '/system/infoGroup',
    method: 'post',
    data: data
  })
}

// 修改项目与群组关系
export function updateInfoGroup(data) {
  return request({
    url: '/system/infoGroup',
    method: 'put',
    data: data
  })
}

// 删除项目与群组关系
export function delInfoGroup(projectId) {
  return request({
    url: '/system/infoGroup/' + projectId,
    method: 'delete'
  })
}
