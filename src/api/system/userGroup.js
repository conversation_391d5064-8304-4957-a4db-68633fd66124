import request from '@/utils/request'

// 查询用户和群组关联列表
export function listUserGroup(query) {
  return request({
    url: '/system/userGroup/list',
    method: 'get',
    params: query
  })
}

// 查询用户和群组关联详细
export function getUserGroup(tenantId) {
  return request({
    url: '/system/userGroup/' + tenantId,
    method: 'get'
  })
}

// 新增用户和群组关联
export function addUserGroup(data) {
  return request({
    url: '/system/userGroup',
    method: 'post',
    data: data
  })
}

// 批量新增用户和群组关联
export function batchAddUserGroup(groupId,data) {
  return request({
    url: '/system/userGroup/batchAdd?groupId='+groupId,
    method: 'post',
    data: data
  })
}

// 修改用户和群组关联
export function updateUserGroup(data) {
  return request({
    url: '/system/userGroup',
    method: 'put',
    data: data
  })
}

// 删除用户和群组关联
export function delUserGroup(tenantId) {
  return request({
    url: '/system/userGroup/' + tenantId,
    method: 'delete'
  })
}
