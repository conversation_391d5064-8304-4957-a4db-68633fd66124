import request from '@/utils/request'

// 查询编辑明细日志列表
export function listFileEditingDetailLog(query) {
  return request({
    url: '/system/fileEditingDetailLog/list',
    method: 'get',
    params: query
  })
}

// 查询编辑明细日志详细
export function getFileEditingDetailLog(id) {
  return request({
    url: '/system/fileEditingDetailLog/' + id,
    method: 'get'
  })
}

// 新增编辑明细日志
export function addFileEditingDetailLog(data) {
  return request({
    url: '/system/fileEditingDetailLog',
    method: 'post',
    data: data
  })
}

// 修改编辑明细日志
export function updateFileEditingDetailLog(data) {
  return request({
    url: '/system/fileEditingDetailLog',
    method: 'put',
    data: data
  })
}

// 删除编辑明细日志
export function delFileEditingDetailLog(id) {
  return request({
    url: '/system/fileEditingDetailLog/' + id,
    method: 'delete'
  })
}

/**
 * 检查文件是否正在被编辑
 * @param {Object} params 检查参数
 * @param {string} params.fileId 文件ID
 * @param {string} params.protoFileId 原型文件ID
 * @param {string} params.bizId 业务ID
 * @returns {Promise<Object>} 检查结果
 */
export function checkFileEditingStatus(params) {
  return request({
    url: `/system/fileEditingDetailLog/check/${params.protoFileId}`,
    method: 'post',
    data: params
  })
}

/**
 * 清除状态
 * @param protoFileId 主文件id
 */
export function releaseStatus(protoFileId) {
  return request({
    url: `/system/fileEditingDetailLog/release/${protoFileId}`,
    method: 'get'
  })
}

/**
 * 打开文件编辑前的检查
 * @param {Object} params 检查参数
 * @param {Function} successCallback 检查通过后的回调
 * @param {Function} failCallback 检查失败后的回调
 */
export async function checkBeforeEdit(params, successCallback, failCallback) {
    const res = await checkFileEditingStatus(params)
    console.log(res)
    if (res.code === 200) {
      // 无人编辑，执行成功回调
      successCallback && successCallback()
    } else if (res.code === 500 && res.data) {
      // 有人编辑，提示并执行失败回调
      const { nickName, userName } = res.data
      const editorName = nickName || userName || '未知用户'
      console.log(editorName)
      failCallback && failCallback(editorName)
    }
}
