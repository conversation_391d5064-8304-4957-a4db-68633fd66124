import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listNotifyTemplate(query) {
  return request({
    url: '/system/notifyTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getNotifyTemplate(templateId) {
  return request({
    url: '/system/notifyTemplate/' + templateId,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addNotifyTemplate(data) {
  return request({
    url: '/system/notifyTemplate/add',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateNotifyTemplate(data) {
  return request({
    url: '/system/notifyTemplate/update',
    method: 'post',
    data: data
  })
}

// 删除【请填写功能名称】
export function delNotifyTemplate(templateId) {
  return request({
    url: '/system/notifyTemplate/' + templateId,
    method: 'delete'
  })
}
