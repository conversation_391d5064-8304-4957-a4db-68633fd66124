import "@/assets/js/ntkobackground.min.js";
import { tansParams } from "@/utils/ruoyi";
import { MessageBox } from 'element-ui';
import {fileLocalDownload} from "@/api/commmon/file";
import {getConfigKey} from "@/api/system/config";

/**
 * fileName 文件名称
 * ossId：云存储id
 * param：文档编辑额外的保存参数（用于保存修改日志）
 * readOnly:是否只读
 * 打开可编辑 文档
 */
export function openViewFileToEdit(fileName,ossId,extParam,readOnly,protectType) {
      verifyViewComponent()
      let ntkoed = ntkoBrowser.ExtensionInstalled();//判断插件是否安装
      var ntkourl = window.location.host;
      var ntkoprotocol = window.location.protocol;
      if (ntkoed) {
        var _url = ntkoprotocol + "//" + ntkourl + process.env.VUE_APP_CONTEXT_PATH + "/ntkoindex.html?cmd=2&baseApi="
            + process.env.VUE_APP_BASE_HOST
            + "&fileUrl=" + process.env.VUE_APP_BASE_HOST +"/process/file/local_download/"+ossId
            + "&fileName=" + fileName;
            if(extParam){
                  // 存在 扩展参数 拼接扩展参数
                  _url = _url +"&"+ tansParams(extParam)
                  _url = _url.slice(0, -1);
            }
            if(readOnly){
              _url = _url +"&readOnly=" +readOnly
              //ntkoBrowser.setReadOnly(readOnly?true:false,"","",protectType)
            }
            if(protectType){
              _url = _url +"&protectType=" +protectType
            }
            console.log(_url)
            ntkoBrowser.openWindow(_url, false, "", "");
      }
}

 // 校验 文档预览控件是否安装
export function verifyViewComponent(){
  let ntkoed = ntkoBrowser.ExtensionInstalled();//判断插件是否安装
  if (!ntkoed) {
     MessageBox.confirm(
        '未安装在线打开文档插件，是否下载安装?',
        '提示信息',
        {
          confirmButtonText: '确认下载',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          downloadOpenFilePlug()
        })
        .catch(() => {

        })
  }
}

function handelFileLocalDownload(id, name) {
  fileLocalDownload(id).then((res) => {
    //console.log("file", res);
    saveFile(res, name);
  });
}
function saveFile(data, name) {
  try {
    const blobUrl = window.URL.createObjectURL(data);
    // console.log('bo',blobUrl);
    const a = document.createElement("a");
    a.style.display = "none";
    a.download = name;
    a.href = blobUrl;
    a.click();
  } catch (e) {
    alert("保存文件出错");
  }
}

async function downloadOpenFilePlug() {
  try {
    const res = await getConfigKey('sys.edit.client')
    handelFileLocalDownload(res.msg,"NTKO控件安装程序-北京众驰伟业科技发展有限公司.exe")
  } catch (error) {
    MessageBox.alert('下载失败，请联系管理员', '系统提示', {type: 'error'});
    console.error(error);
  }
}

