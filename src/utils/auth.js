// import Cookies from 'js-cookie'
import cache from "@/plugins/cache";

const TokenKey = 'Admin-Token'
const AsToken = 'As-Token'
const TenantId='tenantId'
export function getToken() {
  return cache.session.get(TokenKey)
}

export function setToken(token) {
  return cache.session.set(TokenKey, token)
}

export function removeToken() {
  return cache.session.remove(TokenKey)
}
export function getAsToken() {
  return cache.session.get(AsToken)
}

export function setAsToken(token) {
  return cache.session.set(AsToken, token)
}
export function getTenantId() {
  return cache.session.get(TenantId)
}
export function setTenantId(tenantId) {
  return cache.session.set(TenantId,tenantId)
}
export function removeAsToken(token) {
  return cache.session.remove(AsToken)
}
const LanguageDataKey = 'languageData'

export function getLanguageData() {
  return cache.session.get(LanguageKey)
}

export function setLanguageData(lang) {
  return cache.session.set(LanguageDataKey, lang)
}

export function removeLanguageData() {
  return cache.session.remove(LanguageKey)
}

const LanguageKey = 'language'

export function getLanguage() {
  return cache.session.get(LanguageKey)
}

export function setLanguage(lang) {
  return cache.session.set(LanguageKey, lang)
}

export function removeLanguage() {
  return cache.session.remove(LanguageKey)
}
