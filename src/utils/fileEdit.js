import request from '@/utils/request'

/**
 * 检查文件是否正在被编辑
 * @param {Object} params 检查参数
 * @param {string} params.fileId 文件ID
 * @param {string} params.protoFileId 原型文件ID
 * @param {string} params.bizId 业务ID
 * @returns {Promise<Object>} 检查结果
 */
export function checkFileEditingStatus(params) {
  return request({
    url: `/system/fileEditingDetailLog/check/${params.protoFileId}`,
    method: 'get',
    params: {
      fileId: params.fileId,
      status: 'E',
      bizId: params.bizId,
      protoFileId: params.protoFileId
    }
  })
}

/**
 * 打开文件编辑前的检查
 * @param {Object} params 检查参数
 * @param {Function} successCallback 检查通过后的回调
 * @param {Function} failCallback 检查失败后的回调
 */
export async function checkBeforeEdit(params, successCallback, failCallback) {
  try {
    const res = await checkFileEditingStatus(params)
    if (res.code === 200) {
      // 无人编辑，执行成功回调
      successCallback && successCallback()
    } else if (res.code === 500 && res.data) {
      // 有人编辑，提示并执行失败回调
      const { nickName, userName } = res.data
      const editorName = nickName || userName || '未知用户'
      failCallback && failCallback(editorName)
    }
  } catch (error) {
    console.error('检查文件编辑状态失败:', error)
    failCallback && failCallback()
  }
} 