<template>
  <span>
    <el-button style="margin-left: 10px" type="primary" @click="visible=true">{{ $t(`doc.this_dept_select_group`) }}</el-button>
    <el-dialog :visible.sync="visible" width="400px" append-to-body>
      <el-select v-model="groupId" @change="groupChanged" :placeholder="$t('doc.this_dept_select_group')">
        <el-option
          v-for="(item, index) in groupList"
          :key="index"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
         <el-button @click="visible = false">{{ $t(`doc.this_dept_close`) }}</el-button>
        <el-button type="primary" v-dbClick @click="handleSure">{{ $t(`file_handle.change_confirm`) }}</el-button>
      </span>
    </el-dialog>
  </span>
</template>

<script>
import { listGroup } from '@/api/system/group'
import { listUserGroup } from '@/api/system/userGroup'

export default {
  name:"SelectGroup",
  data() {
    return {
      visible: false,
      groupList: [],
      groupId:undefined,
      userGroupList:[]
    }
  },
  created(){
    this.getList()
  },

  methods: {
    /** 查询人员分组列表 */
    getList() {
      this.loading = true
      listGroup({}).then(response => {
        this.groupList = response.rows
      }).finally(() => {
        this.loading = false
      })
    },
    getUserGroupList(groupId) {
      let self = this
      this.loading = true
      let query = {}
      query.groupId = groupId
      listUserGroup(query).then(response => {
        self.userGroupList = response.rows
      })
    },
    groupChanged(){
      if(this.groupId){
        this.getUserGroupList(this.groupId)
      }
    },
    handleSure(){
      this.visible=false
      this.$emit("selectedGroup",this.userGroupList)
    }
  }
}
</script>
