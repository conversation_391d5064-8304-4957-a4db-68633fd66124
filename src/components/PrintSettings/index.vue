<template>
  <el-dialog :title="$t('file.print_settings_title')" :visible.sync="visible" width="500px" append-to-body @close="handleClose">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <!-- 打印文件 -->
      <el-divider content-position="left">{{ $t('file.print_file_section') }}</el-divider>
      <el-form-item :label="$t('file.print_file_label')" prop="docName">
        <el-input v-model="formData.docName" disabled />
      </el-form-item>
      <el-form-item :label="$t('file.print_count_label')" prop="printCount">
        <el-input-number disabled v-model="formData.printCount" :min="1" controls-position="right" />
      </el-form-item>
      <el-form-item :label="$t('file.printer_label')" prop="printerName">
        <el-select v-model="formData.printerName" :placeholder="$t('file.select_printer_placeholder')">
          <el-option v-for="printer in printers" :key="printer.name" :label="printer.name" :value="printer.name">
            <span style="float: left">{{ printer.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 打印参数 -->
      <el-divider content-position="left">{{ $t('file.print_parameters_section') }}</el-divider>
      <el-form-item :label="$t('file.print_color_label')" prop="printColor">
        <el-select v-model="formData.printColor" :placeholder="$t('file.select_print_color_placeholder')">
          <el-option :label="$t('file.color_option')" value="color" />
          <el-option :label="$t('file.black_and_white_option')" value="blackAndWhite" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('file.paper_size_label')" prop="paperSize">
        <el-select v-model="formData.paperSize" :placeholder="$t('file.select_paper_size_placeholder')">
          <el-option :label="$t('file.a4_option')" value="A4" />
          <el-option :label="$t('file.a3_option')" value="A3" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('file.pages_per_sheet_label')" prop="pagesPerSheet">
        <el-select v-model="formData.pagesPerSheet" :placeholder="$t('file.select_pages_per_sheet_placeholder')">
          <el-option :label="$t('file.one_page_option')" value="1" />
          <el-option :label="$t('file.two_pages_option')" value="2" />
          <el-option :label="$t('file.four_pages_option')" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('file.margin_label')" prop="margin">
        <el-select v-model="formData.margin" :placeholder="$t('file.select_margin_placeholder')">
          <el-option :label="$t('file.default_margin_option')" value="default" />
          <el-option :label="$t('file.no_margin_option')" value="0" />
          <el-option :label="$t('file.narrow_margin_option')" value="5" />
          <el-option :label="$t('file.wide_margin_option')" value="25" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('file.scale_label')" prop="scale">
        <el-input-number v-model="formData.scale" :min="1" :max="200" controls-position="right">
          <template slot="append">%</template>
        </el-input-number>
      </el-form-item>

      <!-- 页面范围 -->
      <el-divider content-position="left">{{ $t('file.page_range_section') }}</el-divider>
      <el-form-item :label="$t('file.print_range_label')" prop="printRange">
        <el-radio-group v-model="formData.printRange">
          <el-radio label="all">{{ $t('file.print_range_all') }}</el-radio>
          <el-radio label="custom">{{ $t('file.print_range_custom') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.printRange === 'custom'" :label="$t('file.page_numbers_label')" prop="pageNumbers">
        <el-input v-model="formData.pageNumbers" :placeholder="$t('file.page_numbers_placeholder')" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $t('file.cancel_button') }}</el-button>
      <el-button v-if="Object.keys(printDataList).length > 0" type="primary" @click="handleBatchConfirm">{{ $t('file.batch_print_button') }}</el-button>
      <el-button v-else type="primary" @click="handleConfirm">{{ $t('file.confirm_button') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addPrintTask } from '@/api/file_print/print'
import { mapGetters } from 'vuex'
import {fileLocalDownload} from "@/api/commmon/file";
import {getConfigKey} from "@/api/system/config";
export default {
  name: 'PrintSettings',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    docInfo: {
      type: Object,
      default: () => ({})
    },
    printDataList: {
      type: Object,
      default: () => ([])
    }
  },
  computed: {
    ...mapGetters({
      printers: 'printers',
      printTasks: 'printTasks'
    })
  },
  data() {
    return {
      formData: {
        docName: '',
        printCount: 1,
        printerName: '',
        paperSize: 'A4',
        pagesPerSheet: '1',
        margin: 'default',
        scale: 100,
        printRange: 'all',
        pageNumbers: '',
        printColor: 'blackAndWhite'
      },
      rules: {
        printCount: [
          { required: true, message: this.$t('file.print_count_required_message'), trigger: 'blur' }
        ],
        printerName: [
          { required: true, message: this.$t('file.printer_required_message'), trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    printers: {
      handler(val) {
        if (val && val.length > 0 && !this.formData.printerName) {
          // 如果有默认打印机，则选择默认打印机
          const defaultPrinter = val.find(p => p.isDefault)
          this.formData.printerName = defaultPrinter ? defaultPrinter.name : val[0].name
        }
      },
      immediate: true
    },
    docInfo: {
      handler(val) {
        if (val) {
          this.formData.docName = val.docName || ''
          this.formData.printCount = val.printCount || ''
        }
      },
      immediate: true
    },
    printDataList: {
      handler(val) {
        if (val) {
          this.formData.docName = val.length>0?val[0].docName:''
        }
      },
      immediate: true
    }
  },
  methods: {
    async checkPrinters() {
      if (!this.printers || this.printers.length === 0) {
        try {
          // 连接socket
          await this.$store.dispatch('websocket/initWebSocket', null, { root: true })
          // 获取打印机列表
          await this.$store.dispatch('printTask/requestPrinters')
          await new Promise(resolve => setTimeout(resolve, 2000))
        } catch (error) {
          console.error('获取打印机列表失败:', error);
          this.$confirm(
            this.$t('file.no_printer_detected_message'),
            this.$t('file.hint'),
            {
              confirmButtonText: this.$t('file.download_client_button'),
              cancelButtonText: this.$t('file.cancel_button'),
              type: 'warning'
            }
          ).then(async () => {
            // 这里替换成实际的下载地址
            const res = await getConfigKey('print_client_file_id')
            this.handelFileLocalDownload(res.msg, this.$t('print.client_file_name'))
          }).catch(() => { })
          return false
        }
      }
      return true
    },
    handelFileLocalDownload(id, name) {
      fileLocalDownload(id).then((res) => {
        //console.log("file", res);
        this.saveFile(res, name);
      });
    },
    saveFile(data, name) {
      try {
        const blobUrl = window.URL.createObjectURL(data);
        // console.log('bo',blobUrl);
        const a = document.createElement("a");
        a.style.display = "none";
        a.download = name;
        a.href = blobUrl;
        a.click();
      } catch (e) {
        alert("保存文件出错");
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('closePrint')
    },
    handleBatchConfirm() {
      let _this=this
      this.$refs.form.validate(valid => {
        if (valid) {
          //循环赋值打印
          for (const item of this.printDataList) {
            _this.docInfo=item
            const printData = {
              docId: this.docInfo.docId,
              docName: this.docInfo.docName,
              versionId: this.docInfo.versionId,
              printCount: this.docInfo.nums,
              printerName: this.formData.printerName,
              printDesc: this.$t('file.waiting_print_status'),
              docType: this.docInfo.className,
              electronicFileName: this.docInfo.electronicFileName?this.docInfo.electronicFileName:document.title,
              docDistributeId: this.docInfo.id,
              fileId: this.docInfo.fileId,
              classificationNo: this.docInfo.code
            }
            // 先保存到数据库生成打印任务
            addPrintTask(printData).then(res => {
              if (res.code === 200) {
                const socketData = {
                  deviceId: this.formData.printerName,
                  taskId: res.data.id,
                  taskName: printData.electronicFileName,
                  taskDesc: this.$t('file.print_task_description'),
                  taskStatus: "pending",// 打印状态：pending 待打印，printing 打印中，completed 已打印，error 打印错误
                  taskCreateTime: new Date().toISOString(),
                  fileUrl: res.data.printReturnUrl+item.fileUrl,
                  token: sessionStorage.getItem('Admin-Token'),
                  printCount: printData.printCount,
                  docDistributeId: printData.docDistributeId,
                  printReturnUrl: res.data.printReturnUrl,
                  printParams: {
                    paper: this.formData.paperSize,
                    printDirection: this.formData.orientation,
                    printColor: this.formData.printColor,
                    header: this.formData.header,
                    footer: this.formData.footer,
                    pageRange: this.formData.printRange,
                    pageNumbers: this.formData.pageNumbers,
                    margin: this.formData.margin,
                    scale: this.formData.scale,
                    pagePerSheet: this.formData.pagesPerSheet
                  }
                }
                console.log(socketData)
                //this.$socket.emit('printTask', socketData)
                // 发送到WebSocket
                this.$store.dispatch('printTask/sendPrintTask', {
                  ...socketData
                })
              }
            }).catch(error => {
              this.$message.error(this.docInfo.electronicFileName+'_'+this.$t('file.print_task_submit_failed'))
            })
          }
          this.$message.success(this.$t('file.print_task_submit_success'))
          this.handleClose()
        }
      })

    },
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const printData = {
            docId: this.docInfo.docId,
            docName: this.docInfo.docName,
            versionId: this.docInfo.versionId,
            printCount: this.docInfo.printCount,
            printerName: this.formData.printerName,
            printDesc: this.$t('file.waiting_print_status'),
            docType: this.docInfo.docType,
            electronicFileName: this.docInfo.electronicFileName?this.docInfo.electronicFileName:document.title,
            docDistributeId: this.docInfo.docDistributeId,
            fileId: this.docInfo.fileId,
            classificationNo: this.docInfo.classificationNo
          }
          // 先保存到数据库生成打印任务
          addPrintTask(printData).then(res => {
            if (res.code === 200) {
              const socketData = {
                deviceId: this.formData.printerName,
                taskId: res.data.id,
                taskName: printData.electronicFileName,
                taskDesc: this.$t('file.print_task_description'),
                taskStatus: "pending",// 打印状态：pending 待打印，printing 打印中，completed 已打印，error 打印错误
                taskCreateTime: new Date().toISOString(),
                fileUrl: res.data.printReturnUrl+this.docInfo.fileUrl,
                //token: this.$store.getters.token,
                token: sessionStorage.getItem('Admin-Token'),
                printCount: this.docInfo.printCount,
                docDistributeId: this.docInfo.docDistributeId,
                printReturnUrl: res.data.printReturnUrl,
                printParams: {
                  paper: this.formData.paperSize,
                  printDirection: this.formData.orientation,
                  printColor: this.formData.printColor,
                  header: this.formData.header,
                  footer: this.formData.footer,
                  pageRange: this.formData.printRange,
                  pageNumbers: this.formData.pageNumbers,
                  margin: this.formData.margin,
                  scale: this.formData.scale,
                  pagePerSheet: this.formData.pagesPerSheet
                }
              }
              //this.$socket.emit('printTask', socketData)
              console.log(socketData)
              // 发送到WebSocket
              this.$store.dispatch('printTask/sendPrintTask', {
                ...socketData
              })
              this.$message.success(this.$t('file.print_task_submit_success'))
            }
          }).catch(error => {
            this.$message.error(this.$t('file.print_task_submit_failed'))
          })

          this.handleClose()
        }
      })
    }
  }
}
</script>
