<template>
    <el-row :gutter="15" style="display: flex;">
      <el-col :span="settingValue.selectedShow?settingValue.listSpan:24">
        <div class="gray-card">
          <div class="card-head">
            <div class="cell-title">
              <span>{{settingValue.selectTitle}}</span>
            </div>
          </div>
          <div class="card-body">
            <el-table
              :height="485"
              @select="tableSel"
              @select-all="tableAll"
              ref="selectedTable"
              :row-key="rowKey"
              :data="listData"
            >
              <slot name="list"></slot>
            </el-table>
          </div>
        </div>
        <pagination
          v-show="tableTotal > 0"
          :total="tableTotal"
          :page.sync="queryTableParams.pageNum"
          :limit.sync="queryTableParams.pageSize"
          @pagination="getListData"
        />
      </el-col>
      <el-col :span="settingValue.selectedSpan" v-if="settingValue.selectedShow">
        <div class="gray-card">
          <div class="card-head">
            <div class="cell-title">
              <span>{{settingValue.selectedTitle}}</span>
            </div>
          </div>
          <div class="card-body">
            <el-table
              :data="selected"
              :height="$refs.selectedTable?$refs.selectedTable.$el.clientHeight:null"
            >
              <slot name="selected"></slot>
              <el-table-column
                label="操作"
                width="50px"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(scope.row)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
</template>
<script>
    export default {
        props: {
            rowKey: {
                type: String,
                default: 'id'
            },
            listData: {
                type: Array,
                default: ()=>[]
            },
            queryTableParams: {
                type: Object,
                default: ()=>{ return {pageNum: 1, pageSize: 10}},
            },
            defaultSelected: {
                type: Array,
                default: ()=>[]
            },
            tableTotal: {
                type: Number,
                default: 0
            },
            setting: {
                type: Object,
                default: ()=> {
                    return {
                        selectedShow:true,
                        listSpan:16,
                        selectedSpan:8,
                        selectTitle: '未选择项',
                        selectedTitle: '已选择项'
                    }
                },
            },
            selectable: Function,
        },
        name: 'AcrossPagesSelected',
        data() {
            return {
                settingDefaultValue: {
                    selectedShow:true,
                    listSpan:16,
                    selectedSpan:8,
                    selectTitle: '未选择项',
                    selectedTitle: '已选择项'
                },
                settingValue: {},
                selected: []
            }
        },
        watch:{
            listData: {
                handler: function (val, oldVal) {
                    let _this = this
                    if (_this.selected.length > 0) {
                        _this.$nextTick(() => {
                            _this.listData.forEach((item) => {
                                let row = _this.selected.find(ite => item[_this.rowKey] === ite[_this.rowKey])
                                _this.$refs.selectedTable.toggleRowSelection(item, !!row);
                            });
                        });
                    }else {
                        _this.$nextTick(() => {
                            _this.listData.forEach((item) => {
                                _this.$refs.selectedTable.toggleRowSelection(item, false);
                            });
                        });
                    }
                },
                immediate: true
            },
            defaultSelected: {
                handler: function (val, oldVal) {
                    let _this = this
                    _this.selected=val
                    if (_this.selected.length > 0) {
                        _this.$nextTick(() => {
                            _this.listData.forEach((item) => {
                                let row = _this.selected.find(ite => item[_this.rowKey] === ite[_this.rowKey])
                                _this.$refs.selectedTable.toggleRowSelection(item, !!row);
                            });
                        });
                    }else {
                        _this.$nextTick(() => {
                            _this.listData.forEach((item) => {
                                _this.$refs.selectedTable.toggleRowSelection(item, false);
                            });
                        });
                    }
                },
                immediate: true
            },
            setting: {
                handler: function (val, oldVal) {
                    let _this = this
                    for (let key in _this.settingDefaultValue){
                        _this.settingValue[key] = val[key]?val[key]:_this.settingDefaultValue[key]
                    }
                },
                immediate: true
            },
        },
        methods: {
            getListData(){
                this.$emit('getListData')
            },
            handleDelete(row){
                let _this = this
                _this.listData.forEach(item=>{
                    if(row[_this.rowKey]===item[_this.rowKey]){
                        _this.$refs.selectedTable.toggleRowSelection(item, false);
                    }
                })
                _this.selected.forEach((item,index)=>{
                    if(row[_this.rowKey]===item[_this.rowKey]){
                        _this.selected.splice(index,1)
                    }
                })
                _this.emitDelete(row)
            },
            emitDelete(row){
                this.$emit('handleDelete',row)
            },
            tableSel(select, row) {
                let _this = this
                let selectIds = [];
                if (select.length > 0) {
                    if (_this.selected.length > 0) {
                        _this.selected.forEach((item, index) => {
                            selectIds.push(item[_this.rowKey]);
                            selectIds = [...new Set(selectIds)];
                            if (row[_this.rowKey] === item[_this.rowKey]) {
                                _this.selected.splice(index, 1);
                                _this.emitDelete(item)
                            }
                        });
                        if (selectIds.indexOf(row[_this.rowKey]) === -1) {
                            _this.selected.push(row);
                        }
                    } else {
                        _this.selected.push(row);
                        selectIds.push(row[_this.rowKey]);
                    }
                } else {
                    _this.tableAll([]);
                }
            },
            tableAll(selection) {
                let _this = this
                let selectAll = []
                if (selection.length === 0) {
                    selectAll = []
                    // console.log(this.userTable)
                    // 取消全选时获取当前页的所有数据，在selected中删除
                    _this.listData.forEach((item, index) => {
                        _this.selected.forEach((ite, i) => {
                            if (item[_this.rowKey] === ite[_this.rowKey]) {
                                _this.selected.splice(i, 1)
                                _this.emitDelete(item)
                            }
                        })
                    })
                } else {
                    selectAll = [...selection]
                }
                _this.selected.forEach((item, index) => {
                    selectAll.forEach((ite, i) => {
                        if (item[_this.rowKey] === ite[_this.rowKey]) {
                            selectAll.splice(i, 1)
                        }
                    })
                })
                selectAll.forEach(item => {
                    _this.selected.push(item)
                })
            },
        }
    }
</script>
<style scoped>
  .gray-card {
    background: #f7f7f9;
    border-radius: 10px;
    margin: 20px 0;
  }
  .gray-card .card-head {
    display: table;
    width: 100%;
    table-layout: fixed;
    box-sizing: border-box;
    padding: 15px 20px;
  }
  .gray-card .card-head .cell-title {
    display: block;
    color: #225fc7;
    font-size: 16px;
  }
  .gray-card .card-head .cell-btn {
    display: table-cell;
    vertical-align: middle;
    text-align: right;
  }
  .gray-card .card-head .cell-btn .el-button {
    padding: 6px 15px;
    font-size: 14px;
  }
  .gray-card .card-head .cell-btn .el-button.el-button--default {
    background: transparent;
    border-color: #225fc7;
    color: #225fc7;
  }
  .gray-card .card-head .cell-btn .el-button.el-button--default:hover {
    background: #013288;
    border-color: #225fc7;
    color: #fff;
  }
  .gray-card .card-head + .card-body {
    padding-top: 0;
  }
  .gray-card .card-body {
    padding: 15px 20px;
  }
  .gray-card .el-table th.el-table__cell,
  .gray-card .el-table tr,
  .gray-card .el-table {
    background-color: #f7f7f9;
  }
  .gray-card .el-table--border::after,
  .gray-card .el-table--group::after,
  .gray-card .el-table::before {
    background-color: transparent;
  }
  .gray-card .el-table td.el-table__cell,
  .gray-card .el-table th.is-leaf,
  .gray-card .el-table td {
    border-color: #ededed;
  }
  .gray-card .el-table thead.is-group th {
    background: #f7f7f9;
  }
  .gray-card .el-table.el-table--enable-row-hover tr:hover td,
  .gray-card .el-table tr.hover-row td {
    background: #e5efff;
  }
  .gray-card .el-table .el-table__header-wrapper tr th {
    color: #141414;
    font-size: 14px;
    font-weight: bold;
  }
  .gray-card .el-table .el-table__body-wrapper tr td {
    color: #5e5e5e;
    font-size: 14px;
  }
  .gray-card .card-foot {
    padding: 15px 15px;
    border-top: 1px solid #ededed;
  }

  /*el-card gray-card 灰色卡片-卡片组件*/
  body .el-card.gray-card.is-always-shadow {
    box-shadow: none !important;
  }
  .el-card.gray-card {
    background: #f7f7f9;
    border-radius: 10px;
  }
</style>
