<template>
  <el-drawer
    :wrapperClosable='false'
    title="流程监控"
    size="65%"
    :visible.sync="visible"
    :append-to-body="true"
  >
    <div style="width:100%;height:100%">
      <iframe :src="src" width="100%" height="100%" title="myFrame" style="border: 0;"></iframe>
    </div>
  </el-drawer>
</template>

<script>
    import { getHistAskLogUrl } from "@/api/my_business/workflow";
    export default {
        data() {
            return {
                visible: false,
                src: ''
            };
        },
        methods: {
          normalizeUrlToCurrentOrigin(targetUrl) {
            try {
              // 空值处理
              if (!targetUrl) return '';

              const current = new URL(window.location.href);
              const target = new URL(targetUrl, window.location.href);

              // 不处理非HTTP(S)协议
              if (!['http:', 'https:'].includes(target.protocol)) {
                return target.toString();
              }

              // 如果origin不同则替换
              if (current.origin !== target.origin) {
                return new URL(
                  target.pathname + target.search + target.hash,
                  current.origin
                ).toString();
              }

              return target.toString();
            } catch (e) {
              console.error('URL处理失败:', e);
              // 解析失败时尝试返回相对路径或原URL
              return targetUrl.startsWith('/')
                ? new URL(targetUrl, window.location.origin).toString()
                : targetUrl;
            }
          },
            init(procInstId) {
                let _this = this
                _this.visible = true
                getHistAskLogUrl(procInstId).then(res => {
                    _this.src=this.normalizeUrlToCurrentOrigin(res.data)
                })
            }
        }
    }
</script>
