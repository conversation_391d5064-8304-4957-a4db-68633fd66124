<template>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        @submit.prevent="handleQuery"
        :label-width="columnLangSizeFlag ? '128px' : '68px'"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.className"
                :placeholder="$t('file_set.type_input_name_search')"
                clearable
                @keyup.enter.native="handleQuery"
                @clear="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-form>
        <el-table
          border
          max-height="600"
          :default-expand-all="true"
          v-loading="loading"
          :row-class-name="tableRowClassName"
          ref="dragTable"
          :key="tableKey"
          :data="postList"
          @selection-change="handleSelectionChange"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          @expand-change="handleRowClick"
          row-key="id"
        >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_name')"
            align="left"
            prop="className"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_level')"
            align="left"
            prop="classLevel"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_num')"
            align="left"
            prop="classCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.number_order')"
            align="left"
            prop="sort"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_type')"
            align="left"
            prop="classType"
          >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.class_type" :value="scope.row.classType"/>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('file_set.type_category_status')"
            align="left"
            prop="classStatus"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.classStatus == 1 ? $t('file_set.number_enable') : $t('file_set.number_banned') }}</span>
            </template>
          </el-table-column>
        </el-table>
      <div style="text-align: right;margin-top:10px;">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="saveAdd()">确 定</el-button>
      </div>
    </div>
</template>

<script>
import {
  settingDocClassList,
  settingDocClassIds,
  settingDocClassSort,
} from '@/api/file_settings/type_settings'
import add from '@/views/file_settings/type_settings/add'
import settings from '@/views/file_settings/type_settings/settings.vue'
import Sortable from 'sortablejs'
import { getToken } from '@/utils/auth'
import { listFileClass, addFileClass, updateFileClass } from "@/api/setting/fileClass.js";

export default {
  name: "fileClassTree",
  props: {
    bizId: null,
  },
  dicts: ["sys_normal_disable", "class_status","class_type"],
  components: {
    add,
    settings
    // rzDrawer,
  },
  data() {
    return {
      tableKey: undefined,
      activeRows: [],
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 入参数
      upload: {
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/setting/docClass/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        // pageSize: 10,
        classCode: undefined,
        className: undefined,
        classLevel: undefined,
        classStatus: undefined,
        classType: undefined,
        id: undefined,
        dataType: 'stdd'
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      drawerSettings: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
      fileClassId:[],
      dataList:[],
    };
  },
  created() {
    /*
    by xfc 20230626
    系统分别建立 体系文件类型设置和项目文件类型设置菜单，路由参数不一样
    dataType参数区分：体系文件=stdd、项目文件=project
    */
    // 获取数据类型参数来决定显示 体系文件类型设置列表还是 项目文件类型设置列表
    this.queryParams.dataType = this.$route.query.dataType
    this.getList();
  },
  methods: {
    setSort() {
      const el = document.querySelectorAll("table.el-table__body > tbody")[0];
      if (!el) {
        return;
      }
      let that = this;
      this.sortable = Sortable.create(el, {
        animation: 150, //动画
        filter: ".disabled", //指定不可拖动的类名（el-table中可通过row-class-name设置行的class）
        dragClass: "dragClass", //设置拖拽样式类名
        ghostClass: "ghostClass", //设置拖拽停靠样式类名
        chosenClass: "chosenClass", //设置选中样式类名
        setData: function (dataTransfer) {
          dataTransfer.setData("Text", "");
        },
        // 拖拽移动的时候
        onMove: function ({ dragged, related }) {
          // 对了，树数据中不管是哪一层，都一定要有level字段表示当前是第几层哦
          /*
                          evt.dragged; // 被拖拽的对象
                          evt.related; // 被替换的对象
                         */
          const oldRow = that.activeRows[dragged.rowIndex];
          const newRow = that.activeRows[related.rowIndex];

          if (
            oldRow.classLevel !== newRow.classLevel ||
            oldRow.parentClassId !== newRow.parentClassId
          ) {
            return false;
          }
        },
        onEnd: async ({ oldIndex, newIndex }) => {
          const oldRow = that.activeRows[oldIndex];
          const newRow = that.activeRows[newIndex];
          if (oldRow.classLevel != "1") {
            that.$modal.msg(this.$t('file_set.type_text'));
            //that.getList()
          }
          if (
            oldIndex !== newIndex &&
            oldRow.classLevel === newRow.classLevel &&
            oldRow.parentClassId === newRow.parentClassId
          ) {
            const oldRow = that.activeRows[oldIndex];
            const newRow = that.activeRows[newIndex];
            let oldRowSuffixData = that.activeRows.slice(oldIndex);
            let newRowSuffixData = that.activeRows.slice(newIndex);

            oldRowSuffixData = oldRowSuffixData.filter(
              (d, i) =>
                i <
                that.getLeastIndex(
                  oldRowSuffixData.findIndex(
                    (_d, _i) => _d.classLevel === oldRow.classLevel && _i !== 0
                  )
                )
            );
            newRowSuffixData = newRowSuffixData.filter(
              (d, i) =>
                i <
                that.getLeastIndex(
                  newRowSuffixData.findIndex(
                    (_d, _i) => _d.classLevel === newRow.classLevel && _i !== 0
                  )
                )
            );
            const targetRows = that.activeRows.splice(
              oldIndex,
              oldRowSuffixData.length
            );

            if (oldIndex > newIndex) {
              that.activeRows.splice(newIndex, 0, ...targetRows);
            } else if (oldIndex < newIndex) {
              that.activeRows.splice(
                newIndex + newRowSuffixData.length - oldRowSuffixData.length,
                0,
                ...targetRows
              );
            }
            let ids = [];
            that.activeRows.forEach((item) => {
              if (item.classLevel === "1") {
                ids.push(item.id);
              }
            });
            await settingDocClassSort(ids);
            that.getList();
          }
        },
      });
    },
    getLeastIndex(index) {
      return index >= 1 ? index : 1;
    },
    treeToTile(treeData, childKey = "children") {
      let arr = [];
      const expanded = (data) => {
        if (data && data.length > 0) {
          data
            .filter((d) => d)
            .forEach((e) => {
              arr.push(e);
              expanded(e[childKey] || []);
            });
        }
      };
      expanded(treeData);
      return arr;
    },
    // 设置表格row的class
    tableRowClassName({ row }) {
      // if (row.classLevel !== '1') {
      //     return "disabled";
      // }
      // return "";
    },
    /** 查询列表 */
    async getList() {
      if (this.sortable && this.sortable.el) {
        this.sortable.destroy();
      }
      this.loading = true;
      const { rows } = await settingDocClassList(this.queryParams);
      let showed_rows = rows.filter(v=>v.purview)
      this.dataList = showed_rows;
      this.$set(this, "postList", this.handleTree(showed_rows, "id", "parentClassId"));
      this.$set(this, "activeRows", this.treeToTile(this.postList));
      this.tableKey = new Date().getTime();
      this.$nextTick(() => {
        this.setSort();
        this.loading = false;
        if(this.bizId){
          this.getClassData()
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,

        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.classStatus = "";
      this.queryParams.className = "";
      this.queryParams.classLevel = "";
      this.queryParams.id = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    close() {
      this.$emit("treeClose")
      this.reset(); // 重置表单
    },
    saveAdd() {
      // 获取选中的元素
      if (this.ids.length == 0) {
        this.$message.warning('请至少选择一个');
        return;
      }

      // 组合对象
      const combinedData = {
        combinedMgrId: this.bizId, // 假设这是父组件传递的属性
        combinedMgrIdList: this.ids,
      };
      this.loading = true;
      // 调用保存方法
      addFileClass(combinedData)
        .then(response => {
          this.loading = false;
          this.$message.success('保存成功');
          this.close(); // 保存成功后关闭
        })
        .catch(error => {
          this.loading = false;
          console.error(error)
          // this.$message.error('保存失败: ' + error.message);
        });
    },
    /** 获取文件类型数据 **/
    getClassData(){
      let self = this
      let combinedData = {
        combinedMgrId: this.bizId, // 假设这是父组件传递的属性
      };
      // 调用保存方法
      listFileClass(combinedData)
        .then(res => {
          let data = res.rows
          data.forEach(item=>{
            self.fileClassId.push(item.fileClassId)
          })
          self.setDefaultSelections()
        })
        .catch(error => {
          this.$message.error('保存失败: ' + error.message);
        });
    },
    // 设置默认选中
    setDefaultSelections() {
      this.$nextTick(() => {
        this.dataList.forEach((row) => {
          if (this.fileClassId.includes(row.id)) {
            this.$refs.dragTable.toggleRowSelection(row, true);
          }
        });
      });
    },
    // 点击拉下框选中
    handleRowClick(row) {
      // 检查当前行是否有子节点
      if (this.fileClassId.includes(row.id)) {
        // 切换当前行的展开状态
        this.$refs.dragTable.toggleRowSelection(row, true);
      }
      // 递归检查子节点
      const selectChildren = (children) => {
        children.forEach(child => {
          if (this.fileClassId.includes(child.id)) {
            this.$refs.dragTable.toggleRowSelection(child, true);
          }
          // 如果有子节点，继续递归
          if (child.children && child.children.length > 0) {
            selectChildren(child.children);
          }
        });
      };
      // 如果有子节点，开始递归匹配
      if (row.children && row.children.length > 0) {
        selectChildren(row.children);
      }
    },
  },

};
</script>
<style lang="scss">
@import "../../../public/css/poctstyle.css";
// 拖拽
.dragClass {
  background: rgba($color: #6cacf5, $alpha: 0.5) !important;
}
// 停靠
.ghostClass {
  background: rgba($color: #6cacf5, $alpha: 0.5) !important;
}
// 选择
.chosenClass:hover > td {
  background: rgba($color: #6cacf5, $alpha: 0.5) !important;
}
.prompt-box .el-message-box__content{
  max-height: 500px;
  overflow: auto;
}
</style>
