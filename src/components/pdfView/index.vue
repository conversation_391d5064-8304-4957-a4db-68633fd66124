<template>
  <el-drawer
    title="pdf"
    :wrapperClosable='false'
    :visible.sync="visible"
    :append-to-body="true"
    direction="rtl"
    size="100%"
    :show-close="false"
    modal-append-to-body
    :destroy-on-close="true"
  >

  </el-drawer>
</template>
<script>
import PreviewPdf from "./previewPdf"
import { previewFileId,downloadFileId} from "@/api/as/asFile"
export default {
  name: "PdfView",
  data() {
    return{
      visible: false,
    }
  },
  methods: {
    init(){
      this.visible = true
      this.getDownloadFileId()
    },
    getDownloadFileId(){
      previewFileId({fileId:'1675752597572599810',}).then(res=>{
        this.xhrequest(res.url,(blob, fileType)=>{
          new PreviewPdf({
            blob: blob,
            isAddWatermark: true,
            watermark: {
              type: 'canvas',
              text: 'WFT-CANVAS'
            }
          })
        })
      })
    },
    async xhrequest(url, callback) {
      let DownUrl = url;
      let data = await fetch(DownUrl)
        .then((response) => response.blob())
        .then((res) => {
          //获取文件格式
          var index = DownUrl.lastIndexOf(".");
          //获取文件后缀判断文件格式
          var fileType = DownUrl.substr(index + 1);
          let blob = new Blob([res]);
          if (typeof callback == "function") {
            callback(blob, fileType)
          }
        });
      return data;
    }
  }
}
</script>
