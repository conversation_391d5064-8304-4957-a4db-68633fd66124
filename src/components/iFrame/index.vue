<template>
  <div>
    <div v-loading="loading" :style="'height:' + height">
      <iframe
        :src="src"
        frameborder="no"
        style="width: 100%; height: 100%"
        scrolling="auto"
        sandbox="allow-scripts allow-same-origin"
        ref="iframe"
      />
    </div>
    <deal-drawer v-if="dealDrawerShow" ref="dealDrawer" @closeDrawer="handleCloseChange"></deal-drawer>
  </div>
</template>
<script>
import DealDrawer from '@/components/DealDrawer/index.vue'

export default {
  components: { DealDrawer },
  props: {
    src: {
      type: String,
      required: true,
      default: "",
    },
  },
  data() {
    return {
      dealDrawerShow: false,
      height: document.documentElement.clientHeight - 94.5 + "px;",
      loading: true,
      url: this.src,
    };
  },
  mounted: function () {
    let _this = this
    setTimeout(() => {
      this.loading = false;
    }, 300);
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 94.5 + "px;";
    };
    this.$nextTick(()=>{
      window.addEventListener('message',event=>{
        let data = event.data
        if (data.type==='close'){
          console.debug('iframe close')
          _this.visible = false
          _this.$emit("closeDrawer")
        }else if(data.type==='open') {
          console.debug('iframe open')
          _this.dealDrawerInit(data.url)
        }else if (data.type === 'refresh') {
          console.debug('iframe refresh')
          _this.$message.success('操作成功')
          // 刷新列表
          _this.$refs.iframe.contentWindow.postMessage({ type: 'refresh' })
        } else if (data.type === 'reload') {
          console.debug('ifarme reload')
          location.reload()
        }
      })
      let path = _this.$route.query.path
      if (path) {
        _this.dealDrawerInit(path)
      }
    })
  },
  methods:{
    dealDrawerInit(url) {
      let _this = this
      let src = `${_this.src.substring(0,_this.src.indexOf("/#/"))}/#${url}`
      _this.dealDrawerShow = true
      this.$nextTick(() => {
        this.$refs.dealDrawer.init(src);
      });
    },
    handleCloseChange(){
      this.dealDrawerShow = false
    }
  }
};
</script>
