<!--<script src="../../../public/asas7/js/anyshare.sdk.js"></script>-->
<template>
  <!-- <el-dialog
    :visible.sync="visible"
    width="100%"
    style="height: 1000px"
    :close-on-click-modal="true"
    :modal-append-to-body="false"
    :append-to-body="true"
    :destroy-on-close="true"
    @close="close"
  > -->
  <!--    <div id="root" style="width: 100%;height: 900px" v-show="as7Show">-->
  <!--    -->
  <!--    </div>-->
  <!-- <iframe :src="url" style="width: 100%;height: 900px" v-show="as7Show"></iframe> -->
  <div class="asas7">
    <el-drawer
      size="100%"
      v-if="docPreview"
      :visible.sync="docPreview"
      :modal="false"
    >
      <div id="viewDiv">
        <div v-if="showViewer" style="display: flex; justify-content: center">
          <el-image :fit="fit" :src="imgUrl"></el-image>
        </div>
      </div>
    </el-drawer>
    <!-- <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
    ></el-image-viewer> -->
    <deal-drawer v-if="dealDrawerShow" ref="dealDrawer" @closeDrawer="handleCloseChange"></deal-drawer>
  </div>

  <!-- </el-dialog> -->
</template>

<script>
import {
  getAsAccessToken,
  getFile,
  fileLocalDownload,
} from "@/api/commmon/file";
// import { handleDocType } from "@/utils/onlyOfficeUtil";
// PDF本地文件预览
import {fileLocalPdfView} from "@/api/pdf_preview/index";
import {listFilePdf} from "@/api/file_processing/basicFilePdf.js";
import {
  view,
  personalPreview,
  docPreviewViewByVersion,
} from "@/api/doc_preview/previewOnline.js";
import DealDrawer from '@/components/DealDrawer/index.vue'
import { getConfigKey } from '@/api/system/config'
// import { create } from "../../../public/asas7/js/anyshare.sdk.js";

export default {
  name: "index",
  components: { DealDrawer },
  data() {
    return {
      dealDrawerShow: false,
      url: "",
      doctype: "",
      as7Show: false,
      docPreview: false,
      onlyOfficeShow: false,
      option: {
        url: "",
        key: "",
        callbackUrl: "",
        title: "",
        fileType: "",
        isEdit: false,
      },
      imgUrl:
        "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      srcList: [
        "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      ],
      showViewer: false, // 显示查看器
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: "",
    },
    fileId: {
      type: String,
      default: "",
    },
    fileName: {
      type: String,
      default: "",
    },
    fileRev: {
      type: String,
      default: "",
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleOpenView(id, source, linkType, mode,print,bizId,fileId) {
      let self = this
      // 默认为公司文件
      let sourceValue = 'COMPANY';
      if(source) {
        sourceValue = source;
      }
      let query = {
        fileId:id,
        status:'YES'
      }
      listFilePdf(query).then((res) => {
        console.log(res)
        let encryptPdfArr = res.rows.filter((x) => x.pdfType == "signature")
        let mergePdfArr = res.rows.filter((x) => x.pdfType == "merge")
        let transPdfArr = res.rows.filter((x) => x.pdfType == "transition")
        let pdfId = null
        let fileName = null
        if(encryptPdfArr.length > 0) {
          // 优先打开签章的PDF文件
          pdfId = encryptPdfArr[0].pdfId
          fileName = encryptPdfArr[0].fileObj.fileName
        }else if(mergePdfArr.length > 0){
          // 次之打开合稿的PDF文件
          pdfId = mergePdfArr[0].pdfId
          fileName = mergePdfArr[0].fileObj.fileName
        } else if(transPdfArr.length > 0) {
          // 次之打开普通转换的PDF文件
          pdfId = transPdfArr[0].pdfId
          fileName = transPdfArr[0].fileObj.fileName
        } else {
          if (res.rows.length > 0) {
            pdfId = res.rows[0].pdfId
            fileName = res.rows[0].fileObj.fileName
          }
          console.log("此fileId未找到对应的转换PDF、签章PDF文件，fileId="+id)
        }
        if (pdfId) {
          self.getpersonalPreview(id, sourceValue);
          // fileLocalPdfView(pdfId)
          this.pdfPreviewInit(pdfId,!!print,fileName,!bizId?"":bizId,!fileId?"":fileId)
        }
      });
    },
    /**
     * 修订时文件比对
     * @param id
     * @param compareId
     * @param source
     */
    compareHandleOpenView(id, compareId, source) {
      let self = this
      // 默认为公司文件
      let sourceValue = 'COMPANY';
      if(source) {
        sourceValue = source;
      }
      let fileIdList = [id, compareId]
      let query = {
        fileIdList:fileIdList,
        status:'YES'
      }
      listFilePdf(query).then((res) => {
        let transPdfArr = res.rows.filter((x) => x.pdfType == "transition")
        let comparePdfArr = res.rows.filter((x) => x.pdfType == "compare")
        let pdfId = null
        let comparePdfId = null
        if(comparePdfArr.length > 0) {
          comparePdfId = comparePdfArr[0].pdfId
        }
         if(transPdfArr.length > 0) {
          // 次之打开普通转换的PDF文件
          pdfId = transPdfArr[0].pdfId
        } else {
          if (res.rows.length > 0) {
            pdfId = res.rows[0].pdfId
          }
          console.log("此fileId未找到对应的转换PDF、签章PDF文件，fileId="+id)
        }
        if (pdfId && comparePdfId) {
          self.getpersonalPreview(id, sourceValue);
          self.getpersonalPreview(comparePdfId, sourceValue);
          this.comparePdfPreviewInit(pdfId,comparePdfId)
        }
      });
    },
    async comparePdfPreviewInit(pdfId,comparePdfId) {
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(process.env.VUE_APP_CONTEXT_PATH + '/#/comparePdfPreview?pdfId='+pdfId+'&comparePdfId='+comparePdfId)
      } else {
        this.compareDealDrawerPdfPreview(pdfId,comparePdfId)
      }
    },
    async pdfPreviewInit(pdfId,print,fileName,bizId,fileId) {
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(process.env.VUE_APP_CONTEXT_PATH + '/#/pdfPreview?pdfId='+pdfId+'&print='+print+'&fileName='+fileName+'&bizId='+bizId+'&fileId='+fileId)
      } else {
        this.dealDrawerPdfPreview(pdfId,print)
      }
    },
    dealDrawerPdfPreview(pdfId,print) {
      this.dealDrawerShow = true
      this.$nextTick(() => {
        console.log(!!window.parent[0]&&window.parent[0].name === 'dmsDrawer')
        if (!!window.parent[0]&&window.parent[0].name === 'dmsDrawer') {
          try {
            window.parent.postMessage({type:"pdfPreview",pdfId:pdfId,print:print})
          }catch (e){
            console.log(e)
          }
        }else {
          this.$refs.dealDrawer.pdfPreview({pdfId:pdfId,print:print});
        }
      });
    },
    compareDealDrawerPdfPreview(pdfId,comparePdfId){
      this.dealDrawerShow = true
      this.$nextTick(() => {
        console.log(!!window.parent[0]&&window.parent[0].name === 'dmsDrawer')
        if (!!window.parent[0]&&window.parent[0].name === 'dmsDrawer') {
          try {
            window.parent.postMessage({type:"comparePdfPreview",pdfId:pdfId,comparePdfId:comparePdfId})
          }catch (e){
            console.log(e)
          }
        }else {
          this.$refs.dealDrawer.comparePdfPreview(pdfId,comparePdfId);
        }
      });
    },
    handleCloseChange(){
      this.dealDrawerShow = false
    },
    //source 预览统计  linkType 是否可以批注
    handleOpenView2(id, source, linkType, mode) {
      console.log("linkType====", linkType);
      getFile(id).then((res) => {
        const isOpenView = this.isPreview(id, res.data.fileName);
        if (isOpenView) {
          let config = JSON.parse(sessionStorage.getItem("SYS_CONFIG"));
          // console.log('config=====>', config)
          let obj = config.filter((x) => x.configKey === "ASAS7_PLATFORM")[0];
          //console.log('baseUrl====>', baseUrl)
          if (obj && obj.configValue === "true") {
            this.onlyOfficeShow = false;
            getAsAccessToken().then((res) => {
              this.mainSdk(res.data, id);
            });
          } else {
            this.as7Show = false;
            this.setEditor(id, source, linkType, mode);
          }
          if (source != undefined && source != "") {
            this.getpersonalPreview(id, source);
          }
        }
      });

      //this.as7Show = false
      //this.setEditor(id) view edit
    },
    setEditor(id, source, linkType, mode) {
      console.log("linkType", linkType);
      this.docPreview = true;
      view(id, linkType, mode).then((res) => {
        console.log("res=====>", res);
        console.log("mode", res.data.editorConfig.mode);
        console.log("username", res.data.editorConfig.user.username);
        console.log("nickName", res.data.editorConfig.user.nickName);
        //debugger;
        if (res.code == 200) {
          let config = {
            //包含与文档有关的所有参数（标题，URL，文件类型等）
            document: {
              //文档类型 文档类-text、表格类-spreadsheet、ppt类-presentation
              // text 对应的文件类型有 .doc, .docm, .docx, .dot, .dotm, .dotx, .epub, .fodt, .htm, .html, .mht, .odt, .ott, .pdf, .rtf, .txt, .djvu, .xps
              // spreadsheet 对应的文件类型有 .csv, .fods, .ods, .ots, .xls, .xlsm, .xlsx, .xlt, .xltm, .xltx
              // presentation 对应的文件类型有 .fodp, .odp, .otp, .pot, .potm, .potx, .pps, .ppsm, .ppsx, .ppt, .pptm, .pptx
              fileType: res.data.document.fileType,
              //文件key
              key: res.data.document.key,
              //文件标题
              title: res.data.document.title,
              //包含文档的其他参数（文档作者，存储文档的文件夹，创建日期，共享设置）
              info: {
                //收藏按钮
                //favorite: false,
                //文档创建人
                author: "John Smith",
                //创建时间
                created: "2010-07-07 3:46 PM",
                //文件夹
                folder: res.data.document.storage,
                //访问权限
                sharingSettings: [
                  {
                    //这个只是一个权限信息的展示 不是真正的权限控制
                    permissions: "Full Access",
                    user: "John Smith",
                  },
                  {
                    isLink: true,
                    permissions: "Read Only",
                    user: "Kate Cage",
                  },
                ],
              },
              //定义是否可以编辑和下载文档
              permissions: {
                //能否复制
                copy: true,
                //是否可以批注
                comment: linkType == 'nocomment' ? false : true,
                commentGroups: {
                  edit: ["group-2", ""],
                  remove: ["group-2"],
                },
                //是否可以编辑: 只能查看，传false
                edit: res.data.editorConfig.mode == "edit" ? true : false,
                //edit:true,
                //是否下载
                download: false,
                //修改内容控制
                modifyContentControl: true,
                //筛选
                modifyFilter: true,
                //只有自己才能编辑(批注评论等)
                editCommentAuthorOnly: false,
                ////只有自己才能删除(批注评论等)
                deleteCommentAuthorOnly: false,
                //是否打印
                print: false,
                ////是否可以填写表格，如果将mode参数设置为edit，则填写表单仅对文档编辑器可用。 默认值与edit或review参数的值一致。
                fillForms: true,
                //跟踪变化
                review: false,
              },
              //文件流路径
              url: res.data.document.url,
            },
            //定义与编辑器界面有关的参数：打开模式（查看器或编辑器），界面语言，附加按钮等）
            editorConfig: {
              //回调地址 这个地址用于onlyOffice回调改地址 这个接口保存修改后的文档
              callbackUrl: res.data.editorConfig.callbackUrl,
              //语言
              lang: "zh-CN",
              location: "zh-CN",
              customization: {
                //是否自动保存
                autosave: true,
                // true 表示强制文件保存请求添加到回调处理程序
                forcesave: true,
                //聊天功能
                chat: true,
                //仅有作者可以批注
                commentAuthorOnly: false,
                //是否显示左侧批注列表
                comments: true,
                compactHeader: true,
                compactToolbar: true,
                feedback: {
                  // 隐藏反馈按钮
                  visible: false,
                },
                help: false,
                //定义在第一次加载时是显示还是隐藏右侧菜单。 默认值为false
                hideRightMenu: true,
                //插件
                plugins: false,
                logo: {
                  //image: 'https://file.iviewui.com/icon/viewlogo.png',
                  //imageEmbedded: 'https://file.iviewui.com/icon/viewlogo.png'
                },
                //拼写检查
                spellcheck: false,
              },
              //定义当前正在查看或编辑文档的用户：
              user: {
                id: res.data.editorConfig.user.username,
                name: res.data.editorConfig.user.nickName,
              },
              //view=预览 edit=编辑 等于edit时 callbackUrl必须有值且接口正常
              mode: res.data.editorConfig.mode,
              //mode: "edit",
            },
            //预览/编辑框的长宽
            width: "100%",
            height: "100%",
          };
          console.log(config, "config===");
          let docEditor = new DocsAPI.DocEditor("viewDiv", config);
        } else {
          this.docPreview = false;
          this.$message.error(res.msg);
        }
      });
    },
    mainSdk(token, id) {
      this.as7Show = true;
      this.docPreview = true;
      getFile(id).then((res) => {
        console.log("res====>", res);
        // + "&rev=" + res.data.externalRev
        let item = {
          docid: res.data.externalFileId,
          name: res.data.externalFilePath,
          //rev: this.$route.query.rev,
          //size: this.$route.query.size,
        };
        if (res.data) {
          let config = JSON.parse(sessionStorage.getItem("SYS_CONFIG"));
          //console.log(this.$refs.viewDiv)
          create({
            apiBase: config.filter((x) => x.configKey === "AS_BASE_URL")[0]
              .configValue,
            rootElement: document.getElementById("viewDiv"),
            token: token,
            locale: "zh-cn",
          })
            .then(function resolve(sdk) {
              sdk.preview(
                item,
                false
                //console.log(document.getElementsByClassName("as-officeOnline-iframe")),
                //console.log(document.getElementsByClassName("as-officeOnline-iframe")[0].src)
              );
            })
            .catch(function reject(err) {
              console.error(err);
            });
        }
        //window.open(this.url);
      });
      //let pathOffice = `officeonline?gns=5EF26633E8E740D3AFBB14022CC575CA/6F3457DF6E3D4214AD4544EAFF3D4E9B&name=反馈意见.doc&_tb=none&rev=5256D36BCE084C76BF6F33A9397A5425`;
      // AnyShareSDKFactory.create({
      //  //apiBase: "https://vzshare.vazyme.com:10443",
      //   apiBase: "https://sz.aishu.cn",
      //   rootElement: window.document.getElementById("viewDiv"),
      //   token: token,
      //   locale: "zh-cn",
      // })
    },
    // 记录个人阅知文件记录
    getpersonalPreview(id, source) {
      personalPreview({ id: id, source: source }).then((res) => {
        console.log(res);
      });
    },
    close() {
      this.$emit("close", false);
    },
    //isPreview
    isPreview(url, name) {
      let fileExtension = "";
      let fileType = ["jpg", "png", "docx", "doc", "xls", "ppt", "pdf"];
      if (name.lastIndexOf(".") > -1) {
        fileExtension = name.slice(name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => {
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      if (!isTypeOk) {
        this.$modal.msg(`文件格式不支持预览!`);
        return false;
      }
      const isTupian = this.isTupian(name);
      if (isTupian) {
        this.handeleyulan(url);
      } else {
        return true;
      }
    },
    //校检是否是图片
    isTupian(name) {
      //  debugger;
      let fileExtension = "";
      let fileType = ["jpg", "png"];
      if (name.lastIndexOf(".") > -1) {
        fileExtension = name.slice(name.lastIndexOf(".") + 1);
      }
      console.log("fileExtension", fileExtension);
      const isTypeOk = fileType.some((type) => {
        console.log("type", type);
        console.log("fileExtension.indexOf(type)", fileExtension.indexOf(type));
        //if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      console.log("isTypeOk", isTypeOk);
      return isTypeOk;
    },
    //预览图片文件
    handeleyulan(id) {
      fileLocalDownload(id)
        .then((res) => {
          this.srcList = [];

          // return this.blobUrl(res);
          let blob = new Blob([res], { type: "image/jpeg" });
          const imageUrl = URL.createObjectURL(blob);
          //console.log('imageUrl',imageUrl);
          this.imgUrl = imageUrl;
          this.srcList.push(imageUrl);
          //this.$refs.elimage.$el.click()
          //document.getElementById("img").click();
          this.showViewer = true;
          this.docPreview = true;
        })
        .catch((err) => {
          console.log("导出失败");
        });
    },
    closeViewer() {
      //关闭
      this.showViewer = false;
    },
    handelefileLocalDownload(id, name) {
      fileLocalDownload(id).then((res) => {
        //console.log("file", res);
        this.saveFile(res, name);
      });
    },
    saveFile(data, name) {
      try {
        const blobUrl = window.URL.createObjectURL(data);
        // console.log('bo',blobUrl);
        const a = document.createElement("a");
        a.style.display = "none";
        a.download = name;
        a.href = blobUrl;
        a.click();
      } catch (e) {
        alert("保存文件出错");
      }
    },
  },
};
// export const asPreview = function (item,token) {
//   console.log(token);
//   console.log(item);
//   console.log(document.getElementById("viewDiv"))
//   let config = JSON.parse(sessionStorage.getItem("SYS_CONFIG"));
//   //console.log(this.$refs.viewDiv)
//     create({
//         apiBase: config.filter((x) => x.configKey === "AS_BASE_URL")[0].configValue,
//         rootElement: document.getElementById("viewDiv"),
//         token: token,
//         locale: "zh-cn",
//     })
//         .then(function resolve(sdk) {
//             sdk.preview(
//                 item,
//                 false,
//             );
//         })
//         .catch(function reject(err) {
//             console.error(err);
//         });
// }
</script>

<style lang="scss">
.el-image-viewer__wrapper {
  z-index: 200000 !important;
}
.asas7 {
  /*爱数样式调整*/
  #viewDiv .ant-dropdown-trigger .ant-layout {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 5;
  }
  #viewDiv
    .ant-dropdown-trigger
    .ant-layout
    .as-components-docked-app-container-content {
    width: 100%;
    height: 100%;
  }
  #viewDiv
    .ant-dropdown-trigger
    .ant-layout
    .as-components-docked-app-container-content
    .as-components-officeOnline {
    width: 100%;
    height: 100%;
  }
  #viewDiv
    .ant-dropdown-trigger
    .ant-layout
    .as-components-docked-app-container-content
    .as-components-officeOnline
    .as-officeOnline-iframe {
    width: 100%;
    height: 100%;
    border: 0;
  }
  /*onlyOffice 样式调整*/
  .el-drawer .el-drawer__header {
    padding: 5px 15px 5px 0px;
  }
  .el-drawer .el-drawer__body {
    padding: 0px !important;
  }
  .el-drawer .el-drawer__body {
    padding: 0px !important;
  }
  .el-drawer .el-drawer__header .el-drawer__close-btn {
    width: 20px;
    height: 20px;
  }
  .el-drawer .el-drawer__header {
    padding: 0px !important;
  }
}
</style>
