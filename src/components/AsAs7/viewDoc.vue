<!--onlyoffice 编辑器-->
<template>
  <div >
      <div id="viewDiv">

      </div>
  </div>
</template>

<script>
import { handleDocType } from '@/utils/onlyOfficeUtil'

export default {
  name: 'viewDoc',
  props: {
    option: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      doctype: ''
    }
  },
  create() {
    if (this.option.url) {
      this.setEditor(this.option)
    }
  },
  methods: {
    setEditor(option) {
      this.doctype = handleDocType(option.fileType)
      console.log(this.doctype)
      let config = {
        document: {
          fileType: option.fileType,
          key: option.key,
          title: option.title,
          permissions: {
            comment: true,
            download: false,
            modifyContentControl: true,
            modifyFilter: true,
            print: false,
            edit: option.isEdit,
            fillForms: true,
            review: true
          },
          url: option.url
        },
        // documentType: this.doctype,
        editorConfig: {
          callbackUrl: option.callbackUrl,
          lang: 'zh',
          customization: {
            commentAuthorOnly: false,
            comments: true,
            compactHeader:false,
            compactToolbar:true,
            feedback:false,
            plugins:true
          },
          user:{
            id:option.user.id,
            name:option.user.name
          },
          mode:option.model?option.model:'view',

        },
        width: '100%',
        height: '750px',
        // token:option.token
      }
       console.log(config)
      let docEditor = new DocsAPI.DocEditor('viewDiv', config)
    },
  },
    watch: {
        option: {
            handler: function (n, o) {
                this.setEditor(n)
                this.doctype = handleDocType(n.fileType)
            },
        deep: true,
        }
    }
}
</script>

<style scoped>

</style>
