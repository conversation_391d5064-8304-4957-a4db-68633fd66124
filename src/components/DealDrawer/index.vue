<template>
  <div>
  <el-drawer
    :wrapperClosable="false"
    :visible.sync="visible"
    :append-to-body="true"
    direction="rtl"
    size="90%"
    :with-header="false"
    :show-close="false"
    modal-append-to-body
    :destroy-on-close="true"
  >
    <div style="width:100%; height:100%;overflow: hidden">
      <iframe :src="src" width="100%" name="dmsDrawer" height="100%" title="myFrame" style="border: 0"></iframe>
    </div>
  </el-drawer>
  <el-drawer
    :visible.sync="drawerShow"
    v-if="drawerShow"
    direction="rtl"
    :size="size"
    :append-to-body="true"
    :with-header="false"
    :wrapperClosable="false"
    :show-close="false"
    modal-append-to-body
    :destroy-on-close="true"
  >
    <main-component ref="mainComponent" :code="path" :data="data" @close="handleCodeCloseChange"></main-component>
  </el-drawer>
  </div>
</template>

<script>
/*
流程处理抽屉组件
*/
import MainComponent from '@/components/mainComponent/index.vue'

export default {
  name: 'DealDrawer',
  components: { MainComponent },
  data() {
    return {
      size: '90%',
      workflowMessageCbAdded: false,
      visible: false,
      drawerShow: false,
      data: undefined,
      path: '',
      src: ''
    }
  },
  activated() {
    this.setupMessageListener()
  },
  created() {
    this.setupMessageListener()
  },
  deactivated() {
    this.removeMessageListener()
  },
  beforeDestroy() {
    this.removeMessageListener()
  },
  methods: {
    //建立addEventListener事件
    setupMessageListener() {
      if (!this.workflowMessageCbAdded) {
        window.addEventListener('message', this.handleMsg)
        this.workflowMessageCbAdded = true
      }
    },
    //销毁addEventListener事件
    removeMessageListener() {
      window.removeEventListener('message', this.handleMsg)
      this.workflowMessageCbAdded = false
    },
    handleMsg(event) {
      let _this = this
      let data = event.data
      if (data.type === 'close') {
        _this.visible = false
        if (data.msgType) {
          _this.$modal[data.msgType](data.msg)
        }
        _this.$emit('closeDrawer')
      }else if(data.type === 'pdfPreview'){
        this.pdfPreview(data)
      }else if(data.type === 'comparePdfPreview'){
        this.comparePdfPreview(data.pdfId,data.comparePdfId)
      }
    },
    pdfPreview(data){
      this.size = '100%'
      this.path = 'views/components/pdfPreview/index'
      this.data = data
      this.drawerShow = true
    },
    comparePdfPreview(pdfId,comparePdfId){
      this.path = 'views/components/pdfPreview/compareIndex'
      let data = {}
      data.pdfId = pdfId
      data.comparePdfId = comparePdfId
      this.data = data
      this.drawerShow = true
    },
    init(url) {
      let _this = this
      _this.visible = true
      // iframe加载URL地址
      _this.src = url
    },
    handleCodeCloseChange() {
      this.drawerShow = false
    },
  }
}
</script>
