<template>
  <!-- 用户选人组件 -->
  <el-dialog  :title="title" v-if="show" :visible.sync="show" :width="width" append-to-body :close-on-click-modal="false">
      <el-form
        :model="formSubmit"
        ref="formSubmit"
        class="mt20"
        :rules="formrules"
        label-width="50px"
        v-if="!loading"
      >
      <div class="choose-box">
        <div class="cell">
              <div class="choo-head">
                  <span class="cell-title">待选用户</span>
              </div>
            <!-- 部门人员结构树 -->
            <div class="choo-body">
                <el-input placeholder="关键字搜索" v-model.trim="treeKey" clearable size="small">
                <el-button slot="append" icon="el-icon-search"></el-button>
              </el-input>
              <el-tree
                ref="tree"
                :data="selectingList"
                :props="userProps"
                @node-click="userNodeClick"
                :filter-node-method="filterNode"
                :default-expand-all="false"
                style="width:350px;height:300px;overflow:auto;"></el-tree>
          </div>
        </div>
       <div class="cell cell-yx">
              <div class="choo-head">
                  <span class="cell-title">已选用户</span>
                  <span class="cell-btns"><el-button type="text" @click="handleClear">清空</el-button></span>
              </div>
              <div class="choo-body">
              <ul class="user-list" style="width:300px;height: 330px;overflow:auto;">
                <li v-for="(item, index) in selectedList" :key="index">
                  <i class="el-icon-s-custom"></i> {{ item.deptName != null ? item.deptName+'/' : '' }}{{ item.label }}<i class="el-icon-close" v-if="item.nums==undefined" @click="removeData(item)"></i>
                </li>
              </ul>
            </div>
        </div><!---cell--->
      </div><!---choose-box 选人组件--->  
      </el-form>
       <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleShow(false)">取消</el-button>
            <el-button type="primary" size="mini" @click="handleSubmit">确定</el-button>
      </span>
  </el-dialog>
</template>

<script>
import { treeselect } from "@/api/system/user";
export default {
  components: {},
  // bizId 调用方业务ID
  // width 组件宽度
  // to_data 已选择
  props: ["bizId","width", "to_data"],
  data() {
    return {
      // 左侧树形结构属性
      userProps: {
        children: "children",
        // label: "deptName",
        label: "label",
      },
      // 是否展示组件
      show:false,
      // 调用模块业务ID
      invokeBizId: "",
      // 待选择清单
      selectingList: [],
      // 已选择清单
      selectedList: [],
      loading: true,
      treeKey:"",
    };
  },
  created() {
    // 初始化
    this.selectingList = []
    this.selectedList = []
    // 加载右侧已选清单
    this.loadselectedList();
    // 加载左侧待选清单
    this.loadselectingList();
  },
  watch: {
     // 根据关键字搜索左侧树
    treeKey(val) {
      this.$refs.tree.filter(val);
    },
    bizId: {
        // 调用模块业务ID
      handler(newValue, oldValue) {
        this.loadBizId();
      }
    },
    to_data: {
        // 已选清单集合
      handler(newValue, oldValue) {
        this.loadselectedList();
      },
      deep: true,
    },
    show: {
        // 已选清单集合
      handler(newValue, oldValue) {
        this.loadselectedList();
      },
      deep: true,
    },
    'selectedList'(val){
      // console.log(val);
    }
  },
  mounted() {},
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleClear() {
       this.selectedList = []
    },
    handleShow(val){
       // 展示组件或隐藏组件
       this.show = val 
    },
    handleSubmit() {
        // 返回数据给父组件
        this.$emit("callback",this.invokeBizId,JSON.stringify(this.selectedList))
        this.handleShow(false)
    },
    loadselectingList() {
      // 加载左侧待选清单
      this.loading = true;
      treeselect({}).then(response => {
        this.selectingList = response.data;
        this.loading = false;
      });
    },
    loadselectedList() {
      // 加载右侧已选清单
      // 深COPY
      if(this.to_data) {
          this.selectedList = JSON.parse(JSON.stringify(this.to_data)) ;
      }
    },
    loadBizId() {
        this.invokeBizId = this.bizId;
    },
    //点击成员 导入可选成员
    userNodeClick(val) {
      if(this.selectedList) {
        let arr = this.selectedList.filter((x) => x.id === val.id);
        if (arr.length <= 0 && val.children == undefined) {
          this.selectedList.push(val);
        }
      } else {
        this.selectedList = []
        this.selectedList.push(val);
      }
    },
    //移除成员
    removeData(item) {
      var arr = [];
      this.selectedList.forEach((element) => {
        if (element.id != item.id) {
          arr.push(element);
        }
      });
      this.selectedList = arr;
    },
    /**
     * 两个数组对象去复
     * @param {*} array1
     * @param {*} array2
     */
    arrayRepeat(array1, array2) {
      var result = [];
      for (var i = 0; i < array2.length; i++) {
        var obj = array2[i];
        var num = obj.deptId;
        var isExist = false;
        for (var j = 0; j < array1.length; j++) {
          var aj = array1[j];
          var n = aj.deptId;
          if (n === num) {
            isExist = true;
            break;
          }
        }
        if (!isExist) {
          result.push(obj);
        }
      }
      return result;
    },
  },
};
</script>
