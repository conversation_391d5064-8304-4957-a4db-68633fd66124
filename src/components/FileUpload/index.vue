<template>
  <div class="upload-file rzfujian">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      v-show="editStatus&&fileList.length<limit"
      ref="upload"
    >
      <!-- 上传按钮 -->
      <div style="display: flex" v-if="editStatus">
        <el-link size="mini" :underline="false" type="primary">{{
          title
        }}</el-link>
        <!-- 上传提示 -->
        <div class="el-upload__tip" slot="tip" v-if="showTip">
          请上传
          <template v-if="fileSize">
            大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
          </template>
          <template v-if="fileType">
            格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
          </template>
          的文件
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
      v-if="showTransition"
    >
      <li
        :key="file.url"
        class="el-upload-list__item ele-upload-list__item-content"
        v-for="(file, index) in fileList"
      >
        <!-- <el-link
          v-if="!isTupian(file.name)"
          @click="handlePreview(file.url)"
          :underline="false"
          target="_blank"
        >
          <span class="el-icon-document"> {{ file.name }}</span>
        </el-link>
        <el-link
          v-show="isTupian(file.name)"
          :underline="false"
          @click="handeleyulan(file.url)"
          target="_blank"
        >
          <span class="el-icon-document"> {{ file.name }}</span>
        </el-link> -->
        <el-link
          @click="isPreview(file.url, file.name)"
          :underline="false"
          target="_blank"
        >
          <span class="el-icon-document"> {{ file.name }}</span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link v-if="editStatus" :underline="false" @click="handleDelete(index)" type="danger">{{$t(`doc.this_dept_delete`)}}</el-link>
<!--          <el-link v-if="editStatus && (file.name.includes('.docx') || file.name.includes('.doc') || file.name.includes('.DOCX') || file.name.includes('.DOC'))" :underline="false" @click="handlePreviewFile(index)" type="primary">{{$t(`doc.this_dept_edit`)}}</el-link>-->
          <el-link
            :underline="false"
            v-if="isMode"
            @click="handlePreview(file.url, '', '', 'edit')"
            type="primary"
            >{{$t(`sys_mgr_log.user_signature_upload_text5`)}}</el-link
          >
          <el-link v-if="downStatus || !isDocOrDocxOrPptOrPptx(file.name)"  :underline="false" @click="handelefileLocalDownload(file.url, file.name)" type="primary">{{$t(`doc.this_dept_download`)}}</el-link>
          <el-link v-if="pdfDownStatus" :underline="false" @click="handelefileLocalDownloadPdf(file.url,file.name)" type="primary">{{$t(`doc.this_dept_download`)}}</el-link>
          <el-link v-if="coverDownStatus" :underline="false" @click="handleFileDownload(file.applyId,file.name)" type="primary">{{$t(`doc.this_dept_download`)}}</el-link>
          <slot :fileId="file.url" :fileName="file.name" :protoFileId="file.protoFileId" :isOffice="isOffice(file.name)"></slot>
        </div>
      </li>
    </transition-group>
    <as-pre-view  :visible="viewShow" :id="viewId" ref="viewRef" @close="close">
    </as-pre-view>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
    ></el-image-viewer>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { fileLocalDownload, getFile, processFileLocalUpload } from '@/api/commmon/file'
// 导入组件
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import mixin from "@/layout/mixin/Commmon.js";
import { copyFile, downloadFileDocId } from '@/api/as/asFile'
import { coverDownload } from '@/api/file_processing/fileSignature'
export default {
  name: "FileUpload",
  mixins: [mixin],
  components: {
    ElImageViewer,
  },
  props: {
    downStatus:{
      type: Boolean,
      default: false,
    },
    coverDownStatus:{
      type: Boolean,
      default: false,
    },
    pdfDownStatus:{
      type: Boolean,
      default: false,
    },
    // 值
    value: [String, Object, Array],
    // 上传
    title: {
      type: String,
      // 或者使用函数
      default: function () {
        return this.$t('doc.this_dept_upload');
      }
    },
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 1024,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["docx", "doc", "xls", "ppt", "txt", "pdf", "jpg", "png"],
    },
    // 是否限制文件类型
    isfileType: {
      type: Boolean,
      default: true,
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: false,
    },
    // 是否显示文件列表
    showTransition: {
      type: Boolean,
      default: true,
    },
    // 是否可以在线编辑
    isMode: {
      type: Boolean,
      default: false,
    },
    editStatus:{
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      number: 0,
      fileSizeMin: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/process/file/local_upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      imgUrl: "",
      srcList: [
        // "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      ],
      showViewer: false, // 显示查看器]
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(",");
          //console.log("list", list);
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            if (typeof item === "string") {
              item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    isDocOrDocxOrPptOrPptx(filename) {
      const extensions = ["docx", "doc", "xls", "ppt", "txt", "pdf", "jpg", "png"];
      return extensions.some(ext => filename.endsWith(ext));
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.isfileType) {
        if (this.fileType) {
          let fileExtension = "";
          if (file.name.lastIndexOf(".") > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
          }
          const isTypeOk = this.fileType.some((type) => {
            //if (file.type.indexOf(type) > -1) return true;
            if (fileExtension && fileExtension.indexOf(type) > -1) return true;
            return false;
          });
          if (this.fileType.filter(x => x == fileExtension) <= 0) {
            this.$modal.msgError(
              `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
            );
            return false;
          }
          console.log(isTypeOk);
          if (!isTypeOk) {
            this.$modal.msgError(
              `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
            );
            return false;
          }
        }
      }

      //校验文件重复
      for (let index = 0; index < this.fileList.length; index++) {
        const element = this.fileList[index];
        if (element.name == file.name) {
          this.$modal.msgError(`文件重复!`);
          return false;
        }
      }

      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      // debugger;

      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed(files, fileList) {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传成功回调
    handleUploadSuccess(res,uploadFile,uploadFiles) {
      if (res.code != 200) {
        // 去除文件列表失败文件
        let uid = uploadFile.uid
        let idx = uploadFiles.findIndex(item => item.uid === uid)
        uploadFiles.splice(idx, 1)
        this.uploadList = [];
        this.number = 0;
        this.$modal.msgError(res.msg);
        this.$modal.closeLoading();
        return;
      }
      this.$modal.closeLoading();
      let file={ name: res.data.fileName, url: res.data.fileId,protoFileId:res.data.fileId }
      this.uploadList.push(file);
      if (this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        //console.log("this.fileList1", this.fileList);
        this.$emit("input", this.listToString(this.fileList));
        //console.log("this.fileList2", this.fileList);
      }
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1);
      this.$emit("input", this.listToString(this.fileList));
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return "";
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      // let strs = "";
      // separator = separator || ",";
      // for (let i in list) {
      //   strs += list[i].url + separator;
      // }
      // return strs != "" ? strs.substr(0, strs.length - 1) : "";
      return list;
    },
    //isPreview
    isPreview(url, name) {
      let fileExtension = "";
      let fileType = ["jpg", "png", "docx", "doc", "xls", "ppt", "pdf"];
      if (name.lastIndexOf(".") > -1) {
        fileExtension = name.slice(name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => {
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      console.log(url,name)
      if (!isTypeOk) {
        this.$modal.msg(`文件格式不支持预览!`);
        return false;
      }
      const isTupian = this.isTupian(name);
      console.log(isTupian)
      console.log(url)
      if (isTupian) {
        this.handeleyulan(url);
      } else {
        this.handlePreview(url);
      }
    },
    //校检是否是图片
    isTupian(name) {
      let fileExtension = "";
      let fileType = ["jpg", "png"];
      if (name.lastIndexOf(".") > -1) {
        fileExtension = name.slice(name.lastIndexOf(".") + 1);
      }
      console.log("fileExtension", fileExtension);
      const isTypeOk = fileType.some((type) => {
        console.log("type", type);
        console.log("fileExtension.indexOf(type)", fileExtension.indexOf(type));
        //if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      console.log("isTypeOk", isTypeOk);
      return isTypeOk;
    },
    isOffice(name){
      let fileType = ['docx','doc','xls','xlsx','ppt','pptx'];
      if (name.lastIndexOf(".") > -1) {
        let fileExtension = name.slice(name.lastIndexOf(".") + 1).toLowerCase();
        return fileType.includes(fileExtension)
      }else {
        return false
      }
    },
    //预览图片文件
    handeleyulan(id) {
      fileLocalDownload(id)
        .then((res) => {
          this.srcList = [];
          // return this.blobUrl(res);
          let blob = new Blob([res], { type: "image/jpeg" });
          const imageUrl = URL.createObjectURL(blob);
          this.imgUrl = imageUrl;
          this.srcList.push(imageUrl);
          //this.$refs.elimage.$el.click()
          //document.getElementById("img").click();
          this.showViewer = true;
          console.log("this.showViewer", this.showViewer);
        })
        .catch((err) => {
          console.log("导出失败");
        });
    },
    handlePreviewFile(index) {
      let fileData = this.fileList[index];
      let fileId = fileData.url;
      // 查询附件
      this.loading = this.$loading({
        lock: true,
      });
      return getFile(fileId).then(res => {
        console.log("附件=====>", res)
        let _this = this
        let gns = res.data.externalFileId.split("//")[1]
        let fileName = res.data.fileName;
        // 爱数平台token
        let asToken = sessionStorage.getItem('asToken')||"ory_at_pvI0HBaaKZ2ttsUczTr9PdfKSAPQTdufjYm1bIIgPTY.8yNA_UVWuBjg3_WucH9vDPi3q2jFfF8tedDzAAW4gdM";
        // 复制文件
        return copyFile(gns).then(response => {
          // 打开副本文件预览编辑
          let gns2 = response.data.docid.split("//")[1]
          console.log("副本文件gns2====>", gns2)
          let iframeSrc = `https://sz.aishu.cn/anyshare/officeonline?_tb=none&gns=`+ gns2 + `&name=`+ fileName + `&tokenid=` + asToken;
          let newWindow = window.open(iframeSrc, "_blank");
          // 监听窗口是否关闭
          let timer = setInterval(function() {
            if(newWindow.closed) {
              clearInterval(timer);
              // 下载附件
              console.log("附件=====>", response.data)
              downloadFileDocId(response.data.docid, fileName).then(res => {
                console.log("下载附件====>", res)
                const file = new Blob([res]);
                const formData = new FormData();
                formData.append('file', file, fileName);
                // 重新上传到本地
                processFileLocalUpload(formData).then(res=> {
                  console.log("重新上传====>", res)
                  fileData.name= res.data.fileName
                  fileData.url =  res.data.fileId
                })
              })
            }
          }, 3000);
        })
      }).finally(()=>{
        this.loading.close();
      })
    },
    closeViewer() {
      //关闭
      this.showViewer = false;
    },
    handleFileDownload(applyId,name){
      this.loading = this.$loading({
        lock: true,
      });
      coverDownload(applyId).then((res)=>{
        name = name.substring(0,name.lastIndexOf('.')) + '.pdf'
        this.saveFile(res, name);
      }).finally(()=>{
        this.loading.close();
      })
    },
  },
};
</script>

<style lang="scss">
.el-image-viewer__wrapper {
  z-index: 200000 !important;
}
.rzfujian {
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
  .el-upload {
    border: none !important;
    .el-upload__tip {
      margin-top: 2px;
      margin-left: 7px;
    }
  }
  .el-upload:hover {
    background-color: #fff !important;
  }
  .el-upload__tip {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
