<template>
  <div class="news-card">
    <div class="card-head">
      <div class="cell-title">{{ $t('file_set.file_push') }}</div>
    </div>
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="medium"
        label-position="right"
        label-width="150px"
      >
        <el-row gutter="15">
          <el-col :span="24">
            <!-- 是否下推文件 -->
            <el-form-item :label="$t('file_set.is_push_file')" prop="pushFile">
              <dict-tag v-if="isReadOnly" :options="dict.type.sys_yes_no" :value="form.pushFile ? 'Y' : 'N'"/>
              <el-radio-group v-else v-model="form.pushFile" @change="handlePushFileChange">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.value === 'Y'"
                >
                  {{ dictLanguage(dict) }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <template v-if="form.pushFile">
          <el-row gutter="15">
            <el-col :span="24">
              <!-- 下推公司 -->
              <el-form-item :label="$t('file_set.subsidiary_company')" prop="pushCompanyId">
                <span v-if="isReadOnly">{{ form.pushCompanyName || '-' }}</span>
                <el-select
                  v-else
                  v-model="form.pushCompanyId"
                  :placeholder="$t('file_set.please_select_subsidiary_company')"
                  clearable
                  style="width: 30%"
                  filterable
                  @change="handleCompanyChange"
                >
                  <el-option
                    v-for="item in dict.type.downpush_company"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row gutter="15">
            <el-col :span="24">
              <!-- 下推分类 -->
              <el-form-item :label="$t('file_set.subsidiary_doc_class_name')" prop="pushClassId">
                <span v-if="isReadOnly">{{ form.pushClassName || '-' }}</span>
                <el-select
                  v-else
                  v-model="form.pushClassId"
                  :placeholder="!form.pushCompanyId ? $t('file_set.please_select_subsidiary_company_first') : $t('file_set.please_select_subsidiary_doc_class')"
                  clearable
                  filterable
                  style="width: 30%"
                  :disabled="!form.pushCompanyId"
                  @change="handleDocClassChange"
                >
                  <el-option
                    v-for="item in subsidiaryDocClassOptions"
                    :key="item.id"
                    :label="item.className"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row gutter="15" v-if="showPushFile">
            <el-col :span="24">
              <!-- 下推文件 -->
              <el-form-item :label="$t('file_set.push_file')" prop="pushFileId">
                <el-select
                  v-model="form.pushFileId"
                  :placeholder="!form.pushCompanyId ? $t('file_set.please_select_push_company_first') : $t('file_set.please_select_push_file')"
                  clearable
                  filterable
                  :filter-method="filterDocFiles"
                  style="width: 30%"
                  :disabled="!form.pushCompanyId"
                  @change="handleDocFileChange"
                  ref="pushFileSelect"
                >
                  <el-option
                    v-for="item in displayDocFileOptions"
                    :key="item.id"
                    :label="`${item.docName}${item.docId ? ` (${item.docId})` : ''}`"
                    :value="item.id"
                  >
                    <span>{{ item.docName }}</span>
                    <span v-if="item.docId" style="color: #8492a6; font-size: 13px; margin-left: 8px;">({{ item.docId }})</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row gutter="15">
            <el-col :span="24">
              <!-- 接收人员 -->
              <el-form-item :label="$t('file_set.connector')" prop="receiveUserName">
                <span v-if="isReadOnly">{{ form.receiveNickName || '-' }}</span>
                <el-select
                  v-else
                  v-model="form.receiveUserName"
                  :placeholder="$t('file_set.please_select')"
                  clearable
                  style="width: 30%"
                  filterable
                  @change="handleReceiveUserChange"
                >
                  <el-option
                    v-for="item in connectorOptions"
                    :key="item.userName"
                    :label="item.nickName"
                    :value="item.userName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-row gutter="15" v-if="showReceiptStatus">
          <el-col :span="24">
            <!-- 回执状态 -->
            <el-form-item :label="$t('file_set.receipt_status')">
              <dict-tag :options="dict.type.process_status" :value="form.status"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {settingDocClassId} from "@/api/file_settings/type_settings"
import {getZonciActiveDocClass} from '@/api/setting/docClass'
import {getActiveUsers} from "@/api/system/user"
import {listActiveVersion} from '@/api/document_account/version'

export default {
  name: 'FilePushConfig',
  dicts: ['downpush_company', 'sys_yes_no', 'process_status'],
  props: {
    docClassId: {
      type: String,
      required: true
    },
    docId: {
      type: String,
      required: false
    },
    dataType: {
      type: String,
      default: 'stdd'
    },
    showReceiptStatus: {
      type: Boolean,
      default: false
    },
    receiptStatus: {
      type: String,
      default: ''
    },
    showPushFile: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    },
    required: {
      type: Boolean,
      default: false
    },
    isReadOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      hasDetailData: false,
      form: this.getInitialForm(),
      rules: {
        pushCompanyId: [
          {required: this.required, message: this.$t('file_set.please_select_subsidiary_company'), trigger: 'change'}
        ],
        pushClassId: [
          {required: this.required, message: this.$t('file_set.please_select_subsidiary_doc_class'), trigger: 'change'}
        ],
        pushFileId: [
          {required: this.required, message: this.$t('file_set.please_select_push_file'), trigger: 'change'}
        ],
        receiveUserName: [
          {required: this.required, message: this.$t('file_set.please_select_connector'), trigger: 'change'}
        ]
      },
      subsidiaryDocClassOptions: [],
      subsidiaryDocFileOptions: [],
      allSubsidiaryDocFiles: [],
      connectorOptions: [],
      searchKeyword: ''
    }
  },
  computed: {
    displayDocFileOptions() {
      if (!this.searchKeyword) {
        return this.subsidiaryDocFileOptions
      }
      const searchQuery = this.searchKeyword.toLowerCase()
      return this.subsidiaryDocFileOptions.filter(file => {
        const docName = (file.docName || '').toLowerCase()
        const docId = (file.docId || '').toLowerCase()
        return docName.includes(searchQuery) || docId.includes(searchQuery)
      })
    }
  },
  created() {
    this.initializeForm()
    if (this.docClassId) {
      this.getDetail()
    }
  },
  mounted() {
    if (this.data) {
      this.initializeForm()
    }
    if (!this.data) {
      this.$nextTick(() => {
        this.setDefaultCompany()
      })
    }
  },
  watch: {
    data: {
      handler(newData) {
        if (newData) {
          this.initializeForm()
        }
      },
      immediate: true,
      deep: true
    },
    docId: {
      handler(newDocId) {
        if (newDocId && this.subsidiaryDocFileOptions.length > 0) {
          this.autoSelectFileByDocId()
        }
      }
    },
    docClassId: {
      handler(newVal) {
        if (newVal) {
          this.getDetail()
        }
      },
      immediate: true
    }
  },
  methods: {
    getInitialForm() {
      return {
        pushFile: false,
        pushCompanyId: '',
        pushCompanyName: '',
        pushClassId: '',
        pushClassName: '',
        pushFileId: '',
        pushFileName: '',
        pushVersionId: '',
        pushDocId: '',
        pushDocName: '',
        pushVersionValue: '',
        receiveUserName: '',
        receiveNickName: ''
      }
    },

    initializeForm() {
      if (this.data) {
        this.form = {
          pushFile: this.data.pushFile !== undefined ? this.data.pushFile : false,
          pushCompanyId: this.data.pushCompanyId || '',
          pushCompanyName: this.data.pushCompanyName || '',
          pushClassId: this.data.pushClassId || '',
          pushClassName: this.data.pushClassName || '',
          pushFileId: this.data.pushFileId || '',
          pushFileName: this.data.pushFileName || '',
          pushVersionId: this.data.pushVersionId || '',
          pushDocId: this.data.pushDocId || '',
          pushDocName: this.data.pushDocName || '',
          pushVersionValue: this.data.pushVersionValue || '',
          receiveUserName: this.data.receiveUserName || '',
          receiveNickName: this.data.receiveNickName || ''
        }
      } else {
        this.reset()
      }
    },

    reset() {
      this.form = this.getInitialForm()
      this.subsidiaryDocClassOptions = []
      this.subsidiaryDocFileOptions = []
      this.allSubsidiaryDocFiles = []
      this.connectorOptions = []
      this.searchKeyword = ''
    },

    setDefaultCompany() {
      if (this.data || this.hasDetailData) return

      const companies = this.dict.type.downpush_company
      if (companies && companies.length === 1 && !this.form.pushCompanyId) {
        this.form.pushCompanyId = companies[0].value
        this.handleCompanyChange(this.form.pushCompanyId)
      }
    },

    handlePushFileChange(value) {
      if (!value) {
        this.clearPushData()
      }
    },

    clearPushData() {
      Object.assign(this.form, {
        pushCompanyId: '',
        pushCompanyName: '',
        pushClassId: '',
        pushClassName: '',
        pushFileId: '',
        pushFileName: '',
        pushVersionId: '',
        pushDocId: '',
        pushDocName: '',
        pushVersionValue: '',
        receiveUserName: '',
        receiveNickName: ''
      })
      this.subsidiaryDocClassOptions = []
      this.subsidiaryDocFileOptions = []
      this.allSubsidiaryDocFiles = []
      this.connectorOptions = []
      this.searchKeyword = ''
    },

    async handleCompanyChange(value) {
      this.clearCompanyRelatedData()

      if (value) {
        const selectedCompany = this.dict.type.downpush_company.find(item => item.value === value)
        this.form.pushCompanyName = selectedCompany ? selectedCompany.label : ''

        if (selectedCompany && (selectedCompany.remark || selectedCompany.raw?.remark)) {
          try {
            const remark = selectedCompany.remark || selectedCompany.raw?.remark
            const remarkData = JSON.parse(remark)
            const dbName = remarkData.dbName

            if (dbName) {
              await Promise.all([
                this.getZonciActiveDocClassList(dbName),
                this.getUsers(dbName),
                this.getActiveVersionList(dbName)
              ])

              this.subsidiaryDocFileOptions = [...this.allSubsidiaryDocFiles]
              this.filterDocFiles('')
              this.autoSelectFileByDocId()
            }
          } catch (error) {
            console.error('解析公司remark数据失败:', error)
          }
        }
      }
    },

    clearCompanyRelatedData() {
      this.subsidiaryDocClassOptions = []
      this.subsidiaryDocFileOptions = []
      this.allSubsidiaryDocFiles = []
      this.connectorOptions = []

      if (!this.data) {
        Object.assign(this.form, {
          pushClassId: '',
          pushClassName: '',
          pushFileId: '',
          pushFileName: '',
          pushVersionId: '',
          pushDocId: '',
          pushDocName: '',
          pushVersionValue: '',
          receiveUserName: '',
          receiveNickName: ''
        })
      }
    },

    handleDocClassChange(value) {
      this.clearFileSelection()

      if (value) {
        const selectedDocClass = this.subsidiaryDocClassOptions.find(item => item.id === value)
        this.form.pushClassName = selectedDocClass ? selectedDocClass.className : ''
      } else {
        this.form.pushClassName = ''
      }
    },

    clearFileSelection() {
      Object.assign(this.form, {
        pushFileId: '',
        pushFileName: '',
        pushVersionId: '',
        pushDocId: '',
        pushDocName: '',
        pushVersionValue: ''
      })
    },

    filterDocFiles(query) {
      this.searchKeyword = query || ''
    },

    autoSelectFileByDocId() {
      if (!this.showPushFile || !this.docId || !this.subsidiaryDocFileOptions.length) {
        return
      }

      const matchedFile = this.subsidiaryDocFileOptions.find(file => file.docId === this.docId)
      if (matchedFile && this.form.pushFileId !== matchedFile.id) {
        this.searchKeyword = ''
        setTimeout(() => {
          this.form.pushFileId = matchedFile.id
          this.handleDocFileChange(matchedFile.id)
        }, 200)
      }
    },
    handleDocFileChange(value) {
      if (value) {
        const selectedDocFile = this.subsidiaryDocFileOptions.find(item => item.id === value)
        if (selectedDocFile) {
          Object.assign(this.form, {
            pushFileName: selectedDocFile.docName,
            pushVersionId: selectedDocFile.id,
            pushDocId: selectedDocFile.docId,
            pushDocName: selectedDocFile.docName,
            pushVersionValue: selectedDocFile.versionValue
          })

          if (selectedDocFile.docClass) {
            const matchedClass = this.subsidiaryDocClassOptions.find(classItem => classItem.id === selectedDocFile.docClass)
            if (matchedClass) {
              this.form.pushClassId = matchedClass.id
              this.form.pushClassName = matchedClass.className
            }
          }
        }
      } else {
        this.clearFileSelection()
      }
    },

    handleReceiveUserChange(value) {
      if (value) {
        const selectedUser = this.connectorOptions.find(item => item.userName === value)
        this.form.receiveNickName = selectedUser ? selectedUser.nickName : ''
      } else {
        this.form.receiveNickName = ''
      }
    },

    async getUsers(dbName) {
      try {
        const response = await getActiveUsers(dbName)
        if (response.data) {
          this.connectorOptions = response.data
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
      }
    },

    async getActiveVersionList(dbName) {
      try {
        const response = await listActiveVersion({ dbName })
        if (response && response.data) {
          this.allSubsidiaryDocFiles = response.data.map(item => ({
            id: item.id,
            docId: item.docId,
            docName: item.docName,
            versionId: item.versionId,
            versionValue: item.versionValue,
            docClass: item.docClass
          }))

          if (this.docId && this.showPushFile) {
            this.$nextTick(() => {
              this.autoSelectFileByDocId()
            })
          }
        } else {
          this.allSubsidiaryDocFiles = []
        }
      } catch (error) {
        console.error('获取活跃下推文件列表失败:', error)
        this.allSubsidiaryDocFiles = []
      }
    },

    async getZonciActiveDocClassList(dbName) {
      try {
        const response = await getZonciActiveDocClass(dbName)
        if (response.data) {
          this.subsidiaryDocClassOptions = response.data
        }
      } catch (error) {
        console.error('获取下推文件分类失败:', error)
        this.$modal.msgError(this.$t('file_set.get_subsidiary_doc_class_failed'))
      }
    },

    async getDetail() {
      if (!this.docClassId) return

      try {
        const response = await settingDocClassId(this.docClassId)
        if (response && response.data) {
          const detailData = response.data

          if (this.data) {
            if (this.data.pushCompanyId) {
              await this.handleCompanyChange(this.data.pushCompanyId)
            }
            return
          }

          this.hasDetailData = true
          this.form.pushFile = !!(detailData.subsidiaryCompany || detailData.subsidiaryDocClassId || detailData.connectorCode)

          if (detailData.subsidiaryCompany) {
            this.form.pushCompanyId = detailData.subsidiaryCompany
            this.form.pushCompanyName = detailData.subsidiaryCompanyName || ''

            await this.handleCompanyChange(this.form.pushCompanyId)

            if (detailData.subsidiaryDocClassId) {
              this.form.pushClassId = detailData.subsidiaryDocClassId
              this.form.pushClassName = detailData.subsidiaryDocClassName || ''
              await this.handleDocClassChange(this.form.pushClassId)

              if (detailData.subsidiaryDocFileId) {
                Object.assign(this.form, {
                  pushFileId: detailData.subsidiaryDocFileId,
                  pushFileName: detailData.subsidiaryDocFileName || '',
                  pushVersionId: detailData.pushVersionId || '',
                  pushDocId: detailData.pushDocId || '',
                  pushDocName: detailData.pushDocName || '',
                  pushVersionValue: detailData.pushVersionValue || ''
                })
              }
            }

            if (detailData.connectorCode) {
              this.form.receiveUserName = detailData.connectorCode
              this.form.receiveNickName = detailData.connector || ''
            }
          }
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$modal.msgError(this.$t('file_set.get_detail_data_failed'))
      }
    },
    handleReset() {
      this.reset()
      this.$refs.form.resetFields()
    },

    async validateAndGetData() {
      try {
        if (this.isReadOnly) {
          return {
            valid: true,
            data: this.form
          }
        }

        if (!this.form.pushFile) {
          return {
            valid: true,
            data: null
          }
        }

        const valid = await new Promise((resolve) => {
          this.$refs.form.validate((isValid) => {
            resolve(isValid)
          })
        })

        if (!valid) {
          return {
            valid: false,
            message: '请完善推送配置信息'
          }
        }

        return {
          valid: true,
          data: { ...this.form }
        }

      } catch (error) {
        console.error('校验推送配置失败:', error)
        return {
          valid: false,
          message: '校验推送配置时发生错误'
        }
      }
    }
  }
}
</script>

<style scoped>
/* 使用与 add_doc.vue 一致的样式 */


</style>
