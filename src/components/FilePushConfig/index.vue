<template>
  <div class="news-card">
    <div class="card-head">
      <div class="cell-title">{{ $t('file_set.file_push') }}</div>
    </div>
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="medium"
        label-position="right"
        label-width="150px"
      >
        <el-row gutter="15">
          <el-col :span="24">
            <!-- 是否下推文件 -->
            <el-form-item :label="$t('file_set.is_push_file')" prop="pushFile">
              <!-- 只读模式 -->
              <dict-tag v-if="isReadOnly" :options="dict.type.sys_yes_no" :value="displayData.pushFile ? 'Y' : 'N'"/>
              <!-- 编辑模式 -->
              <el-radio-group v-else v-model="form.pushFile" @change="handlePushFileChange">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.value === 'Y'"
                >
                  {{ dictLanguage(dict) }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row gutter="15" v-if="displayData.pushFile">
          <el-col :span="24">
            <!-- 下推公司 -->
            <el-form-item
              :label="$t('file_set.subsidiary_company')"
              prop="pushCompanyId"
            >
              <!-- 只读模式 -->
              <span v-if="isReadOnly">{{ displayData.pushCompanyName || '-' }}</span>
              <!-- 编辑模式 -->
              <el-select
                v-else
                v-model="form.pushCompanyId"
                :placeholder="$t('file_set.please_select_subsidiary_company')"
                clearable
                style="width: 30%"
                filterable
                @change="handleCompanyChange"
              >
                <el-option
                  v-for="item in dict.type.downpush_company"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row gutter="15" v-if="displayData.pushFile">
          <el-col :span="24">
            <!-- 下推分类 -->
            <el-form-item
              :label="$t('file_set.subsidiary_doc_class_name')"
              prop="pushClassId"
            >
              <!-- 只读模式 -->
              <span v-if="isReadOnly">{{ displayData.pushClassName || '-' }}</span>
              <!-- 编辑模式 -->
              <el-select
                v-else
                v-model="form.pushClassId"
                :placeholder="!form.pushCompanyId ? $t('file_set.please_select_subsidiary_company_first') : $t('file_set.please_select_subsidiary_doc_class')"
                clearable
                filterable
                style="width: 30%"
                :disabled="!form.pushCompanyId"
                @change="handleDocClassChange"
              >
                <el-option
                  v-for="item in subsidiaryDocClassOptions"
                  :key="item.id"
                  :label="item.className"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row gutter="15" v-if="displayData.pushFile && showPushFile">
          <el-col :span="24">
            <!-- 下推文件 -->
            <el-form-item
              :label="$t('file_set.push_file')"
              prop="pushFileId"
            >
              <el-select
                v-model="form.pushFileId"
                :placeholder="!form.pushCompanyId ? $t('file_set.please_select_push_company_first') : $t('file_set.please_select_push_file')"
                clearable
                filterable
                :filter-method="filterDocFiles"
                style="width: 30%"
                :disabled="!form.pushCompanyId"
                @change="handleDocFileChange"
                ref="pushFileSelect"
              >
                <el-option
                  v-for="item in displayDocFileOptions"
                  :key="item.id"
                  :label="`${item.docName}${item.docId ? ` (${item.docId})` : ''}`"
                  :value="item.id"
                >
                  <span>{{ item.docName }}</span>
                  <span v-if="item.docId" style="color: #8492a6; font-size: 13px; margin-left: 8px;">({{ item.docId }})</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row gutter="15" v-if="displayData.pushFile">
          <el-col :span="24">
            <!-- 接收人员 -->
            <el-form-item
              :label="$t('file_set.connector')"
              prop="receiveUserName"
            >
              <!-- 只读模式 -->
              <span v-if="isReadOnly">{{ displayData.receiveNickName || '-' }}</span>
              <!-- 编辑模式 -->
              <el-select
                v-else
                v-model="form.receiveUserName"
                :placeholder="$t('file_set.please_select')"
                clearable
                style="width: 30%"
                filterable
                @change="handleReceiveUserChange"
              >
                <el-option
                  v-for="item in connectorOptions"
                  :key="item.userName"
                  :label="item.nickName"
                  :value="item.userName"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row gutter="15" v-if="showReceiptStatus">
          <el-col :span="24">
            <!-- 回执状态 -->
            <el-form-item :label="$t('file_set.receipt_status')">
              <dict-tag :options="dict.type.process_status" :value="displayData.status"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {settingDocClassId} from "@/api/file_settings/type_settings"
import {getZonciActiveDocClass} from '@/api/setting/docClass'
import {getActiveUsers} from "@/api/system/user"
import {listActiveVersion} from '@/api/document_account/version'

export default {
  name: 'FilePushConfig',
  dicts: ['downpush_company', 'sys_yes_no', 'process_status'],
  props: {
    docClassId: {
      type: String,
      required: true
    }, docId: {
      type: String,
      required: false
    },
    dataType: {
      type: String,
      default: 'stdd'
    },
    // 是否显示回执状态
    showReceiptStatus: {
      type: Boolean,
      default: false
    },
    // 回执状态值
    receiptStatus: {
      type: String,
      default: ''
    },
    // 是否显示下推文件选择
    showPushFile: {
      type: Boolean,
      default: false
    },
    // 只读数据，用于展示模式
    data: {
      type: Object,
      default: null
    },
    required: {
      type: Boolean,
      default: false
    },
    isReadOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      submitLoading: false,
      // 标志位：是否已从接口获取详情数据
      hasDetailData: false,
      form: {
        pushFile: false,
        pushCompanyId: '',
        pushCompanyName: '',
        pushClassId: '',
        pushClassName: '',
        pushFileId: '',
        pushFileName: '',
        pushVersionId: '',
        pushDocId: '',
        pushDocName: '',
        pushVersionValue: '',
        receiveUserName: '',
        receiveNickName: ''
      },
      rules: {
        pushCompanyId: [
          {required: this.required, message: this.$t('file_set.please_select_subsidiary_company'), trigger: 'change'}
        ],
        pushClassId: [
          {required: this.required, message: this.$t('file_set.please_select_subsidiary_doc_class'), trigger: 'change'}
        ],
        pushFileId: [
          {required: this.required, message: this.$t('file_set.please_select_push_file'), trigger: 'change'}
        ],
        receiveUserName: [
          {required: this.required, message: this.$t('file_set.please_select_connector'), trigger: 'change'}
        ]
      },
      // 下推文件分类选项
      subsidiaryDocClassOptions: [],
      // 下推文件选项（当前显示的）
      subsidiaryDocFileOptions: [],
      // 所有下推文件（预加载的完整列表）
      allSubsidiaryDocFiles: [],
      // 接收人选项
      connectorOptions: [],
      // 搜索关键词
      searchKeyword: ''
    }
  },
  computed: {
    // 显示的数据（优先使用data，没有data时使用form）
    displayData() {
      return this.form
    },

    // 显示的下推文件选项（根据搜索关键词过滤）
    displayDocFileOptions() {
      if (!this.searchKeyword) {
        return this.subsidiaryDocFileOptions
      }

      const searchQuery = this.searchKeyword.toLowerCase()
      return this.subsidiaryDocFileOptions.filter(file => {
        const docName = (file.docName || '').toLowerCase()
        const docId = (file.docId || '').toLowerCase()
        return docName.includes(searchQuery) || docId.includes(searchQuery)
      })
    }
  },
  created() {
    console.log('FilePushConfig created, data:', this.data)
    // 如果有传入data，优先使用data初始化form，否则使用reset
    if (this.data) {
      console.log('有data，使用data初始化')
      this.initFormWithData()
    } else {
      console.log('无data，使用reset初始化')
      this.reset()
    }

    if (this.docClassId) {
      this.getDetail()
    }
  },
  mounted() {
    console.log('FilePushConfig mounted, props:', {
      data: this.data,
      docClassId: this.docClassId,
      showPushFile: this.showPushFile
    })
    console.log('FilePushConfig mounted, form:', this.form)

    // 如果有data，再次确保form已正确初始化
    if (this.data) {
      this.initFormWithData()
    }

    // 只在编辑模式下或有特殊需求时设置默认公司
    if (this.docClassId && !this.data) {
      this.$nextTick(() => {
        this.setDefaultCompany()
      })
    }
  },
  watch: {
    // 监听 data 的变化，更新 form
    data: {
      handler(newData, oldData) {
        console.log('data监听器触发:', { newData, oldData })
        if (newData) {
          this.initFormWithData()
        }
      },
      immediate: true,
      deep: true
    },

    // 监听 docId 的变化，重新执行自动匹配
    docId: {
      handler(newDocId, oldDocId) {
        // 如果有新的 docId 且文件列表已加载，重新执行自动匹配
        if (newDocId && this.subsidiaryDocFileOptions.length > 0) {
          this.autoSelectFileByDocId()
        }
      },
      immediate: false // 不需要立即执行，因为初始化时文件列表还没加载
    },
    docClassId: {
      handler(newVal) {
        if (newVal) {
          this.getDetail()
        }
      },
      immediate: true
    },
    // 监听字典数据变化，设置默认下推公司
    'dict.type.downpush_company': {
      handler(newVal) {
        // 如果有传入data，不执行默认设置
        if (this.data) {
          console.log('有data，跳过默认下推公司设置')
          return
        }
        if (newVal && newVal.length === 1 && !this.form.subsidiaryCompany) {
          this.$nextTick(() => {
            this.setDefaultCompany()
          })
        }
      },
      immediate: true
    },
    // 监听下推公司变化，确保联动逻辑
    'form.subsidiaryCompany': {
      handler(newVal, oldVal) {
        if (!newVal && oldVal) {
          // 如果下推公司被清空，同时清空下推文件分类
          this.form.subsidiaryDocClassId = ''
          this.subsidiaryDocClassOptions = []
        }
      }
    },
    // 监听下推文件分类值变化
    'form.subsidiaryDocClassId': {
      handler(newVal) {
        console.log('下推文件分类值变化:', newVal)
      }
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        pushFile: false,
        pushCompanyId: '',
        pushCompanyName: '',
        pushClassId: '',
        pushClassName: '',
        pushFileId: '',
        pushFileName: '',
        pushVersionId: '',
        pushDocId: '',
        pushDocName: '',
        pushVersionValue: '',
        receiveUserName: '',
        receiveNickName: ''
      }
      // 清空所有选项数据
      this.subsidiaryDocClassOptions = []
      this.subsidiaryDocFileOptions = []
      this.allSubsidiaryDocFiles = []
      this.connectorOptions = []
      this.searchKeyword = ''
    },

    // 使用传入的data初始化form
    initFormWithData() {
      if (this.data) {
        console.log('开始使用data初始化form:', this.data)
        // 优先使用传入的data，如果data中没有某个字段，使用默认值
        this.form = {
          pushFile: this.data.pushFile !== undefined ? this.data.pushFile : false,
          pushCompanyId: this.data.pushCompanyId || '',
          pushCompanyName: this.data.pushCompanyName || '',
          pushClassId: this.data.pushClassId || '',
          pushClassName: this.data.pushClassName || '',
          pushFileId: this.data.pushFileId || '',
          pushFileName: this.data.pushFileName || '',
          pushVersionId: this.data.pushVersionId || '',
          pushDocId: this.data.pushDocId || '',
          pushDocName: this.data.pushDocName || '',
          pushVersionValue: this.data.pushVersionValue || '',
          receiveUserName: this.data.receiveUserName || '',
          receiveNickName: this.data.receiveNickName || ''
        }
        console.log('使用data初始化form完成:', this.form)
        console.log('form详细信息:', {
          pushFile: this.form.pushFile,
          pushCompanyId: this.form.pushCompanyId,
          pushCompanyName: this.form.pushCompanyName,
          pushClassName: this.form.pushClassName,
          receiveNickName: this.form.receiveNickName
        })
      }
    },

    // 设置默认下推公司
    setDefaultCompany() {
      // 如果有传入data，不执行默认设置
      if (this.data) {
        console.log('有data，跳过默认下推公司设置')
        return
      }

      // 如果已从接口获取详情数据，则不执行默认设置（互斥逻辑）
      if (this.hasDetailData) {
        console.log('已获取详情数据，跳过默认下推公司设置')
        return
      }

      const companies = this.dict.type.downpush_company

      if (companies &&
          companies.length === 1 &&
          !this.form.subsidiaryCompany) {
        this.form.subsidiaryCompany = companies[0].value
        // 自动触发公司变化事件
        this.handleCompanyChange(this.form.subsidiaryCompany)
      }
    },

    // 是否下推文件变化处理
    handlePushFileChange(value) {
      if (!value) {
        // 如果选择不下推，清空所有相关字段
        this.form.pushCompanyId = ''
        this.form.pushCompanyName = ''
        this.form.pushClassId = ''
        this.form.pushClassName = ''
        this.form.pushFileId = ''
        this.form.pushFileName = ''
        this.form.pushVersionId = ''
        this.form.pushDocId = ''
        this.form.pushDocName = ''
        this.form.pushVersionValue = ''
        this.form.receiveUserName = ''
        this.form.receiveNickName = ''
        this.subsidiaryDocClassOptions = []
        this.subsidiaryDocFileOptions = []
        this.allSubsidiaryDocFiles = []
        this.connectorOptions = []
        this.searchKeyword = ''
      }
    },

    // 下推公司变化处理
    async handleCompanyChange(value) {

      // 清空下拉选项数据
      this.subsidiaryDocClassOptions = []
      this.subsidiaryDocFileOptions = []
      this.allSubsidiaryDocFiles = []
      this.connectorOptions = []

      // 如果有传入data，不清空form字段，保持data的值
      if (!this.data) {
        this.form.pushClassId = ''
        this.form.pushClassName = ''
        this.form.pushFileId = ''
        this.form.pushFileName = ''
        this.form.pushVersionId = ''
        this.form.pushDocId = ''
        this.form.pushDocName = ''
        this.form.pushVersionValue = ''
        this.form.receiveUserName = ''
        this.form.receiveNickName = ''
      }

      if (value) {
        // 设置下推公司名称
        const selectedCompany = this.dict.type.downpush_company.find(item => item.value === value)
        this.form.pushCompanyName = selectedCompany ? selectedCompany.label : ''

        if (selectedCompany && (selectedCompany.remark || selectedCompany.raw?.remark)) {
          try {
            // 解析remark字段中的JSON数据获取dbName
            const remark = selectedCompany.remark || selectedCompany.raw?.remark
            const remarkData = JSON.parse(remark)
            const dbName = remarkData.dbName

            if (dbName) {
              // 并行调用接口获取下推文件分类列表、接收人和下推文件列表
              await Promise.all([
                this.getZonciActiveDocClassList(dbName),
                this.getUsers(dbName),
                this.getActiveVersionList(dbName)
              ])

              // 获取数据后，立即显示所有下推文件
              this.subsidiaryDocFileOptions = [...this.allSubsidiaryDocFiles]
              // 初始化过滤选项，调用过滤方法确保正确初始化
              this.filterDocFiles('')

              // 自动匹配并选中文件
              this.autoSelectFileByDocId()
            }
          } catch (error) {
            console.error('解析公司remark数据失败:', error)
          }
        }
      }
    },

    // 下推文件分类变化处理
    async handleDocClassChange(value) {
      // 清空下推文件选择
      this.form.pushFileId = ''
      this.form.pushFileName = ''
      this.form.pushVersionId = ''
      this.form.pushDocId = ''
      this.form.pushDocName = ''
      this.form.pushVersionValue = ''

      if (value) {
        // 设置下推分类名称
        const selectedDocClass = this.subsidiaryDocClassOptions.find(item => item.id === value)
        this.form.pushClassName = selectedDocClass ? selectedDocClass.className : ''
      } else {
        this.form.pushClassName = ''
      }

      // 下推文件不需要根据分类过滤，始终显示所有文件
      // 文件列表已在选择公司时加载完成
    },

    // 下推文件自定义过滤方法
    filterDocFiles(query) {
      // 更新搜索关键词，计算属性会自动重新计算
      this.searchKeyword = query || ''
    },

    // 根据当前文档ID自动选中匹配的下推文件
    autoSelectFileByDocId() {
      // 只有在显示下推文件选择且有docId的情况下才进行自动匹配
      if (!this.showPushFile || !this.docId) {
        return
      }

      // 如果文件列表为空，跳过匹配
      if (!this.subsidiaryDocFileOptions || this.subsidiaryDocFileOptions.length === 0) {
        return
      }

      // 在下推文件列表中查找匹配的文件
      const matchedFile = this.subsidiaryDocFileOptions.find(file => file.docId === this.docId)
      if (matchedFile) {
        // 如果已经选中了相同的文件，跳过重复设置
        if (this.form.pushFileId === matchedFile.id) {
          console.log('文件已经选中，跳过重复设置:', matchedFile.id)
          return
        }
        // 清空搜索关键词，确保显示所有选项
        this.searchKeyword = ''
        // 延迟执行，确保组件完全渲染
        setTimeout(() => {
          // 找到匹配的文件，自动选中
          this.form.pushFileId = matchedFile.id

          // 触发文件变化处理，设置相关参数
          this.handleDocFileChange(matchedFile.id)

        }, 200)
      } else {
        console.log(`未找到匹配的下推文件，docId: ${this.docId}, 可用文件:`, this.subsidiaryDocFileOptions.map(f => f.docId))
      }
    },



    // 下推文件变化处理
    handleDocFileChange(value) {
      if (value) {
        // 根据选择的文件ID查找对应的文件信息（从原始数据中查找）
        const selectedDocFile = this.subsidiaryDocFileOptions.find(item => item.id === value)
        if (selectedDocFile) {
          // 组装推送文件相关参数
          this.form.pushFileName = selectedDocFile.docName
          this.form.pushVersionId = selectedDocFile.id  // 对应 id 字段
          this.form.pushDocId = selectedDocFile.docId   // 对应 doc_id 字段
          this.form.pushDocName = selectedDocFile.docName  // 对应 doc_name 字段
          this.form.pushVersionValue = selectedDocFile.versionValue  // 对应 version_value 字段

          // 根据文件的 docClass 自动设置下推文件分类（联动）
          if (selectedDocFile.docClass) {
            // 在分类选项中查找匹配的分类
            const matchedClass = this.subsidiaryDocClassOptions.find(classItem => classItem.id === selectedDocFile.docClass)
            if (matchedClass) {
              this.form.pushClassId = matchedClass.id
              this.form.pushClassName = matchedClass.className
            }
          }
        }
      } else {
        // 清空推送文件相关参数
        this.form.pushFileName = ''
        this.form.pushVersionId = ''
        this.form.pushDocId = ''
        this.form.pushDocName = ''
        this.form.pushVersionValue = ''
        // 注意：这里不清空分类，因为用户可能先选择了分类再选择文件，或者需要保持分类选择
      }
    },

    // 接收人变化处理
    handleReceiveUserChange(value) {
      if (value) {
        // 设置接收人昵称
        const selectedUser = this.connectorOptions.find(item => item.userName === value)
        this.form.receiveNickName = selectedUser ? selectedUser.nickName : ''
      } else {
        this.form.receiveNickName = ''
      }
    },

    async getUsers(dbName) {
      const response = await getActiveUsers(dbName)
      if (response.data) {
        this.connectorOptions = response.data
      }
    },

    // 获取下推文件列表（活跃版本）
    async getActiveVersionList(dbName) {
      try {
        const response = await listActiveVersion({ dbName })
        if (response && response.data) {
          // 存储所有文件到 allSubsidiaryDocFiles，用于后续过滤
          this.allSubsidiaryDocFiles = response.data.map(item => ({
            id: item.id,
            docId: item.docId,
            docName: item.docName,
            versionId: item.versionId,
            versionValue: item.versionValue,
            docClass: item.docClass
          }))
          // 如果文件列表加载完成后有 docId，尝试自动匹配
          if (this.docId && this.showPushFile) {
            // 延迟执行，确保 subsidiaryDocFileOptions 已更新
            this.$nextTick(() => {
              this.autoSelectFileByDocId()
            })
          }
        } else {
          this.allSubsidiaryDocFiles = []
        }
      } catch (error) {
        console.error('获取活跃下推文件列表失败:', error)
        // 不显示错误提示，因为这是预加载，用户可能还没选择分类
        this.allSubsidiaryDocFiles = []
      }
    },

    // 获取下推文件分类列表
    async getZonciActiveDocClassList(dbName) {
      try {
        const response = await getZonciActiveDocClass(dbName)

        if (response.data) {
          this.subsidiaryDocClassOptions = response.data
        }
      } catch (error) {
        console.error('获取下推文件分类失败:', error)
        this.$modal.msgError(this.$t('file_set.get_subsidiary_doc_class_failed'))
      }
    },


    // 获取详情（编辑时使用）
    async getDetail() {
      if (!this.docClassId) {
        console.warn('获取详情失败：缺少id参数')
        return
      }

      try {
        const response = await settingDocClassId(this.docClassId)

        if (response && response.data) {
          const detailData = response.data

          // 如果有传入data，只加载选项数据，不填充表单
          if (this.data) {
            console.log('有data，只加载选项数据，不填充表单')
            // 仍然需要执行handleCompanyChange来加载下拉选项
            if (this.data.pushCompanyId) {
              await this.handleCompanyChange(this.data.pushCompanyId)
            }
            return
          }

          // 设置标志位，表示已从接口获取详情数据
          this.hasDetailData = true

          // 填充表单数据（只在没有data时执行）


          // 判断是否启用推送（如果有任何推送相关数据则认为启用了推送）
          this.form.pushFile = !!(detailData.subsidiaryCompany || detailData.subsidiaryDocClassId || detailData.connectorCode)

          // 先设置下推公司
          if (detailData.subsidiaryCompany) {
            this.form.pushCompanyId = detailData.subsidiaryCompany
            this.form.pushCompanyName = detailData.subsidiaryCompanyName || ''

            // 先加载下推文件分类选项，再设置值
            await this.handleCompanyChange(this.form.pushCompanyId)

            // 在选项加载完成后再设置下推文件分类的值
            if (detailData.subsidiaryDocClassId) {
              this.form.pushClassId = detailData.subsidiaryDocClassId
              this.form.pushClassName = detailData.subsidiaryDocClassName || ''

              // 触发分类变化处理，设置分类名称和加载文件列表
              await this.handleDocClassChange(this.form.pushClassId)

              // 在文件选项加载完成后再设置下推文件的值
              if (detailData.subsidiaryDocFileId) {
                this.form.pushFileId = detailData.subsidiaryDocFileId
                this.form.pushFileName = detailData.subsidiaryDocFileName || ''
                this.form.pushVersionId = detailData.pushVersionId || ''
                this.form.pushDocId = detailData.pushDocId || ''
                this.form.pushDocName = detailData.pushDocName || ''
                this.form.pushVersionValue = detailData.pushVersionValue || ''
              }
            }

            // 在接收人选项加载完成后再设置接收人的值
            if (detailData.connectorCode) {
              this.form.receiveUserName = detailData.connectorCode
              this.form.receiveNickName = detailData.connector || ''
            }
          }
        } else {
          console.warn('接口返回数据为空')
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$modal.msgError(this.$t('file_set.get_detail_data_failed'))
      }
    },
    // 重置表单
    handleReset() {
      this.reset()
      this.$refs.form.resetFields()
    },



    // 供父组件调用的方法：校验并获取推送配置数据
    async validateAndGetData() {
      try {
        // 如果是只读模式，直接返回当前的form数据（已包含data）
        if (this.isReadOnly) {
          return {
            valid: true,
            data: this.form
          }
        }

        // 如果未启用推送，直接返回空配置
        if (!this.form.pushFile) {
          return {
            valid: true,
            data:null
          }
        }

        // 启用推送时进行表单校验
        const valid = await new Promise((resolve) => {
          this.$refs.form.validate((isValid) => {
            resolve(isValid)
          })
        })

        if (!valid) {
          return {
            valid: false,
            message: '请完善推送配置信息'
          }
        }

        // 校验通过，准备返回数据
        const formData = { ...this.form }


        return {
          valid: true,
          data: formData
        }

      } catch (error) {
        console.error('校验推送配置失败:', error)
        return {
          valid: false,
          message: '校验推送配置时发生错误'
        }
      }
    }
  }
}
</script>

<style scoped>
/* 使用与 add_doc.vue 一致的样式 */


</style>
