<template>
  <el-select
    v-model="ext20"
    :disabled="disabled"
    multiple
    filterable
    allow-create
    default-first-option
    style="width:100%"
    :placeholder="placeholder"
    @change="changed"
  >
  </el-select>
</template>

<script>

export default {
  name: 'RelationPlanNo',
  props: {
    value:{
      type:String,
      required:true
    },
    placeholder:{
      type:String
    },
    disabled:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {}
  },
  created() {
  },
  computed: {
    ext20: {
      get() {
        if (this.value) {
          return this.value.split(',')
        } else {
          return []
        }
      },
      set(v) {
        let _value = v.join(',')
        this.$emit('update:value',_value)
      }
    }
  },

  methods: {
    changed(v) {
      this.$emit('changed', v)
    }
  }
}
</script>
