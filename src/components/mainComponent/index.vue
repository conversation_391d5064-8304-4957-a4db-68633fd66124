<template>
  <component :is="dashboard" ref="component" :data="data" :dataType="dataType"  @close="close">
    <slot></slot>
  </component>
</template>
<script>
export default {
  name: 'MainComponent',
  props: ['code','data','dataType'],
  data () {
    return {
      dashboard: null,
    }
  },
  watch: {
    code (val) {
        this.init(val)
    },
  },
  mounted () {
      this.init(this.code)
  },
  methods: {
    init (val) {
        try {
          if (val) {
            this.dashboard = resolve => require(['@/' + val + '.vue'], resolve)
          }
        }catch (e) {}
    },
    close (...value) {
      this.$emit('close', ...value)
    },
  }
}
</script>
