<!--
 * @description 全局备案信息组件
 * <AUTHOR>
 * @date 2024-01-01
 -->
<template>
  <div class="icp-info" :class="{ 'login-page-icp': isLoginPage }">
    <img src="../../assets/images/sys_logo.png" style="height:20px;">
    <span>长沙睿展数据科技有限公司 湘ICP备20002376号-2</span>
  </div>
</template>

<script>
export default {
  name: 'IcpInfo',
  data() {
    return {}
  },
  computed: {
    isLoginPage() {
      return this.$route.path === '/login'
    }
  }
}
</script>

<style lang="scss" scoped>
.icp-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #666;
  padding: 15px 0;
  background-color: #ffffff;
  z-index: 1000;

  span {
    display: inline-block;
    line-height: 1.4;
  }
}

/* 登录页面特殊样式 */
.icp-info.login-page-icp {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  background-color: rgba(245, 245, 245, 0.95) !important;
  backdrop-filter: blur(5px);
}

.icp-info img {
  vertical-align: middle;
  margin-bottom: -2px;
  margin-right: 10px;
}
</style>
