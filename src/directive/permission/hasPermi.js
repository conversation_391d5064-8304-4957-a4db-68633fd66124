 /**
 * v-hasPermi 操作权限处理
 * Copyright (c) 2019 ruoyi
 */

import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    const all_permission = "*:*:*";
    const permissions = store.getters && store.getters.permissions

    let config = JSON.parse(sessionStorage.getItem("SYS_CONFIG"));
    let sysConfig = config.filter((x) => x.configKey === "sys:config")[0]

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      let hasPermissions = permissions.some(permission => {
        return all_permission === permission || permissionFlag.includes(permission)
      })
      if(sysConfig.configValue.split(',').includes(value[0])){
        hasPermissions = false
      }
      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}
