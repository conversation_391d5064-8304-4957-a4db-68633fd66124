<template>
  <div class="app-container companyindex el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane
          v-for="(item, i) in linkTypeTab"
          :key="i"
          :class="{ switch: varChangeColor == item.id }"
          :name="i"
          @click="state(item.id)"
        >
          <span slot="label">
            {{ item.className }}
          </span>
        </el-tab-pane>
      </el-tabs>
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div
          class="global-ser"
          id="add"
          :class="!boxClass ? '' : 'open'"
        >
          <div class="ser-top">
            <div class="cell-left">
              <!-- <treeselect
                  v-model.trim="queryParams.deptId"
                  :options="deptOptions"
                  :normalizer="normalizer"
                  placeholder="选择部门"
                  :searchable="false"
                  size="small"
                  style="float: left; height: 34px; margin-right: 5px;width: 300px;"
                /> -->
              <el-date-picker
                v-model="value2"
                type="datetimerange"
                :picker-options="pickerOptions"
                :range-separator="$t(`doc.this_dept_to`)"
                :start-placeholder="$t(`doc.this_dept_start_date`)"
                :end-placeholder="$t(`doc.this_dept_end_date`)"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                align="right"
                size="small"
                style="float: left; height: 34px; margin-right: 5px"
              >
              </el-date-picker>
              <el-input
                v-model.trim="queryParams.searchValue"
                :placeholder="$t(`doc.this_dept_insert_keyword`)"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >{{$t(`doc.this_dept_reset`)}}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button
                @click="handleExport"
                v-hasPermi="['process:external:export']"
                type="primary"
                >{{ $t(`doc.this_dept_down_temp`) }}</el-button>
              <el-button
                v-hasPermi="['process:external:import']"
                @click="handlestandardimport"
                plain
                >{{ $t(`doc.this_dept_import`) }}</el-button>
              <el-button
                v-hasPermi="['process:external:export']"
                @click="handlestandardexport"
                plain
              >{{ $t(`doc.exterior_dept_export`) }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @sort-change="sortChange"
          ref="multipleTable"
          header-align="left"
        >
          <el-table-column
            :label="$t(`doc.this_dept_file_name`)"
            align="left"
            sortable="custom"
            width="300"
            prop="docName"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_interior_code`)"
            align="left"
            sortable="internalNumber"
            width="300"
            prop="internalNumber"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_file_code`)"
            sortable="custom"
            align="left"
            prop="docCode"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
<!--          <el-table-column-->
<!--            label="文件版本号"-->
<!--            align="left"-->
<!--            prop="version"-->
<!--            :show-overflow-tooltip="true"-->
<!--          />-->

          <el-table-column
            :label="$t(`doc.this_dept_release_unit`)"
            align="left"
            prop="deptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_release_date`)"
            sortable="custom"
            align="left"
            prop="startDate"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.startTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
<!--          <el-table-column-->
<!--            label="作废日期"-->
<!--            align="left"-->
<!--            sortable="custom"-->
<!--            prop="releaseTime"-->
<!--            :show-overflow-tooltip="true"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              <el-date-picker-->
<!--                style="width: 150px;"-->
<!--                v-model="scope.row.endTime"-->
<!--                type="date"-->
<!--                format="yyyy-MM-dd"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                @change="changeEndDate(scope.row)"-->
<!--                placeholder="选择日期">-->
<!--              </el-date-picker>-->
<!--              &lt;!&ndash; <span>{{ parseTime(scope.row.endTime, "{y}-{m}-{d}") }}</span> &ndash;&gt;-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column
            :label="$t(`doc.this_dept_attach`)"
            align="left"
            sortable="custom"
            prop="fileId"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div class="upload-file rzfujian" v-if="!scope.row.fileId">
                <el-upload
                  v-if="scope.row.createUserId===userInfo.userId || iswk"
                  class="upload-file-uploader"
                  ref="fileIds"
                  :data="scope.row"
                  :http-request="appendixesUpload">
                  <div style="display: flex">
                    <el-link size="mini" :underline="false" type="primary">{{ $t(`doc.this_dept_upload`) }}</el-link>
                  </div>
                </el-upload>
              </div>
              <el-button
                size="mini" v-if="scope.row.fileId || iswk"
                type="slot" @click="handelefileLocalDownload(scope.row.fileId,scope.row.fileVo?scope.row.fileVo.fileName:'')"
                style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                <span >{{scope.row.fileVo?scope.row.fileVo.fileName:''}}</span>
              </el-button>
              <i v-if="scope.row.fileId && (scope.row.createUserId===userInfo.userId || iswk)" class="el-icon-circle-close" @click="deleteFile(scope.row)" style="color: red; margin-left: 10px;"></i>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            width="95"
            fixed="right"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.createUserId===userInfo.userId || iswk"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >
                <span class="red">{{ $t(`doc.this_dept_delete`) }}</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!--批量修改上传-->
      <input
        v-show="false"
        ref="filetandardimport"
        type="file"
        @change="handlestandardimportfileChange"
      />
    </div>
  </div>
</template>

<script>
import {
  listVersion,delVersion,standardimport,updateVersion,deleteFile
} from "@/api/document_account/external";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import { processFileLocalUpload,fileLocalDownload } from "@/api/commmon/file";
import mainComponent from "@/components/mainComponent/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from "@/api/system/dept";
import mixin from "@/layout/mixin/Commmon.js";
// 引入文件新增页面（同时适应文件修订、文件作废发起页面）
import { selectStatusByDocId } from "@/api/my_business/workflowApplyLog";
import initHistory from "@/views/system/dispose/initHistory";
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    Treeselect,
    mainComponent,
    initHistory
  },
  props: ["pButton", "menuitem"],
  mixins: [mixin],
  data() {
    return {
      deptOptions: {},
      activeName: undefined,
      data: undefined,
      dateTime: new Date().getTime(),
      boxClass: false,
      // 遮罩层
      loading: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: "",
        docId: null,
        docClass: null,
        docNames: null,
        deptId: null,
        params: {
          startTime: "",
          endTime: "",
        },
        dataType: undefined,
        // 有效版本文件 1有效、2失效
        status: '1'
      },
      // 表单参数
      form: {},
      varChangeColor: "",
      companySelection: [],
      linkTypeTab: [],
      selectInfoData: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      value2: "",
      iswk:false
    };
  },
  created() {
    if(this.$route.query.content){
      this.queryParams.searchValue = this.$route.query.content
    }

    this.initWk();
    listDept({ status: 0 }).then((response) => {
      this.deptOptions = this.handleTree(response.data, "deptId");
    });
  },
  watch: {
    value2(val) {
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
  },
  mounted() {
    let self = this
    // 加载体系文件的-文件类型页签
    settingDocClassList({ classStatus: "1",dataType:"stdd",parentClassId:"DEO",openPurview:true }).then(
      (response) => {
        this.handleTree(response.rows.filter(item=>item.purview), "id", "parentClassId").forEach(
          (element) => {
            this.linkTypeTab.push({
              className: element.className,
              id: element.id,
            });
          }
        );
        if (this.linkTypeTab.length>0) {
          this.queryParams.docClass = this.linkTypeTab[0].id;
          this.varChangeColor = this.linkTypeTab[0].id;
          //全文检索跳转时用到
          if (this.$route.query.type) {
            for (let i = 0; i < self.linkTypeTab.length; i++) {
              let item = self.linkTypeTab[i];
              if (item.id == self.$route.query.type) {
                this.activeName = i
                this.varChangeColor = item.id;
                this.queryParams.docClass = item.id;
                break;
              }
            }
          }
        }
        this.getList();
      }
    );
  },
  methods: {
    initWk(){
      let roles = this.userInfo.roles
      roles.forEach(role => {
        console.info(role.roleKey)
        if(role.roleKey === '12'){
          this.iswk=true
        }
      });
      console.info(this.iswk)
    },
     /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/process/external/export",
        {},
        `post_${new Date().getTime()}.xlsx`
      );
    },
    deleteFile(row){
      deleteFile(row).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      });
    },
    appendixesUpload(params) {
      let row=params.data
      let fd = new FormData();
      fd.append("file", params.file); //传文件
      processFileLocalUpload(fd).then((res) => {
        row.fileId=res.data.fileId
        this.changeEndDate(row);
      });
    },
    changeEndDate(val){
      updateVersion(val).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      });
    },
    handlestandardimportfileChange(e) {
      let file = e.target.files[0];
      this.formimportfile = new FormData();
      this.formimportfile.append("file", file);
      this.formimportfile.append("docClass", this.varChangeColor);
      this.formimportfile.append("tenantId", cache.session.get('tenantId'));
      if (file != undefined) {
        standardimport(this.formimportfile).then((res) => {
          if(res.data.initFileMsg.length>0||res.data.initRecordMsg.length>0||res.data.linkFileMsg.length>0 ){
            this.$message({
              showClose: true,
              dangerouslyUseHTMLString: true,
              duration: 0,
              message:
                res.data.initFileMsg +
                "<br/>" +
                res.data.initRecordMsg +
                "<br/>" +
                res.data.linkFileMsg,
              type: "warning",
            });
          }else{
            this.$message.success("操作成功");
          }
          this.getList();
          this.$refs.filetandardimport.value=null;
        });
      }
    },
    handlestandardimport() {
      this.$refs.filetandardimport.dispatchEvent(new MouseEvent("click"));
    },
    //导出
    handlestandardexport(){
      this.download(
        "/process/external/export/data",
        {
          ...this.queryParams,
        },
        this.linkTypeTab[this.activeName].className + `_${new Date().getTime()}.xlsx`
      );
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm(this.$t(`doc.this_dept_is_remove_code`) + row.docCode + this.$t(`file_set.signature_text1`))
        .then(function () {
          return delVersion(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t(`doc.this_dept_remove_success`));
        })
        .catch(() => {});
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    /** 查询列表 */
    getList() {
      if (this.queryParams.docClass) {
        this.loading = true;
        listVersion(this.queryParams).then((response) => {
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.searchValue = "";
      this.queryParams.docClass = this.varChangeColor;
      this.value2 = null;
      this.queryParams.deptId = null;
      this.handleQuery();
    },
    sortChange({ column, prop, order }){
        if (order==='ascending') {
            this.queryParams.orderByColumn = prop
            this.queryParams.isAsc = 'asc'
        }else if(order==='descending') {
            this.queryParams.orderByColumn = prop
            this.queryParams.isAsc = 'desc'
        }else {
            this.queryParams.orderByColumn = undefined
            this.queryParams.isAsc = undefined
        }
        this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.companySelection = selection;
      this.selectInfoData = selection;
    },
    state(tab) {
      let t = tab.index;
      let index = this.linkTypeTab[t].id;
      this.varChangeColor = index;
      this.queryParams.docClass = index;
      this.getList();
    },
  },
};
</script>
<style lang="scss">
@import "../../../../public/css/poctstyle.css";

body .el-upload, body .registerbox .avatar-uploader .el-upload{
  background: #0000 !important;
}
</style>
