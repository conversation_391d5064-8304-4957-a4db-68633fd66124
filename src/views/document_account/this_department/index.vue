<template>
  <div class="app-container companyindex el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane
          v-for="(item, i) in linkTypeTab"
          :key="i"
          :class="{ switch: varChangeColor == item.id }"
          :name="i"
          @click="state(item.id)"
        >
          <span slot="label">
            {{ item.className }}
          </span>
        </el-tab-pane>
      </el-tabs>
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :label-width=" columnLangSizeFlag? '88px' : '68px'"
      >
        <div
          class="global-ser"
          id="add"
          :class="!boxClass ? '' : 'open'"
        >
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.docName"
                :placeholder="$t(`doc.this_dept_name_select`)"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
              <el-button @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              <el-button @click="boxClass = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>更多
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>更多
              </el-button>
<!--              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{$t(`doc.this_dept_reset`)}}
              </el-button>-->
            </div>
            <div class="cell-right">
                <el-button
                  type="primary"
                  @click="handleChange()"
                  v-hasPermi="['process:standard:dept:change']"
                >{{$t(`doc.this_dept_change_apply`)}}</el-button>
                <el-button
                  v-hasPermi="['process:distribute:dept:range']"
                  @click="handleDistributeRange()"
                >{{$t(`doc.this_dept_promotion_scope`)}}</el-button>
                <el-button
                  v-hasPermi="['process:standard:dept:add']"
                  @click="handleAdd()"
                >{{ $t(`doc.this_dept_new_add`) }}</el-button>
                <el-dropdown trigger="click" v-if="(checkPermi(['doc:class:'+permission])&&
                checkPermi(['process:standard:dept:updata','process:standard:dept:disuse']))||
                checkPermi(['process:standard:dept:review','process:distribute:dept:print','process:standard:dept:export',
                'process:standard:dept:import','process:standard:dept:extra','process:standard:dept:reissue',
                'process:standard:dept:lost','process:standard:dept:borrow','process:standard:dept:retain',
                'process:standard:dept:batch:add','process:standard:dept:batch:update','process:standard:dept:batch:disuse'])">
                <span class="el-dropdown-link">{{ $t(`doc.this_dept_more`) }}</span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:batch:add']"
                        @click="handleAddBatch()"
                      >
                        {{$t(`doc.batch_title_text_4`)}}{{$t(`doc.this_dept_new_add`)}}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:batch:update']"
                        @click="handleUpdateBatch()"
                      >
                        {{$t(`doc.batch_title_text_4`)}}{{ $t(`doc.this_dept_revision`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:batch:disuse']"
                        @click="handleDisuseBatch()"
                      >
                        {{$t(`doc.batch_title_text_4`)}}{{ $t(`doc.this_dept_cancel`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-if="checkPermi(['doc:class:'+permission])"
                        v-hasPermi="['process:standard:dept:updata']"
                        @click="handleUpdata()"
                      >
                        {{ $t(`doc.this_dept_revision`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-if="checkPermi(['doc:class:'+permission])"
                        v-hasPermi="['process:standard:dept:disuse']"
                        @click="handleDisuse()"
                      >
                        {{ $t(`doc.this_dept_cancel`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:review']"
                        @click="handleReview()"
                      >

                        {{ $t(`doc.this_dept_review`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:ls:review']"
                        @click="handleReviewLs()"
                      >

                        {{ $t(`doc.this_temp_file_dept_review`)}}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:extra']"
                        @click="handleExtra()"
                      >
                        {{ $t(`doc.this_dept_additional_issuance`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:reissue']"
                        @click="handleReissue()"
                      >
                        {{ $t(`doc.this_dept_supply`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:lost']"
                        @click="handleLost()"
                      >
                        {{ $t(`doc.this_dept_loss`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:borrow']"
                        @click="handleBorrow()"
                      >
                        {{ $t(`doc.this_dept_borrow`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:retain']"
                        @click="handleRetain()"
                      >
                        {{ $t(`doc.this_dept_retain`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:distribute:dept:print']"
                        @click="handleDistribute()"
                      >{{ $t(`doc.invalid_external_distribution_scope`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:import']"
                        @click="importShow=true"
                      >
                        {{ $t(`doc.this_dept_import`) }}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item class="drop-button">
                      <div
                        v-hasPermi="['process:standard:dept:export']"
                        @click="handleExport"
                      >
                        {{ $t(`doc.exterior_dept_export`) }}
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-date-picker
                v-model="value2"
                type="datetimerange"
                :picker-options="pickerOptions"
                :range-separator="$t(`doc.this_dept_to`)"
                :start-placeholder="$t(`doc.this_dept_start_date`)"
                :end-placeholder="$t(`doc.this_dept_end_date`)"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                align="right"
                size="small"
                style="float: left; height: 34px; margin-right: 5px">
              </el-date-picker>
              <el-form-item :label="$t(`doc.this_dept_file_code`)" prop="docId">
                <el-input
                  :placeholder="$t(`doc.this_dept_file_code`)"
                  v-model.trim="queryParams.docId"
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_staffing_dept`)" :label-width="columnLangSizeFlag ? '178px' : '88px'" prop="deptId">
                <treeselect
                  v-model.trim="queryParams.deptId"
                  :options="deptOptions"
                  :normalizer="normalizer"
                  :placeholder="$t(`doc.this_dept_select_dept`)"
                  style="width: 200px"
                  :searchable="true"
                  size="mini"
                />
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_file_type`)" prop="docClass">
                <treeselect
                  v-model.trim="queryParams.docClass"
                  :options="docClassTree.filter(item=>item.id===varChangeColor)"
                  @select="handleSelectNode"
                  :normalizer="normalizerFile"
                  :defaultExpandLevel="1"
                  :placeholder="$t(`doc.this_dept_select_type`)"
                  style="width: 200px"
                  :searchable="true"
                  :clearable="false"
                  size="mini"
                  class="pop-width400"
                />
              </el-form-item>

              <!-----------------------增加扩展字段查询条件-------------------------->
              <!--  订单号          -->
              <template v-if="settingDetail['ext2']">
                <el-form-item :label="$t('dicts.form_control_ext2')" prop="ext2">
                  <el-input
                    :placeholder="$t('dicts.form_control_ext2')"
                    v-model.trim="queryParams.ext2"
                  ></el-input>
                </el-form-item>
              </template>
              <!--  计划单号          -->
              <template v-if="settingDetail['ext4']">
                <el-form-item :label="$t('dicts.form_control_ext4')" prop="ext4">
                  <el-input
                    :placeholder="$t('dicts.form_control_ext4')"
                    v-model.trim="queryParams.ext4"
                  ></el-input>
                </el-form-item>
              </template>
              <!--  合同编号          -->
              <template v-if="settingDetail['ext8']">
                <el-form-item :label="$t('dicts.form_control_ext8')" prop="ext8">
                  <el-input
                    :placeholder="$t('dicts.form_control_ext8')"
                    v-model.trim="queryParams.ext8"
                  ></el-input>
                </el-form-item>
              </template>
              <!--  外部编号          -->
              <template v-if="settingDetail['ext12']">
                <el-form-item :label="$t('dicts.form_control_ext12')" prop="ext12">
                  <el-input
                    :placeholder="$t('dicts.form_control_ext12')"
                    v-model.trim="queryParams.ext12"
                  ></el-input>
                </el-form-item>
              </template>
              <!-----------------------增加扩展字段查询条件-------------------------->
              <template>
                <el-form-item :label="$t(`doc.this_dept_file_status`)" prop="status">
                  <el-select value-key="id" clearable v-model="queryParams.status"
                             style="width:200px"
                  >
                    <el-option
                      v-for="item in dict.type.standard_status"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template>
                <el-form-item :label="$t(`doc.this_dept_staffs`)" prop="nickName">
                  <el-input
                    :placeholder="$t(`doc.this_input_dept_staffs`)"
                    v-model.trim="queryParams.nickName"
                  ></el-input>
                </el-form-item>
              </template>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          border
          v-loading="loading"
          :data="postList"
          @sort-change="sortChange"
          @selection-change="handleSelectionChange"
          :row-class-name="tableRowClassName"
          ref="multipleTable"
          header-align="left"
        >

          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <template>
            <el-table-column
              :label="$t(`doc.this_dept_file_code`)"
              sortable="custom"
              align="left"
              width="120"
              prop="docId"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.docId }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_file_name`)"
              align="left"
              sortable="custom"
              prop="docName"
              width="100"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span class="wenjcolor"><a @click="handlePreview(scope.row, 'COMPANY')">{{scope.row.docName}}</a ></span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_file_version`)"
              align="left"
              prop="versionValue"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_dept_release_date1`)"
              align="left"
              sortable="custom"
              prop="releaseTime"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_effective_date`)"
              align="left"
              sortable="custom"
              prop="startDate"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>

<!--            <el-table-column-->
<!--              :label="$t(`doc.this_dept_revise_date`)"-->
<!--              align="left"-->
<!--              sortable="custom"-->
<!--              prop="revisionDate"-->
<!--              :show-overflow-tooltip="true"-->
<!--            >-->
<!--              <template slot-scope="scope">-->
<!--                <span>{{ parseTime(scope.row.revisionDate, "{y}-{m}-{d}") }}</span>-->
<!--              </template>-->
<!--            </el-table-column>-->

            <el-table-column
              :label="$t(`doc.this_dept_change_content`)"
              align="left"
              prop="content"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_document_review_date`)"
              align="left"
              sortable="custom"
              prop="reviewTime"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.reviewTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_file_status`)"
              align="left"
              prop="status"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <dict-tag :options="dict.type.standard_status" :value="scope.row.status"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_staffing_dept`)"
              align="left"
              prop="deptName"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.deptName }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_staffing`)"
              align="left"
              prop="nickName"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.nickName }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            :label="$t(`doc.this_dept_advise_count`)"
            align="left"
            prop="adviseCount"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
                <span>
                  <el-tag
                    key="adviseCount"
                    :type="scope.row.adviseCount>0?'warning':'info'"
                    effect="dark">
                      {{ scope.row.adviseCount }}
                  </el-tag>
                </span>
            </template>
          </el-table-column>

          <!-----------------------增加扩展字段-------------------------->
          <!--  订单号          -->
          <template v-if="settingDetail['ext2']">
            <el-table-column
              :label="$t('dicts.form_control_ext2')"
              align="left"
              prop="ext2"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </template>
          <!--  计划单号          -->
          <template v-if="settingDetail['ext4']">
            <el-table-column
              :label="$t('dicts.form_control_ext4')"
              align="left"
              prop="ext4"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </template>
          <!--  合同编号          -->
          <template v-if="settingDetail['ext8']">
            <el-table-column
              :label="$t('dicts.form_control_ext8')"
              align="left"
              prop="ext8"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </template>
          <!--  外部编号          -->
          <template v-if="settingDetail['ext12']">
            <el-table-column
              :label="$t('dicts.form_control_ext12')"
              align="left"
              prop="ext12"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </template>
          <!-----------------------增加扩展字段-------------------------->
          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            width="95"
            fixed="right"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
              >{{ $t(`doc.this_dept_detail`) }}
              </el-button>

              <el-button
                type="text"
                size="mini"
                @click="handleAddProposal(scope.row)"
                v-hasPermi="['proposal:myProposal:advice']"
              >{{ $t(`doc.this_dept_put_summary`) }}</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <as-pre-view
        :visible="viewShow"
        :id="viewId"
        ref="viewRef"
        @close="close"
      >
      </as-pre-view>
      <el-drawer
        :visible.sync="drawerShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <main-component style="height: calc(100vh);overflow: auto;" ref="mainComponent" :code="path+code"  :data="data"  :dataType="queryParams.dataType" @close="handleCloseChange"></main-component>
      </el-drawer>
      <el-drawer
        :visible.sync="distributePrintShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <distribute-print ref="distributePrint"  :data="data" @close="handleCloseChange"></distribute-print>
      </el-drawer>
      <el-drawer
        :visible.sync="distributeRangeShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <distribute-range ref="distributeRange"  :data="data" @close="handleCloseChange"></distribute-range>
      </el-drawer>
      <el-drawer
        :visible.sync="importShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="true"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <div class="drawer-head">
          <div class="cell-title">
            <p class="title">{{ $t(`doc.this_dept_import_history_data`) }}</p>
          </div>
          <div class="cell-btn">
            <el-button @click="importShow=false">{{ $t(`doc.this_dept_close`) }}</el-button>
          </div>
        </div>
        <div class="drawer-body">
          <initHistory dataType="stdd" :docClass="varChangeColor"></initHistory>
        </div>
      </el-drawer>

      <el-dialog
        :title="proposalTitle"
        :visible.sync="proposalOpen"
        v-if="proposalOpen"
        width="60%"
        append-to-body
        :close-on-click-modal = "false"
        :show-close = "false"
        @close="handleCloseAdd"
      >
        <addPropose @close="handleCloseAdd" :filePropose="filePropose"></addPropose>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listVersion,
  distributeLogcheckSign,
} from "@/api/document_account/version";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import mainComponent from "@/components/mainComponent/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from "@/api/system/dept";
import mixin from "@/layout/mixin/Commmon.js";
// 引入文件新增页面（同时适应文件修订、文件作废发起页面）
import { selectStatusByDocId } from "@/api/my_business/workflowApplyLog";
import initHistory from "@/views/system/dispose/initHistory";
import DistributePrint from '@views/document_account/distribute/print.vue'
import DistributeRange from '@views/document_account/distribute/range.vue'
import { checkPermi } from '@/utils/permission'
import { getInfoBy } from '@/api/setting/docClassSetting'
import { getFormRuleRecursive } from '@/api/setting/formRule'
import addPropose from "@views/proposal/my_proposal/add.vue";

export default {
  name: "Post",
  dicts: ["sys_normal_disable","standard_status","tenant_list"],
  components: {
    DistributeRange,
    DistributePrint,
    Treeselect,
    mainComponent,
    initHistory,
    addPropose
  },
  props: ["pButton", "menuitem"],
  mixins: [mixin],
  data() {
    return {
      // 弹出层标题
      proposalTitle: "",
      // 是否显示弹出层
      proposalOpen: false,
      filePropose:{},
      classTypeList:['DOC','RECORD','NOTE'],
      docClassList:[],
      distributePrintShow: false,
      distributeRangeShow: false,
      disable: false,
      deptOptions: {},
      activeName: undefined,
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      dateTime: new Date().getTime(),
      viewShow: false,
      viewId: "",
      boxClass: false,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        searchValue: "",
        docId: null,
        docClass: null,
        docName:null,
        // 部门对应的组织架构一级部门ID
        secDeptId: JSON.parse(sessionStorage.getItem("USER_INFO")).dept.deptId,
        deptId: undefined,
        params: {
          startTime: "",
          endTime: "",
        },
        dataType: undefined,
        // 有效版本文件 1有效、2失效
        status: undefined,
        statusList:[],
        partNumber: null,
        partRemark: null,
        factorys: null,
        customerCode: null,
        deviceCode: null,
        productVersion: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      borrowOpen: false,
      addBorrow: false,
      drawerDetails: false,
      taskFormData: {},
      varChangeColor: "",
      permission: '',
      changeClassType: '',
      companySelection: [],
      selectDoc: "",
      linkTypeTab: [],
      addDrawer: false,
      selectInfoData: [],
      pListData: {},
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      value2: "",
      selectionData: [],
      importShow:false,
      docClassTree: [],
      isShowPart:false,
      isCustomerShow:false,
      isDeviceShow:false,
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
      isProductVersionShow:false,
      settingDetail: []
    };
  },
  created() {
    /*
    by xfc 20230626
    系统分别建立 体系文件类型设置和项目文件类型设置菜单，菜单配置路由参数不一样
    dataType参数区分：体系文件=stdd、项目文件=project
    status参数区分：有效文件=1、失效文件=2
    */
    this.queryParams.dataType = this.$route.query.dataType
    let status = this.$route.query.status
    if (status.indexOf(',')>0) {
      this.queryParams.statusList = status.split(',')
    }else {
      this.queryParams.status = status
    }
    let openFunc= this.$route.query.openFunc
    if(openFunc==='add'){
      this.handleAdd()
    }
    listDept({ status: 0}).then((response) => {
      this.deptOptions = this.handleTree(response.data, "deptId");
    });
  },
  watch: {
    value2(val) {
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
  },
  mounted() {
    // 加载体系文件的-文件类型页签
    settingDocClassList({ classStatus: "1",dataType:this.queryParams.dataType,neClassType:'foreign',openPurview:true }).then(
      (response) => {
        this.docClassList = JSON.parse(JSON.stringify(response.rows))
        this.docClassTree = this.handleTree(response.rows.filter(item=>item.purview), "id", "parentClassId")
        this.docClassTree.forEach(
          (element) => {
            this.linkTypeTab.push({
              className: element.className,
              id: element.id,
            });
          }
        );
        if (this.linkTypeTab.length>0) {
          this.queryParams.docClass = this.linkTypeTab[0].id;
          this.varChangeColor = this.linkTypeTab[0].id;
          const docClass = this.docClassTree.find(item=>item.id===this.varChangeColor)
          this.permission = docClass.permission
          this.changeClassType = docClass.classType
          this.handleSelectNode(docClass)
          this.getByDocClass(this.queryParams.docClass)
        }
        this.getList();
      }
    );
  },
  methods: {
    handleCloseAdd() {
      this.proposalOpen = false
      this.filePropose = {}
      //this.getList();
    },
    checkPermi,
    handleCloseChange(){
      this.distributePrintShow = false
      this.distributeRangeShow = false
      this.drawerShow = false
      this.getList();
    },
    closeAdd() {
      this.drawerAdd = false
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    handleDistribute() {
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        this.data={docId:row.docId,versionId:row.versionId,flag:'0'}
        this.distributePrintShow = true
      }
    },
    handleDistributeRange() {
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        this.data={docId:row.docId,versionId:row.versionId,flag:'0'}
        this.distributeRangeShow = true
      }
    },
    handleReview(){
      let _this = this
      _this.handleDetail({type:'review_doc_apply',classTypeList:_this.classTypeList})
    },
    handleReviewLs(){
      let _this = this
      _this.handleDetail({type:'ls_review_doc_apply',classTypeList:_this.classTypeList})
    },
    handleExtra(){
      let _this = this
      _this.handleDetail({type:'extra_doc',classTypeList:_this.classTypeList})
    },
    handleReissue(){
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
      } else {
        let row = this.selectInfoData[0]
        let data = {
          type:'reissue_doc',
          docClass:row.docClass,
          docId:row.docId,
          docName:row.docName,
          versionValue:row.versionValue,
          versionId:row.versionId,
        }
        _this.handleDetail(data)
      }
    },
    handleLost(){
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
      } else {
        let row = this.selectInfoData[0]
        let data = {
          type:'lost_doc',
          docClass:row.docClass,
          docId:row.docId,
          docName:row.docName,
          versionValue:row.versionValue,
          versionId:row.versionId,
        }
        _this.handleDetail(data)
      }
    },
    handleChange(){
      let _this = this
      let row = ''
      if (this.selectInfoData.length > 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        return
        //this.$refs.this_department.clearSelection();
      } else {
        row = this.selectInfoData[0]
      }
      _this.handleDetail({type:'change_doc_apply',classTypeList:_this.classTypeList,row:row})
    },
    handleAdd() { // 新增
      let _this = this
      _this.handleDetail({type:'add_doc',classTypeList:_this.classTypeList})
    },
    handleDetail(row){
      let _this = this
      _this.code = row.type
      _this.data = row
      _this.drawerShow = true
    },
    handleAddBatch(){
      let _this = this
      _this.handleDetail({type:'add_doc_batch',classTypeList:_this.classTypeList})
    },
    handleUpdateBatch(){
      let _this = this
      _this.handleDetail({type:'update_doc_batch',classTypeList:_this.classTypeList})
    },
    handleDisuseBatch(){
      let _this = this
      _this.handleDetail({type:'disuse_doc_batch',classTypeList:_this.classTypeList})
    },
    handleUpdata() { // 修订
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        if (row.status!=='1') {
          this.$modal.msg(_this.$t(`doc.this_dept_select_valid_data`));
          return
        }
        _this.getStatusByDocId({type:'update_doc',docId:row.docId,versionId:row.versionId,flag:'1'})
      }

    },
    handleDisuse() { // 作废
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        _this.getStatusByDocId({type:'disuse_doc',docId:row.docId,versionId:row.versionId,flag:'1'})
      }
    },
    getStatusByDocId(query){
      let _this = this
      selectStatusByDocId(query).then(res=>{
        if (res.data==0) {
          _this.handleDetail(query)
        }else {
          _this.$modal.msgWarning(res.msg);
        }
      })
    },
    /** 查询列表 */
    getList() {
      if (this.queryParams.docClass) {
        this.loading = true;
        listVersion(this.queryParams).then((response) => {
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.value2=[]
      this.$refs.queryForm.resetFields();
      this.queryParams.params.startTime = null;
      this.queryParams.params.endTime = null;
      this.queryParams.searchValue = "";
      this.queryParams.docClass = this.varChangeColor;
      this.queryParams.docName = null;
      this.queryParams.nickName = null;
      this.handleSelectNode(this.docClassTree.find(item=>item.id===this.varChangeColor))
      this.handleQuery();
    },
    sortChange({ column, prop, order }){
      if (order==='ascending') {
        this.queryParams.orderByColumn = prop
        this.queryParams.isAsc = 'asc'
      }else if(order==='descending') {
        this.queryParams.orderByColumn = prop
        this.queryParams.isAsc = 'desc'
      }else {
        this.queryParams.orderByColumn = undefined
        this.queryParams.isAsc = undefined
      }
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.companySelection = selection;
      this.selectInfoData = selection;
    },
    handlePreview(row, source) {
      // console.log('row====>', row)
      if (row.encryptFileId != null) {
        this.viewId = row.encryptFileId;
      } else if (row.mergeFileId != null) {
        this.viewId = row.mergeFileId;
      } else {
        this.viewId = row.fileId;
      }
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    close() {
      this.viewShow = false;
    },
    handleAddProposal(row) {
      this.proposalOpen = true;
      this.filePropose = row;
      this.proposalTitle = this.$t(`doc.this_dept_new_add`);
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(process.env.VUE_APP_CONTEXT_PATH + '/#/workflow?type=detail/index'+'&docId='+row.docId+'&versionId='+row.versionId+'&flag=0&status='+row.status)
      }else {
        _this.handleDetail({type:'detail/index',docId:row.docId,versionId:row.versionId,flag:'0',status:row.status})
      }
    },
    /** 借阅按钮操作 */
    handleBorrow(){
      let _this = this
      _this.handleDetail({type:'borrow_doc',classTypeList:_this.classTypeList})
    },
    handleRetain(){
      let _this = this
      _this.handleDetail({type:'retain_doc',classTypeList:_this.classTypeList})
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t(`doc.this_dept_update_success`));
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t(`doc.this_dept_add_success`));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm(this.$t(`doc.this_dept_is_remove_code`) + postIds + this.$t(`file_set.signature_text1`))
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t(`doc.this_dept_remove_success`));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$set(this.queryParams,'exportName',"导出本部门文件")
      this.download(
        "/process/version/exportForList",
        {
          ...this.queryParams,
        },
        this.linkTypeTab[this.activeName].className + `_${new Date().getTime()}.xlsx`
      );
    },
    closeDrawerDetails() {
      this.drawerDetails = false;
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    handleCloseBorrow() {
      this.borrowOpen = false;
      this.getList();
    },
    state(tab) {
      let t = tab.index;
      let index = this.linkTypeTab[t].id;
      this.varChangeColor = index;
      const docClass = this.docClassTree.find(item=>item.id===index)
      this.permission = docClass.permission
      this.changeClassType = docClass.classType
      this.handleSelectNode(docClass)
      this.queryParams.docClass = index;

      this.getByDocClass(index);
      this.getList();
    },
    handleSelectNode(node){
      let docClassList = []
      this.getChildrenList(docClassList,node,'id',)
      this.queryParams.docClassList = docClassList
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.p2 == this.$t(`doc.this_dept_lost_efficacy`)) {
        //console.log(row.p2);
        return "yidu";
      }
      return "";
    },
    /** 增发按钮操作 */
    handleAddReissue(e) {
      if (this.companySelection.length <= 0) {
        this.$message.warning(this.$t(`doc.this_dept_select_extra_file`));
        return;
      }
      let array = this.companySelection;

      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (element.status == 2) {
          this.$message.warning(this.$t(`doc.this_dept_file_lost_efficacy`));
          return;
        }
      }

      let length = 0;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        distributeLogcheckSign({
          docId: element.docId,
          versionId: element.id,
        }).then((res) => {
          if (res.data != true) {
            this.$message.warning(res.msg);
          } else {
            length = length + 1;
            //debugger;
            if (length == array.length) {
              this.addDrawer = true;
              this.title = this.$t(`doc.this_dept_additional_issuance`);
            }
          }
        });
      }
    },
    chinldClose() {
      this.addDrawer = false;
      this.drawerDetails = false;
      this.getList();
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      let item = _this.docClassList.find(item=>item.id===cellValue)
      return item?item.className:cellValue
    },
    getByDocClass(docClass) {
      let _this = this
      _this.loading = true
      getFormRuleRecursive(docClass).then(res => {
        let settingDetail = {}
        if(res.data && res.data.ruleDetails) {
          let data = JSON.parse(res.data.ruleDetails)
          data.forEach(item => {
            settingDetail[item.value] = item.show === 'Y'
          })
          _this.loading = false
        }
        _this.settingDetail = settingDetail
      }).finally(err => {
        _this.loading = false
      })
    },
    getDictLabel(dictValue) {
      const dictItem = this.dict.type.tenant_list.find(item => item.value === dictValue);
      return dictItem ? this.dictLanguage(dictItem) : dictValue;
    }
  },
};
</script>

