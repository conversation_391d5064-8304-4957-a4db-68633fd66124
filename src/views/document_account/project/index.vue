<template>
  <!-- 项目文件台账 -->
  <div id="app">
    <div class="xmwj-box">

      <div class="xmwj-left-card">
        <el-input class="big-search" v-model.trim="keySearch" prefix-icon="el-icon-search" placeholder="请输入内容"></el-input>
        <el-tree ref="tree" :data="treeList" :props="treeProps" :filter-node-method="filterNode" :sort-method="sortNode"
          loadOptions="" node-key="id" @node-click="nodeClick"
          style="border: 0px solid rgb(230, 235, 245); min-height: 282px">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span v-if="data.nodeType == 'project'">
              <i class="el-tree-icon1"></i>{{ node.label }}
            </span>
            <span v-else>
              <i class="el-tree-icon3"></i>{{ node.label }}
            </span>
          </span>
        </el-tree>
      </div><!--xmwj-left-card 左边卡片-->

      <div class="xmwj-right-card">
        <div class="card-head">{{ currNodeDesc == null ? '' : currNodeDesc }}</div><!--card-head 左边内容-->
        <div class="card-body">
          <el-tabs v-model="tabsValue" @tab-click="handleClick">
            <el-tab-pane :key="item.code" v-for="(item, index) in tabs" :label="item.name" :name="item.code">
              <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="68px">
                <div class="global-ser" id="add">
                  <div class="ser-top">
                    <div class="cell-left">
                      <el-date-picker v-model="value2" type="datetimerange" :picker-options="{}" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss" unlink-panels align="right" size="small"
                        style="float: left; height: 34px; margin-right: 5px">
                      </el-date-picker>
                      <el-input v-model.trim="queryParams.docName" placeholder="输入文件名称搜索"
                        @keyup.enter.native="handleQuery" class="input-with-select">
                        <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
                      </el-input>
                      <el-button class="button" @click="activeSearchBox" v-if="!boxClass">
                        <i class="icon iconfont icon-zongheshaixuan-"></i>
                      </el-button>
                      <el-button v-else class="button" @click="activeSearchBox"
                        style="background: #013288; border-color: #013288; color: #fff">
                        <i class="icon iconfont icon-zongheshaixuan-"></i>
                      </el-button>
                      <el-button icon="el-icon-refresh" @click="resetQuery">重置
                      </el-button>
                    </div> <!-- cell-left end -->
                    <div class="cell-right" style="width:480px">
                      <el-button v-if="queryParams.status == 1" type="primary"  v-hasPermi="['process:xm:distribute:open']" @click="handleDistribute()">分发</el-button>
                      <el-button v-if="queryParams.status == 1" type="danger"
                           v-hasPermi="['process:xm:fileBase:open']"
                        :disabled="!selectInfoData || selectInfoData.length == 0"
                        @click="handleFileBase()">基线申请</el-button>
                      <el-button v-if="queryParams.status == 1" v-hasPermi="['process:xm:standard:add']" type="primary" @click="handleAdd()">新增</el-button>
                      <el-button
                        v-if="queryParams.status == 1"
                        v-hasPermi="['process:xm:standard:updata']"
                        type="primary"
                        @click="handleUpdata()"
                      >修订</el-button>
                      <el-button
                        v-if="queryParams.status == 1"
                        v-hasPermi="['process:xm:standard:disuse']"
                        type="primary"
                        @click="handleDisuse()"
                      >作废</el-button>
                      <el-button v-if="queryParams.status == 1"
                       v-hasPermi="['process:xm:standard:import']"
                       @click="importShow = true">
                        导入</el-button>
                    </div> <!-- cell-right end -->
                  </div> <!-- ser-top end -->
                  <div class="ser-bottom">
                    <div class="cell-left">
                      <el-form-item label="文件编号">
                        <el-input placeholder="文件编号" v-model.trim="queryParams.docId"></el-input>
                      </el-form-item>
                      <el-form-item label="编制部门">
                        <treeselect v-model.trim="queryParams.deptId" :options="deptOptions" :normalizer="normalizer"
                          placeholder="选择部门" style="width: 200px" :searchable="false" size="mini" />
                      </el-form-item>
                    </div> <!-- cell-left end -->
                    <div class="cell-right">
                      <el-button type="primary" @click="handleQuery">查询</el-button>
                      <el-button @click="resetQuery">重置</el-button>
                      <el-button @click="boxClass = false">取消</el-button>
                    </div> <!-- cell-right end -->
                  </div> <!-- end ser-bottom -->
                </div> <!-- end global-ser -->
              </el-form>

              <el-card class="gray-card">
                <el-table v-loading="loading" :data="postList" @sort-change="sortChange"
                  @selection-change="handleSelectionChange" ref="multipleTable" header-align="left">
                  <el-table-column type="selection" width="55" align="left" :show-overflow-tooltip="true" />
                  <el-table-column label="文件名称" align="left" sortable="custom" width="300" prop="docName"
                    :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <span class="wenjcolor" v-show="scope.row.hasPerms == true"><a
                          @click="handlePreview(scope.row, 'COMPANY')">{{ scope.row.docName }}</a></span>
                      <span v-show="!scope.row.hasPerms || scope.row.hasPerms == false">{{ scope.row.docName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="文件编号" sortable="custom" align="left"
                    :prop="queryParams.linkType == 'REC' ? 'recordDocId' : 'docId'" :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <span v-if="queryParams.linkType == 'REC' && scope.row.recordDocId != 0
                        ">{{ scope.row.recordDocId }}</span>
                      <span v-else>{{ scope.row.docId }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="文件版本" align="left" width="110" prop="versionValue" :show-overflow-tooltip="true" />
                  <el-table-column label="编制部门" align="left" width="200" prop="deptName" :show-overflow-tooltip="true" />
                  <el-table-column label="编制人员" align="left" width="120" prop="nickName" :show-overflow-tooltip="true" />
                  <el-table-column label="文件状态" align="left" width="80" prop="status"
                    :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <span style="color: red" v-if="scope.row.status == '2'">已失效</span>
                      <span style="color: #09bb25" v-else>生效中</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="生效日期" sortable="custom" width="110" align="left" prop="startDate"
                    :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="发布日期" align="left" width="110" sortable="custom" prop="releaseTime"
                    :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="失效日期" align="left" width="110" v-if="queryParams.status == 2" sortable="custom"
                    prop="releaseTime" :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.endDate, "{y}-{m}-{d}") }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="left" width="110" fixed="right"
                    class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                      <el-button size="mini" type="text" @click="handleDetails(scope.row)">详情
                      </el-button>
                      <el-button size="mini" type="text" v-if="scope.row.baseStatus === '1'"
                        @click="handleBaseLog(scope.row)">基线记录
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
              <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <el-drawer :visible.sync="drawerShow" direction="rtl" size="90%" :with-header="false"
               :wrapperClosable="false" :show-close="false" modal-append-to-body :destroy-on-close="true">
      <main-component ref="mainComponent" :code="path + code" :data="data" dataType="project"
                      @close="handleCloseChange"></main-component>
    </el-drawer>
    <el-drawer :visible.sync="baseLogShow" direction="rtl" size="90%" :with-header="false"
               :wrapperClosable="false" :show-close="false" modal-append-to-body :destroy-on-close="true">
      <FileBaseLog :data="standardId" @close="handleCloseChange"></FileBaseLog>
    </el-drawer>
    <el-drawer :visible.sync="distributeShow" direction="rtl" size="90%" :with-header="false"
               :wrapperClosable="false" :show-close="false" modal-append-to-body :destroy-on-close="true">
      <distribute-print ref="distribute" :data="data" @close="handleCloseChange"></distribute-print>
    </el-drawer>
    <el-drawer :visible.sync="importShow" direction="rtl" size="90%" :with-header="false"
               :wrapperClosable="false" :show-close="true" modal-append-to-body :destroy-on-close="true">
      <div class="drawer-head">
        <div class="cell-title">
          <p class="title">导入历史数据</p>
        </div>
        <div class="cell-btn">
          <el-button @click="closeInitHistory()">关闭</el-button>
        </div>
      </div>
      <div class="drawer-body">
        <initHistory dataType="project" :projectVal="projectVal" :projectDesc="currNodeDesc"></initHistory>
      </div>
    </el-drawer>
    <DealDrawer v-if="dealDrawerShow" ref="dealDrawer" @closeDrawer="handleCloseChange"></DealDrawer>
  </div>
</template>

<script>
import { checkPermi, checkRole } from "@/utils/permission"; // 权限判断函数
import {
  listVersion,
  distributeLogcheckSign,
} from "@/api/document_account/version";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from "@/api/system/dept";
import mixin from "@/layout/mixin/Commmon.js";
import { treeSelect } from "@/api/system/project";
// 引入文件新增页面（同时适应文件修订、文件作废发起页面）
import { getInfoByBpmnId, linkLoglistlink } from "@/api/file_processing/modifiyApply";
import mainComponent from "@/components/mainComponent/index.vue";
import FileBaseLog from '@views/document_account/project/fileBaseLog.vue'
import initHistory from "@/views/system/dispose/initHistory";
import { selectStatusByDocId } from "@/api/my_business/workflowApplyLog";
import DealDrawer from '@/components/DealDrawer/index.vue'
import { selectStatusByVersionId } from '@/api/process/baseApplyDetail'
import DistributePrint from '@views/document_account/distribute/print.vue'
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    DistributePrint,
    DealDrawer,
    FileBaseLog,
    mainComponent,
    Treeselect,
    initHistory,
  },
  props: ["pButton", "menuitem"],
  mixins: [mixin],
  data() {
    return {
      bomPath: process.env.VUE_APP_BOM_PATH,
      dealDrawerShow: false,
      distributeShow: false,
      baseLogShow: false,
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      deptOptions: {},
      treeProps: {
        children: "children",
        label: "label",
      },
      standardId: undefined,
      treeList: [],
      currNodeDesc: null,
      dateTime: new Date().getTime(),
      viewShow: false,
      viewId: "",
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        linkType: "DEO",
        searchValue: "",
        docId: null,
        docClass: null,
        docNames: null,
        docName: null,
        deptId: null,
        params: {
          startTime: "",
          endTime: "",
          projectId: ""
        },
        dataType: 'project',
        // 有效版本文件 1有效、2失效
        status: '1'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: "不能为空", trigger: "blur" }],
        postCode: [{ required: true, message: "不能为空", trigger: "blur" }],
        postSort: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      taskData: [], // 任务数据
      addBorrow: false,
      drawerDetails: false,
      taskFormData: {},
      varChangeColor: "",
      companySelection: [],
      selectDoc: "",
      linkTypeTab: [],
      addDrawer: false,
      selectInfoData: [],
      pListData: {},
      classLevelOptions: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      value2: "",
      importShow: false,
      projectVal: '',
      keySearch: undefined,
      tabs: [{ name: '台账文件', code: 'file' }],
      tabsValue: 'file',
      docClass:null,
    };
  },
  created() {
    /*
    by xfc 20230705
    系统分别建立 体系文件类型设置和项目文件类型设置菜单，菜单配置路由参数不一样
    status参数区分：有效文件=1、失效文件=2
    */
    this.queryParams.status = this.$route.query.status
    let openFunc = this.$route.query.openFunc
    if (openFunc === 'add') {
      this.handleAdd()
    }
    this.getList();
    listDept({ status: 0 }).then((response) => {
      this.deptOptions = this.handleTree(response.data, "deptId");
    });
    this.loadTreeList()
  },
  watch: {
    value2(val) {
      console.log(val);
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
    // 树形关键字搜索
    keySearch(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {
  },
  methods: {
    checkPermi,
    checkRole,
    // 排序节点
    sortDataById(data) {
      // 按id排序函数
      function sortById(a, b) {
        return parseInt(a.sort) - parseInt(b.sort)
      }
      // 递归排序子级
      function sortChildren(node) {
        if (node.children && node.children.length > 0) {
          node.children.sort(sortById);
          node.children.forEach(child => {
            sortChildren(child);
          });
        }
      }
      // 初始排序
      data.sort(sortById);
      // 对每个节点递归排序子级
      data.forEach(node => {
        sortChildren(node);
      });
      return data;
    },
    handleClick(tab, event) {
      if(tab.name==="record"){
        this.docClass=this.queryParams.linkType
        this.queryParams.linkType="PROJECT-R"
      }else{
        this.queryParams.linkType=this.docClass
        this.docClass=null
      }
      this.getList();
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 左侧节点点击事件
    nodeClick(val) {
      this.$refs.tree.setCurrentKey(val)
      const selected = this.$refs.tree.getCurrentNode()
      let currNode = this.$refs.tree.getNode(selected)
      if (currNode.data.nodeType == 'class') {
        // 点击项目文件类型
        this.currNodeDesc = currNode.parent.data.label + '>' + currNode.data.label
        // 设置参数：项目文件类型
        this.queryParams.linkType = currNode.data.classId
        this.queryParams.params.projectId = currNode.data.parentId
        this.tabs=[{ name: '台账文件', code: 'file' }]
      } else if (currNode.data.nodeType == 'project') {
        // 点击项目
        this.currNodeDesc = currNode.data.label
        // 设置参数：项目文件类型和项目ID
        this.queryParams.linkType = ''
        this.queryParams.params.projectId = currNode.data.id
        this.projectVal = currNode.data.id
        this.tabs=[{ name: '台账文件', code: 'file' }, { name: '表单记录', code: 'record' }]
      }
      this.tabsValue = 'file'
      this.getList();
    },
    loadTreeList() {
      // 加载左侧项目文件类型树
      this.loading = true;
      treeSelect().then(response => {
        // 不展示记录文件分类
        response.data.forEach(project => {
          project.children = project.children.filter(
            (item) => (item.classId != "PROJECT-R")
          );
        });
        this.treeList = this.sortDataById(response.data)
        this.loading = false;
      });
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    async handleFileBase() {
      let _this = this
      let bool = _this.selectInfoData.every(item => item.versionValue && (item.versionValue.includes('X') || item.versionValue.includes('x')))
      if (!bool) {
        _this.$message.warning("选中的数据中，具有非X版本文件请重新选择！")
        return
      }
      let res = await selectStatusByVersionId(this.ids)
      if (res.data) {
        _this.$message.warning("选中的数据中，文件编号" + res.data + "已经在流程中！")
        return
      }
      _this.handleDetail({ type: 'file_base', detailList: _this.selectInfoData })
    },
    handleDistribute() {
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg("选择一条数据");
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        this.data = { docId: row.docId, versionId: row.versionId, flag: '0' }
        this.distributeShow = true
      }
    },
    handleAdd() { // 新增
      let _this = this
      _this.handleDetail({ type: 'add_doc' })
    },
    handleDetail(row) {
      let _this = this
      _this.code = row.type
      _this.data = row
      _this.drawerShow = true
    },
    handleUpdata() { // 修订
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg("选择一条数据");
      } else {
        let row = this.selectInfoData[0]
        _this.getStatusByDocId({ type: 'update_doc', docId: row.docId, versionId: row.versionId, flag: '1' })
      }

    },
    handleDisuse() { // 作废
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg("选择一条数据");
      } else {
        let row = this.selectInfoData[0]
        _this.getStatusByDocId({ type: 'disuse_doc', docId: row.docId, versionId: row.versionId, flag: '1' })
      }
    },
    getStatusByDocId(query) {
      let _this = this
      selectStatusByDocId(query).then(res => {
        if (res.data !== 1) {
          _this.handleDetail(query)
        } else {
          _this.$modal.msg(res.msg);
        }
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listVersion(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.value2 = []
      this.queryParams.params.startTime = null;
      this.queryParams.params.endTime = null;
      this.queryParams.searchValue = "";
      this.queryParams.docId = null;
      this.queryParams.docClass = this.varChangeColor;
      this.queryParams.docName = null;
      this.queryParams.deptId = null;
      this.handleQuery();
    },
    sortChange({ column, prop, order }) {
      if (order === 'ascending') {
        this.queryParams.orderByColumn = prop
        this.queryParams.isAsc = 'asc'
      } else if (order === 'descending') {
        this.queryParams.orderByColumn = prop
        this.queryParams.isAsc = 'desc'
      } else {
        this.queryParams.orderByColumn = undefined
        this.queryParams.isAsc = undefined
      }
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.versionId);
      this.companySelection = selection;
      this.selectInfoData = selection;
    },
    handlePreview(row, source) {
      // console.log('row====>', row)
      if (row.encryptFileId != null) {
        this.viewId = row.encryptFileId;
      } else if (row.mergeFileId != null) {
        this.viewId = row.mergeFileId;
      } else {
        this.viewId = row.fileId;
      }
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    close() {
      this.viewShow = false;
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      if (!!row.isBom) {
        _this.handleDeal(row)
      } else {
        _this.handleDetail({ type: 'detail/index', docId: row.docId, versionId: row.versionId, flag: '0', status: this.queryParams.status })
      }
    },
    handleDeal(row) {
      this.dealDrawerShow = true;
      this.$nextTick(() => {
        this.$refs.dealDrawer.init(this.bomPath + '/#/iframe/code/bom-audit/' + row.versionId + "?operation=view");
      });
    },
    handleBaseLog(row) {
      this.standardId = row.standardId
      this.baseLogShow = true
    },
    handleCloseChange() {
      this.distributeShow = false
      this.drawerShow = false
      this.baseLogShow = false
      this.getList();
    },
    closeDrawerDetails() {
      this.drawerDetails = false;
    },
    chinldClose() {
      this.addDrawer = false;
      this.drawerDetails = false;
      this.getList();
    },
    closeInitHistory(){
      this.importShow=false
      this.getList();
    }
  },
};
</script>
<style lang="scss">
@import "../../../../public/css/poctstyle.css";
</style>
