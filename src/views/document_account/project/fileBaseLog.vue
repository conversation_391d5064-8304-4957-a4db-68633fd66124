<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ title }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="this.formData&&this.formData.procInstId" @click="handleMonitor">流程监控</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </div>
    <el-tabs v-if="this.formData&&this.formData.procInstId" v-model="activeName">
      <el-tab-pane label="信息内容" name="info"></el-tab-pane>
      <el-tab-pane label="审批记录" name="log"></el-tab-pane>
    </el-tabs>
    <div v-if="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">基本信息</div>
        </div>
        <el-card class="gray-card">
          <el-table
            :data="[formData]"
            border
            ref="table"
            header-align="left"
          >
            <el-table-column
              label="文件名称"
              align="left"
              prop="docName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="文件编号"
              align="left"
              prop="docCode"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="基线前版本"
              align="left"
              prop="currVersion"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="基线后版本"
              align="left"
              prop="targetVersion"
              :show-overflow-tooltip="true"
            />
          </el-table>
        </el-card>
      </div>
    </div>
    <div v-if="activeName==='log'">
      <workflow-logs :procInstId = "formData.procInstId"></workflow-logs>
    </div>
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
  </div>
</template>
<script>
import processcode from "@views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import { getInfoByStandardId } from '@/api/process/baseApplyDetail'
export default {
  dicts: ["shlk_no_source_type","sys_yes_no","shlk_review_result","shlk_file_type",'shlk_change_type'],
  name: "FileBaseLog",
  components: {WorkflowLogs, processcode },
  props: ['data'],
  data() {
    return {
      title: '基线申请',
      activeName: "info",
      pListData: undefined,
      loading: false,
      detailLoading: false,
      editStatus: false,
      monitorDrawerVisible:false,
      procDefKey: "file_base_apply_shlk",
      changeTypeList: [],
      dialogVisible: false,
      isSummary: false,
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      passoptions: [
        { value: "pass", label: "通过" },
        { value: "un_pass", label: "不通过" },
      ],
      yesOrNo: [
        { value: "N", label: "否" },
        { value: "Y", label: "是" },
      ],
      formSubmit: { summary: "", actionType: "", pass: "" },
      formData: {
        id: undefined,
        tenantId: undefined,
        procInstId: undefined,
        bpmnStatus: undefined,
        detailList:[],
      },
      rules: {
        pass: [
          { required: true, message: "请选择", trigger: "blur" },
        ],
      },
      workflowStatus: false,
      kuozhanshuju: {},
    };
  },
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    init(standardId){
      let _this = this
      _this.rest()
      // _this.loading = true
      //是否编辑模式
      this.$nextTick(() => {
          this.getDetail(standardId);
      });
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.formData= {
        id: undefined,
        tenantId: undefined,
        procInstId: undefined,
        bpmnStatus: undefined,
        detailList:[],
      }
    },
    getDetail(standardId) {
      let _this = this
      _this.detailLoading = true
      getInfoByStandardId(standardId).then((res) => {
        _this.formData = res.data
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    close() {
      this.$emit("close")
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.formData.procInstId);
      });
    },
  },
};
</script>
