<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">文件宣贯范围</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">基本信息</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item label="文件名称:" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文件编号:" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item label="文件版本:" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="当前生效版本:">
                  <div class="link-box bzlink-box">
                    <span
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                    >{{ formData.preStandardDoc?formData.preStandardDoc.fileName:'' }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item label="宣贯范围:" prop="versionValue">
                  <el-select
                    placeholder="宣贯范围"
                    v-model.trim="formData.distributeType"
                  >
                    <el-option
                      v-for="dict in dict.type.sys_distribute_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                  <el-tooltip placement="top">
                    <div slot="content">
                      公司：宣贯到的公司可查看文件详情<br/>
                      部门：宣贯到的部门可查看文件详情<br/>
                      个人：宣贯到的个人可查看文件详情<br/>
                      部门和个人：宣贯到的个人和部门可查看文件详情
                    </div>
                    <i style="margin-left: 15px" class="el-icon-question"></i>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="编制部门:">
                  <span>{{formData.deptName}}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card" v-show="formData.distributeType==='company'">
        <div class="card-head">
          <div class="cell-title">宣贯公司操作</div>
        </div>
        <el-card class="gray-card table-card no-padding" v-loading="deptLoading">
          <el-button type="primary" @click="selectionBoxInit('companyDataList',companyDataList,companyList,{title:'选择公司',id:'id',label:'tenantName',valueId:'receiveUserDeptId',valueLabel:'receiveUserDept',valueModel:restData()})">选择公司</el-button>
          <el-table :data="companyDataList" border max-height="500">
            <el-table-column
              label="公司"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-button
                  circle
                  type="danger"
                  @click="handleDel('companyDataList',scope.$index)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="news-card" v-show="formData.distributeType.includes('dept')">
        <div class="card-head">
          <div class="cell-title">宣贯部门操作</div>
        </div>
        <el-card class="gray-card table-card no-padding" v-loading="deptLoading">
          <el-button type="primary" v-if="formData.distributeType.includes('dept')" @click="selectionBoxInit('deptDataList',deptDataList,deptOptions,{title:'选择部门',id:'deptId',label:'deptName',valueId:'receiveUserDeptId',valueLabel:'receiveUserDept',valueModel:restData(),anySelect:true,FSLink:true})">选择部门</el-button>
          <el-table :data="deptDataList" border max-height="500">
            <el-table-column
              label="部门"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-button
                  circle
                  type="danger"
                  @click="handleDel('deptDataList',scope.$index)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="news-card" v-show="formData.distributeType.includes('person')">
        <div class="card-head">
          <div class="cell-title">宣贯个人操作</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-button type="primary" @click="userSearchInit('personDataList',null,null,personDataList.map(item=>item.receiveUserName))">选择用户</el-button>
          <el-table :data="personDataList" border max-height="500" v-loading="personLoading">
            <el-table-column
              label="用户"
              align="center"
              prop="userName"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{scope.row.receiveNickName}}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="部门"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-button
                  circle
                  type="danger"
                  @click="handleDel('personDataList',scope.$index)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>
    <selection-box ref="selectionBox" @selectHandle="selectBoxHandle"></selection-box>

    <as-pre-view
      :visible="viewShow"
      :id="viewId"
      ref="viewRef"
      @close="close"
    >
    </as-pre-view>
  </div>
</template>
<script>
import {standardGetDetail} from "@/api/document_account/standard";
import {fileLocalPdfView} from "@/api/pdf_preview";
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import { listDistribute, updateList } from '@/api/process/distribute'
import { listDept } from '@/api/system/dept'
import SelectionBox from '@viewscomponents/selectionBox/index.vue'
import { getCompanyList, queryCompanyList } from '@/api/system/user'
export default {
  name: "DistributeRange",
  components: { SelectionBox, UserList },
  dicts:['sys_distribute_type'],
  props: ['data'],
  data() {
    return {
      viewId: "",
      deptDataList: [],
      personDataList: [],
      companyDataList: [],
      deptList: [],
      companyList: [],
      deptOptions: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        distributeType: '',
        preChangeCode: undefined,
      },
      loading: false,
      deptLoading: false,
      personLoading: false,
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    init(row){
      let _this = this
      _this.rest()
      _this.getDeptList()
      _this.getCompanyList()
      _this.getDetail(row)
    },
    async getDetail(query) {
       let _this = this
      _this.loading = true
      let res = await standardGetDetail(query)
      _this.formData = res.data;
      _this.loading = false
      _this.getDeptDataList(res.data.versionId)
      _this.getPersonDataList(res.data.versionId)
      _this.getCompanyDataList(res.data.versionId)
    },
    getCompanyList() {
      let _this = this
      getCompanyList({}).then(res=>{
        _this.companyList = res.data
      })
    },
    getDeptList(){
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0}).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    getDeptDataList(versionId){
      let _this = this
      _this.deptLoading = true
      listDistribute({versionId:versionId,type:'dept'}).then(res=>{
        if (res.data&&res.data.length>0) {
          _this.deptDataList = res.data
        }else{
          _this.deptDataList = []
        }
      }).finally(()=>{
        _this.deptLoading = false
      })
    },
    getPersonDataList(versionId){
      let _this = this
      _this.personLoading = true
      listDistribute({versionId:versionId,type:'person'}).then(res=>{
        if (res.data&&res.data.length>0) {
          _this.personDataList = res.data
        }else{
          _this.personDataList = []
        }
      }).finally(()=>{
        _this.personLoading = false
      })
    },
    getCompanyDataList(versionId){
      let _this = this
      _this.personLoading = true
      listDistribute({versionId:versionId,type:'company'}).then(res=>{
        if (res.data&&res.data.length>0) {
          _this.companyDataList = res.data
        }else{
          _this.companyDataList = []
        }
      }).finally(()=>{
        _this.personLoading = false
      })
    },
    rest(){
      let _this = this
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        distributeType: '',
        preChangeCode: undefined
      }
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    handlePreview(id) {
      this.viewId = id;
      this.viewShow = true;
      this.$refs.viewRef.handleOpenView(id);

    },
    // PDF文件预览
    handlePdfPreview(pdfFileId) {
      if(pdfFileId == '') {
        alert("当前变更版本文档未转PDF文件！");
        return false;
      }
      fileLocalPdfView(pdfFileId)
    },
    handleAddShow(source,index){
      return this[source].length === index + 1
    },
    handleAdd(source){
      this[source].push(this.restData())
    },
    restData(){
      return  {
        id: undefined,
        versionId: this.formData.versionId,
        code: undefined,
        docId: this.formData.docId,
        docClass: this.formData.docClass,
        docName: this.formData.docName,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    submitForm(){
      let _this = this
      _this.$confirm("是否确定宣贯范围吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let personDataList = []
        if (_this.formData.distributeType.includes('person')) {
          personDataList = JSON.parse(JSON.stringify(_this.personDataList))
        }
        let deptDataList = []
        if (_this.formData.distributeType.includes('dept')) {
          deptDataList = JSON.parse(JSON.stringify(_this.deptDataList))
        }
        let companyDataList = []
        if (_this.formData.distributeType ===  'company') {
          companyDataList = this.companyDataList;
        }
        let data = {
          versionId: _this.formData.versionId,
          distributeType: _this.formData.distributeType,
          deptDataList: deptDataList,
          personDataList: personDataList,
          companyDataList: companyDataList,
        }
        updateList(data).then(res => {
          _this.$modal.msgSuccess("提交成功！")
          _this.deptDataList = res.data.deptDataList
          _this.personDataList = res.data.personDataList
        })
      }).catch(() => {});
    },
    handleDel(source,index){
      this[source].splice(index,1)
    },
    selectionBoxInit(label,selectedList,dataList,settings) {
      this.$refs.selectionBox.init(label,undefined,selectedList,dataList,settings,this.deptList)
    },
    selectBoxHandle(label,index,selectedList,valueList) {
      this.$set(this,label, valueList)
    },
    userSearchInit(source,index,roleKey,selectList){
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(source,index,null,roleKey,true,selectList,undefined)
      })
    },
    userSelectHandle(source,index,userList){
      if (userList&&userList.length>0) {
        for (let i=0;i<userList.length;i++) {
          let user = userList[i]
            let data = this.restData()
            data.receiveUserName = user.userName
            data.receiveNickName = user.nickName
            data.receiveUserDeptId = user.dept.deptId
            data.receiveUserDept = user.dept.deptName
            this[source].push(data)
        }
      }
    },
  },
};
</script>
