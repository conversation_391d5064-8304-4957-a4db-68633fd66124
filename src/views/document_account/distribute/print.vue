<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t('doc.this_dept_distribute_print') }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" @click="submitForm">{{ $t('doc.this_dept_annex') }}</el-button>
        <el-button @click="close">{{ $t('doc.this_dept_close') }}</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t('doc.this_dept_base_msg') }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`file_handle.print_current_effec_ver`)">
                  <div class="link-box bzlink-box">
                    <span
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                    >{{ formData.preStandardDoc?formData.preStandardDoc.fileName:'' }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{$t(`doc.this_dept_distribute_operation`)}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-table :data="dataList" border>
            <el-table-column
              :label="$t(`doc.this_dept_signatory`)"
              align="center"
              prop="userName"
              width="200"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-input
                  v-if="!scope.row.id"
                  v-model.trim="scope.row.receiveNickName"
                  :placeholder="$t(`sys_mgr.dispose_select_drop_down`)"
                  readonly
                >
                  <el-button
                    slot="append"
                    type="primary"
                    icon="el-icon-more"
                    @click="userSearchInit('dataList',scope.$index)"
                  ></el-button>
                </el-input>
                <span v-else>{{scope.row.receiveNickName}}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_dept`)"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_print_paper`)"
              align="center"
              prop="printPaperType"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-select
                  :placeholder="$t(`doc.this_print_paper`)"
                  v-model.trim="scope.row.printPaperType"
                  v-if="!scope.row.id"
                >
                  <el-option
                    v-for="dict in dict.type.print_paper_type"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
                <dict-tag v-else :options="dict.type.print_paper_type" :value="scope.row.printPaperType"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`file.print_table_classification_no`)"
              align="center"
              prop="code"
              :formatter="formatterCode"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_date`)"
              align="center"
              prop="receiveTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_status`)"
              align="center"
              prop="recovery"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_date`)"
              align="center"
              prop="recoveryTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_operation`)"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-button
                  circle
                  type="primary"
                  icon="el-icon-plus"
                  v-if="handleAddShow(scope.$index)"
                  @click="handleAdd()"
                ></el-button>
                <el-button
                  v-if="!scope.row.id||!scope.row.receive"
                  circle
                  type="danger"
                  icon="el-icon-minus"
                  @click="handleDel(scope.$index,scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>

    <as-pre-view
      :visible="viewShow"
      :id="viewId"
      ref="viewRef"
      @close="close"
    >
    </as-pre-view>
  </div>
</template>
<script>
import {standardGetDetail} from "@/api/document_account/standard";
import {fileLocalPdfView} from "@/api/pdf_preview";
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import { listDistribute, updateList, updateListCode } from '@/api/process/distribute'
export default {
  name: "DistributePrint",
  components: { UserList },
  props: ['data'],
  dicts: ['print_paper_type'],
  data() {
    return {
      viewId: "",
      dataList: [{}],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      delList: [],
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
      },
      loading: false,
      detailLoading: false,
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    init(row){
      let _this = this
      _this.rest()
      _this.getDetail(row)
    },
    async getDetail(query) {
       let _this = this
      _this.loading = true
      let res = await standardGetDetail(query)
      _this.formData = res.data;
      _this.loading = false
      _this.getListDistribute()
    },
    getListDistribute(){
      let _this = this
      _this.detailLoading = true
      listDistribute({versionId:_this.formData.versionId,type:'print'}).then(res=>{
        if (res.data&&res.data.length>0) {
          _this.dataList = res.data
        }else{
          _this.dataList = [_this.restData()]
        }
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    rest(){
      let _this = this
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined
      }
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    // PDF文件预览
    handlePdfPreview(pdfFileId) {
      if(pdfFileId == '') {
        alert("当前变更版本文档未转PDF文件！");
        return false;
      }
      fileLocalPdfView(pdfFileId)
    },
    handleAddShow(index){
      return this.dataList.length === index + 1
    },
    handleAdd(){
      this.dataList.push(this.restData())
    },
    restData(){
      return  {
        id: undefined,
        versionId: this.formData.versionId,
        code: undefined,
        type: 'print',
        docId: this.formData.docId,
        docClass: this.formData.docClass,
        docName: this.formData.docName,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    submitForm(){
      let _this = this
      let printDataList = _this.dataList.filter(item=>!item.id)
      let bool = printDataList.every(item=>!!item.receiveUserName)
      if (bool) {
        let data={
          versionId:this.formData.versionId,
          printDataList: printDataList,
          printDelList: _this.delList
        }
        _this.$confirm("确定分发给选中的人员吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          updateListCode(data).then(res=>{
            _this.$modal.msgSuccess("分发成功！")
            _this.delList = []
            _this.getListDistribute()
          })
        }).catch(() => {});
      }else {
        _this.$modal.msgError("请完善签收人信息！")
      }
    },
    handleDel(index,row){
      if (this.dataList.length>1) {
        this.dataList.splice(index,1)
        if (row.id){
          this.delList.push(row.id)
        }
      }else {
        this.dataList = [this.restData()]
      }
    },
    userSearchInit(source,index){
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(source,index,null,null,true)
      })
    },
    userSelectHandle(source,index,userList){
      if (userList&&userList.length>0) {
        for (let i=0;i<userList.length;i++) {
          let user = userList[i]
          if (i===0) {
            this.$set(this[source][index],'receiveUserName', user.userName)
            this.$set(this[source][index],'receiveNickName', user.nickName)
            this.$set(this[source][index],'receiveUserDeptId', user.dept.deptId)
            this.$set(this[source][index],'receiveUserDept', user.dept.deptName)
          }else {
            let data = this.restData()
            data.receiveUserName = user.userName
            data.receiveNickName = user.nickName
            data.receiveUserDeptId = user.dept.deptId
            data.receiveUserDept = user.dept.deptName
            this[source].push(data)
          }
        }
      }
    },
  },
};
</script>
