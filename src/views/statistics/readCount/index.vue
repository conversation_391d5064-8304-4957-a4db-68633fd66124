<template>
  <el-card class="app-container document_changeindex">
    <div slot="header" class="clearfix">
      <span> {{ $t(`file_count.read`) }} </span>
    </div>
    <div>
      <el-row type="flex" justify="space-between">
        <div>
          <el-form :model="queryParams" ref="searchForm" :inline="true" inline size="mini" label-width="68px">
            <el-form-item prop="queryName">
              <el-input
                v-model.trim="queryParams.queryName"
                :placeholder="$t(`doc.this_dept_insert_text`)"
                clearable
                width="210px"
                @keyup.enter.native="handleQuery"
              >
              </el-input>
            </el-form-item>
            <el-form-item prop="docClass">
              <treeselect
                v-model="queryParams.docClass"
                :options="classLevelOptions"
                @select="(node)=>handleSelectNode(node,'queryParams')"
                :normalizer="normalizer"
                :show-count="true"
                :searchable="false"
                :placeholder="$t(`doc.this_dept_select_type`)"
                style="width: 150px;"
              />
            </el-form-item>
            <el-form-item prop="tranStatus">
              <el-select
                v-model="queryParams.tranStatus"
                :placeholder="$t(`file_count.read_training_situation`)"
                clearable
              >
                <el-option  v-for="dict in dict.type.read_train_situation"
                            :key="dict.value"
                            :label="dictLanguage(dict)"
                            :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item prop="status">
              <el-select
                v-model="queryParams.status"
                :placeholder="$t(`doc.this_dept_file_status`)"
                clearable
              >
                <el-option  v-for="dict in dict.type.standard_status"
                            :key="dict.value"
                            :label="dictLanguage(dict)"
                            :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">
                {{ $t(`doc.this_dept_query`) }}
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">
                {{ $t(`doc.this_dept_reset`) }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-row>
      <el-card class="gray-card">
        <el-table header-align="left" v-loading="loading" :data="list">
          <el-table-column
            prop="docName"
            :label="$t(`doc.this_dept_file_name`)"
            align="left"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="docId"
            :label="$t(`doc.this_dept_file_code`)"
            align="left"
            :show-overflow-tooltip="true"
          />
          <!--56112313-->
          <el-table-column
            :label="$t(`doc.this_dept_file_type`)"
            align="left"
            prop="docClass"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span style="margin-left: 10px">{{ getClassName(scope.row.docClass) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="versionValue"
            :label="$t(`doc.this_dept_file_versions2`)"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="deptName"
            :label="$t(`doc.this_dept_staffing_dept`)"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_file_status`)"
            align="left"
            prop="status"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.standard_status" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column
            prop="startDate"
            :label="$t(`file_count.read_effective_time`)"
            align="left"
            :show-overflow-tooltip="true"
          >
          <template slot-scope="scope">
            <span>{{parseTime(scope.row.startDate,"{y}-{m}-{d}")}}</span>
          </template>
          </el-table-column>
          <el-table-column
            prop="unlearnedCount"
            :label="$t(`file_count.read_non_learners`)"
            align="left"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.num +'/'+ scope.row.total}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t(`file_count.read_training_situation`)" align="left">
            <template slot-scope="scope">
              <span>{{ scope.row.total > scope.row.num ? $t(`file_count.read_uncompleted`) : $t(`file_count.read_completed`) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            fixed="right"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
              >{{ $t(`doc.this_dept_detail`) }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <el-drawer
        :visible.sync="detailShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <read-detail ref="detail" :data="data" @close="handleCloseChange"></read-detail>
      </el-drawer>
    </div>
  </el-card>
</template>

<script>
import { readTrainPage } from '@/api/process/distribute.js'
import mixin from "@/layout/mixin/Commmon.js";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import readDetail from '@views/statistics/readCount/detail/index.vue'

export default {
  name: "readCountIndex",
  dicts: ['read_train_situation','standard_status'],
  mixins: [mixin],
  components: {
    Treeselect,
    readDetail
  },
  data() {
    return {
      form: {
      },
      queryParams: {
        queryName:null,
        tranStatus:null,
        docClass:null,
        docClassList:undefined,
        pageNum:1,
        pageSize:10
      },
      list: [],
      loading: false,
      docClassList:undefined,
      classLevelOptions: [],
      total: 0,
      data: undefined,
      detailShow: false,
    };
  },
  computed: {
    releaseTime() {
      if (!this.form.releaseTime || !this.form.releaseTime[1]) {
        return { startTime: null, endTime: null }
      }
      const [startTime, endTime] = this.form.releaseTime
      return {
        startTime: `${startTime} 00:00:00`,
        endTime: `${endTime} 23:59:59`,
      }
    },
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      if (!this.queryParams.docClass) {
        this.queryParams.docClassList = undefined
      }
      readTrainPage(this.queryParams).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表单重置
    resetForm() {
      this.queryParams.docClassList = undefined
      this.$refs["searchForm"].resetFields();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm()
      this.handleQuery()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      _this.data = row
      _this.detailShow = true
      // _this.handleDetail({type:'detail/index',docId:row.docId,versionId:row.versionId,flag:'0',status:this.queryParams.status})
    },
    getClassName(val) {
      let res = val
      this.docClassList.forEach(a => {
        if (val === a.id) {
          res = a.className
        }
      })
      return res
    },
    handleSelectNode(node, source){
      let docClassList = []
      this.getChildrenList(docClassList,node,'id')
      this[source].docClassList = docClassList
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    handleCloseChange(){
      let _this = this
      _this.detailShow = false
      _this.getList()
    }

  },
  mounted() {
    // 加载体系文件的-文件类型页签
    settingDocClassList({ classStatus: "1" }).then(
      (response) => {
        this.classLevelOptions = [];
        this.docClassList = response.rows
        this.classLevelOptions = this.handleTree(
          response.rows,
          "id",
          "parentClassId"
        );
      }
    ),
      this.resetForm()
    this.handleQuery()
  },
};
</script>
