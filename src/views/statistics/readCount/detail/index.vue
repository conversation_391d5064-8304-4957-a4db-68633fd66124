<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`file_count.read`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <div>
      <el-row type="flex" justify="space-between">
        <div>
          <el-form :model="queryParams" ref="searchForm" :inline="true" inline size="mini" label-width="68px">
            <el-form-item prop="deptId">
              <treeselect v-model.trim="queryParams.deptId" :options="deptOptions" class="input-with-select"
                          style=" width: 320px; display: inline-block;" :normalizer="normalizer" :placeholder="$t(`doc.this_dept_select_dept`)"
                          :searchable="false" size="mini" />
            </el-form-item>
            <el-form-item prop="tranStatus">
              <el-select
                v-model="queryParams.status"
                :placeholder="$t(`file_count.read_train_status`)"
                clearable
              >
                <el-option value="Y" :label="$t(`file_count.read_learned`)" />
                <el-option value="N" :label="$t(`file_count.read_unlearned`)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">
                {{ $t(`doc.this_dept_query`) }}
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetForm">
                {{ $t(`doc.this_dept_reset`) }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-row>
      <div>
        <span>{{ $t(`file_count.read_unlearned`) }}{{formData.notLearned}}{{ $t(`file_count.read_people`) }}，{{ $t(`file_count.read_learned`) }}{{formData.learned}}{{ $t(`file_count.read_people`) }}</span>
      </div>
      <el-card class="gray-card">
        <el-table header-align="left" v-loading="detailLoading" :data="dataList">
          <el-table-column
            prop="receiveUserDept"
            :label="$t(`doc.this_dept_dept`)"
            align="left"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="receiveNickName"
            :label="$t(`doc.this_dept_name`)"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`file_count.read_train_status`)"
            align="left"
            prop="status"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span style="margin-left: 10px">{{ conversionStatus(scope.row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="times"
            :label="$t(`file_count.read_time`)"
            align="left"
            :show-overflow-tooltip="true"
          />
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="init(data)"
      />
    </div>
  </div>
</template>
<script>
import { trainDetailList, trainDetailStatusList } from '@/api/process/distribute'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from "@/api/system/dept";

export default {
  name: "PrintDetail",
  props: ['data'],
  components: {
    Treeselect,
  },
  data() {
    return {
      viewId: "",
      selectInfoData: [],
      dataList: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {
        learned: 0,
        notLearned: 0,
      },
      queryParams: {
        deptId: undefined,
        status:undefined,
        versionId: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      detailLoading: false,
      selectedList: [],
      currentPage: 1,
      // 部门树选项
      deptOptions: [],
      sourceDataList: [],
      pageSize: 10 // 每页显示的数据条数
    };
  },
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    this.initDept()
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    init(row){
      let _this = this
      _this.rest()
      _this.getDetailStatus(row)
      _this.getDetail(row)
    },
    getDetail(query) {
      let _this = this
      _this.detailLoading = true
      _this.queryParams.versionId = query.versionId
      trainDetailList(_this.queryParams).then(res=>{
        this.dataList = res.rows;
        this.total = res.total;
        this.detailLoading = false;
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    getDetailStatus(query) {
      let _this = this
      _this.queryParams.versionId = query.versionId
      trainDetailStatusList(_this.queryParams).then(res=>{
        let formData= {
          learned: 0,
          notLearned: 0
        }
        res.data.forEach(item=>{
          if (item.status == 'Y'){
            formData.learned = item.total
          }else if (item.status == 'N'){
            formData.notLearned = item.total
          }
        })
        _this.formData = formData
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    rest(){
      let _this = this
    },
    close() {
      this.$emit("close")
    },
    restData(){
      return  {
        id: undefined,
        code: undefined,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    conversionStatus(value){
      if(value == 'Y'){
        return this.$t(`file_count.read_learned`);
      }else if(value == 'N'){
        return this.$t(`file_count.read_unlearned`);
      }
    },
    initDept() {
      listDept({ status: 0 }).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    handleSelectNodeDept(node,source){
      let deptIds = []
      this.getChildrenList(deptIds,node,'deptId')
      this[source].deptIds = deptIds
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.init(this.data)
    },
    // 表单重置
    resetForm() {
      this.$refs["searchForm"].resetFields();
      this.handleQuery()
    },

  },

};
</script>
