<template>
  <div class="app-container companyindex  is-always-shadow ">

    <el-tabs v-model.trim="activeName" class="out-tabs tj-tabs">
      <el-tab-pane name="1">
        <span slot="label">{{ $t(`file_count.personal_change`) }}</span>
      </el-tab-pane>
    </el-tabs>

    <el-card v-if="varChangeColor == 1">
      <div slot="header" class="clearfix"><span>{{ $t(`file_count.personal_type`) }}</span></div>
      <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="100px">
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">

              <el-form :inline="true" :model="queryParams" class="demo-form-inline">
                <el-form-item>
                  <treeselect v-model.trim="queryParams.docClass" style=" width: 300px; display: inline-block;" class="input-with-select"
                              @select="(node)=>handleSelectNode(node,'queryParams')"
                :options="classLevelOptions" :normalizer="normalizer1" :placeholder="$t(`file_count.personal_select_type`)" :searchable="false"
                size="mini" />
                </el-form-item>
                <el-form-item>
                  <treeselect v-model.trim="queryParams.deptIds" :options="deptOptions" class="input-with-select"
                  style=" width: 320px; display: inline-block;"  :normalizer="normalizer" :placeholder="$t(`doc.this_dept_select_dept`)"
                  :searchable="false" size="mini" />
                </el-form-item>
                <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" style="display: inline-block;">{{ $t(`doc.this_dept_search`) }}</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery" style="display: inline-block;">{{ $t(`doc.this_dept_reset`) }}</el-button>
                <el-button icon="el-icon-download" @click="handleExport">{{ $t(`doc.exterior_dept_export`) }}</el-button>
                </el-form-item>
              </el-form>


                <!-- <el-input placeholder="文件名称/编号/版本" class="input-with-select" v-model.trim="queryParams.searchValue">
                <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
              </el-input> -->


            </div>
            <div class="cell-right" style="width:550px;">
              <el-radio-group v-model="radio1" size="small">
                <el-radio-button label="0">{{ $t(`file_count.personal_this_month`) }}</el-radio-button>
                <el-radio-button label="1">{{ $t(`file_count.personal_half_year`) }}</el-radio-button>
                <el-radio-button label="2">{{ $t(`file_count.personal_whole_year`) }}</el-radio-button>
              </el-radio-group>
              <el-date-picker v-model="startEndDate" type="daterange" value-format="yyyy-MM-dd" range-separator="~"
                :start-placeholder="$t(`doc.this_dept_start_date`)" :end-placeholder="$t(`doc.this_dept_end_date`)" size="small">
              </el-date-picker>
            </div>
          </div>
        </div>
      </el-form>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="gray-card no-margin-top">
            <el-table v-loading="loading" :data="changeTypeList">
              <el-table-column :label="$t(`doc.this_dept_file_type`)" align="left" width="200" prop="docClass">
                <template slot-scope="scope">
                  <span style="margin-left: 10px">{{ getClassName(scope.row.docClass) }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t(`doc.this_dept_staffing_dept`)" align="left" prop="deptName" show-overflow-tooltip="true" />
              <el-table-column :label="$t(`doc.this_dept_new_add`)" align="left" prop="addNum" />
              <el-table-column :label="$t(`doc.this_dept_revision`)" align="left" prop="updateNum" />
              <el-table-column :label="$t(`doc.this_dept_cancel`)" align="left" prop="disuseNum" />
              <el-table-column :label="$t(`file_count.personal_total`)" align="left" prop="total" />
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize" @pagination="getList" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <div class="align-right">
            <treeselect v-model.trim="params.deptIds" :options="deptOptions"
              style=" width: 320px; display: inline-block; margin: 0 0 0 10px;" :normalizer="normalizer"
              :placeholder="$t(`doc.this_dept_select_dept`)" :searchable="false" size="mini" />
            <treeselect v-model.trim="params.docClass" style=" width: 300px; display: inline-block; margin: 0 0 0 10px;"
                        @select="(node)=>handleSelectNode(node,'params')"
              :options="classLevelOptions" :normalizer="normalizer1" :placeholder="$t(`file_count.personal_select_type`)" :searchable="false" size="mini" />
          </div>
          <RzPieChart style="margin-top: 60px;" height="500px" ref="chart" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { changeType, changeTypeSum } from "@/api/statistics/personal";
import RzPieChart from "../../dashboard/rz/RzPieChart";
import Treeselect from "@riophae/vue-treeselect";
import { listDept } from "@/api/system/dept";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    RzPieChart,
    Treeselect,
  },
  watch: {
    radio1: {
      handler(newVal, oldVal) {
        this.initDate(newVal)
      }
    },
    startEndDate: {
      handler(newVal) {
        if (newVal != null) {
          this.params.params.startTime = newVal[0];
          this.params.params.endTime = newVal[1];
        } else {
          this.params.params.startTime = "";
          this.params.params.endTime = "";
        }
        this.getSum()
      }
    },
    'params.deptIds': {
      handler(newVal) {
        this.getSum()
      }
    },
    'params.docClass': {
      handler(newVal) {
        if (!newVal) {
          this.params.docClassList = undefined
        }
        this.getSum()
      }
    },
  },
  data() {
    return {
      docClass: undefined,
      deptId: undefined,
      // 遮罩层
      loading: false,
      activeName: '1',
      radio1: '1',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      changeTypeList: [],
      startEndDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docClassList: undefined,
        dataType: undefined,
        deptIds: undefined,
      },
      params: {
        dataType: undefined,
        docClass: undefined,
        docClassList: undefined,
        deptIds: undefined,
        params: {
          startTime: undefined,
          endTime: undefined,
        }
      },
      // 部门树选项
      deptOptions: [],
      // 表单参数
      form: {},
      varChangeColor: 1,
      deptId1: undefined,
      qiehuan: 2,
      classLevelOptions: [],
      classLevelList: [],
      queryForm: {},
    };
  },
  created() {
    this.initDept()
    this.getSettingDocClassTreeseList()
    this.initDate(1)
    this.getList()
  },
  methods: {
    getClassName(val) {
      let res = val
      this.classLevelList.forEach(a => {
        if (val === a.id) {
          res = a.className
        }
      })
      return res
    },
    initDept() {
      listDept({ status: 0 }).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: "1"}).then(
        (response) => {
          this.classLevelOptions = [];
          response.rows.forEach((element, index) => {
            element.children = [];
            if (element.parentClassId === undefined || element.parentClassId === '0') {
              element.parentClassId = element.dataType
            }
          });
          let list = response.rows
          this.classLevelList = JSON.parse(JSON.stringify(list))
          // list.push({ id: 'project', className: '项目文件',dataType: 'project'})
          list.push({ id: 'stdd', className: '体系文件',dataType: 'stdd'})
          this.classLevelOptions = this.handleTree(
            list,
            "id",
            "parentClassId"
          );
        }
      );
    },
    initDate(int) {
      if (int == 0) {
        let start = new Date()
        start.setDate(1)
        let end = new Date()
        this.startEndDate = [start, end]
      } else if (int == 1) {
        let start = new Date()
        start.setMonth(start.getMonth() - 6)
        let end = new Date()
        this.startEndDate = [start, end]
      } else {
        let start = new Date()
        start.setMonth(0, 1)
        let end = new Date()
        this.startEndDate = [start, end]
      }
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      if (!this.queryParams.docClass) {
        this.queryParams.docClassList = undefined
      }
      changeType(this.queryParams).then((response) => {
        this.changeTypeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getSum() {
      changeTypeSum(this.params).then((response) => {
        this.$refs.chart.initChart(response.data)
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryForm = { ...this.queryParams }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.searchValue = undefined;
      this.queryParams.docClass = undefined;
      this.queryParams.docClassList = undefined;
      this.queryParams.deptIds = undefined;
      this.handleQuery();
    },
    /** 导出数据 */
    handleExport() {
      this.download('/process/doc-statistics/export-change-type', this.queryForm, `文件变更统计_${new Date().getTime()}.xlsx`)
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    /** 转换菜单数据结构 */
    normalizer1(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
        dataType: node.dataType
      };
    },
    handleSelectNode(node, source){
      let docClassList = []
      this.getChildrenList(docClassList,node,'id')
      this[source].docClassList = docClassList
      this[source].dataType = node.dataType
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    }
  },
};
</script>
<style lang="scss" scoped>

.typetext {
  align-self: flex-start;
  box-sizing: border-box;
  width: 100%;
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}

.rz-top {
  margin-top: 5px;
  margin-left: 5px;
}

.switch {
  color: #409eff;
}

.personal {
  .time {
    display: flex;
    justify-content: flex-end;
  }

  .qiehuan {
    background-color: #409eff;
    border-radius: 5px;
    display: flex;
    align-items: center;

    span {
      padding: 0 5px;
      color: #fff;
    }
  }

  .cursor {
    display: flex;
    cursor: pointer;
  }

  .timeqiehuan {
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-right: 10px;
    width: 120px;
    justify-content: space-between;
  }

  .saixuan {
    display: flex;

    margin: 5px;
    justify-content: space-between;

    div {
      margin-right: 10px;
    }
  }
}
</style>
