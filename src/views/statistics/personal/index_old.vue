<template>
  <div class="app-container companyindex  is-always-shadow ">
    
    <el-tabs v-model.trim="activeName" class="out-tabs tj-tabs" @tab-click="tabClick">
      <el-tab-pane name="1" >
        <span slot="label"> 文件变更统计</span>
      </el-tab-pane>
      <el-tab-pane name="2" >
        <span slot="label"> 文件回收统计</span>
      </el-tab-pane>
      <el-tab-pane name="3" >
        <span slot="label"> 签收回收率统计</span>
      </el-tab-pane>
    </el-tabs>

    <el-card v-if="varChangeColor == 1">
      <div slot="header" class="clearfix"><span>文件类型统计占比</span></div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            v-show="showSearch"
            label-width="100px" >
            <div class="global-ser" id="add">
              <div class="ser-top">
                <div class="cell-left">
                        <el-input placeholder="文件名称/编号/版本" class="input-with-select" v-model.trim="queryParams.postCode">
                            <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
                        </el-input>
                        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                    </div>
                    <div class="cell-right" style="width:550px;">
                        <el-radio-group v-model="radio1" size="small">
                          <el-radio-button label="本月"></el-radio-button>
                          <el-radio-button label="半年"></el-radio-button>
                          <el-radio-button label="全年"></el-radio-button>
                        </el-radio-group>
                        <el-date-picker
                          v-model.trim="value1"
                          placeholder="选择日期"
                          size="small" >
                        </el-date-picker>
                    </div>
              </div>
            </div>
          </el-form>
          <el-row :gutter="20">
            <el-col :span="12">
                <el-card class="gray-card no-margin-top">
                  <el-table
                    v-loading="loading"
                    :data="changeTypeList"
                    @selection-change="handleSelectionChange" >
                    <el-table-column
                      label="文件类型"
                      align="left"
                      width="200"
                      prop="docClass"  />
                    <el-table-column label="文件类型" align="left" prop="deptId" />
                    <el-table-column label="新增" align="left" prop="addNum" />
                    <el-table-column label="修订" align="left" prop="updateNum" />
                    <el-table-column label="作废" align="left" prop="disuseNum" />
                    <el-table-column label="总计" align="left" prop="total" />
                  </el-table>
                  <pagination
                    v-show="total > 0"
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getList" />
                </el-card>
            </el-col>
            <rz-drawer
                :with-header="false"
                @changeDrawer="changeDrawer"
                :task="taskFormData"
                :drawer="drawer" >
                <detailsIndex></detailsIndex>
              </rz-drawer>
            <el-col :span="12">
             <div class="align-right">
                    <treeselect
                      v-model.trim="deptid"
                      :options="deptOptions"
                      style=" width: 170px; display: inline-block; margin: 0 0 0 10px;"
                      :normalizer="normalizer"
                      placeholder="选择部门"
                      :searchable="false"
                      size="mini" />
                
                    <treeselect
                      v-model.trim="classLevelid"
                      style=" width: 150px; display: inline-block; margin: 0 0 0 10px;"
                      :options="classLevelOptions"
                      :normalizer="normalizer1"
                      placeholder="选择类型"
                      :searchable="false"
                      size="mini"
                    />
                
             </div>
                <RzPieChart />
            </el-col>
          </el-row>
    </el-card>
      <el-card v-if="varChangeColor == 1">
      <div slot="header" class="clearfix"><span>变更要素统计占比</span></div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            v-show="showSearch"
            label-width="100px" >
            <div class="global-ser" id="add">
              <div class="ser-top">
                <div class="cell-left">
                        <el-input placeholder="文件名称/编号/版本" class="input-with-select" v-model.trim="queryParams.postCode">
                            <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
                        </el-input>
                        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                    </div>
                    <div class="cell-right" style="width:550px;">
                        <el-radio-group v-model="radio1" size="small">
                          <el-radio-button label="本月"></el-radio-button>
                          <el-radio-button label="半年"></el-radio-button>
                          <el-radio-button label="全年"></el-radio-button>
                        </el-radio-group>
                        <el-date-picker
                          v-model.trim="value1"
                          placeholder="选择日期"
                          size="small" >
                        </el-date-picker>
                    </div>
              </div>
            </div>
          </el-form>
          <el-row :gutter="20">
            <el-col :span="12">
                <el-card class="gray-card no-margin-top">
                  <el-table
            v-loading="loading"
            :data="changeFactorList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column label="文件类型" align="left" prop="docClass" />

            <el-table-column
              label="编制部门"
              align="left"
              prop="deptId"
              width="90"
            />
            <el-table-column label="人" align="left" prop="personNum" width="50" />
            <el-table-column label="机" align="left" prop="machineNum" width="50" />

            <el-table-column label="法" align="left" prop="lawNum" width="50" />
            <el-table-column label="料" align="left" prop="materialNum" width="50" />
            <el-table-column label="环" align="left" prop="linkNum" width="50" />
            <el-table-column label="测" align="left" prop="testNum" width="50" />
            <el-table-column label="总计" align="left" prop="total" />
          </el-table>
                  <pagination
                    v-show="total > 0"
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getList" />
                </el-card>
            </el-col>
            <rz-drawer
                :with-header="false"
                @changeDrawer="changeDrawer"
                :task="taskFormData"
                :drawer="drawer" >
                <detailsIndex></detailsIndex>
              </rz-drawer>
            <el-col :span="12">
              <div class="align-right">
                    <treeselect
                      v-model.trim="deptid"
                      :options="deptOptions"
                      style=" width: 170px; display: inline-block; margin: 0 0 0 10px;"
                      :normalizer="normalizer"
                      placeholder="选择部门"
                      :searchable="false"
                      size="mini" />
                
                    <treeselect
                      v-model.trim="classLevelid"
                      style=" width: 150px; display: inline-block; margin: 0 0 0 10px;"
                      :options="classLevelOptions"
                      :normalizer="normalizer1"
                      placeholder="选择类型"
                      :searchable="false"
                      size="mini"
                    />
                
             </div>
                <RzPieChart />
            </el-col>
          </el-row>
    </el-card>

    <el-card v-if="varChangeColor == 2" >
      <el-form label-width="100px" class="demo-form-inline">
            <div class="global-ser" id="add">
                <div class="ser-top">
                    <div class="cell-left">
                        <el-input placeholder="文件名称/编号/版本" class="input-with-select">
                            <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
                        </el-input>
                        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                    </div>
                </div>
            </div><!---global-ser 全局搜索---->
        </el-form><!----更多筛选---->
      <el-card class="gray-card no-margin-top">
      <el-table
        v-loading="loading"
        :data="postList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="文件类型" align="left" width="200" prop="p1" />
        <el-table-column label="文件名称" align="left" prop="p2" />
        <el-table-column label="文件编号" align="left" prop="p2" />
        <el-table-column label="文件版本" align="left" prop="p2" />
        <el-table-column label="编制部门" align="left" prop="p2" />
        <el-table-column label="分发部门数" align="left" prop="p3" />
        <el-table-column label="生效日期" align="left" prop="p4" 
        >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.p4, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
        <el-table-column label="失效日期" align="left" prop="p5" 
        >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.p5, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      </el-table>
      </el-card>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <el-card v-if="varChangeColor == 3" style="padding: 10px">
      <el-form label-width="100px" class="demo-form-inline">
            <div class="global-ser" id="add">
                <div class="ser-top">
                    <div class="cell-left">
                        <el-radio-group v-model="radio1" size="small">
                          <el-radio-button label="本月"></el-radio-button>
                          <el-radio-button label="半年"></el-radio-button>
                          <el-radio-button label="全年"></el-radio-button>
                        </el-radio-group>
                        <el-form-item label="签收时间段">
                            <el-date-picker v-model="value1" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="回收时间段">
                            <el-date-picker v-model="value1" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                    </div>
                </div>
            </div><!---global-ser 全局搜索---->
        </el-form><!----更多筛选---->
      <RzCategory width="100%"/>
    </el-card>
  </div>
</template>

<script>
import { listDept } from "@/api/system/dept";
import { changeFactor,changeType } from "@/api/statistics/personal";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import RzPieChart from "../../dashboard/rz/RzPieChart";
import RzCategory from "../../dashboard/rz/RzCategory";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// import rzDrawer from "../../components/rz-drawer";
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    RzPieChart,
    Treeselect,
    RzCategory,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      activeName:'1',
      radio1:'',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      changeTypeList:[],
      changeFactorList:[],
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawer: false,
      value1: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined,
      },
      // 部门树选项
      deptOptions: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: "不能为空", trigger: "blur" }],
        postCode: [{ required: true, message: "不能为空", trigger: "blur" }],
        postSort: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      taskFormData: {},
      varChangeColor: 1,
      linkTypeTab: [
        { id: 1, className: "文件变更统计" },
        { id: 2, className: "文件回收统计" },
        { id: 3, className: "签收回收率统计" },
      ],
      deptid: undefined,
      classLevelid: undefined,
      qiehuan: 2,
      classLevelOptions: [],
    };
  },
  created() {
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      changeType(this.queryParams).then((response) => {
        this.changeTypeList = response.data;
        this.total = response.total;
        this.loading = false;
      });
      changeFactor(this.queryParams).then((response) => {
        this.changeFactorList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增";
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      const postId = row.postId || this.ids;
      this.title = "详情";
      this.drawer = true;
      // getPost(postId).then((response) => {
      //   this.form = response.data;
      //   this.drawer = true;
      //   this.title = "详情";
      // });
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    state(index) {
      this.varChangeColor = index;
    },
    tabClick(val){
  
      this.varChangeColor =this.activeName;
    },

    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    /** 转换菜单数据结构 */
    normalizer1(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
  },
};
</script>
<style lang="scss" >
@import "../../../../public/css/poctstyle.css";
.typetext {
  align-self: flex-start;
  box-sizing: border-box;
  width: 100%;
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
.rz-top {
  margin-top: 5px;
  margin-left: 5px;
}
.switch {
  color: #409eff;
}
.personal {
  .time {
    display: flex;
    justify-content: flex-end;
  }
  .qiehuan {
    background-color: #409eff;
    border-radius: 5px;
    display: flex;
    align-items: center;
    span {
      padding: 0 5px;
      color: #fff;
    }
  }
  .cursor {
    display: flex;
    cursor: pointer;
  }
  .timeqiehuan {
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-right: 10px;
    width: 120px;
    justify-content: space-between;
  }
  .saixuan {
    display: flex;

    margin: 5px;
    justify-content: space-between;
    div {
      margin-right: 10px;
    }
  }
}
</style>
