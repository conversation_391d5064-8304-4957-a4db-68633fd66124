<template>
  <!--为echarts准备一个具备大小的容器dom-->
  <div>
    <div id="echartsLine" style="width:100%;height: 400px;"></div>
  </div>
</template>

<script>
import echarts from 'echarts'

export default {
  name: "EchartsLine",
  data() {
    return {
    }
  },
  watch: {},
  created() {

  },
  methods: {
    
    drawLine(id) {

        var chartDom = document.getElementById('echartsLine');
        var myChart = echarts.init(chartDom);
        var option;
        option = {
        title: {
            text: ''
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
            type: 'shadow'
            }
        },
        legend: {},
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisLine:{
                show:false
              },
              axisTick:{       //y轴刻度线
                show:false
              },
            boundaryGap: [0, 0.01]
        },
        yAxis: {
            type: 'category',
            axisLine:{
                show:false
              },
              axisTick:{       //y轴刻度线
                show:false
              },
            data: ['xxx文件1.0', 'xxx文件1.0', 'xxx文件1.0', 'xxx文件1.0', 'xxx文件1.0', 'xxx文件1.0']
        },
        series: [
            {
            name: '2011',
            type: 'bar',
            label: {
              show: true,
              position: 'right'
            },
            itemStyle: {
              color: "#90B3F0",
            },
            data: [13, 22, 31, 2, 33, 65]
            }
        ]
        };

        option && myChart.setOption(option);


    }
  },
  //调用
  mounted() {
    this.$nextTick(function () {
      this.drawLine('echartsLine')
    })
  }
};
</script>
<style>
.cell-tjtext i {
  margin-right: 10px;
  margin-left: 10px;
}
</style>
