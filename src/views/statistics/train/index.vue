<template>
  <el-card class="app-container document_changeindex">
    <div slot="header" class="clearfix">
      <span> {{ $t(`file_count.train_ledger`) }} </span>
    </div>
    <div>
      <el-row type="flex" justify="space-between">
        <div>
          <el-form :model="form" inline size="mini" label-width="68px">
            <el-form-item>
              <el-input
                v-model.trim="form.docName"
                :placeholder="$t(`doc.this_dept_file_name`)"
                clearable
                width="210px"
                @keyup.enter.native="handleQuery"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="form.releaseTime"
                type="daterange"
                :range-separator="$t(`doc.this_dept_to`)"
                :start-placeholder="$t(`doc.this_dept_start_date`)"
                :end-placeholder="$t(`doc.this_dept_end_date`)"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                unlink-panels
                align="right"
                size="small"
                clearable
                @change="handleQuery"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model.trim="form.nickName"
                :placeholder="$t(`doc.this_dept_staffs`)"
                clearable
                @keyup.enter.native="handleQuery"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="form.trained"
                :placeholder="$t(`file_count.train_whether_complete`)"
                clearable
                @change="handleQuery"
              >
                <el-option :value="true" :label="$t(`doc.this_dept_yes`)" />
                <el-option :value="false" :label="$t(`doc.this_dept_no`)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">
                {{ $t(`doc.this_dept_query`) }}
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">
                {{ $t(`doc.this_dept_reset`) }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <div>
          <el-button @click="handleExport">{{ $t(`doc.exterior_dept_export`) }}</el-button>
        </div>
      </el-row>
      <el-card class="gray-card">
        <el-table header-align="left" v-loading="loading" :data="list">
          <el-table-column
            prop="index"
            :label="$t(`file_handle.recovery_num`)"
            align="center"
            width="50"
          >
          </el-table-column>
          <el-table-column
            prop="releaseTime"
            :label="$t(`doc.this_dept_release_time`)"
            align="left"
            width="120">
            <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
                </template>
          </el-table-column>
          <el-table-column
            prop="nickName"
            :label="$t(`doc.this_dept_staffs`)"
            align="left"
            width="180"
          >
          </el-table-column>
          <el-table-column
            prop="docName"
            :label="$t(`doc.this_dept_file_name`)"
            align="left"
            min-width="150"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="docId"
            :label="$t(`doc.this_dept_file_code`)"
            align="left"
            min-width="150"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="versionValue"
            :label="$t(`doc.this_dept_file_versions2`)"
            align="left"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column :label="$t(`file_count.train_whether_complete`)" align="left" width="140">
            <template slot-scope="scope">
              <span>{{ scope.row.trained ? $t(`doc.this_dept_yes`) : $t(`doc.this_dept_no`) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </el-card>
</template>

<script>
import { training } from '@/api/statistics/personal'
import mixin from "@/layout/mixin/Commmon.js";
import { parseTime } from "@/utils/ruoyi"
const initForm = {
  docName: '',
  releaseTime: [],
  nickName: '',
  trained: null,
  pageNum: 1,
  pageSize: 10,
}

export default {
  name: "TrainingStatistics",
  mixins: [mixin],
  data() {
    return {
      form: {},
      queryParams: {},
      list: [],
      loading: false,
    };
  },
  computed: {
    releaseTime() {
      if (!this.form.releaseTime || !this.form.releaseTime[1]) {
        return { startTime: null, endTime: null }
      }
      const [startTime, endTime] = this.form.releaseTime
      return {
        startTime: `${startTime} 00:00:00`,
        endTime: `${endTime} 23:59:59`,
      }
    },
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      training(this.queryParams)
        .then(({ data }) => data)
        .then(({ rows, total }) => {
          this.list = rows;
          this.total = total;
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 表单重置
    resetForm() {
      this.form = { ...initForm }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm()
      this.handleQuery()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams = {
        ...this.form,
        releaseTimeStart: this.releaseTime.startTime,
        releaseTimeEnd: this.releaseTime.endTime,
      }
      console.debug(this.form, this.queryParams)
      this.getList();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/process/doc-statistics/export-training",
        this.queryParams,
        `文件培训台账_${new Date().getTime()}.xlsx`
      );
    },
  },
  mounted() {
    this.resetForm()
    this.handleQuery()
  },
};
</script>
