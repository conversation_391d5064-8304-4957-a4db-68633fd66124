<template>
  <div class="app-container companyindex el-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="tabClick">
          <el-tab-pane name="1" >
            <span slot="label"> 进行中的流程</span
            >
          </el-tab-pane>
          <el-tab-pane name="2" >
            <span slot="label"> 已办结流程</span>
          </el-tab-pane>
          <el-tab-pane name="3" >
            <span slot="label"> 使用及环节耗时统计</span>
          </el-tab-pane>
        </el-tabs>
    <div v-if="activeName == 1">
    <el-form
      
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-row type="flex">
        <el-col :span="12">
          <el-form-item label="" prop="postCode">
            <el-input
              v-model.trim="queryParams.postCode"
              placeholder="输入关键字搜索"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh"  @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <!-- <el-button plain @click="handleAdd1()">变更</el-button> -->

          <!-- <el-button icon="el-icon-more" plain></el-button> -->
        </el-col>
      </el-row>
    </el-form>
    <el-card class="gray-card no-margin-top">
    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
     
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文件名称" align="center" width="200" prop="p3">
        <template slot-scope="scope">
          <span class="wenjcolor">{{ scope.row.p3 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件编号" align="center" prop="p3_1" />
      <el-table-column label="文件版本" align="center" prop="p4" />
      <el-table-column label="文件类型" align="center" prop="p1" />

      <el-table-column label="编制部门" align="center" prop="p6" />
      <el-table-column label="阅知用户" align="center" prop="p7" />
      <el-table-column label="所属部门" align="center" prop="p6" />

      
      <el-table-column label="累计阅读次数" align="center" prop="p10" />

      <!-- <el-table-column
        label="接收时间"
        align="center"
        prop="p8"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetails(scope.row)"
            >详情</el-button
          >
         
        </template>
      </el-table-column> -->
    </el-table>
    </el-card>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    </div>

    <div v-if="activeName == 2">
    <el-form
      
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-row type="flex">
        <el-col :span="12">
          <el-form-item label="" prop="postCode">
            <el-input
              v-model.trim="queryParams.postCode"
              placeholder="输入关键字搜索"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh"  @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <!-- <el-button plain @click="handleAdd1()">变更</el-button> -->

          <!-- <el-button icon="el-icon-more" plain></el-button> -->
        </el-col>
      </el-row>
    </el-form>
    <el-card class="gray-card no-margin-top">
    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
     
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文件名称" align="center" width="200" prop="p3">
        <template slot-scope="scope">
          <span class="wenjcolor">{{ scope.row.p3 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件编号" align="center" prop="p3_1" />
      <el-table-column label="文件版本" align="center" prop="p4" />
      <el-table-column label="文件类型" align="center" prop="p1" />

      <el-table-column label="编制部门" align="center" prop="p6" />
      <el-table-column label="阅知用户" align="center" prop="p7" />
      <el-table-column label="所属部门" align="center" prop="p6" />

      
      <el-table-column label="累计阅读次数" align="center" prop="p10" />

      <!-- <el-table-column
        label="接收时间"
        align="center"
        prop="p8"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetails(scope.row)"
            >详情</el-button
          >
         
        </template>
      </el-table-column> -->
    </el-table>
    </el-card>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    </div>

    <div v-if="activeName == 3">
      <el-form label-width="100px" class="demo-form-inline">
            <div class="global-ser" id="add">
                <div class="ser-top">
                    <div class="cell-left">
                        <el-radio-group v-model="radio1" size="small">
                          <el-radio-button label="本月"></el-radio-button>
                          <el-radio-button label="半年"></el-radio-button>
                          <el-radio-button label="全年"></el-radio-button>
                        </el-radio-group>
                        <el-form-item label="">
                            <el-date-picker v-model="value1" type="daterange"
                              style='position:relative;top:-12px'
                             range-separator="至" start-placeholder="开始日期"
                              end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="">
                          <treeselect
                            v-model.trim="deptid"
                            :options="deptOptions"
                            :normalizer="normalizer"
                            style="width:170px"
                            placeholder="选择部门"
                            :searchable="false"
                            size="mini" />
                        </el-form-item>
                        <el-form-item label="">
                          <treeselect
                              v-model.trim="classLevelid"
                              style="width:170px"
                              :options="classLevelOptions"
                              :normalizer="normalizer1"
                              placeholder="选择类型"
                              :searchable="false"
                              size="mini"
                            />
                        </el-form-item>
                        
                    </div>
                </div>
            </div><!---global-ser 全局搜索---->
        </el-form><!----更多筛选---->
      <RzCategory width="100%"/>
    </div>

 
    <!-- 详情-->
    <rz-drawer
      :with-header="false"
      @changeDrawer="changeDrawer"
      :task="taskFormData"
      :drawer="drawer"
    >
      <detailsIndex></detailsIndex>
    </rz-drawer>
        </div>
  </div>
</template>

<script>
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
import RzCategory from "../../dashboard/rz/RzCategory";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import { listDept } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    Treeselect,
    RzCategory,
  },
  data() {
    return {
      classLevelOptions: [],
      deptOptions: [],
      activeName:'1',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawer: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: "不能为空", trigger: "blur" }],
        postCode: [{ required: true, message: "不能为空", trigger: "blur" }],
        postSort: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      taskFormData: {},
      varChangeColor: 1,
    };
  },
  created() {
   
  },
  methods: {
    normalizer1(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    tabClick(val){
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listPost(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.postList = [
          {
            p1: "新增",
            p2: "生效中",
            p3: "湖南分公司生产管理规范V",
            p3_1: "HN-20211023",
            p4: "V1.1",
            p5: "管理规范",
            p6: "生产质量部",
            p7: "张xx",
            p8: "2020-2-01 09:00",
            p9: "2020-3-01 ",
            p10: "1",
          },
          {
            p1: "新增",
            p2: "生效中",
            p3: "湖南分公司生产管理规范V",
            p3_1: "HN-20211023",
            p4: "V1.1",
            p5: "管理规范",
            p6: "生产质量部",
            p7: "张xx",
            p8: "2020-2-01 09:00",
            p9: "2020-3-01 ",
            p10: "1",
          },
          {
            p1: "新增",
            p2: "已失效",
            p3: "湖南分公司生产管理规范V",
            p3_1: "HN-20211023",
            p4: "V1.1",
            p5: "管理规范",
            p6: "生产质量部",
            p7: "张xx",
            p8: "2020-2-01 09:00",
            p9: "2020-3-01 ",
            p10: "11",
          },
          {
            p1: "新增",
            p2: "已失效",
            p3: "湖南分公司生产管理规范V",
            p3_1: "HN-20211023",
            p4: "V1.1",
            p5: "管理规范",
            p6: "生产质量部",
            p7: "张xx",
            p8: "2020-2-01 09:00",
            p9: "2020-3-01 ",
            p10: "3",
          },
        ];
        //console.log(this.postList);
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增";
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      const postId = row.postId || this.ids;
      this.title = "详情";
      this.drawer = true;
      // getPost(postId).then((response) => {
      //   this.form = response.data;
      //   this.drawer = true;
      //   this.title = "详情";
      // });
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    state(index) {
      this.varChangeColor = index;
    },
  
  },
};
</script>
<style lang="scss" >
@import "../../../../public/css/poctstyle.css";
</style>
