<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      v-show="showSearch"
      :label-width="columnLangSizeFlag ? '128px' : '68px'"
      style="display: flex; flex-wrap: wrap"
    >
      <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
        <div class="ser-top">
          <div class="cell-left">
            <el-form-item :label="$t('sys_mgr_log.operlog_module')" prop="title">
              <el-input
                v-model.trim="queryParams.title"
                :placeholder="$t('sys_mgr_log.operlog_input_module')"
                clearable
                style="width: 240px"
                size="small"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="$t('sys_mgr.notice_operator')" prop="operName">
              <el-select
                v-model.trim="queryParams.operName"
                filterable
                :placeholder="$t('sys_mgr.notice_operator')"
                clearable
                size="small"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in userList"
                  :key="dict.userName"
                  :label="dict.userName"
                  :value="dict.userName"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('sys_mgr_log.operlog_type')" prop="businessType">
              <el-select
                v-model.trim="queryParams.businessType"
                :placeholder="$t('sys_mgr_log.operlog_type')"
                clearable
                size="small"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.sys_oper_type"
                  :key="dict.value"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('sys_mgr_log.operlog_status')" prop="status">
              <el-select
                v-model.trim="queryParams.status"
                :placeholder="$t('sys_mgr_log.operlog_status')"
                clearable
                size="small"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.sys_common_status"
                  :key="dict.value"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('sys_mgr_log.operlog_time')">
              <el-date-picker
                v-model.trim="dateRange"
                size="small"
                style="width: 240px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="$t('doc.this_dept_start_date')"
                :end-placeholder="$t('doc.this_dept_end_date')"
              ></el-date-picker>
            </el-form-item>
            <el-form-item :label="$t('doc.this_dept_change_type')" prop="changeType">
              <el-select
                v-model.trim="queryParams.changeType"
                :placeholder="$t('doc.this_dept_change_type')"
                clearable
                size="small"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.change_type"
                  :key="dict.value"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="cell-right">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >{{ $t('doc.this_dept_search') }}</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
            >{{ $t('myItem.handle_reset') }}</el-button
            >
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['monitor:operlog:export']"
            >{{ $t('doc.exterior_dept_export') }}</el-button
            >
          </div>
        </div>
      </div>
    </el-form>

    <el-table
      ref="tables"
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('sys_mgr_log.operlog_num')" align="center" prop="operId" />
      <el-table-column :label="$t('sys_mgr_log.operlog_module')" align="center" prop="title" />
      <el-table-column :label="$t('sys_mgr_log.operlog_type')" align="center" prop="businessType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_oper_type"
            :value="scope.row.businessType"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('doc.this_dept_change_type')" align="center" prop="changeType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.change_type"
            :value="scope.row.changeType"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('sys_mgr_log.operlog_req')" align="center" prop="requestMethod" />
      <el-table-column
        :label="$t('sys_mgr.notice_operator')"
        align="center"
        prop="operName"
        width="100"
        :show-overflow-tooltip="true"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column
        :label="$t('sys_mgr_log.operlog_url')"
        align="center"
        prop="operIp"
        width="130"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column
        label="MAC"
        align="center"
        prop="mac"
        width="130"
        :show-overflow-tooltip="true"
      /> -->
      <el-table-column
        :label="$t('sys_mgr_log.operlog_address')"
        align="center"
        prop="operLocation"
        :show-overflow-tooltip="true"
      />
      <el-table-column :label="$t('sys_mgr_log.operlog_status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_common_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('sys_mgr_log.operlog_time')"
        align="center"
        prop="operTime"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
              parseTime(scope.row.operTime, "{y}-{m}-{d} {h}:{i}")
            }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('myItem.msg_operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleView(scope.row, scope.index)"
            v-hasPermi="['monitor:operlog:query']"
          >{{ $t('doc.this_dept_detail') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 操作日志详细 -->
    <el-dialog
      :title="$t('sys_mgr_log.operlog_detail')"
      :visible.sync="open"
      width="700px"
      append-to-body
    >
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_op_module'))"
            >{{ form.title }} / {{ typeFormat(form) }}</el-form-item
            >
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_login_msg'))"
            >{{ form.operName }} / {{ form.operIp }} /
              {{ form.operLocation }}</el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_req_url'))">{{ form.operUrl }}</el-form-item>
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_req'))">{{
                form.requestMethod
              }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_method'))">{{ form.method }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_req_param'))">{{ form.operParam }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_return_param'))">{{
                form.jsonResult
              }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_status'))">
              <div v-if="form.status === 0">{{ $t('sys_mgr.user_normal') }}</div>
              <div v-else-if="form.status === 1">{{ $t('sys_mgr_log.operlog_fail') }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_time'))">{{
                parseTime(form.operTime)
                  ? parseTime(form.operTime).substring(0, 16)
                  : ""
              }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('sys_mgr_log.operlog_err_msg'))" v-if="form.status === 1">{{
                form.errorMsg
              }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">{{ $t('sys_mgr_log.operlog_close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { list, delOperlog, cleanOperlog } from "@/api/monitor/operlog";
import { listUser } from "@/api/system/user";
export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status", "change_type","apply_type"],
  data() {
    return {
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: { prop: "operTime", order: "descending" },
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        operName: undefined,
        businessType: undefined,
        status: undefined,
        apply_type: undefined,
      },
      userList: [],
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
    listUser({ pageNum: 1, pageSize: 100 }).then((response) => {
      this.userList = response.rows;
    });
  },
  methods: {
    /** 查询登录日志 */
    getList() {
      this.loading = true;
      list(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.list = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 操作日志类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(
        this.dict.type.sys_oper_type,
        row.businessType
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order);
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.operId);
      this.multiple = !selection.length;
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const operIds = row.operId || this.ids;
      this.$modal
        .confirm(this.$t('sys_mgr_log.operlog_text') + operIds + this.$t('file_set.signature_text1'))
        .then(function () {
          return delOperlog(operIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$modal
        .confirm(this.$t('sys_mgr_log.operlog_text1'))
        .then(function () {
          return cleanOperlog();
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('sys_mgr_log.operlog_clear_succ'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "monitor/operlog/export",
        {
          ...this.queryParams,
        },
        `operlog_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

