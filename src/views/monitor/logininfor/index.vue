<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      v-show="showSearch"
      :label-width="columnLangSizeFlag ? '128px' : '68px'"
      style="display: flex; flex-wrap: wrap"
    >
      <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
        <div class="ser-top">
          <div class="cell-left">
            <el-form-item :label="$t('sys_mgr_log.logininfor_url')" prop="ipaddr">
              <el-input
                v-model.trim="queryParams.ipaddr"
                :placeholder="$t('sys_mgr_log.logininfor_input_url')"
                clearable
                style="width: 240px"
                size="small"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="$t('sys_mgr.user_name')" prop="userName">
              <el-input
                v-model.trim="queryParams.userName"
                :placeholder="$t('sys_mgr.user_input_name')"
                clearable
                style="width: 240px"
                size="small"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="$t('doc.this_dept_status')" prop="status">
              <el-select
                v-model.trim="queryParams.status"
                :placeholder="$t('sys_mgr_log.logininfor_status')"
                clearable
                size="small"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.sys_common_status"
                  :key="dict.value"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('sys_mgr_log.logininfor_time')">
              <el-date-picker
                v-model.trim="dateRange"
                size="small"
                style="width: 240px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="$t('doc.this_dept_start_date')"
                :end-placeholder="$t('doc.this_dept_end_date')"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div class="cell-right">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >{{ $t('doc.this_dept_search') }}</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
            >{{ $t('myItem.handle_reset') }}</el-button
            >
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['monitor:logininfor:export']"
            >{{ $t('doc.exterior_dept_export') }}</el-button
            >
          </div>
        </div>
      </div>
    </el-form>

    <el-table
      ref="tables"
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('sys_mgr_log.logininfor_access_num')" align="center" prop="infoId" />
      <el-table-column
        :label="$t('sys_mgr.user_name')"
        align="center"
        prop="userName"
        :show-overflow-tooltip="true"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column
        :label="$t('sys_mgr_log.logininfor_url')"
        align="center"
        prop="ipaddr"
        width="130"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="MAC"
        align="center"
        prop="MAC"
        width="130"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('sys_mgr_log.logininfor_address')"
        align="center"
        prop="loginLocation"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('sys_mgr_log.logininfor_browser')"
        align="center"
        prop="browser"
        :show-overflow-tooltip="true"
      />
      <el-table-column :label="$t('sys_mgr_log.logininfor_operating_sys')" align="center" prop="os" />
      <el-table-column :label="$t('sys_mgr_log.logininfor_status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_common_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('doc.this_dept_operation_msg')" align="center" prop="msg" />
      <el-table-column
        :label="$t('sys_mgr_log.logininfor_time')"
        align="center"
        prop="loginTime"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.loginTime, "{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { list, delLogininfor, cleanLogininfor } from "@/api/monitor/logininfor";

export default {
  name: "Logininfor",
  dicts: ["sys_common_status"],
  data() {
    return {
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: { prop: "loginTime", order: "descending" },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ipaddr: undefined,
        userName: undefined,
        status: undefined,
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询登录日志列表 */
    getList() {
      this.loading = true;
      list(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.list = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order);
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.infoId);
      this.multiple = !selection.length;
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const infoIds = row.infoId || this.ids;
      this.$modal
        .confirm(this.$t('sys_mgr_log.logininfor_text') + infoIds + this.$t('file_set.signature_text1'))
        .then(function () {
          return delLogininfor(infoIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$modal
        .confirm(this.$t('sys_mgr_log.logininfor_text1'))
        .then(function () {
          return cleanLogininfor();
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('sys_mgr_log.operlog_clear_succ'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "monitor/logininfor/export",
        {
          ...this.queryParams,
        },
        `logininfor_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
