<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm" v-show="showSearch" :label-width="columnLangSizeFlag ? '88px' : '68px'">
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-form-item  prop="docClass">
                <treeselect
                  v-model.trim="queryParams.docClass"
                  :options="docClassTree"
                  @select="handleSelectNode"
                  :normalizer="normalizerFile"
                  :placeholder="$t(`doc.this_dept_select_type`)"
                  style="width: 200px"
                  :searchable="false"
                  :clearable="false"
                />
              </el-form-item>
              <el-form-item >
                <el-input
                  v-model.trim="queryParams.docName"
                  :placeholder="$t(`doc.this_dept_name_select`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model.trim="queryParams.docId"
                  :placeholder="$t(`file_handle.change_enter_file_number`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search"  type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>

      <el-card class="gray-card">
        <el-table
          border
          v-loading="loading"
          :data="postList"
          @sort-change="sortChange"
          @selection-change="handleSelectionChange"
          :row-class-name="tableRowClassName"
          ref="multipleTable"
          header-align="left"
        >
          <template>
            <el-table-column
              :label="$t(`doc.this_dept_file_type`)"
              align="left"
              :formatter="formatterDocClass"
              prop="docClass"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_file_code`)"
              sortable="custom"
              align="left"
              prop="docId"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.docId }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_file_name`)"
              align="left"
              sortable="custom"
              prop="docName"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_file_version`)"
              align="left"
              prop="versionValue"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_dept_release_time`)"
              align="left"
              sortable="custom"
              prop="releaseTime"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            width="95"
            fixed="right"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
                >{{$t(`doc.this_dept_detail`)}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <as-pre-view
        :visible="viewShow"
        :id="viewId"
        ref="viewRef"
        @close="close"
      >
      </as-pre-view>
      <el-drawer
        :visible.sync="drawerShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <print-detail ref="detail"  :data="data" @close="handleCloseChange"></print-detail>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listVersion,
} from "@/api/document_account/version";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import mixin from "@/layout/mixin/Commmon.js";
import PrintDetail from '@views/file_processing/unlimitedPrint/detail/index.vue'


export default {
  name: "NnlimitedPrint",
  components: {
    Treeselect,
    PrintDetail
  },
  mixins: [mixin],
  data() {
    return {
      classType: '',
      docClassList:[],
      disable: false,
      activeName: undefined,
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      dateTime: new Date().getTime(),
      viewShow: false,
      viewId: "",
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        searchValue: "",
        docId: null,
        docClass: null,
        docNames: null,
        docName:null,
        secDeptId: null,
        params: {
          startTime: "",
          endTime: "",
        },
        dataType: undefined,
        // 有效版本文件 1有效、2失效
        status: '1',
        partNumber: null,
        partRemark: null,
        factorys: null,
        customerCode: null,
        deviceCode: null,
        productVersion: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      borrowOpen: false,
      addBorrow: false,
      drawerDetails: false,
      taskFormData: {},
      varChangeColor: "",
      companySelection: [],
      selectDoc: "",
      addDrawer: false,
      selectInfoData: [],
      pListData: {},
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      selectionData: [],
      importShow:false,
      docClassTree: [],
      isShowPart:false,
      isCustomerShow:false,
      isDeviceShow:false,
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
      isProductVersionShow:false,
    };
  },
  created() {
    this.queryParams.dataType = this.$route.query.dataType?this.$route.query.dataType:'stdd'
    this.queryParams.status = this.$route.query.status?this.$route.query.status:'1'
    this.classType = this.$route.query.classType?this.$route.query.classType:'NOTE'
  },
  mounted() {
    let _this = this
    // 加载体系文件的-文件类型页签
    settingDocClassList({ classStatus: "1",dataType:_this.queryParams.dataType,classType:_this.classType,openPurview:true}).then(
      (response) => {
        _this.docClassList = JSON.parse(JSON.stringify(response.rows))
        _this.docClassTree = _this.handleTree(response.rows.filter(item=>item.purview), "id", "parentClassId")
        debugger
        if (_this.docClassTree.length>0) {
          _this.varChangeColor =  _this.docClassTree[0].id
          _this.queryParams.docClass =  _this.varChangeColor
          _this.handleSelectNode( _this.docClassTree[0])
          _this.getList();
        }
      }
    );
  },
  methods: {
    handleCloseChange(){
      this.drawerShow = false
      this.getList();
    },
    closeAdd() {
      this.drawerAdd = false
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    handleDetail(row){
      let _this = this
      _this.data = row
      _this.drawerShow = true
    },
    /** 查询列表 */
    getList() {
      if (this.queryParams.docClass) {
        this.loading = true;
        listVersion(this.queryParams).then((response) => {
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.queryParams.searchValue = "";
      this.queryParams.docName = null;
      this.queryParams.docClass = this.varChangeColor;

      this.handleSelectNode(this.docClassTree[0])
      this.handleQuery();
    },
    sortChange({ column, prop, order }){
        if (order==='ascending') {
            this.queryParams.orderByColumn = prop
            this.queryParams.isAsc = 'asc'
        }else if(order==='descending') {
            this.queryParams.orderByColumn = prop
            this.queryParams.isAsc = 'desc'
        }else {
            this.queryParams.orderByColumn = undefined
            this.queryParams.isAsc = undefined
        }
        this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.companySelection = selection;
      this.selectInfoData = selection;
    },
    handlePreview(row, source) {
      this.viewId = row.fileId;
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    close() {
      this.viewShow = false;
    },

    changeDrawer(v) {
      this.drawer = v;
    },
    handleSelectNode(node){
      let docClassList = []
      this.getChildrenList(docClassList,node,'id')
      this.queryParams.docClassList = docClassList
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
          docClassList.push(node[key])
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.p2 == this.$t(`doc.this_dept_lost_efficacy`)) {
        //console.log(row.p2);
        return "yidu";
      }
      return "";
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      let item = _this.docClassList.find(item=>item.id===cellValue)
      return item?item.className:cellValue
    },
  },
};
</script>
