<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`home.guide_file_print`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" ">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_ver`)+`:`">
                  <div class="link-box bzlink-box">
                    <span
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                    >{{ formData.preStandardDoc.fileName }}</span>
                    <span
                      style="color: #385bb4; cursor: pointer; margin-left: 10px"
                      @click="
                        handelefileLocalDownload(
                          formData.preStandardDoc.fileId,
                          formData.preStandardDoc.fileName
                        )
                      "
                    >{{ $t(`doc.this_dept_download`) }}</span
                    >
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_annexes_ver`)+`:`" prop="">
                  <div
                    v-for="(item, i) in formData.preAppendixes"
                    class="link-box bzlink-box"
                    :key="i"
                  >
                    <span
                      style="color: #385bb4; margin-left: 10px; cursor: pointer"
                      :key="i"
                      @click="handlePreview(item.fileId)"
                    >{{ item.fileName }}
                    </span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
  </div>
</template>
<script>
import {standardGetDetail} from "@/api/document_account/standard";
import {signEffectiveDisByVersionId} from "@/api/file_processing/fileSignature";
import {listFilePdf} from "@/api/file_processing/basicFilePdf.js";
import {addPrintDataAuth} from "@/api/file_print/printDataAuth.js"
import mixin from "@/layout/mixin/Commmon.js";
export default {
  name: "NnlimitedPrintDetail",
  props: ['data'],
  mixins: [mixin],
  data() {
    return {
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
      },
      loading: false,
      detailLoading: false,
      printDetailList: [],
      printDetailShow: false,
      selectInfoData: [],
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    init(row){
      let _this = this
      _this.rest()
      _this.getDetail(row)
    },
    async getDetail(query) {
      let _this = this
      _this.loading = true
      let res = await standardGetDetail({versionId:query.id})
      _this.formData = res.data;
      this.handleViewPrintDetail(_this.formData);
      _this.loading = false
      _this.detailLoading = true
    },
    rest(){
      let _this = this
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined
      }
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    formatterStatus(row){
      if (row.status==='lost')  {
        return this.$t(`doc.this_dept_loss`)
      }else if (row.status==='recovery') {
        return this.$t(`file_handle.print_recovered`)
      }else {
        return this.$t(`file_handle.print_unrecover`)
      }
    },
    handlePreview(id) {
      let printDetail=this.printDetailList.find(item=>item.fileId===id)
      if (printDetail) {
        this.viewId = printDetail.pdfId;
        this.$refs.viewRef.handleOpenView(printDetail.pdfId,null,null,null,true);
        this.viewShow = true;
      }
    },
    restData(){
      return  {
        id: undefined,
        versionId: this.formData.versionId,
        code: undefined,
        docId: this.formData.docId,
        docClass: this.formData.docClass,
        docName: this.formData.docName,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    // 展示打印明细框
    handleViewPrintDetail(row){
      let self = this
      let query = {
        bizId:row.versionId,
        pdfType:'distribute',
        status:'YES'
      }
      self.loading = true
      listFilePdf(query).then((res) => {
        if(res.rows.length == 0) {
          // 未生成过分发PDF文件
          self.$modal.alert(self.$t(`file_handle.print_text`));
          self.loading = true
          signEffectiveDisByVersionId(row.versionId).then((res) => {
              self.loading = false
              if (res.code===200) {
                self.handleViewPrintDetail(row)
              } else {
                self.$modal.alert(res.msg);
              }
          });
        } else {
          // 已生成
          self.loading = false
          self.printDetailList = res.rows
          self.printDetailShow = true
        }
      });
    },
    // 打印行记录
    handlePrint(row){
      // fileLocalPdfView(row.pdfId)
      this.$refs.viewRef.handleOpenView(row.pdfId);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectInfoData = selection;
    },
    //分配打印权限
    disPrintAuth(){
      let self = this
      if (self.selectInfoData.length < 1) {
        self.$modal.msg(self.$t(`file_handle.print_least_one_data`));
        return
      }
      self.loading = true
      addPrintDataAuth({docDistributeVoList:self.selectInfoData}).then((res) => {
        if(res.code == 200){
          self.$modal.msgSuccess(self.$t(`file_handle.print_assign_authority_succ`))
          self.selectInfoData = []
          self.$refs.dataListTable.clearSelection();
        }else{
          self.$modal.msgWarning(self.$t(`file_handle.print_assign_authority_fail`))
        }
        self.loading = false
      }).catch(err => {
        self.loading = false
        console.error(err)})
    }
  },

};
</script>
