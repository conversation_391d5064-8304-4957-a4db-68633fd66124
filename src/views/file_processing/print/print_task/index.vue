<template>
  <div class="print-task-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" ref="searchForm">
        <el-form-item prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            :placeholder="$t('file.print_table_search_keyword')"
            clearable
            style="width: 220px"
          />
        </el-form-item>
        <el-form-item prop="dateRange">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            :start-placeholder="$t('file.print_table_search_start_date')"
            :end-placeholder="$t('file.print_table_search_end_date')"
            value-format="yyyy-MM-dd"
            style="width: 340px"
          />
        </el-form-item>
        <el-form-item prop="fileNameOrPrinter">
          <el-input
            v-model="searchForm.fileNameOrPrinter"
            :placeholder="$t('file.print_table_search_file_name_or_printer')"
            clearable
            style="width: 220px"
          />
        </el-form-item>

        <el-form-item prop="status">
          <el-select
            :placeholder="$t('file.print_table_search_status')"
            v-model.trim="searchForm.status"
          >
            <el-option
              v-for="dict in dict.type.print_status"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('file.print_table_button_search') }}</el-button>
          <el-button @click="handleReset">{{ $t('file.print_table_button_reset') }}</el-button>
        </el-form-item>

        <!-- 操作按钮区域 -->
        <div class="operation-area" style="float: right;">
          <el-button
            type="success"
            @click="handleExport"
          >{{ $t('file.print_table_button_export') }}</el-button>
        </div>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="docType" :label="$t('file.print_table_doc_type')" width="200" />
        <el-table-column prop="docName" :label="$t('file.print_table_doc_name')" min-width="200" />
        <el-table-column prop="docId" :label="$t('file.print_table_doc_id')" width="120" />
        <el-table-column prop="versionId" :label="$t('file.print_table_version_id')" width="100" />
        <el-table-column prop="classificationNo" :label="$t('file.print_table_classification_no')" width="100" />
        <el-table-column prop="electronicFileName" :label="$t('file.print_table_electronic_file_name')" min-width="200" />
        <el-table-column prop="createName" :label="$t('file.print_table_create_name')" width="100" />
        <el-table-column prop="createTime" :label="$t('file.print_table_create_time')" width="160" />
        <el-table-column prop="printCount" :label="$t('file.print_table_print_count')" width="100" />
        <el-table-column prop="printedCount" :label="$t('file.print_table_printed_count')" width="100" />
        <el-table-column prop="printerName" :label="$t('file.print_table_printer_name')" width="120" />
        <el-table-column prop="status" :label="$t('file.print_table_status')" width="100">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.print_status" :value="scope.row.status" />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.currentPage"
          :limit.sync="pagination.pageSize"
          @pagination="handlePagination"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getPrintTaskList,
  deletePrintTasks,
  reprintTask,
  exportPrintTasks,
  batchReprintTasks,
  cancelPrintTask
} from '@/api/file_print/print'
import Pagination from '@/components/Pagination'
import PrintSettings from '@/components/PrintSettings'

export default {
  name: 'PrintTask',
  dicts: ['print_status'],
  components: {
    Pagination,
    PrintSettings
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        keyword: '',
        dateRange: [],
        fileNameOrPrinter: ''
      },
      // 表格数据
      tableData: [],
      // 选中的行
      selectedRows: [],
      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 加载状态
      loading: false,
      printSettingsVisible: false,
      currentDoc: null
    }
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 获取表格数据
    async fetchData() {
      this.loading = true;
      try {
        const data = await getPrintTaskList({
          ...this.searchForm,
          startTime: this.searchForm.dateRange[0],
          endTime: this.searchForm.dateRange[1],
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize
        });
        this.tableData = data.rows;
        this.pagination.total = data.total;
      } catch (error) {
        console.error('获取打印任务列表失败：', error);
        this.$message.error('获取打印任务列表失败');
      } finally {
        this.loading = false;
      }
    },
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.fetchData();
    },
    // 重置
    handleReset() {
      this.$refs.searchForm.resetFields();
      this.handleSearch();
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.selectedRows = val;
    },

    // 导出列表
    async handleExport() {
      try {
        this.download('process/printTask/export', {
          ...this.searchForm,
          startTime: this.searchForm.dateRange[0],
          endTime: this.searchForm.dateRange[1]
        }, `打印任务列表_${new Date().getTime()}.xlsx`);
      } catch (error) {
        console.error('导出打印任务列表失败：', error);
        this.$message.error('导出失败');
      }
    },
    // 分页变化
    handlePagination({ page, limit }) {
      this.pagination.currentPage = page;
      this.pagination.pageSize = limit;
      this.fetchData();
    }
  }
}
</script>

<style lang="scss" scoped>
.print-task-container {
  padding: 20px;
  background-color: #fff;
  height: 100%;
  .search-area {
    margin-bottom: 20px;
  }
  .operation-area {
    margin-bottom: 20px;
  }
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
