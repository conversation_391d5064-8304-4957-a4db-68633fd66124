<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`home.guide_file_print`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`file_handle.print_current_effec_ver`)">
                  <div class="link-box bzlink-box">
                    <span
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                    >{{ formData.preStandardDoc.fileName }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`file_handle.print_process_record`) }}</div>
          <div class="cell-btn">
            <el-button
              v-hasPermi="['file:docPrint:distribution:auth']"
              @click="disPrintAuth()"
              plain
            >{{ $t(`file_handle.print_assign_authority`) }}</el-button>
          </div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-table :data="dataList" border @selection-change="handleSelectionChange" ref="dataListTable">
            <el-table-column
              type="selection"
              width="55"
              align="left"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_dept_sign_dept`)"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_signatory`)"
              align="center"
              prop="receiveNickName"
              width="200"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_distribute_info`)"
              align="center"
              prop="code"
              :formatter="formatterCode"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_print_paper`)"
              align="center"
              prop="printPaperType"
            >
              <template slot-scope="scope">
                <dict-tag :options="dict.type.print_paper_type" :value="scope.row.printPaperType"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_date`)"
              align="center"
              prop="receiveTime"
            >
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_print_auth`)"
              align="center"
              prop="printFlag"
            >
              <template v-slot="scope">
                <span style="color: green" v-if="scope.row.printFlag==='Y'">{{$t(`doc.this_print_auth_Y`)}}</span>
                <span style="color: red" v-else>{{$t(`doc.this_print_auth_N`)}}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_print_count`)"
              align="center"
              prop="printNums"
            >
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_recover_status`)"
              align="center"
              prop="status"
              :formatter="formatterStatus"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_date`)"
              align="center"
              prop="recoveryTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_operation`)"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleViewPrintDetail(scope.row)"
                >{{ $t(`file_handle.print_detail`) }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
    <!-- 打印明细框 -->
    <el-dialog
      :visible.sync="printDetailShow"
      modal-append-to-body
      destroy-on-close
      @close="close2"
      :title="$t(`file_handle.print_detail`)"
      width="750px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-button size="small" type="primary" @click="batchPrint">{{ $t(`doc.batch_print`) }}</el-button>
      <br/><br/>
      <el-table :data="printDetailList" border @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"
          align="left"
          :selectable="isSelectable"
        />
            <el-table-column
              :label="$t(`doc.this_dept_file_name`)"
              align="center"
              prop="fileObj.fileName" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
              :label="$t(`file_handle.print_generation_time`)"
              align="left"
              prop="createTime"
              :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{parseTime(scope.row.createTime,"{y}-{m}-{d} {h}:{i}")}}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.print_status`)"
              align="center"
              prop="printStatus">
              <template v-slot="scope">
                <span style="color: green" v-if="scope.row.printStatus==='Y'">{{$t(`doc.print_status_Y`)}}</span>
                <span style="color: red" v-else>{{$t(`doc.print_status_N`)}}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_operation`)"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  v-if="fileType.includes(scope.row.fileObj.fileType)"
                  type="text"
                  @click="handlePrintViem(scope.row)"
                >{{ $t(`print.file_preview`) }}</el-button>

                <el-button
                  size="mini"
                  v-if="fileType.includes(scope.row.fileObj.fileType)"
                  type="text"
                  @click="handlePrint(scope.row)"
                >{{ $t(`doc.this_dept_printing`) }}</el-button>
                <el-button
                  size="mini"
                  v-if="!fileType.includes(scope.row.fileObj.fileType)"
                  type="text"
                  @click="fileLocalDownload(scope.row)"
                >{{ $t(`doc.this_dept_download`) }}</el-button>
              </template>
            </el-table-column>
          </el-table>
    </el-dialog>

    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <print-settings
      @closePrint="closePrint"
      ref="printSettings"
      :visible.sync="printSettingsVisible"
      :doc-info="docInfo"
      :printDataList="printDataList"
    />
  </div>
</template>
<script>
import {standardGetDetail} from "@/api/document_account/standard";
import {fileLocalPdfView} from "@/api/pdf_preview";
import {authFilterListDistribute, getPrintAuthBatchById, listPrintGroupByFileId} from '@/api/process/distribute'
import {signEffectiveDis} from "@/api/file_processing/fileSignature";
import {listFilePdf} from "@/api/file_processing/basicFilePdf.js";
import {addPrintDataAuth} from "@/api/file_print/printDataAuth.js"
import { fileLocalDownload } from '@/api/commmon/file'
import PrintSettings from "@/components/PrintSettings/index.vue";
import {getConfigKey} from "@/api/system/config";
import printJS from "print-js";
import {addPrintFileDetail} from "@/api/file_processing/printFileDetail";
import {addPrintTask, addPrintTaskCallBack} from "@/api/file_print/print";
export default {
  name: "PrintDetail",
  components: {PrintSettings},
  props: ['data'],
  dicts: ['print_paper_type'],
  data() {
    return {
      viewId: "",
      fileType: ['doc','xlsx','docx','xls','xlst','ppt','pptx','pdf'],
      dataList: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
      },
      loading: false,
      detailLoading: false,
      printDetailList: [],
      printDetailShow: false,
      selectInfoData: [],
      printSettingsVisible: false,
      docInfo: {},
      printData:{},
      printDataList:{},
      intervalId: null, // 用于存储 setInterval 的 ID
      getDetailData:{}
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  beforeDestroy() {
    // 组件销毁时清除定时器
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId=null
    }
  },
  methods: {
    closePrint(){
      //延迟5秒自动刷新
      if (this.intervalId) {
        console.log('已经开始了，无需重复启动');
        return;
      }

      this.intervalId = setInterval(() => {
        this.handleViewPrintDetail(this.getDetailData)
        this.init(this.data)
      }, 5000); // 5000 毫秒 = 5 秒

    },

    // 判断当前行是否可选
    isSelectable(row, index) {
      // 例如：status 为 'inactive' 的行不可选
      return row.printStatus !== 'Y' && this.fileType.includes(row.fileObj.fileType);
    },
    init(row){
      let _this = this
      _this.rest()
      _this.getDetail(row)
    },
    async getDetail(query) {
      let _this = this
      _this.loading = true
      let res = await standardGetDetail(query)
      _this.formData = res.data;
      _this.loading = false
      _this.detailLoading = true
      authFilterListDistribute({versionId:res.data.versionId,type:'print'}).then(res=>{
        if (res.data&&res.data.length>0) {
          _this.dataList = res.data
        }else{
          _this.dataList = []
        }
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    rest(){
      let _this = this
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined
      }
    },
    close2() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId=null
      }
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    formatterStatus(row){
      if (row.status==='lost')  {
        return this.$t(`doc.this_dept_loss`)
      }else if (row.status==='recovery') {
        return this.$t(`file_handle.print_recovered`)
      }else {
        return this.$t(`file_handle.print_unrecover`)
      }
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    restData(){
      return  {
        id: undefined,
        versionId: this.formData.versionId,
        code: undefined,
        docId: this.formData.docId,
        docClass: this.formData.docClass,
        docName: this.formData.docName,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    // 展示打印明细框
    async handleViewPrintDetail(row) {
      let self = this
      self.getDetailData=row
      let query = {
        bizId: row.id,
        pdfType: 'distribute',
        status: 'YES'
      }
      self.loading = true
      let res = await listFilePdf(query)
      if (res.rows.length == 0) {
        // 未生成过分发PDF文件
        self.$modal.alert(self.$t(`file_handle.print_text`));
        self.loading = true
        let res1 = await signEffectiveDis(row.id, row.code)
          self.loading = false
          if (res1.code === 200) {
            let res2 = await listFilePdf(query)
            self.loading = false
            self.printDetailList = res2.rows
            self.printDetailShow = true
          } else {
            self.$modal.alert(res.msg);
          }
      } else {
        // 已生成
        self.loading = false
        self.printDetailList = res.rows
        self.printDetailShow = true
      }
    },
    //批量打印
    async batchPrint(){
      if (this.selectInfoData.length < 1) {
        this.$modal.msg(this.$t(`file_handle.print_least_one_data`));
        return
      }
      const loading = this.$loading({
        lock: true,
        text: this.$t('doc.this_dept_loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      try {
        // 获取PrintSettings组件的引用
        const printSettings = this.$refs.printSettings
        if (printSettings && await printSettings.checkPrinters()) {
          const printData = {
            id: this.selectInfoData[0].bizId,
            fileIds: this.selectInfoData.map(v=>v.fileId)
          }
          const respose = await getPrintAuthBatchById(printData)
          this.printDataList=respose.data
          if (this.printDataList.length !== 0) {
            const contextPath = process.env.VUE_APP_BASE_API

            this.printDataList.forEach(item=>{
              this.selectInfoData.forEach(a=>{
                if(item.fileId===a.fileId){
                  item.electronicFileName=a.fileObj.fileName
                  item.fileUrl= `/process/file-signature/previewPdfWatermark?pdfFileId=${a.pdfId}`
                }
              })
            })
            //符合批量打印，跳转打印
            const res = await getConfigKey('is_control_print')
            if (res.msg === 'true') {
              loading.close();
              // 显示打印设置对话框
              this.showPrintSettings()
            }
          }else{
            this.handleViewPrintDetail(this.getDetailData)
            this.init(this.data)
            this.$message.error(this.$t('doc.this_dept_print_auth_fail'))
          }
        }
        loading.close()
      } catch (error) {
        loading.close();
        this.$message.error(this.$t('doc.this_dept_print_task_fail'))
      }


    },
    // 直接打印
    async handlePrint(row) {
      const loading = this.$loading({
        lock: true,
        text: this.$t('doc.this_dept_loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      try {
          const respose = await listPrintGroupByFileId({id:row.bizId,fileId:row.fileId})
          this.printData = respose.data
          this.printDataList=[]
          if(this.printData != null){
            const res = await getConfigKey('is_control_print')
            if (res.msg === 'true') {
              const contextPath = process.env.VUE_APP_BASE_API
              this.docInfo = {
                docId: this.printData.docId,
                docName: this.printData.docName,
                printCount: this.printData.nums,
                versionId: this.printData.versionId,
                classificationNo: this.printData.code,
                docType: this.printData.className,
                docDistributeId: this.printData.id,
                electronicFileName: row.fileObj.fileName,
                fileId: this.printData.fileId,
                fileUrl: `/process/file-signature/previewPdfWatermark?pdfFileId=${row.pdfId}`
              }
              loading.close();
              // 显示打印设置对话框
              this.showPrintSettings()
            }
          }else{
            loading.close();
            this.handleViewPrintDetail(this.getDetailData)
            this.init(this.data)
            this.$message.error(this.$t('doc.this_dept_print_auth_fail'))
          }

      } catch (error) {
        loading.close();
        this.$message.error(this.$t('doc.this_dept_print_setting_fail'))
      }
    },
    async showPrintSettings() {
      const loading = this.$loading({
        lock: true,
        text: this.$t('doc.this_dept_loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      // 获取PrintSettings组件的引用
      const printSettings = this.$refs.printSettings
      if (printSettings && await printSettings.checkPrinters()) {
        this.printSettingsVisible = true
        loading.close();
      }else {
        loading.close();
      }
    },
    //预览打印
    handlePrintViem(row){
      // fileLocalPdfView(row.pdfId)
      this.$refs.viewRef.handleOpenView(row.pdfId,null,null,null,true,row.bizId,row.fileId);
    },
    async fileLocalDownload(row){
      try {
        const loading = this.$loading({
          lock: true,
          text: this.$t('doc.this_dept_loading'),
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        const respose = await listPrintGroupByFileId({id:row.bizId,fileId:row.fileId})
        this.printData = respose.data
        if(this.printData != null){
          const printData = {
            docId: this.printData.docId,
            docName: this.printData.docName,
            versionId: this.printData.versionId,
            printCount: 1,
            printerName: "已下载",
            printDesc: "等待打印",
            docType: this.printData.className,
            electronicFileName: row.fileObj.fileName,
            docDistributeId: this.printData.id,
            fileId: this.printData.fileId,
            classificationNo: this.printData.code
          }
          addPrintTask(printData).then(res => {
            if (res.code === 200) {
              loading.close();
              this.$refs.viewRef.handelefileLocalDownload(row.pdfId,row.fileObj.fileName)

              //模拟成功回调
              const callBack = {
                id: res.data.id,
                printedCount: 1,
                status: 'completed'
              }
              addPrintTaskCallBack(callBack).then(res => {
              }).catch(error => {
              })
            }
          }).catch(error => {
            loading.close();
            this.$message.error(this.$t('doc.this_dept_print_task_fail'))
          })
        }else{
          loading.close();
          this.handleViewPrintDetail(this.getDetailData)
          this.init(this.data)
          this.$message.error(this.$t('doc.this_dept_print_task_fail'))
        }

      } catch (error) {
        loading.close();
        this.$message.error(this.$t('doc.this_dept_print_task_fail'))
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectInfoData = selection;
    },
    //分配打印权限
    disPrintAuth(){
      let self = this
      if (self.selectInfoData.length < 1) {
        self.$modal.msg(self.$t(`file_handle.print_least_one_data`));
        return
      }
      self.loading = true
      addPrintDataAuth({docDistributeVoList:self.selectInfoData}).then((res) => {
        if(res.code == 200){
          self.$modal.msgSuccess(self.$t(`file_handle.print_assign_authority_succ`))
          self.selectInfoData = []
          self.$refs.dataListTable.clearSelection();
        }else{
          self.$modal.msgWarning(self.$t(`file_handle.print_assign_authority_fail`))
        }
        self.init(self.data)
        self.loading = false
      }).catch(err => {
        self.loading = false
        console.error(err)})
    }
  },

};
</script>
