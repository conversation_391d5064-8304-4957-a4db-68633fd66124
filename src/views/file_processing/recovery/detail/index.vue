<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`home.guide_file_recycle`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" v-hasPermi="['fileProcessing:recovery:sendEmail']" :disabled="!selectInfoData||selectInfoData.length==0" @click="sendEMail">{{ $t(`file_handle.recovery_send_email`) }}</el-button>
        <el-button type="primary" :disabled="!selectInfoData||selectInfoData.length==0" @click="submitRecovery">{{ $t(`file_handle.recovery_recycle`) }}</el-button>
        <el-button type="danger" :disabled="!selectInfoData||selectInfoData.length===0" @click="submitLost">{{ $t(`doc.this_dept_loss`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`file_handle.print_current_effec_ver`)">
                  <div class="link-box bzlink-box">
                    <span
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                    >{{ formData.preStandardDoc.fileName }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_distribute_operation`) }}</div>
        </div>

        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item>
            <treeselect v-model.trim="deptId" :options="deptOptions" class="input-with-select"
                        style=" width: 320px; display: inline-block;" @select="(node)=>handleSelectNodeDept(node,'queryParams')" :normalizer="normalizer" :placeholder="$t(`doc.this_dept_select_dept`)"
                        :searchable="false" size="mini" />
          </el-form-item>

          <el-date-picker
            v-model="dataValue"
            type="datetimerange"
            :picker-options="pickerOptions"
            :range-separator="$t(`doc.this_dept_to`)"
            :start-placeholder="$t(`file_handle.recovery_sign_start_time`)"
            :end-placeholder="$t(`file_handle.recovery_sign_end_time`)"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            unlink-panels
            align="right"
            size="small"
            style="height: 34px; margin-right: 5px"
          >
          </el-date-picker>

          <el-button type="primary" icon="el-icon-search" @click="handleQuery" style="display: inline-block;">{{ $t(`doc.this_dept_search`) }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery" style="display: inline-block;">{{ $t(`myItem.handle_reset`) }}</el-button>
          <el-button icon="el-icon-download" @click="handleExport" style="display: inline-block;">{{ $t(`doc.exterior_dept_export`) }}</el-button>
        </el-form>

        <el-card class="gray-card table-card no-padding">
          <el-table :data="dataList" border @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="55"
              align="left"
            />
            <el-table-column
              :label="$t(`doc.this_dept_sign_dept`)"
              sortable
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_signatory`)"
              align="center"
              prop="receiveNickName"
              width="200"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_distribute_info`)"
              align="center"
              prop="code"
              :formatter="formatterCode"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_date`)"
              sortable
              align="center"
              prop="receiveTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_status`)"
              align="center"
              prop="status"
              :formatter="formatterStatus"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`file_handle.recovery_print_frequency`)"
              align="center"
              prop="printTotal"
            >
              <template slot-scope="scope">
                <span class="wenjcolor" ><a @click="inquirePrintDetail(scope.row)">{{scope.row.printTotal}}</a></span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_date`)"
              align="center"
              prop="recoveryTime"
            >
            </el-table-column>
          </el-table>
        </el-card>

        <el-drawer :visible.sync="dialogPrintDetail"
                   direction="rtl"
                   :with-header="false"
                   :wrapperClosable="false"
                   size="50%"
                   :show-close="true"
                   :destroy-on-close="true" :append-to-body="true">
          <div class="document_change_add">
            <div class="drawer-head">
              <div class="cell-title">
                <div>
                  <p class="title">{{ $t(`file_handle.recovery_print_frequency`) }}</p>
                </div>
              </div>
              <div class="cell-btn">
                <el-button @click="closeDialogPrintDetail">{{ $t(`doc.this_dept_close`) }}</el-button>
              </div>
            </div>
          </div>
          <el-table :data="printDetailList" border>
            <el-table-column type="index" :label="$t(`file_handle.recovery_num`)"></el-table-column>
            <el-table-column property="fileName" :label="$t(`doc.this_dept_file_name`)"></el-table-column>
            <el-table-column property="createTime" :label="$t(`file_handle.recovery_print_time`)"></el-table-column>
            <el-table-column property="printUserNickName" :label="$t(`file_handle.recovery_print_people`)"></el-table-column>
          </el-table>
        </el-drawer>
      </div>
    </div>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="closeAS">
    </as-pre-view>
  </div>
</template>
<script>
import { listDept } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {standardGetDetail} from "@/api/document_account/standard";
import {fileLocalPdfView} from "@/api/pdf_preview";
import mixin from "@/layout/mixin/Commmon.js";
import { postListDistribute, lostByIds, recoveryByIds, sendRecoveryEMail } from '@/api/process/distribute'
import {getListBydistributeId} from "../../../../api/file_processing/printFileDetail";
export default {
  name: "RecoveryDetail",
  props: ['data'],
  components: {
    Treeselect
  },
  mixins: [mixin],
  data() {
    return {
      viewId: "",
      selectInfoData: [],
      dataList: [{}],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
      },
      loading: false,
      detailLoading: false,
      // 查询参数
      queryParams: {},
      deptId: undefined,
      dataValue: "",
      dialogPrintDetail: false,
      printDetailList: [],
      // 部门树选项
      deptOptions: []
    };
  },
  computed: {},
  watch: {
    dataValue(val) {
      if (val != null) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  created() {
    this.initDept()
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    init(row){
      let _this = this
      _this.rest()
      _this.getDetail(row)
    },
    async getDetail(query) {
      let _this = this
      _this.loading = true
      let res = await standardGetDetail(query)
      _this.formData = res.data;
      _this.loading = false
      _this.getList()
    },
    getList(){
      let _this = this
      _this.detailLoading = true
      this.queryParams.versionId = _this.formData.versionId
      this.queryParams.type = 'print'
      postListDistribute(this.queryParams).then(res=>{
          _this.dataList = res.data
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    rest(){
      let _this = this
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
      }
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    formatterStatus(row){
      if (row.status==='lost')  {
        return this.$t(`doc.this_dept_loss`)
      }else if (row.status==='recovery') {
        return this.$t(`file_handle.print_recovered`)
      }else {
        return this.$t(`file_handle.print_unrecover`)
      }
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    closeAS() {
      this.viewShow = false;
    },
    // PDF文件预览
    handlePdfPreview(pdfFileId) {
      if(pdfFileId == '') {
        alert(this.$t(`file_handle.recovery_text`));
        return false;
      }
      fileLocalPdfView(pdfFileId)
    },
    restData(){
      return  {
        id: undefined,
        versionId: this.formData.versionId,
        code: undefined,
        docId: this.formData.docId,
        docClass: this.formData.docClass,
        docName: this.formData.docName,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    handleSelectionChange(selection) {
      this.selectInfoData = selection;
    },
    checkStatus(){
      let _this = this
      let code = []
      let ids = _this.selectInfoData.map(item=>{
        if (!item.receive) {
          code.push(this.formatterCode(item))
        }
        return item.id
      })
      if (code.length>0) {
        _this.$message.warning(this.$t(`file_handle.recovery_text1`)+code.join("、")+this.$t(`file_handle.recovery_text2`))
        return
      }
      return ids
    },
    sendEMail(){
      let _this = this
      let ids = _this.checkStatus()
      if (!ids) {
        return
      }
      _this.$confirm(_this.$t(`file_handle.recovery_text3`), _this.$t(`file_handle.change_tip`), {
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        type: "warning",
      }).then(() => {
        sendRecoveryEMail(ids).then((res) => {
          _this.$message.success(_this.$t(`file_handle.recovery_recover_succ`));
          _this.getList();
        });
      }).catch(() => {});
    },
    submitRecovery(){
      let _this = this
      let ids = _this.checkStatus()
      if (!ids) {
        return
      }
      _this.$confirm(_this.$t(`file_handle.recovery_text4`), _this.$t(`file_handle.change_tip`), {
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        type: "warning",
      }).then(() => {
         recoveryByIds(ids).then((res) => {
           _this.$message.success(_this.$t(`file_handle.recovery_recover_succ`));
           _this.getList();
         });
      }).catch(() => {});
    },
    submitLost(){
      let _this = this
      let ids = _this.checkStatus()
      if (!ids) {
        return
      }
      _this.$confirm(_this.$t(`file_handle.recovery_text5`), _this.$t(`file_handle.change_tip`), {
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        type: "warning",
      }).then(() => {
        lostByIds(ids).then((res) => {
          _this.$message.success(_this.$t(`doc.this_dept_operation_succ`));
          _this.getList();
        });
      }).catch(() => {});
    },
    initDept() {
      listDept({ status: 0 }).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    handleSelectNodeDept(node,source){
      let deptIds = []
      this.getChildrenList(deptIds,node,'deptId')
      this[source].deptIds = deptIds
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.deptId = undefined
      this.queryParams.deptIds = []
      this.dataValue=[]
      this.queryParams.startTime = ''
      this.queryParams.endTime = ''
      this.getList()
    },
    inquirePrintDetail(row){
      getListBydistributeId({distributeId:row.id}).then(res=>{
        if (res.data&&res.data.length>0) {
          this.printDetailList = res.data
        }
      })
      this.dialogPrintDetail = true
    },
    closeDialogPrintDetail(){
      this.dialogPrintDetail = false
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "process/distribute/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    }
  },
};
</script>
