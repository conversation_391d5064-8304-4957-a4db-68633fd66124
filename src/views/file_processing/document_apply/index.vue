<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t(`home.guide_file_apply`) }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :label-width="columnLangSizeFlag ? '88px' : '68px'"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
<!--              <el-select :placeholder="$t(`sys_mgr.account_data_type`)" v-model="queryParams.dataType" style="float: left; height: 34px; margin-right: 5px;width:100px" @change="getList()">-->
<!--                  <el-option value="stdd" :label="$t(`file_handle.change_sys`)" />-->
<!--                  <el-option value="project" :label="$t(`file_handle.change_project`)" />-->
<!--                </el-select>-->
              <el-date-picker
                v-model="value2"
                type="datetimerange"
                :picker-options="pickerOptions"
                :range-separator="$t(`doc.this_dept_to`)"
                :start-placeholder="$t(`doc.this_dept_start_date`)"
                :end-placeholder="$t(`doc.this_dept_end_date`)"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                align="right"
                size="small"
                style="float: left; height: 34px; margin-right: 5px"
              >
              </el-date-picker>
              <el-button type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >{{ $t(`doc.this_dept_reset`) }}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button v-hasPermi="['fileProcessing:documentChange:export']"
                         @click="handleExport()">{{ $t(`doc.exterior_dept_export`) }}</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item :label="$t(`doc.this_dept_change_type`)" :label-width="columnLangSizeFlag ? '128px' : '68px'">
                <el-select
                  :placeholder="$t(`doc.this_dept_change_type`)"
                  clearable
                  v-model.trim="queryParams.applyClass"
                >
                  <el-option
                    v-for="dict in dict.type.sys_apply_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t(`file_handle.change_process_state`)" :label-width="columnLangSizeFlag ? '128px' : '68px'">
                <el-select
                  :placeholder="$t(`file_handle.change_process_state`)"
                  v-model.trim="queryParams.procStatus"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.process_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
              <el-button @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              <el-button @click="boxClass = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table header-align="left" v-loading="loading" :data="dataList" border>
          <el-table-column
            :label="$t(`doc.this_dept_change_type`)"
            align="left"
            width="95"
            prop="applyClass"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_apply_type" :value="scope.row.applyClass"/>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`myItem.handle_process_title`)"
            align="left"
            prop="docName"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_file_code`)"
            align="left"
            prop="docId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_appli_dept`)"
            align="left"
            prop="deptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_claimant`)"
            align="left"
            prop="nickName"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`file_handle.change_process_state`)"
            align="left"
            width="120"
            :show-overflow-tooltip="true"
            prop="recordStatus"
          >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.process_status" :value="scope.row.procStatus"/>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`myItem.handle_current_stage`)"
            align="left"
            :show-overflow-tooltip="true"
            prop="procName"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`myItem.handle_current_stage_ handler`)"
            align="left"
            prop="procNameUser"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_appli_date`)"
            align="left"
            width="150">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.applyTime,'{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(`file_handle.change_end_time`)"
            align="left"
            width="150">
            <template slot-scope="scope">
              <span>{{scope.row.procStatus==='done'||scope.row.procStatus==='cancel'?parseTime(scope.row.updateTime,'{y}-{m}-{d} {h}:{i}'):'' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            width="55"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
                v-hasPermi="['fileProcessing:documentChange:detail']"
                >{{ $t(`doc.this_dept_detail`) }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 添加或修改-->
    </div>
    <el-drawer
      :visible.sync="drawerShow"
      direction="rtl"
      size="90%"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <MainComponent ref="mainComponent" :code="path+code" :data="data" @close="handleCloseChange"></MainComponent>
    </el-drawer>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import MainComponent from '@/components/mainComponent/index.vue'
import { listWorkflowApplyLog } from '../../../api/my_business/workflowApplyLog'
export default {
  name: "Post",
  dicts: ["sys_apply_type", "process_status"],
  components: {
    MainComponent,
    Treeselect,
  },
  data() {
    return {
      drawerShow: false,
      boxClass: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawerDetails: false,
      changeShow: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        neApplyClass: ['add','update','disuse'],
        params: {
          startTime: "",
          endTime: "",
        },
        applyClass:null,
        userName: this.checkPermi(['fileProcessing:documentChange:all']) ? '' : JSON.parse(sessionStorage.getItem("USER_INFO")).userName,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          {
            required: true,
            message: "$t(`file_set.signature_not_null`)",
            trigger: "blur",
          },
        ],
        postCode: [
          {
            required: true,
            message: "$t(`file_set.signature_not_null`)",
            trigger: "blur",
          },
        ],
        postSort: [
          {
            required: true,
            message: "$t(`file_set.signature_not_null`)",
            trigger: "blur",
          },
        ],
      },
      fileTypeList: [],
      taskData: [], // 任务数据
      drawer: false,
      taskFormData: {},
      docClassTree: [],
      value2: "",
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  watch: {
    value2(val) {
      console.log(val);
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
  },
  mounted() {
  },
  methods: {
    checkPermi,
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    handleChange() {
      this.changeShow = true;
    },
    handleCloseChange() {
      this.drawerShow = false;
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listWorkflowApplyLog(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.value2=[]
      this.queryParams.params.startTime = "";
      this.queryParams.params.endTime = "";
      this.queryParams.docName = null;
      this.queryParams.procStatus = null;
      this.queryParams.applyClass = null;
      // console.log(this.queryParams)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      let type = row.changeType
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(process.env.VUE_APP_CONTEXT_PATH + '/#/workflow?type='+type+'&procInstId='+row.procInstId+'&status=2')
      }else {
        _this.code = type
        _this.data = { procInstId: row.procInstId, type: type, status: '2' }
        _this.drawerShow = true
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/process/workflowApplyLog/export",
        {
          ...this.queryParams,
          pageSize: undefined,
          pageNum: undefined,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
