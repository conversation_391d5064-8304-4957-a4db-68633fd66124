<template>
  <main-component ref="mainComponent" :code="code" :data="data"  @close="handleCloseChange"></main-component>
</template>

<script>
import MainComponent from "@/components/mainComponent/index.vue";
import { parseTime } from '@/utils/ruoyi'
import watermark from 'watermark-dom'
export default {
  name: "workflowRouter",
  dicts: [],
  components: {
    MainComponent,
  },
  data() {
    return {
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      status: true,
    }
  },
  created() {
    this.$nextTick(()=>{
      this.handleDetails(this.$route.query);
    })
  },
  mounted() {
    // 配置系统界面文字水印
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
        const userName = userInfo.userName
        const nickName = userInfo.nickName
        // const loginDate = userInfo.loginDate
        const loginDate = new Date()
        const txt = `${userName},${nickName},${parseTime(loginDate, '{y}-{m}-{d}')}`
        watermark.init({ watermark_txt: txt, watermark_alpha: 0.08, watermark_width: 260, watermark_height: 150, })
    }
  },
  methods: {
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      if (row.type&&_this.status) {
        _this.code = _this.path + row.type
        _this.data = row
      }
    },
    init(row){
      let _this = this
      _this.code = _this.path + row.type
      _this.data = row
      _this.status = false
      debugger
    },
    handleCloseChange() {
      let _this = this
      if (_this.status) {
        window.parent.postMessage({type:"close"})
        window.parent.close()
      }else{
        _this.$emit("closeDrawer")
      }
    }
  },
};
</script>
