<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <el-card class="gray-card" style="width: 60%">
      <el-table v-loading="loading" :data="dataList">
        <el-table-column :label="$t(`doc.this_dept_link`)" align="left" prop="actDefName" width="200px"/>
        <el-table-column :label="$t(`doc.this_dept_comments`)" align="left" prop="opinion" >
          <template slot-scope="scope">
            <span :style="{color:passList[scope.row.pass].color}">{{ passList[scope.row.pass].label }}</span><br/>
            <span>{{ scope.row.opinion }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t(`doc.this_dept_operation_msg`)" align="left" prop="createTime" >
          <template slot-scope="scope">
            <span>{{ scope.row.deptName }}</span>
            <span style="margin-left:10px;">{{ scope.row.nickName }}</span>
            <span style="margin-left:20px;color:#AAAAAA">{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { shlkSelectLogByProcInstId } from "@/api/my_business/workflow";

export default {
  name: "WorkflowLogs",
  props: ["procInstId"],
  data() {
    return {
      loading: true,
      dataList: [],
      passList: { 'true':{label:this.$t(`doc.this_dept_pass`),color:'#70B603'},'false':{label:this.$t(`doc.this_dept_not_pass`),color:'#D9001B'}},
    };
  },
  watch: {
    procInstId (val) {
      if (val) {
        this.getList(val)
      }
    },
  },
  mounted() {
    if (this.procInstId) {
      this.getList(this.procInstId)
    }
  },
  methods: {
    /** 查询岗位列表 */
    getList(procInstId) {
      this.loading = true;
      shlkSelectLogByProcInstId(procInstId).then(response => {
        this.dataList = response.data;
        this.loading = false;
      });
    },
  }
};
</script>
