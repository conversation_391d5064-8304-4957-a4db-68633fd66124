<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{$t(`doc.batch_title_text_4`)}}{{ $t(`doc.this_dept_file`) }}{{$t(`doc.this_dept_cancel`)}}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus" @click="selectPresetUser">{{ $t(`file_handle.change_select_people`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_cancel')&&procInstId&&workflowStatus" type="danger" @click="deleteForm" v-dbClick>{{ $t(`file_handle.change_revoke`) }}</el-button>
        <el-button v-if="procInstId&&workflowStatus" @click="showVerify({type:'transfer'})" v-dbClick>{{ transferStatus?$t(`file_handle.transfer_return`):$t(`file_handle.transfer`) }}</el-button>
        <el-button v-if="procInstId&&!workflowStatus&&'doing'===formData.processStatus&&formData.createBy===userInfo.userName&&backFlowToOneStatus" type="danger" @click="showVerify({type:'backFlow'})" v-dbClick>{{$t(`file_handle.change_withdraw`)}}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus" @click="showVerify({type:'turnDown'})" type="danger">{{ $t(`file_handle.change_reject_to_preparer`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_generate_code')&&workflowStatus" @click="shengchengbianhao()" type="primary">{{ $t(`file_handle.change_generate_num`) }}</el-button>
        <!-- 【签章生效】和【执行发布】一般出现在发布环节 -->
        <el-button v-if="nodeShow('publish_setup_time')&&workflowStatus" @click="setupStartTime()"  type="primary">{{ $t(`doc.this_dept_select_effective_date`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_file_cover')&&workflowStatus" @click="handleCoverEffective()"  type="primary">{{ $t(`file_handle.change_generate_cover`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_setup_time')&&workflowStatus" @click="handleSignEffective()"  type="primary">{{ $t(`file_handle.change_signature_effc`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_publish_file')&&workflowStatus" @click="showVerify({type:'publish'})"  type="primary">{{ $t(`file_handle.change_execute_release`) }}</el-button>
        <!-- 提交按钮在 非【执行发布】环节出现 -->
        <el-button v-if="!nodeShow('top_btn_publish_file')&&workflowStatus" type="primary" @click="submitForm" v-dbClick>{{$t('doc.this_dept_annex')}}</el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_types`)" prop="docClass">
                  <treeselect
                    v-if="!procInstId"
                    :appendToBody="true"
                    z-index="9999"
                    v-model.trim="formData.docClass"
                    :options="classLevelOptions"
                    :normalizer="normalizer"
                    :disable-branch-nodes="true"
                    :searchable="false"
                    :show-count="true"
                    :clearable="false"
                    @select="selectDocClass"
                    :placeholder="$t(`doc.this_dept_select_type`)"
                    :disabled="procInstId||dataList.length>0"
                  />
                  <span v-else>{{docClassData.className}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="editStatus" label="" prop=" ">
                  <!--                  <modify-apply-upload-->
                  <!--                    :fileType="['docx','doc','xls','xlsx','pdf','ppt','pptx']"-->
                  <!--                    :formData="formData"-->
                  <!--                    @upLoadDataList="upLoadDataList"-->
                  <!--                    @dataRefresh="dataRefresh"-->
                  <!--                  ></modify-apply-upload>-->
                  <el-button style="float: right" @click="versionSearchInit">{{$t(`doc.button_add`)}}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table
            :data="dataList"
            v-loading="tableLoading"
            ref="table"
            header-align="left"
          >
<!--            <el-table-column-->
<!--              v-if="editStatus"-->
<!--              label="ID"-->
<!--              align="left"-->
<!--              prop="id"-->
<!--              :show-overflow-tooltip="true"-->
<!--            >-->
<!--            </el-table-column>-->
            <el-table-column
              :label="$t(`doc.this_dept_file_name`)"
              align="left"
              prop="docName"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_file_code`)"
              align="left"
              prop="docId"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_file_versions2`)"
              align="left"
              prop="versionValue"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_staffs`)"
              align="left"
              prop="nickName"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_staffing_dept`)"
              align="left"
              prop="deptName"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_current_effec_ver`)"
              align="left"
              prop="standardDoc"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span
                  v-if="scope.row.preStandardDoc"
                  style="color: #385bb4; cursor: pointer"
                  @click="handlePreview(scope.row.preStandardDoc.fileId)"
                >{{ scope.row.preStandardDoc.fileName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_operation`)"
              align="center"
              width="95"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="!editStatus"
                  size="mini"
                  type="text"
                  @click="handleUpdate(scope.row,scope.$index)"
                >{{$t(`doc.this_dept_detail`)}}
                </el-button>
                <el-button
                  v-if="editStatus"
                  size="mini"
                  type="text"
                  @click="handleUpdate(scope.row,scope.$index)"
                >{{$t(`file_set.version_edit`)}}
                </el-button>
                <el-button
                  v-if="editStatus"
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.$index)"
                >{{$t(`doc.this_dept_delete`)}}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="news-card" v-if="nodeShow('whether_customer_record')||formData.whetherCustomer">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.customer_record`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elFormCustomer"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.whether_customer_records`)+`:`" prop="whetherCustomer">
                  <el-radio-group  v-if="workflowStatus&&nodeShow('whether_customer_record')" @input="updateDataList('whetherCustomer')" v-model.trim="formData.whetherCustomer">
                    <el-radio
                      v-for="(item, index) in dict.type.sys_yes_no"
                      :key="index"
                      :label="item.value"
                    >{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.whetherCustomer"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card" v-if="!!formData.yNTrain">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_train`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elFormTrain"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_or_not`)+`:`" prop="yNTrain">
                  <el-radio-group  v-if="editStatus" v-model.trim="formData.yNTrain" @input="updateDataList('yNTrain')">
                    <el-radio
                      v-for="(item, index) in dict.type.sys_yes_no"
                      :key="index"
                      :label="item.value"
                    >{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.yNTrain"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <approval-box
        v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&approvalStatus"
        ref="approvalBox" :submitLabel="submitLabel"
        :selected="nodeShow('default_selected')"
        :userListStatus="!nodeShow('user_list')"
        :order = "order"
        :searchQuery="searchQuery"
        :hideNodeCode = "hideNodeCode"
        :defaultStaff="defaultStaff"
        :pListData="pListData"
        :status="(nodeShow('shenhe')||nodeShow('pizhun'))"
      ></approval-box>
      <div class="news-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!approvalStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15" >
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_conclusion`)" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"  @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comment`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading = flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :order = "order"
        :selected="nodeShow('default_selected')"
        :userListStatus="!nodeShow('user_list')"
        :searchQuery="searchQuery"
        :hideNodeCode = "hideNodeCode"
        :defaultStaff="defaultStaff"
        :pListData="pListData"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="showVerify({type:'flowSubmit'})"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <doc-id-list-box ref="docIdListBox" v-if="shenchenbianhao" @setDocId="setDocId" @setRecordDocId="setRecordDocId"></doc-id-list-box>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <el-drawer
      :wrapperClosable='false'
      :visible.sync="dealDrawerShow"
      :append-to-body="true"
      direction="rtl"
      size="90%"
      :with-header="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <div style="width:100%; height:100%;overflow: hidden">
        <workflow-router ref="dealDrawer" @closeDrawer="handleCloseChange"></workflow-router>
      </div>
    </el-drawer>
    <preset-user ref="presetUser" @selectHandle="selectHandlePresetUser"></preset-user>
    <el-drawer
      :visible.sync="drawerShow"
      direction="rtl"
      :size="drawerSize"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      append-to-body
      modal-append-to-body
      :destroy-on-close="true"
    >
      <main-component ref="mainComponent" :code="path+code"  :data="form"  :dataType="formData.dataType" @close="handleClose"></main-component>
    </el-drawer>
    <transfer-flow ref="transferFlow" @close="close"></transfer-flow>
    <user-list ref="userList" @selectHandle="handleSubmitUser"></user-list>
    <setup-start-time
      ref="setupStartTime"
      :batchId="formData.batchId"
      @updateStartTime="updateStartTime"
    ></setup-start-time>
    <error-msg ref="errorMsg"></error-msg>
    <version-list :selectValidate="true" :versionData="dataList" :dataType="formData.dataType" :classTypeList="classTypeList" :docClass="formData.docClass"  ref="versionList" @selectHandle="versionSelectHandle" :selectableFun="selectableFun"></version-list>
    <identity-verify
      :visible.sync="verifyVisible"
      :business-params="currentParams"
      @business-execute="handleBusinessExecute"
      @verify-cancel="handleCancel"
    />
  </div>
</template>
<script>
import { settingDocClassId, settingDocClassList } from '@/api/file_settings/type_settings'
import IdentityVerify from './add_import/identityVerify.vue';
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  addModifyApplyBatch,
  getBatchInfoByBpmnId, updateModifyApply,
  updateModifyApplyBatch,linkLoglistlink
} from '@/api/file_processing/modifiyApply'
import { signEffectiveBatch } from '@/api/file_processing/fileSignature'
import { getByUpDocClassAndBizType } from "@/api/setting/docClassFlow";
import { getInfo } from "@/api/setting/docClassFlowNodeDetail";
import {modifyApplyLinklist} from "@/api/document_account/standard";
import { listModifyApplyDistribute } from '@/api/my_business/modifyApplyDistribute'
import {
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, workflowbacktostart, getRecordbyPorcInstId, getRedirectDefId
} from '@/api/my_business/workflow'
import { queryUserProjectList } from '@/api/system/project'
import DealDrawer from '@/components/DealDrawer/index.vue'
import { checkPermi } from '@/utils/permission'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import { listPresetUser } from '@/api/setting/presetUser'
import { selectStatusByDocId } from '@/api/my_business/workflowApplyLog'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import mainComponent from '@/components/mainComponent/index.vue'
import DocIdListBox from '@views/workflowList/addWorkflow/add_import/docIdListBox.vue'
import TransferFlow from './add_import/transferFlow.vue'
import { getDivisionLeader, getDocManagersByDeptId, getLeader } from '../../../api/system/user'
import ApprovalBox from './add_import/approvalBox.vue'
import UserList from './add_import/userList.vue'
import { backFlowToOne } from '../../../api/my_business/workflow'
import { getCodeRuleDetailByDocClass } from '../../../api/setting/codeRule'
import { validateByUserName } from '@/api/system/userSignature'
import ModifyApplyUpload from './add_import/modifyApplyUpload.vue'
import WorkflowRouter from '../workflowRouter.vue'
import { trainValidateRequired } from '../../../api/file_processing/modifiyApply'
import SetupStartTime from './add_import/setupStartTime.vue'
import { coverEffectiveBatch } from '../../../api/file_processing/fileSignature'
import ErrorMsg from './add_import/errorMsg.vue'
import { getInfoBy } from '@/api/setting/docClassSetting'
import VersionList from './add_import/versionList.vue'
import { selectLinkList } from '../../../api/system/standard'
import { listDistributeGroupDetail } from '../../../api/setting/distributeGroupDetail'
export default {
  dicts: ["sys_yes_no"],
  components: {
    IdentityVerify,
    VersionList,
    ErrorMsg,
    SetupStartTime,
    WorkflowRouter,
    ModifyApplyUpload,
    UserList,
    ApprovalBox,
    TransferFlow,
    DocIdListBox,
    mainComponent,
    PresetUser,
    DealDrawer,
    Treeselect,
    processcode,
    WorkflowLogs
  },
  name: "UpdateDocBatch",
  props: ["dataType",'data'],
  data() {
    return {
      currentType: '',
      currentParams: null,
      verifyVisible: false,
      docClassData: {className:''},
      mark:undefined,
      applyType: 'disuse_doc_batch',
      batch:undefined,
      order: 0,
      hideNodeCode: [],
      yes: 'Y',
      no: 'N',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      codeRuleDetail: [],
      backFlowToOneStatus: true,
      approvalStatus: false,
      transferStatus: false,
      drawerSize: '90%',
      form: {},
      path: 'views/workflowList/addWorkflow/',
      code: '',
      drawerShow: false,
      dataList: [],
      classTypeList: undefined,
      defaultStaff:undefined,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      searchQuery: {},
      dealDrawerShow: false,
      submitLabel:this.$t('doc.this_dept_annex'),
      isProject: false,
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: "" },
      pButton: 'DISUSE',
      isSummary: false,
      projectList:[],
      project:{id:'',name:''},
      activeName: "info",
      nodeDetail: {},
      nodeDetailList: [],
      procDefKey: undefined,
      processData: {},
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "2",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible:false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      isLast: true,
      formData: {
        dataType: undefined,
        docClass: undefined,
        batchId: undefined,
        changeType: undefined,
        recordStatus: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        createBy: undefined,
        processStatus: undefined,
        whetherCustomer: undefined,
        yNTrain: undefined,
      },
      rules: {
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_reason_more_long`),
          },
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_content_more_long`),
          },
        ],
      },
      kuozhanshujuBool: {},
      kuozhanshuju: {},
      appendixesfileList: [],
      standardDocfileList: [],
      classLevelOptions: [],
      docClassList: [],
      summary: "",
      pListData: {},
      editStatus:false,
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      loading: false,
      detailLoading: false,
      flowStepLoading : false,
      procInstId: undefined,
      tableLoading: false,
    };
  },
  computed: {
  },
  watch: {
    "formData.docClass"(val,oldVal) {
      let _this = this
      if (val) {
        _this.getNodeDetailInfo()
        if (!_this.procInstId) {
          _this.getByUpDocClassAndBizType(val)
        }
        _this.getDocClassById(val);
      }
    },
    "formData.dataType"(val) {
      let _this = this
      _this.isProject = val==='project'
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  async created() {
    let response1 = await this.getConfigKey("back_flow_to_one")
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true';
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    showVerify(invokeFrom) {
      this.currentParams = invokeFrom
      // 判断是否需要身份验证
      if (this.nodeShow('authentication')) {
        this.verifyVisible = true
      } else {
        // 不需要验证时直接执行业务逻辑
        this.handleBusinessExecute(this.currentParams)
      }
    },

    // 执行业务逻辑
    handleBusinessExecute(params) {
      switch(params.type) {
        case 'flowSubmit':
          this.handleWorkflowSubmit(params)
          break
        case 'transfer':
          this.transferForm(params)
          break
        case 'backFlow':
          this.handleBackFlowToOne(params)
          break
        case 'publish':
          this.handlePublish(params)
          break
        case 'turnDown':
          this.handelpbohuiqicaoren(params)
          break
      }
    },
    handleExport2() {
      this.$refs.pdfView.init()
    },
    init(row) {
      let _this = this
      _this.loading = true
      _this.order = row.order?row.order:0
      _this.batch = row.batch
      _this.mark = row.mark
      _this.classTypeList = row.classTypeList
      _this.drawerSize = !!row.businessKey?'100%':'90%'
      // _this.getQueryUserProjectList()
      //是否编辑模式
      _this.$nextTick(async() => {
        if (row && row.procInstId) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          _this.getDetail(_this.procInstId)
        } else {
          _this.rest()
          _this.getSettingDocClassTreeseList();
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          // _this.getWorkflowprocesskey();
        }
      });
    },
    async getByUpDocClassAndBizType(docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass,_this.pButton)
      _this.procDefKey = data&&data.flowKey?data.flowKey:"";
      _this.getWorkflowprocesskey()
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      getBatchInfoByBpmnId(procInstId).then((res) => {
        _this.dataList = res.data;
        _this.formData = res.data[0]
        _this.setDataList()
        _this.getPresetUserList(_this.formData.batchId)
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    getDocClassById(val) {
      let _this = this
      settingDocClassId(val).then((response) => {
        _this.docClassData=response.data
      });
    },
    getPresetUserList(bizId){
      let _this = this
      listPresetUser({bizId: bizId}).then(res=>{
        _this.formData.presetUserList = res.data
      })
    },
    setDataList(){
      let _this = this
      _this.dataList.forEach(item=>{
        modifyApplyLinklist({applyId: item.id, linkType: "REF_DOC"}).then(res=>{
          item.docLinks = res.rows;
        })
        modifyApplyLinklist({applyId: item.id, linkType: "RECORD"}).then(res=>{
          item.recordLinks = res.rows;
        })
        modifyApplyLinklist({applyId: item.id, linkType: "NOTE"}).then(res=>{
          item.noteLinks = res.rows;
        })
        modifyApplyLinklist({applyId: item.id, linkType: "NOTE_DOC"}).then(res=>{
          item.noteDocLinks = res.rows;
        })
        listModifyApplyDistribute({applyId:item.id}).then(res => {
          item.distributeList = res.data
        })
      })
    },
    getQueryUserProjectList(){
      let _this = this
      queryUserProjectList().then(res=>{
        _this.projectList = res.data
      })
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.procInstId = undefined,
        _this.formData= {
          docClass: undefined,
          batchId: _this.$uuid.v4().split('-').join(''),
          changeType: _this.pButton,
          dataType: _this.dataType,
          recordStatus: undefined,
          presetUserList: [],
          applyTime: new Date().getTime(),
          createBy: _this.userInfo.userName,
          processStatus: undefined,
          whetherCustomer: undefined,
          yNTrain: undefined,
        }
      _this.dataList = []
    },
    procInstInfoAndStatus(procInstId){
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
          _this.getExtAttributeModel()
        }else {
          _this.loading = false
          _this.pListData = {procInstId:procInstId}
        }
      });
    },
    nodeShow(code){
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      }else  {
        return  false
      }
    },
    nodeFunCondition(code){
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item=>item.code===code)
      if (nodeDetail&&nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      }else {
        return undefined
      }
    },
    getWorkflowprocesskey() {
      let _this = this
      // _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      }else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel(){
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId&&curActDefId) {
        _this.getNodeDetailInfo()
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {
          console.log("扩展属性====>", res);
          let kuozhanshujuBool = {}
          let kuozhanshuju = {}
          res.data.forEach(item=>{
            if (item.objType==='Boolean') {
              kuozhanshujuBool[item.objKey] = item.objValue
            } else {
              kuozhanshuju[item.objKey] = item.objValue
            }
          })
          _this.kuozhanshujuBool = kuozhanshujuBool;
          _this.kuozhanshuju = kuozhanshuju;
        });
      }else {
        _this.kuozhanshujuBool = {}
        _this.kuozhanshuju = {}
      }
    },
    attributeModelBool(val){
      if (this.kuozhanshujuBool&&this.kuozhanshujuBool!=={}) {
        let obj = this.kuozhanshujuBool[val]
        return !!obj&&obj==='true'
      }else {
        return false
      }
    },
    attributeModel(val){
      return this.kuozhanshuju[val]
    },
    getNodeDetailInfo(){
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData&&curActDefId&&_this.formData.docClass) {
        getInfo(_this.formData.docClass,_this.pButton,curActDefId).then(res=>{
          let nodeDetail = {}
          res.data.forEach(item=>{
            nodeDetail[item.code] = true
          })
          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    async initStatus() {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.nodeShow('shenhe')?_this.$t(`file_handle.change_auditing`):_this.$t('doc.this_dept_annex')
      if (!_this.batchStatus){
        if (_this.nodeShow('whether_train')) {
          let funCondition = _this.nodeFunCondition('whether_train')
          if (funCondition){
            if(funCondition.limitValue && !_this.formData.yNTrain) {
              _this.formData.yNTrain = funCondition.limitValue
            }
          }
        }
        if (_this.nodeShow('whether_customer_record')) {
          let funCondition = _this.nodeFunCondition('whether_customer_record')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.whetherCustomer) {
              _this.formData.whetherCustomer = funCondition.limitValue
            }
          }
        }
        _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
        if (_this.nodeShow('top_btn_generate_code')) {
          _this.getCodeRuleDetailList()
        }
        if (_this.approvalStatus) {
          _this.jointReviewRedirect()
          await _this.setPresetUserList()
          if (_this.$refs.approvalBox) {
            _this.$refs.approvalBox.init()
          }
        }
      }
      _this.loading = false
    },
    getCodeRuleDetailList(){
      let _this = this
      let query = {
        docClass: _this.formData.docClass,
        ruleTypeList: ['FORM','DICT']
      }
      getCodeRuleDetailByDocClass(query).then(res=>{
        let codeRuleDetail = []
        for (let item of res.data) {
          let keys = item.ruleValue.split('@')
          if ("classCode" !== keys[0]) {
            if (keys.length>1) {
              codeRuleDetail.push(keys[1])
            }else {
              if ("deptCode" === keys[0]) {
                codeRuleDetail.push('deptId')
              }else {
                codeRuleDetail.push(keys[0])
              }
            }
          }
        }
        _this.codeRuleDetail = codeRuleDetail
      })
    },
    close() {
      this.viewShow = false;
      this.$emit("close")
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    //不需要验证必填的保存
    async saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      if (_this.dataList.length < 1) {
        _this.$modal.msgWarning(_this.$t(`doc.this_dept_select_file1`)+"！");
        return;
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.dataList = _this.dataList
      formData.editStatus = _this.editStatus
      formData.recordStatus = "draft";
      formData.procTitle = '【'+_this.$t(`doc.batch_title_text_1`)+'】' + _this.$t(`doc.batch_title_text_2`,[_this.dataList[0].docName,_this.dataList.length])
      if (_this.procInstId) {
        let res = await updateModifyApplyBatch(formData)
        if (res.code===200) {
          _this.setDataListId(res.data)
          _this.$message({
            message: _this.$t(`file_handle.change_save_succ`),//提示的信息
            type:'success',　　//类型是成功
            duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose:()=>{
              _this.loading = false
            }
          });
        }
      } else {
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.procTitle,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          order: _this.order,
          mark: _this.mark,
          type: _this.applyType,
        };
        let res = await addModifyApplyBatch(formData)
        if (res.code === 200) {
          _this.setDataListId(res.data.idList)
          _this.procInstId = res.data.processInstanceModel.procInstId
          _this.procInstInfoAndStatus(_this.procInstId)
          _this.$message({
            message: _this.$t(`file_handle.change_save_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.loading = false
            }
          });
        }
        ;
      }
    },
    setDataListId(idList){
      let _this = this
      _this.dataList.forEach((item,index)=>{
        _this.$set(item,'id',idList[index])
      })
    },
    handleBackFlowToOne(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`);
          }
        },
      }).then(({ value })=> {
        _this.loading = true
        let wf_procTitle = '【'+_this.$t(`doc.batch_title_text_1`)+'】' + _this.$t(`doc.batch_title_text_2`,[_this.dataList[0].docName,_this.dataList.length])
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: wf_procTitle,
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName,
              },
              bizType: _this.pButton,
              review: true,
              applyStatus: false,
              status: 'draft',
              type: _this.applyType,
              mark: _this.mark,
              order: 0,
            };
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break;
            }
          }
          _this.close(true);
        })
      })
    },
    deleteForm(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`);
          }
        },
      }).then(({ value })=> {
        _this.loading = true
        let formData = {
          batchId: _this.formData.batchId,
          dataList: _this.dataList,
          bpmClientInputModel: {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_curComment: value,
            },
            order: _this.order,
            type: _this.applyType,
            mark: _this.mark,
            review: true,
            applyStatus: false,
          },
          recordStatus: 'cancel',
          editStatus: false
        }
        addModifyApplyBatch(formData).then((res) => {
          if (res.code === 200) {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`));
            this.close(true);
          }
        });
      })
    },
    transferForm(){
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData,_this.formData.batchId,_this.applyType,_this.order,_this.pButton)
    },
    async dataListFormValidate() {
      let _this = this
      let errorList = []
      for (let item of _this.dataList) {
        for (let key in _this.rules) {
          for (let rule of _this.rules[key]) {
            if (rule.classType && rule.classType !== item.classType) {
              continue
            }
            if (rule.required && !rule.validator && !item[key] && (rule.nodeShow ? _this.nodeShow(rule.nodeShow) : true)) {
              errorList.push({ docName: item.docName, msg: rule.message })
            }
            if (rule.max && item[key] && item[key].length > rule.max) {
              errorList.push({ docName: item.docName, msg: rule.message })
            }
            if (rule.required && rule.validator) {
              let msg = await rule.validator(item)
              if (msg) {
                errorList.push({ docName: item.docName, msg: msg })
              }
            }
          }
        }
      }
      if (errorList.length > 0) {
        _this.$refs.errorMsg.init(errorList)
        return true
      }
      return false
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      let dialogVisible = true
      if (_this.nodeShow('top_btn_generate_code')) {
        if (_this.dataList.some(item=>!item.docId)) {
          _this.$modal.alert(_this.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`));
          return;
        }
      }
      if (_this.editStatus) {
        if (_this.dataList.length < 1) {
          _this.$modal.msgWarning(_this.$t(`doc.this_dept_select_file1`)+"！");
          _this.loading = false;
          return true;
        }
        if (await _this.dataListFormValidate()){
          return
        }
      }
      //审核
      if ((_this.nodeShow('shenhe')||_this.nodeShow('pizhun'))) {
        if (_this.approvalStatus) {
          _this.formSubmit = _this.$refs.approvalBox.formSubmit
        }
        if (_this.formSubmit.pass===undefined) {
          _this.$modal.msgError(_this.submitLabel+_this.$t(`file_handle.change_result_not_null`));
          return;
        }
        // 验证是否填写了审核意见
        if(!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`));
          return;
        }
      }
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')){
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(_this.formData.docClass,_this.pButton,_this.formData.presetUserList,nodeCode)
        if (bool) {
          return true;
        }
      }
      //培训记录
      if (_this.nodeShow('page_oper_add_train_record')&&_this.formData.yNTrain===_this.yes&& _this.formSubmit.pass!==false) {
        let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
        if (!funCondition||(funCondition&&funCondition.validate)) {
          let res = await trainValidateRequired({batchId:_this.formData.batchId,type:'train'})
          if (!res.data) {
            _this.$modal.msgError('【'+res.msg+'】'+_this.$t(`doc.this_dept_pls_upload_train_file`));
            return true;
          }
        }
      }
      //客户封面
      if (_this.nodeShow('page_oper_add_customer_cover')&&_this.formData.whetherCustomer===_this.yes&& _this.formSubmit.pass!==false) {
        let funCondition = _this.nodeFunCondition('page_oper_add_customer_cover')
        if (!funCondition||(funCondition&&funCondition.validate)) {
          let res = await trainValidateRequired({batchId:_this.formData.batchId,type:'cover'})
          if (!res.data) {
            _this.$modal.msgError('【'+res.msg+'】'+_this.$t(`sys_mgr_log.user_signature_upload_text1`)+_this.$t(`doc.customer_cover`)+'！');
            return true;
          }
        }
      }
      //客户记录
      if (_this.nodeShow('add_customer_record')&&_this.formData.whetherCustomer===_this.yes&& _this.formSubmit.pass!==false) {
        let funCondition = _this.nodeFunCondition('add_customer_record')
        if (!funCondition||(funCondition&&funCondition.validate)) {
          let res = await trainValidateRequired({batchId:_this.formData.batchId,type:'customer'})
          if (!res.data) {
            _this.$modal.msgError('【'+res.msg+'】'+_this.$t(`sys_mgr_log.user_signature_upload_text1`)+_this.$t(`doc.customer_record`)+'！');
            return true;
          }
        }
      }
      //签名
      if (_this.nodeShow('log_title')) {
        // 如果是编制环节校验编制人
        if(_this.editStatus){
          let res = await validateByUserName({userCode: _this.formData.createBy})
          if (!res.data) {
            _this.$modal.msgError(_this.$t(`doc.user_organizer_validate`));
            return true;
          }
        }
        let res = await validateByUserName({userCode: _this.userInfo.userName})
        if (!res.data) {
          _this.$modal.msgError(_this.$t(`doc.user_signature_validate`));
          return true;
        }
      }
      if (await _this.validate()){
        _this.loading = false;
        return
      }
      _this.loading = false;
      if (!_this.approvalStatus) {
        _this.jointReviewRedirect()
        await _this.setPresetUserList()
        this.dialogVisible = true;
      }else {
        _this.$confirm(_this.$t(`file_handle.submit_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: "warning",
        })
          .then(() => {
            _this.handleWorkflowSubmit({})
          })
      }
    },
    async setPresetUserList(){
      let _this = this
      if(_this.nodeShow('preset_countersign')){
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition&&funCondition.nodeCode&& funCondition.nodeCode.length > 0&&funCondition.groupId) {
          let users = [];
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item=>{
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept,
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 部门分管领导
      if (_this.nodeShow('next_set_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          console.log(funCondition)
          console.log(_this.userInfo)
          if (funCondition.validate && _this.formData.deptId) {
            console.log("111")
            let res = await getLeader(_this.userInfo.userName, _this.formData.deptId)
            user = res.data
          } else {
            console.log("222")
            let res = await getLeader(_this.userInfo.userName, _this.userInfo.dept.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      if (_this.nodeShow('next_set_division_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_division_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getDivisionLeader(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDivisionLeader(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      //设置流程待选任意为文控
      if (_this.nodeShow('set_flow_select_list')) {
        let funCondition = _this.nodeFunCondition('set_flow_select_list')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getDocManagersByDeptId(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDocManagersByDeptId(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = []
            user.forEach(item=>{
              users.push({
                userName: item.userName,
                nickName: item.nickName,
                deptId: item.deptId,
                deptName: item.dept.deptName
              })
            })
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (_this.formData.presetUserList.length > 0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if (_this.nodeShow('cdxmd') && _this.batch) {
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition && funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({
            batch: _this.batch,
            nextDefId: _this.pListData.curActDefId,
            havaDetail: true
          })
          let nodeCode = ""
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({
              userName: item.sender,
              nickName: item.nickName,
              deptId: item.senderDeptId,
              deptName: item.deptName
            })
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode === nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.isTrain = _this.formData.yNTrain
      _this.searchQuery.isCustomer = _this.formData.whetherCustomer
      _this.searchQuery.ext5 = _this.formData.ext5
      _this.searchQuery.pass = _this.formSubmit.pass
      _this.searchQuery.batch = !!_this.batch
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')) {
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition && funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围  过滤出没有预选人员的环节
          hideNodeCode = funCondition.nodeCode.filter(item=>!defaultStaff.some(dpu=>item===dpu.nodeCode&&dpu.users&&JSON.parse(dpu.users).length>0))
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode && hideNodeCode.length === length) {
            hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length - hideNodeCode.length) <= limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length === 0) {
            defaultStaff.forEach(item => {
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
          }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect(){
      let _this = this
      if(_this.nodeShow('hscdx')||_this.nodeShow('wgcdx')){
        getRecordbyPorcInstId(_this.procInstId).then(res=>{
          if (res.data.length===1){
            let query={
              docClass: _this.formData.docClass,
              bizType: _this.pButton,
              code: "cdxmd",
              batch: _this.batch,
            }
            getRedirectDefId(query).then(res1=>{
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition&&funCondition.nodeCode&&funCondition.nodeCode.length===1) {
                  let next = res1.data.find(item=>item.nextDefId===funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          }else{
            _this.isLast = false
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if(val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validate(){
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      //验证文件
      return validate;
    },
    // 签章生效
    async handleCoverEffective() {
      let self = this
      // 验证设置生效时间
      if (self.nodeShow('publish_setup_time')&&self.formData.setupTime == null) {
        self.$modal.alert(self.$t(`doc.this_dept_set_effective_date`));
        return false;
      }
      if (self.dataList.some(item=>!item.docId)){
        self.$modal.msgWarning(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`))
        return
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      coverEffectiveBatch(self.formData.batchId, 'effective').then((res) => {
        if (res.code === 200) {
          res.data.forEach((element, i) => {
            self.dataList.forEach((val, ix) => {
              if (val.id === element.id) {
                self.dataList[ix].encryptFileId = element.encryptFileId;
                self.dataList[ix].isSignature = 'C';
              }
            });
          });
          self.$modal.msgSuccess(self.$t(`doc.this_dept_operation_succ`));
        } else {
          self.$modal.alert(res.msg);
        }
        self.loading = false
      });
    },
    // 签章生效
    async handleSignEffective() {
      let self = this
      if (self.dataList.some(item=>!item.docId)){
        self.$modal.msgWarning(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`))
        return
      }
      if (!self.dataList.some(item=>item.isSignature!=='E')) {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }
      if (self.nodeShow('top_btn_file_cover')) {
        if (self.dataList.some(item=>item.isSignature!=='C')) {
          self.$modal.alert(self.$t(`doc.this_dept_pls_gen_execute`)+'【'+self.$t(`file_handle.change_generate_cover`)+'】');
          return false;
        }
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      signEffectiveBatch(self.formData.batchId, 'effective').then((res) => {
        if (res.code === 200) {
          res.data.forEach((element, i) => {
            self.dataList.forEach((val, ix) => {
              if (val.id === element.id) {
                self.dataList[ix].encryptFileId = element.encryptFileId;
                self.dataList[ix].isSignature = 'E';
              }
            });
          });
          self.$modal.alert(self.$t(`file_handle.change_signature_text3`));
        } else {
          self.$modal.alert(res.msg);
        }
        self.loading = false
      });
    },
    // 执行发布（推送文件到文件台账）
    async handlePublish() {
      let self = this
      self.$modal.confirm(self.$t(`doc.this_dept_confirm_release`)).then(function() {
        // 执行发布的签章
        self.loading = true
        signEffectiveBatch(self.formData.batchId, 'publish').then((res) => {
          if (res.code === 200) {
            self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text6`));
            self.handleWorkflowSubmit('publish');
          } else {
            self.$modal.alert(res.msg);
          }
          self.loading = false
        });
      }).then(() => {
        // 取消操作
      }).catch(() => {
      });
    },
    //提交表单和流程数据
    async handleWorkflowSubmit(invokeFrom) {
      let _this = this
      if (await _this.validate()) {
        return
      }
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.dataList = this.dataList
      let nextData = undefined
      let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
      if (typeof (invokeFrom) == 'object') {
        let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
        nextData = prochild.handleWorkflowSubmit();
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        nextData ={
          wf_nextActDefId: 'end',
          wf_nextActDefName: '结束',
          direction: true,
          wf_receivers: [],
          fields: {},
        }
      }
      if (!nextData) {
        return;
      }
      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      let wf_procTitle = '【'+_this.$t(`doc.batch_title_text_1`)+'】' + _this.$t(`doc.batch_title_text_2`,[_this.dataList[0].docName,_this.dataList.length])
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: wf_procTitle,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: nextData.wf_nextActDefName,
            fields: nextData.fields
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction,
        };
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {
          model: {
            wf_procTitle: wf_procTitle,
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: nextData.wf_nextActDefName,
            fields: nextData.fields
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction,
        };
      }
      if (_this.nodeShow('log_title')) {
        let funCondition = _this.nodeFunCondition('log_title')
        if (funCondition && funCondition.limitValue) {
          formData.bpmClientInputModel.title = funCondition.limitValue
        }
      }
      if (nextData.wf_nextActDefId === 'end') {
        //办结
        formData.recordStatus = 'done'
        formData.bpmClientInputModel.jointReview = false
      } else {
        //进行中
        formData.recordStatus = 'doing'
        formData.bpmClientInputModel.jointReview = nextData.multi
      }
      if (_this.nodeShow('hscdx')||_this.nodeShow('wgcdx')) {
        formData.bpmClientInputModel.batch = _this.batch
        formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
        formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
        if (_this.redirectOrder) {
          formData.bpmClientInputModel.order = _this.redirectOrder
        }
        formData.bpmClientInputModel.isLast = _this.isLast
      }
      formData.editStatus = _this.editStatus
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader')|| _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
      formData.customerEdit = _this.nodeShow('whether_customer_record')
      if(_this.nodeShow('file_range_preview_edit') || _this.nodeShow('distribution_edit')){
        formData.editStatus = true
      }
      addModifyApplyBatch(formData).then((res) => {
        if (res.code === 200) {
          _this.$message({
            message:_this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false;
              _this.close();
            }
          });
        }
      });
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: "1", dataType:this.formData.dataType,classTypeList:this.classTypeList,openPurview:true }).then(
        (res) => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          let classLevelOptions = []
          if (this.editStatus) {
            classLevelOptions = this.handleTree(res.rows.filter(item=>item.purview),"id","parentClassId");
            classLevelOptions = classLevelOptions.filter(item=>{
              return checkPermi(["doc:class:"+item.permission])
            })
          }else {
            classLevelOptions = this.handleTree(res.rows,"id","parentClassId");
          }
          this.classLevelOptions = classLevelOptions
          if (!this.formData.classType) {
            this.formData.classType = classLevelOptions[0].classType
          }
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.procInstId);
      });
    },
    handelpbohuiqicaoren() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_reject_text`);
          }
        },
      })
        .then(({ value }) => {
          // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
          let wf_procTitle = '【'+_this.$t(`doc.batch_title_text_1`)+'】' + _this.$t(`doc.batch_title_text_2`,[_this.dataList[0].docName,_this.dataList.length])
          let backarr = {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_businessKey: _this.formData.batchId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_procTitle: wf_procTitle,
              wf_procDefId: _this.pListData.procDefId,
              wf_curComment: value,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
            },
            order: 0,
            review: true,
            applyStatus: false,
            type: _this.applyType,
            bizType: _this.pButton
          };
          workflowbacktostart(backarr).then((response) => {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_reject_succ`));
            _this.close();
          });
        })
    },
    async shengchengbianhao() {
      let _this = this
      if (_this.editStatus && _this.dataList.some(item=>!item.docId)) {
        if (_this.codeRuleDetail) {
          let bool = false
          let errorList = []
          for (let data of _this.dataList) {
            for (let item of _this.codeRuleDetail) {
              if (_this.rules[item] && !data[item]) {
                errorList.push({docName:data.docName,msg:_this.rules[item][0].message})
                bool = true
              }
            }
          }
          if (bool) {
            _this.$refs.errorMsg.init(errorList)
            return
          }
        }
        await _this.saveForm()
      }
      _this.shenchenbianhao = true;
      let applyIdList = _this.dataList.map(item => item.id)
      _this.$nextTick(() => {
        _this.$refs.docIdListBox.init(applyIdList);
      })
    },
    setDocId(docIdData){
      let _this =this
      docIdData.forEach((element, i) => {
        let index = _this.dataList.findIndex(val=>val.id === element.busId)
        _this.$set(_this.dataList[index],'docId',element.newNo)
        if (_this.dataList[index].isSignature !== 'N') {
          _this.$set(_this.dataList[index],'isSignature','N')
          _this.setSignatureStatus(element.busId)
        }
      })
    },
    setRecordDocId(recordLinks){
      let _this =this
      recordLinks.forEach((element, i) => {
        _this.formData.recordLinks.forEach((val, ix) => {
          if (val.fileId === element.busId) {
            _this.formData.recordLinks[ix].docId = element.newNo;
          }
        });
      });
    },
    selectDocClass(node){
      this.formData.classType = node.classType
      this.formData.docClass = node.id
      this.$refs.elForm.validateField("docClass");
    },
    selectPresetUser(){
      let _this = this
      _this.$nextTick(()=>{
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null,null,_this.formData.docClass,_this.pButton,_this.formData.presetUserList,nodeCode)
      })
    },
    async selectHandlePresetUser(source, index, data) {
      let _this = this
      _this.$set(_this.formData, "presetUserList", data)
      if (_this.approvalStatus) {
        if (_this.$refs.approvalBox) {
          _this.$refs.approvalBox.init()
        }
      }
    },
    handleCloseChange(){
      this.dealDrawerShow = false
    },
    async handleAdd(data) { // 新增
      let _this = this
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (valid) {
          _this.handleDetail({ type: 'disuse_doc', classTypeList: _this.classTypeList,batchId: _this.formData.batchId, docClass: _this.formData.docClass,versionId:data.versionId,flag:'1'})
        }
      }
    },
    handleUpdate(row,index){
      let _this = this
      _this.handleDetail({ ...row,type: 'disuse_doc', classTypeList: _this.classTypeList,procInstId:this.procInstId,status:_this.workflowStatus?'1':'2',dataListIndex:index })
    },
    handleDelete(index){
      this.dataList.splice(index,1);
    },
    handleDetail(row){
      let _this = this
      _this.code = row.type
      _this.form = JSON.parse(JSON.stringify(row))
      _this.drawerShow = true
    },
    handleClose(value,index){
      if (value) {
        if (!isNaN(index)) {
          if (this.dataList.some((item,i)=>item.docName===value.docName&&index!==i)) {
            this.$message.warning(this.$t(`doc.this_dept_file_name_exist`))
            return
          }
          this.$set(this.dataList,index,value)
        }else {
          if (this.dataList.some(item=>item.docName===value.docName)) {
            this.$message.warning(this.$t(`doc.this_dept_file_name_exist`))
            return
          }
          this.dataList.push(value)
        }
      }
      this.drawerShow = false
    },
    setSignatureStatus(id){
      updateModifyApply({
        id: id,
        isSignature:'N',
        onlyEdit: true
      })
    },
    handleSelect() {
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(null,null,null)
      })
    },
    handleSubmitUser(source,index,user) {
      let _this = this
      _this.formData.nickName = user.nickName;
      _this.formData.userName = user.userName;
      _this.formData.deptId = user.dept.deptId;
      _this.formData.deptName = user.dept.deptName;
      _this.updateDataList('nickName','userName','deptId','deptName');
    },
    updateDataList(...keys){
      let _this = this
      _this.dataList.forEach(item=>{
        for (let key of [...keys]){
          item[key] = _this.formData[key]
        }
      })
    },
    async upLoadDataList(dataList, callback) {
      let _this = this
      if (dataList && dataList.length > 0) {
        _this.dataList.push(...dataList)
      }
      await _this.saveForm()
      callback()
    },
    dataRefresh(){
      this.getDetail(this.procInstId)
    },
    setupStartTime(){
      let _this = this
      _this.$refs.setupStartTime.init(_this.formData.setupTime)
    },
    updateStartTime(setupTime){
      let _this = this
      _this.formData.isSignature = 'N'
      _this.formData.setupTime = setupTime
      _this.updateDataList('isSignature','setupTime');
    },
    versionSearchInit(){
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.versionList.init(null,null,true)
      })
    },
    selectableFun(row, index, versionData, selectInfoData){
      if (!row.hasPerms) {
        return false
      }
      if (versionData) {
        return versionData.findIndex(item=>item.docId===row.docId)<0
      }else {
        return true
      }
    },
    async versionSelectHandle(source, index, data, callback) {
      let _this = this
      if (!(data&&data.length>0)) {
        callback(true)
        return
      }
      _this.tableLoading = true
      for (const item of data) {
        let res = await selectStatusByDocId({ versionId: item.versionId, notInDraft: false })
        if (res.data != 0) {
          _this.$modal.msgWarning(res.msg);
          _this.tableLoading = false
          callback(false)
          return
        }
      }
      callback(true)
      _this.formData.docClass = data[0].docClass
      let dataList = []
      _this.tableLoading = true
      for (const item of data) {
        item.id = undefined
        item.batchId = _this.formData.batchId
        item.changeType = _this.pButton
        if(!item.userName) {
          item.userName= _this.userInfo.dept.deptId
          item.deptName= _this.userInfo.dept.deptName
          item.userName= _this.userInfo.userName
          item.nickName= _this.userInfo.nickName
        }
        item.createBy= _this.formData.createBy
        item.changeReason= ''
        item.content = ''
        item.docStatus = item.status
        item.applyTime = _this.formData.applyTime
        item.whetherCustomer = _this.formData.whetherCustomer
        item.whetherRetain = _this.no
        await _this.setVersionDataList(item)
        dataList.push(item)
      }
      _this.dataList.push(...dataList)
      _this.tableLoading = false
    },
    async setVersionDataList(data) {
      //主文件
      let res = await selectLinkList({ linkType: "DOC", versionId: data.versionId })
      data.preStandardDoc = res.data[0]
      //主文件
      let res0 = await selectLinkList({ linkType: "APPENDIX", versionId: data.versionId })
      data.preAppendixes = res0.data
      //关联文件
      let res1 = await linkLoglistlink({ linkType: "REF_DOC", versionId: data.versionId })
      data.docLinks = res1.data
      //关联记录
      let res2 = await linkLoglistlink({ linkType: "RECORD", versionId: data.versionId, status: '1' })
      data.recordLinks = res2.data
      //关联记录
      let res3 = await linkLoglistlink({ linkType: "NOTE", versionId: data.versionId, status: '1' })
      data.noteLinks = res3.data
      //关联记录
      let res4 = await linkLoglistlink({ linkType: "NOTE_DOC", versionId: data.versionId, status: '1' })
      data.noteDocLinks = res4.data
    },
  },
};
</script>
