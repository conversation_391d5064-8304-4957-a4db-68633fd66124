<template>
  <div class="document_change_add"
       v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`file_set.type_revise`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="procInstId"
                   @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus&&!batchStatus"
                   @click="selectPresetUser">{{ $t(`file_handle.change_select_people`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_cancel')&&procInstId&&workflowStatus&&!batchStatus"
                   type="danger"
                   @click="deleteForm"
                   v-dbClick>{{ $t(`file_handle.change_revoke`) }}</el-button>
        <el-button v-if="procInstId&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'transfer'})"
                   v-dbClick>{{ transferStatus?$t(`file_handle.transfer_return`):$t(`file_handle.transfer`)}}</el-button>
        <el-button v-if="procInstId&&!workflowStatus&&'doing'===formData.processStatus&&formData.createBy===userInfo.userName&&backFlowToOneStatus&&!batchStatus"
                   type="danger"
                   @click="showVerify({type:'backFlow'})"
                   v-dbClick>{{$t(`file_handle.change_withdraw`)}}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'turnDown'})"
                   type="danger">{{ $t(`file_handle.change_reject_to_preparer`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_generate_code')&&workflowStatus&&!batchStatus"
                   @click="shengchengbianhao()"
                   type="primary">{{ $t(`file_handle.change_generate_num`) }}</el-button>
        <!-- 【签章生效】和【执行发布】一般出现在发布环节 -->
        <el-button v-if="nodeShow('publish_setup_time')&&workflowStatus&&!batchStatus"
                   @click="setupStartTime()"
                   type="primary">{{ $t(`doc.this_dept_select_effective_date`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_file_cover')&&workflowStatus&&!batchStatus"
                   @click="handleCoverEffective()"
                   type="primary">{{ $t(`file_handle.change_generate_cover`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_setup_time')&&workflowStatus&&!batchStatus"
                   @click="handleSignEffective()"
                   type="primary">{{ $t(`file_handle.change_signature_effc`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_publish_file')&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'publish'})"
                   type="primary">{{ $t(`file_handle.change_execute_release`) }}</el-button>
        <!-- 提交按钮在 非【执行发布】环节出现 -->
        <el-button v-if="!nodeShow('top_btn_publish_file') && workflowStatus&&!batchStatus&&isShowSubmit()"
                   type="primary"
                   @click="submitForm"
                   v-dbClick>
          {{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button v-if="editStatus"
                   type="primary"
                   @click="saveForm"
                   v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_edit_record`)" name="edit"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elForm"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15"
                    v-if="isProject">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_project`)+`:`"
                              prop="projectId">
                  <span>{{formData.projectName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" ">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_types`)"
                              prop="docClass">
                  <span>{{docClassData.className}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_type`)+`:`"
                              prop="changeType">
                  <span>{{$t(`doc.this_dept_revision`)}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`)+`:`"
                              prop="upVersionId">
                  <span>{{formData.upDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)"
                              prop="parentDocId">
                  <span>{{formData.parentDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)"
                              prop="docName">
                  <el-input v-if="editStatus"
                            v-model="formData.docName"
                            :placeholder="$t(`doc.this_dept_insert_name`)"
                            clearable
                            :style="{ width: '100%' }"
                            maxlength="500"
                            show-word-limit
                            :disabled="disabled"></el-input>
                  <span v-else>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" "></el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">

              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)"
                              prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)"
                              prop="versionValue">
                  <el-input v-if="editStatus"
                            v-model="formData.versionValue"
                            :placeholder="$t(`doc.this_dept_insert_ver`)"
                            clearable
                            :style="{ width: '100%' }"
                            maxlength="50"
                            show-word-limit></el-input>
                  <span v-else>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staffing_depts`)"
                              prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staff`)"
                              prop="userName">
                  <el-input v-if="editStatus&&!batchStatus"
                            style="width: 80%; margin-right: 10px"
                            readonly
                            v-model.trim="formData.nickName"
                            placeholder="请选择编制人员">
                    <el-button slot="append"
                               icon="el-icon-search"
                               @click="handleSelect"></el-button>
                  </el-input>
                  <span v-else>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`"
                              prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" "></el-form-item>
              </el-col>
            </el-row>

            <dynamic-form ref="dynamic"
                          :formRuleList="formRuleList"
                          :formData="formData"
                          :editStatus="editStatus"
                          :deptOptions2="deptOptions2"
                          :dict="dict" />

            <!----------------------------------根据文件分类，编制时新增字段 ------------------------------------------------>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.whether_retain`)+':'"
                              prop="whetherRetain">
                  <el-radio-group v-if="editStatus"
                                  v-model.trim="formData.whetherRetain">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.whetherRetain" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item v-if="formData.whetherRetain===yes"
                              :label="$t(`doc.retain_deadline`)+`:`"
                              prop="retainDeadline">
                  <el-date-picker v-if="editStatus"
                                  v-show="formData.whetherRetain===yes"
                                  v-model="formData.retainDeadline"
                                  align="right"
                                  type="date"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  :placeholder="$t(`doc.this_dept_select_date`)">
                  </el-date-picker>
                  <span v-else
                        v-show="formData.whetherRetain===yes">{{parseTime(formData.retainDeadline, "{y}-{m}-{d}")}}</span>
                </el-form-item>
                <el-form-item v-else
                              label=" "
                              prop=" ">
                </el-form-item>
              </el-col>
            </el-row>

            <!--            <el-row gutter="15" v-if="formData.classType===classTypeForeign">-->
            <!--              <el-col :span="12">-->
            <!--                <el-form-item  :label="$t(`doc.this_dept_file_effective_date`)+`:`" prop="fileEffectiveDate">-->
            <!--                  <el-date-picker-->
            <!--                    v-if="editStatus"-->
            <!--                    v-model="formData.fileEffectiveDate"-->
            <!--                    type="datetime"-->
            <!--                    value-format="yyyy-MM-dd HH:mm:ss"-->
            <!--                    :placeholder="$t(`doc.this_dept_select_date`)"-->
            <!--                    align="right">-->
            <!--                  </el-date-picker>-->
            <!--                  <span v-else>{{ parseTime(formData.fileEffectiveDate) }}</span>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :span="12">-->
            <!--                <el-form-item :label="$t(`doc.this_dept_revise_date`)+`:`" prop="revisionDate">-->
            <!--                  <el-date-picker-->
            <!--                    v-if="editStatus"-->
            <!--                    v-model="formData.revisionDate"-->
            <!--                    type="datetime"-->
            <!--                    value-format="yyyy-MM-dd HH:mm:ss"-->
            <!--                    :placeholder="$t(`doc.this_dept_select_date`)"-->
            <!--                    align="right">-->
            <!--                  </el-date-picker>-->
            <!--                  <span v-else>{{ parseTime(formData.revisionDate) }}</span>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_temp`)">
                  <fileUpload
                    :editStatus="false"
                    v-model.trim="mobanwenjian"
                    :isShowTip="false"
                    :downStatus="true"
                    v-slot="{fileId,fileName,isOffice}"
                  >
                    <online-edit v-if="editStatus&&isOffice&&nodeShow('online_edit')" :fileId="fileId" :fileName="fileName"
                                 :protoFileId="standardDocfileList.length>0?standardDocfileList[0].protoFileId:undefined"
                                 :handleMsg="messageListener"
                    >{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                  </fileUpload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_source`)+`:`"
                              prop="invokeId">
                  <span style="color: #385bb4; cursor: pointer"
                        @click="handleDeal(formData)">{{formData.preChangeCode}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_effec_ver`)+`:`">
                  <fileUpload
                    :editStatus="false"
                    v-model.trim="preStandardDocfileList"
                    :isShowTip="false"
                    :downStatus="editStatus&&(formData.classType!==classTypeRecord&&checkPermi(['doc:file:download'])||(formData.classType===classTypeRecord&&checkPermi(['record:file:download'])))"
                    v-slot="{fileId,fileName,isOffice}"
                  >
                    <online-edit v-if="editStatus&&isOffice&&nodeShow('online_edit')" :fileId="fileId" :fileName="fileName"
                                 :protoFileId="standardDocfileList.length>0?standardDocfileList[0].protoFileId:undefined"
                                 :handleMsg="messageListener"
                    >{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                  </fileUpload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_effec_ver`)+`:`" prop="preAppendixes">
                  <div class="link-box bzlink-box" v-show="nodeShow('page_oper_file_download') && appendixesfileList && appendixesfileList.length>0">
                    <span style="color: #385bb4; margin-left: 10px; cursor: pointer"></span>
                    <span style="color: #385bb4; margin-left: 10px; cursor: pointer;" @click="handelefileLocalDownloadAll(appendixesfileList)" >
                      下载全部
                    </span>
                  </div>
                  <fileUpload
                    :editStatus="false"
                    v-model.trim="preAppendixesfileList"
                    :downStatus="editStatus&&(formData.classType!==classTypeRecord&&checkPermi(['doc:file:download'])||(formData.classType===classTypeRecord&&checkPermi(['record:file:download'])))"
                    :fileType="[]"
                    :isfileType="false"
                    :limit="100"
                    @input="fileUpdate('appendixesfileList')"
                    :isShowTip="false"
                    v-slot="{fileId,fileName,isOffice,protoFileId}"
                  >
                    <online-edit v-if="editStatus&&isOffice&&nodeShow('online_edit')" cbType="saveAppendix" :fileId="fileId"
                                 :fileName="fileName" :handleMsg="messageListener"
                    >{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                  </fileUpload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_change_ver`)+`:`"
                              prop="standardDocfileList">
                  <!-- <div class="fileuploadbzremark"> -->
                  <fileUpload
                    :editStatus="editStatus"
                    v-model.trim="standardDocfileList"
                    :downStatus="nodeShow('page_oper_file_download')"
                    :coverDownStatus="nodeShow('cover_file_download')"
                    limit="1"
                    @input="fileUpdate('standardDocfileList')"
                    :fileType="['docx','doc','xls','xlsx','pdf','ppt','pptx']"
                    :isShowTip="false"
                    :isMode="nodeShow('page_oper_file_edit')"
                    v-slot="{fileId,fileName,isOffice,protoFileId}"
                  >
                    <online-edit v-if="isOffice&&nodeShow('online_edit')" :fileId="fileId" :fileName="fileName"
                                 :protoFileId="protoFileId" :handleMsg="messageListener"
                    >{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_revision')" :fileId="fileId" :fileName="fileName"
                                 :protoFileId="protoFileId" :handleMsg="messageListener" type="0"
                                 cbPath="/process/modifyApplyLink/online/update" :applyId="formData.id"
                    >{{$t(`dicts.flow_node_fun_list_online_revision`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_annotation')" :fileId="fileId" :fileName="fileName"
                                 :protoFileId="protoFileId" :handleMsg="messageListener" type="1"
                                 cbPath="/process/modifyApplyLink/online/update" :applyId="formData.id"
                    >{{$t(`dicts.flow_node_fun_list_online_annotation`)}}
                    </online-edit>
                  </fileUpload>
                  <!-- </div> -->
                  <el-button v-if="standardDocfileList.length>0"
                             @click="handleCompareFile">{{$t(`doc.file_comparison`)}}</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_ver_annex`)+`:`"
                              prop="appendixesfileList">
                  <!-- <div class="fileuploadbzremark"> -->
                  <fileUpload
                    :editStatus="editStatus"
                    :downStatus="nodeShow('page_oper_file_download')"
                    :pdfDownStatus="nodeShow('cover_file_download')"
                    @input="fileUpdate('appendixesfileList')"
                    :limit="100"
                    :isfileType="false"
                    v-model.trim="appendixesfileList"
                    :isShowTip="false"
                    v-slot="{fileId,fileName,isOffice,protoFileId}"
                  >
                    <online-edit v-if="isOffice&&nodeShow('online_edit')" cbType="saveAppendix" :fileId="fileId"
                                 :fileName="fileName" :protoFileId="protoFileId" :handleMsg="messageListener"
                    >{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_revision')" cbType="saveAppendix" :fileId="fileId"
                                 :fileName="fileName" :protoFileId="protoFileId" :handleMsg="messageListener" type="0"
                                 cbPath="/process/modifyApplyLink/online/update" :applyId="formData.id"
                    >{{$t(`dicts.flow_node_fun_list_online_revision`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_annotation')" cbType="saveAppendix" :fileId="fileId"
                                 :fileName="fileName" :protoFileId="protoFileId" :handleMsg="messageListener" type="1"
                                 cbPath="/process/modifyApplyLink/online/update" :applyId="formData.id"
                    >{{$t(`dicts.flow_node_fun_list_online_annotation`)}}
                    </online-edit>
                  </fileUpload>
                  <!-- </div> -->
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="formData.classType===classTypeForeign">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`)+`:`"
                              prop="compliance">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.compliance"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_compliancy`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`)+`:`"
                              prop="changeReason">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.changeReason"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`)+`:`"
                              prop="content">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.content"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_content`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`"
                              prop="remark">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.remark"
                            type="textarea"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="(trains&&trains.length>0)||(workflowStatus&&nodeShow('page_oper_add_train_record'))">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`"
                              prop="remark">
                  <fileUpload v-model.trim="trains"
                              :editStatus="workflowStatus&&nodeShow('page_oper_add_train_record')"
                              limit="100"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','ppts','bmp','jpg','png','svg','tif','gif']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'train')" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="nodeShow('recovery_confirm')||formData.processStatus==='done'">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.recovery_confirm`)+`:`"
                              prop="recoveryConfirm">
                  <el-radio-group v-if="nodeShow('recovery_confirm')"
                                  @input="handelRecoveryConfirm"
                                  v-model.trim="formData.recoveryConfirm">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.recoveryConfirm" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <!-- 文件推送配置 -->
      <FilePushConfig
        v-if="nodeShow('file_push')"
        :doc-class-id="formData.docClass"
        :data-type="formData.dataType"
        :docId="formData.docId"
        show-push-file
        :data="formData.filePush"
        :required="getValidateByCode('file_push')&&editStatus"
        ref="filePushConfig"
      />

      <div class="news-card"
           v-if="nodeShow('whether_customer_record')||formData.whetherCustomer">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.customer_record`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormCustomer"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.whether_customer_records`)+`:`"
                              prop="whetherCustomer">
                  <el-radio-group v-if="workflowStatus&&customerStatus&&!batchStatus"
                                  v-model.trim="formData.whetherCustomer">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.whetherCustomer" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.whetherCustomer===yes">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_cover`)+`:`"
                              prop="customerCover">
                  <fileUpload v-model.trim="cover"
                              :editStatus="workflowStatus&&nodeShow('page_oper_add_customer_cover')"
                              :down-status="workflowStatus&&nodeShow('add_customer_record')"
                              limit="1"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'cover')" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_approval_record`)+`:`"
                              prop="customerRecord">
                  <customer-box ref="customerBox"
                                :applyId="formData.id"
                                :versionValue="formData.versionValue"
                                :editStatus="workflowStatus&&nodeShow('add_customer_record')&&formData.whetherCustomer!==no"></customer-box>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card"
           v-if="nodeShow('whether_train')||!!formData.yNTrain">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_train`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormTrain"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_or_not`)+`:`"
                              prop="yNTrain">
                  <el-radio-group v-if="workflowStatus&&trainStatus&&!batchStatus"
                                  v-model.trim="formData.yNTrain">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.yNTrain" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <distribute-box v-if="!batchStatus"
                      :editStatus="editStatus"
                      :workflowStatus="workflowStatus"
                      :setDeptReceiver="nodeShow('set_dept_receiver')"
                      :deptList="deptList"
                      :deptOptions="deptOptions"
                      :companyList="companyList"
                      :distributeList="distributeList"
                      :nodeDetailList="nodeDetailList"
                      ref="distributeBox"></distribute-box>

      <div class="el-card news-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs v-model.trim="activeIndex"
                   class="news-tabs">
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeNote&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_master_file`) }}</span>
              <link-doc :editStatus="editStatus"
                        v-show="activeIndex  == '1'"
                        :dataList="formData.noteDocLinks"
                        ref="linkDoc"
                        :dataType="formData.dataType"></link-doc>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeDoc&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note :editStatus="editStatus"
                         v-show="activeIndex  == '1'"
                         :dataList="formData.noteLinks"
                         ref="linkNote"
                         :dataType="formData.dataType"></link-note>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeDoc&&!classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record ref="linkRecord"
                           :editStatus="editStatus"
                           :dataList="formData.recordLinks"
                           v-show="activeIndex == '1'"
                           :dataType="formData.dataType"></link-record>
            </el-tab-pane>
            <el-tab-pane name="2"
                         v-if="formData.classType===classTypeDoc">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file :editStatus="editStatus"
                         :dataList="formData.docLinks"
                         v-show="activeIndex == '2'"
                         ref="linkFile"
                         :dataType="formData.dataType"></link-file>
            </el-tab-pane>
            <el-tab-pane name="3">
              <span slot="label">{{ $t(`doc.this_dept_file_history`) }}</span>
              <el-card class="gray-card">
                <historicalVersion @handleClick="handleDeal"
                                   :detatailsData="formData"
                                   v-show="activeIndex == '3'"></historicalVersion>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="news-card"
           v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!batchStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="validateForm"
                   :model="formSubmit"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="200px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_conclusion`)"
                              prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"
                                  @input="commentItemSelect">
                    <el-radio v-for="dict in passoptions"
                              :key="dict.value"
                              :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)+`:`">
                  <el-input v-model="formSubmit.summary"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"
                            maxlength="200"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "procInstId"></workflow-logs>
    </div>
    <div v-show="activeName==='edit'">
      <EditRecord :applyId="formData.id" :isVisible="activeName==='edit'" :docFile="standardDocfileList" :appendixes="appendixesfileList"></EditRecord>
    </div>
    <!-- 流程选择下一环节及人员 -->
    <el-dialog :title="$t(`doc.this_dept_select_next`)"
               v-if="dialogVisible"
               :visible.sync="dialogVisible"
               width="60%"
               append-to-body
               v-loading=flowStepLoading
               :close-on-click-modal="false"
               :close-on-press-escape="false">
      <processcode ref="prochild"
                   :selected="nodeShow('default_selected')"
                   :userListStatus="!nodeShow('user_list')"
                   :order="order"
                   :searchQuery="searchQuery"
                   :hideNodeCode="hideNodeCode"
                   :defaultStaff="defaultStaff"
                   :pListData="pListData"
                   :isSummary="isSummary"
                   :hiddenUserBox="nodeShow('wgcdx')"></processcode>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary"
                   v-dbClick
                   @click="showVerify({type:'flowSubmit'})">{{ $t(`doc.this_dept_annex`) }}</el-button>
      </span>
    </el-dialog>
    <monitor-drawer v-if="monitorDrawerVisible"
                    ref="monitorDrawer"></monitor-drawer>
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="closeAS">
    </as-pre-view>
    <doc-id-box ref="docIdBox"
                v-if="shenchenbianhao"
                :docIdDisabled="true"
                :recordIdDisabled="recordIdDisabled"
                @setDocId="setDocId"
                @setRecordDocId="setRecordDocId"></doc-id-box>
    <!-- PDF文件预览组件 -->
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="close"></as-pre-view>
    <el-drawer :wrapperClosable='false'
               :visible.sync="dealDrawerShow"
               :append-to-body="true"
               direction="rtl"
               size="90%"
               :with-header="false"
               :show-close="false"
               modal-append-to-body
               :destroy-on-close="true">
      <div style="width:100%; height:100%;overflow: hidden">
        <workflow-router ref="dealDrawer"
                         @closeDrawer="handleCloseChange"></workflow-router>
      </div>
    </el-drawer>
    <preset-user ref="presetUser"
                 @selectHandle="selectHandlePresetUser"></preset-user>
    <user-list ref="userList"
               @selectHandle="handleSubmitUser"></user-list>
    <version-list :dataType="formData.dataType"
                  :classTypeList="[classTypeDoc]"
                  ref="versionList"
                  @selectHandle="versionSelectHandle"></version-list>
    <el-dialog :title="$t(`doc.this_dept_tip`)"
               v-if="pushDialogVisible"
               :visible.sync="pushDialogVisible"
               width="35%"
               v-loading="loading"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               append-to-body>
      <div>
        <span>{{ $t(`doc.this_dept_set_effective_date`) }}：
          <el-date-picker v-model="setupTime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          type="date"
                          :placeholder="$t(`doc.this_dept_select_effective_date`)">
          </el-date-picker>
        </span>
      </div>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="pushDialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary"
                   @click="pushCommit">{{ $t(`file_handle.change_confirm`) }}</el-button>
      </div>
    </el-dialog>
    <transfer-flow ref="transferFlow"
                   @close="close"></transfer-flow>
    <identity-verify
      :visible.sync="verifyVisible"
      :business-params="currentParams"
      @business-execute="handleBusinessExecute"
      @verify-cancel="handleCancel"
    />
  </div>
</template>
<script>
import IdentityVerify from './add_import/identityVerify.vue'
import historicalVersion from "./add_import/historicalVersion";
import { getFile, processFileLocalUpload } from '@/api/commmon/file'
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  addModifyApply,
  getModifyApply,
  updateModifyApply,
  linkLoglistlink,
  getInfoByBpmnId, getDocNoByApplyId, getRecordDocNoByLinkId, updateById, queryModifyApplyTrain, compareFile
} from '@/api/file_processing/modifiyApply'
import { signEffective, coverEffective } from "@/api/file_processing/fileSignature";
import { settingDocClassId } from "@/api/file_settings/type_settings";
import { getByUpDocClassAndBizType } from "@/api/setting/docClassFlow";
import { getInfo } from "@/api/setting/docClassFlowNodeDetail";
import { modifyApplyLinklist, standardGetDetail } from "@/api/document_account/standard";
import { isExistByName } from "@/api/document_account/standard";
import { updateModifyApplyTrainList } from '@/api/my_business/modifyApplyTrain'

import {
  workflowSubmit,
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, workflowbacktostart, getRecordbyPorcInstId, getRedirectDefId, backFlowToOne
} from '@/api/my_business/workflow'
import { queryUserProjectList } from '@/api/system/project'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import WorkflowRouter from '@views/workflowList/workflowRouter.vue'
import DocIdBox from '@views/workflowList/addWorkflow/add_import/docIdBox.vue'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import { selectStatusByDocId, selectStatusRecord } from '@/api/my_business/workflowApplyLog'
import { listPresetUser } from '@/api/setting/presetUser'
import { checkPermi } from '@/utils/permission'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import { getNextVersion } from '@/api/setting/versionRuleDetail'
import DistributeBox from '@views/workflowList/addWorkflow/add_import/distributeBox.vue'
import { listDept, treeselect } from '@/api/system/dept'
import {
  getCompanyList,
  getDocManagersByDeptId,
  getLeader,
  getDivisionLeader,
  getNumByDeptId,
  getNumByTenantId
} from '@/api/system/user'
import { listModifyApplyDistribute } from '@/api/my_business/modifyApplyDistribute'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import { getInfoBy } from '@/api/setting/docClassSetting'
import {
  validateUniqueness
} from '@/api/document_account/version'
import { parseTime } from '@/utils/ruoyi'
import { validateByUserName } from '@/api/system/userSignature'
import { listDistribute, listPrintGroup } from '@/api/process/distribute'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import LinkDoc from '@views/workflowList/addWorkflow/add_import/linkDoc.vue'
import { checkNoIsExist } from '@/api/system/standard'
import { getLeaderByUserName } from '../../../api/system/user'
import { getFormRuleRecursive } from '@/api/setting/formRule'
import { copyFile, downloadFileDocId } from '@/api/as/asFile'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import CustomerBox from '@views/workflowList/addWorkflow/add_import/customerBox.vue'
import RelationPlanNo from '@/components/RelationPlanNo.vue'
import { listDistributeGroupDetail } from '@/api/setting/distributeGroupDetail'
import OnlineEdit from '@viewscomponents/onlineEdit/index.vue'
import EditRecord from '@views/workflowList/addWorkflow/add_import/editRecord.vue'
import DynamicForm from '@/views/components/DynamicForm.vue'
import {releaseStatus} from "@/api/system/fileEditingDetailLog";
import {checkAnnotation} from "@/api/process/baseApply";
import mixin from "@/layout/mixin/Commmon.js";
import FilePushConfig from '@/components/FilePushConfig'

export default {
  dicts: ["business_status", "sys_yes_no", "tenant_list", "file_status", "series_code", "face_option", "institutional", "file_type"],
  components: {
    IdentityVerify,
    EditRecord,
    OnlineEdit,
    RelationPlanNo,
    CustomerBox,
    TransferFlow,
    LinkDoc,
    LinkNote,
    VersionList,
    UserList,
    DistributeBox,
    PresetUser,
    DocIdBox,
    WorkflowRouter,
    LinkRecord,
    LinkFile,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs,
    DynamicForm,
    FilePushConfig
  },
  name: "Add_doc",
  props: ["dataType", 'data'],
  mixins: [mixin],
  data () {
    let validateFileUrl = (rule, value, callback) => {
      if (this.standardDocfileList.length < 1) {
        //我控制了FileList 长度代表文件个数
        callback(new Error(this.$t(`doc.this_dept_to_upload_file`)));
      } else {
        callback();
      }
    };
    let validateVersion = (rule, val, callback) => {
      if (this.formData.versions.some(item => item.versionValue === val)) {
        callback(new Error(this.$t(`doc.this_dept_file_version_exist`)));
      } else {
        callback();
      }
    };
    let validateDocName = (rule, val, callback) => {
      if (this.editStatus) {
        if (this.formData.docName != undefined && this.formData.docName.trim() != "") {
          isExistByName({ docName: this.formData.docName, applyId: this.formData.id, docId: this.formData.docId }).then((response) => {
            if (response.data == true) {
              return callback(new Error(this.$t(`doc.this_dept_file_name_exist`)));
            } else {
              callback();
            }
          });
        } else {
          callback(new Error(this.$t(`doc.this_dept_file_name_not_null`)));
        }
      } else {
        callback();
      }
    };
    return {
      currentType: '',
      currentParams: null,
      verifyVisible: false,
      applyType: 'update_doc',
      dataListIndex: undefined,
      order: 0,
      pushDialogVisible: false,
      setupTime: undefined,
      trains: [],
      cover: [],
      yes: 'Y',
      no: 'N',
      trainType: '',
      distributeList: [],
      deptList: [],
      companyList: [],
      deptOptions: [],
      deptOptions2: [],
      hideNodeCode: [],
      defaultStaff: undefined,
      classTypeRecordMN: true,
      backFlowToOneStatus: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      searchQuery: {},
      shlkPath: process.env.VUE_APP_SHLK_PATH,
      procInstId: undefined,
      dealDrawerShow: false,
      submitLabel: undefined,
      isProject: false,
      recordIdDisabled: [],
      projectList: [],
      project: { id: '', name: '' },
      docIdData: {},
      jiluliData: [],
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: undefined },
      pButton: 'UPDATE',
      isSummary: false,
      title: this.$t(`home.quick_action_file_add`),
      activeName: "info",
      nodeDetail: {},
      nodeDetailList: [],
      procDefKey: undefined,
      processData: {},
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png", 'ppt', "pptx"],
      monitorDrawerVisible: false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      isLast: true,
      quantity: [],
      formRuleList: [],
      formData: {
        invokeType: '',
        docStatus: undefined,
        whetherRetain: undefined,
        retainDeadline: undefined,
        whetherCustomer: undefined,
        distributeList: [],
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "A0",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: { fileName: '' }, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        noteDocLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        presetUserList: [],
        step: undefined,
        batch: undefined,
        yNDistribute: '',
        distributeType: '',
        yNTrain: undefined,
        trainType: "",
        productLine: undefined,
        process: undefined,
        productType: undefined,
        haveLinkFile: undefined,
        custodyDeptId: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        setupTime: undefined,
        factorys: null,
        recoveryConfirm: undefined,
        internalDocId: null,
      },
      rules: {
        whetherRetain: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.whether_retain`), trigger: "blur,change" },
        ],
        retainDeadline: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.retain_deadline`), trigger: "blur,change" },
        ],
        whetherCustomer: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.whether_customer_records`), trigger: "blur,change" },
        ],
        fileEffectiveDate: [
          { required: true, message: this.$t(`doc.this_dept_select_effective_date`), trigger: "blur,change" },
        ],
        productLine: [
          { required: true, message: this.$t(`doc.this_dept_select_base_line`), trigger: "blur,change" },
        ],
        process: [
          { required: true, message: this.$t(`doc.this_dept_select_process`), trigger: "blur,change" },
        ],
        productType: [
          { required: true, message: this.$t(`doc.this_dept_select_category`), trigger: "blur,change" },
        ],
        yNTrain: [
          { required: true, message: this.$t(`doc.this_dept_select_is_train`), trigger: "blur,change" },
        ],
        yNDistribute: [
          { required: true, message: this.$t(`doc.this_dept_select_is_distribute`), trigger: "blur,change" },
        ],
        pass: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur" },
        ],
        docClass: [
          { required: true, message: this.$t(`doc.this_dept_preparer`), trigger: "blur" },
        ],
        docName: [
          {
            max: 1000,
            message: this.$t(`doc.this_dept_file_name_more_long`),
          },
          { validator: validateDocName, trigger: "blur" },
        ],
        versionValue: [
          { required: true, message: this.$t(`doc.this_dept_insert_ver`), trigger: "blur" },
          { validator: validateVersion, trigger: "blur" },
        ],
        custodyDeptId: [
          { required: true, message: this.$t(`file_handle.change_select_dept`), trigger: "blur,change" },
        ],
        compliance: [
          { required: true, message: this.$t(`doc.this_dept_ins_compliancy`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_compliancy_more_long`),
          },
        ],
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_reason_more_long`),
          },
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_content_more_long`),
          },
        ],
        standardDocfileList: [
          {
            required: true,
            validator: validateFileUrl,
            trigger: ["blur", "change", "input"],
          },
        ],
        internalDocId: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`) + this.$t(`doc.this_dept_internal_file_number`),
            trigger: "blur,change",
          }
        ]
      },
      kuozhanshuju: {},
      kuozhanshujuBool: {},
      field117Action: "",
      action: "/dms-admin/process/file/local_upload",
      appendixesfileList: [],
      standardDocfileList: [],
      preStandardDocfileList: [],
      preAppendixesfileList: [],
      docClassList: [],
      summary: "",
      pListData: {},
      editStatus: false,
      trainStatus: false,
      customerStatus: false,
      transferStatus: false,
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading: false,
      userinfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      isShowPart: false,
      isCustomerShow: false,
      isDeviceShow: false,
      isCodeAndTypeShow: false,
      docClassData: {},
      theFirstStatus: true,
      batch: undefined,
      distributeSetting: { trainType: 'trainType', distributeType: 'distributeType', isDistribute: 'yNDistribute' }
    };
  },
  computed: {
    batchStatus () {
      return !!this.formData.batchId
    }
  },
  watch: {
    "formData.docClass" (val) {
      let _this = this
      if (val) {
        _this.getNodeDetailInfo()
        if (!_this.procInstId) {
          _this.getByUpDocClassAndBizType(val)
        }
      }
    },
    "formData.dataType" (val) {
      let _this = this
      _this.isProject = val === 'project'
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  async created () {
    let response = await this.getConfigKey("record.doc.type")
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
    let response1 = await this.getConfigKey("back_flow_to_one")
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true';
  },
  mounted () {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    // 根据code获取validate值
    getValidateByCode(code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        try {
          const funCondition = JSON.parse(nodeDetail.funCondition)
          return funCondition.validate
        } catch (error) {
          console.error('解析funCondition失败:', error)
          return undefined
        }
      } else {
        return undefined
      }
    },
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    showVerify(invokeFrom) {
      this.currentParams = invokeFrom
      // 判断是否需要身份验证
      if (this.nodeShow('authentication')) {
        this.verifyVisible = true
      } else {
        // 不需要验证时直接执行业务逻辑
        this.handleBusinessExecute(this.currentParams)
      }
    },

    // 执行业务逻辑
    handleBusinessExecute(params) {
      switch(params.type) {
        case 'flowSubmit':
          this.handleWorkflowSubmit(params)
          break
        case 'transfer':
          this.transferForm(params)
          break
        case 'backFlow':
          this.handleBackFlowToOne(params)
          break
        case 'publish':
          checkAnnotation({ id: this.formData.id, batchId:''}).then(async (res) => {
            if(!res.data || res.data.length == 0){
              this.handlePublish(params)
            }else{
              this.$message.warning(this.$t(`doc.file_exist_record`))
            }
          })
          break
        case 'turnDown':
          this.handelpbohuiqicaoren(params)
          break
      }
    },
    checkPermi,
    init (row) {
      let _this = this
      _this.theFirstStatus = true
      _this.rest()
      _this.getDeptList()
      _this.getCompanyDataList()
      _this.loading = true
      _this.order = row.order ? row.order : 0
      _this.batch = row.batch
      _this.mark = row.mark
      //是否编辑模式
      this.$nextTick(() => {
        if (row && (!!row.procInstId || !isNaN(row.dataListIndex))) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          if (row.batchId) {
            _this.setFileList(row)
            _this.formData = row
            _this.dataListIndex = row.dataListIndex
            if (_this.formData.classType !== _this.classTypeDoc && _this.formData.classType !== _this.classTypeNote) {
              _this.activeIndex = '3'
            }
            _this.settingDocClassId(_this.formData.docClass);
            _this.getByDocClass(_this.formData.docClass);
            // _this.initDistributeBox(_this.formData.distributeList)
            if (row.id) {
              _this.getModifyApplyTrain('cover', 'cover')
              _this.getModifyApplyTrain('trains', 'train')
            }
          } else {
            _this.getDetail(_this.procInstId)
          }
        } else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.getStandardDetail(row)
          // this.getWorkflowprocesskey();
        }
      });
    },
    async getStandardDetail (query) {
      let _this = this
      _this.detailLoading = true
      await standardGetDetail(query).then(async (res) => {
        let formData = res.data;
        formData.batchId = query.batchId

        if (formData.preStandardDoc) {
          if(formData.preStandardDoc.fileId) {
            _this.preStandardDocfileList = [
              {
                applyId: formData.id,
                name: formData.preStandardDoc.fileName,
                url: formData.preStandardDoc.fileId,
                protoFileId: formData.preStandardDoc.protoFileId,
              },
            ];
          }
        }
        if (formData.preAppendixes != null) {
          formData.preAppendixes.forEach((element) => {
            _this.preAppendixesfileList.push({
              name: element.fileName,
              url: element.fileId,
              protoFileId: element.protoFileId
            });
          });
        }

        //关联文件
        let res1 = await linkLoglistlink({ linkType: "REF_DOC", versionId: formData.versionId })
        formData.docLinks = res1.data
        //关联记录
        let res2 = await linkLoglistlink({ linkType: "RECORD", versionId: formData.versionId, status: '1' })
        formData.recordLinks = res2.data
        //关联记录
        let res3 = await linkLoglistlink({ linkType: "NOTE", versionId: formData.versionId, status: '1' })
        formData.noteLinks = res3.data
        //关联记录
        let res4 = await linkLoglistlink({ linkType: "NOTE_DOC", versionId: formData.versionId, status: '1' })
        formData.noteDocLinks = res4.data
        formData.id = undefined
        formData.releaseTime = undefined
        formData.changeType = _this.pButton;
        // _this.project={
        //   id: formData.projectId,
        //   name: formData.projectName
        // }
        formData.userName = _this.userInfo.userName
        formData.nickName = _this.userInfo.nickName
        // formData.deptId = _this.userInfo.dept.deptId
        // 部门对应的组织架构一级部门ID
        formData.deptId = _this.userInfo.dept.deptId
        formData.deptName = _this.userInfo.dept.deptName
        formData.applyTime = new Date().getTime()
        formData.presetUserList = []
        if (formData.classType !== _this.classTypeDoc && formData.classType !== _this.classTypeNote) {
          _this.activeIndex = '3'
        }
        formData.yNDistribute = _this.no
        _this.trainType = formData.distributeType
        formData.distributeType = ""
        formData.distributeList = []
        //formData.whetherCustomer = undefined
        formData.docStatus = formData.status
        formData.whetherCustomer = undefined
        formData.whetherRetain = _this.no
           // 确保 filePush 对象存在，如果为 null 则初始化
        if (!formData.filePush) {
          formData.filePush = {}
        }
        formData.filePush.pushFile = formData.ext8 === 'Y'

        _this.formData = formData
        await _this.getDistributeList(formData.versionId)
        _this.getNextVersion()
        _this.$nextTick(() => {
          _this.$refs.distributeBox.initDistributeGroup(formData.docClass, _this.distributeSetting).then(distributeList => {
            _this.distributeList = distributeList
          })
        })
        await _this.settingDocClassId(formData.docClass);

        //查询物料是否展示
        this.getByDocClass(formData.docClass);
      }).finally(() => {
        _this.detailLoading = false
      });
    },
    getModifyApplyTrain (source, type) {
      let _this = this
      let query = {
        applyId: _this.formData.id,
        type: type,
        isDeleted: 0
      }
      let list = []
      queryModifyApplyTrain(query).then(res => {
        if (res.data) {
          res.data.forEach(item => {
            list.push({
              url: item.fileIds,
              name: item.files[0].fileName
            })
          })
        }
        _this.$set(_this, source, list)
      })
    },
    getModifyApplyDistributeList (applyId) {
      let _this = this
      listModifyApplyDistribute({ applyId: applyId }).then(res => {
        _this.initDistributeBox(res.data)
      })
    },
    initDistributeBox (distributeList) {
      let _this = this
      _this.distributeList = distributeList
      _this.$nextTick(() => {
        if (_this.$refs.distributeBox) {
          _this.$refs.distributeBox.init(_this.formData, { trainType: 'trainType', distributeType: 'distributeType', isDistribute: 'yNDistribute' })
        }
      })
    },
    getDetail (procInstId) {
      let _this = this
      _this.detailLoading = true
      getInfoByBpmnId(procInstId).then(async (res) => {
        let formData = res.data;
        let res1 = await modifyApplyLinklist({ applyId: formData.id, linkType: "REF_DOC" })
        formData.docLinks = res1.rows;
        let res2 = await modifyApplyLinklist({ applyId: formData.id, linkType: "RECORD" })
        formData.recordLinks = res2.rows;
        let res4 = await modifyApplyLinklist({ applyId: formData.id, linkType: "NOTE" })
        formData.noteLinks = res4.rows;
        let res5 = await modifyApplyLinklist({ applyId: formData.id, linkType: "NOTE_DOC" })
        formData.noteDocLinks = res5.rows;
        //关联记录
        // let res2 = await linkLoglistlink({linkType: "RECORD", versionId: formData.versionId,status:'1'})
        // formData.recordLinks = res2.data

        let res3 = await listPresetUser({ bizId: formData.id })
        formData.presetUserList = res3.data
        _this.setFileList(formData)
        // _this.project={
        //   id: formData.projectId,
        //   name: formData.projectName
        // }
        if (formData.classType !== _this.classTypeDoc && formData.classType !== _this.classTypeNote) {
          _this.activeIndex = '3'
        }
           // 确保 filePush 对象存在，如果为 null 则初始化
        if (!formData.filePush) {
          formData.filePush = {}
        }
        formData.filePush.pushFile = formData.ext8 === 'Y'

        _this.formData = formData
        _this.getModifyApplyTrain('cover', 'cover')
        _this.getModifyApplyTrain('trains', 'train')
        _this.getModifyApplyDistributeList(formData.id)
        await _this.settingDocClassId(formData.docClass);

        //查询物料是否展示
        _this.getByDocClass(formData.docClass);
      }).finally(() => {
        _this.detailLoading = false
      });
    },
    setFileList (formData) {
      let _this = this
      if (formData.standardDoc) {
        if (formData.standardDoc.fileId) {
          _this.standardDocfileList = [
            {
              applyId: formData.id,
              name: formData.standardDoc.docName,
              url: formData.standardDoc.fileId,
              protoFileId: formData.standardDoc.protoFileId,
            },
          ];
        }
      }
      if (formData.appendixes != null) {
        formData.appendixes.forEach((element) => {
          _this.appendixesfileList.push({
            name: element.docName,
            url: element.fileId,
            protoFileId: element.protoFileId
          });
        });
      }
      if (formData.preStandardDoc) {
        if(formData.preStandardDoc.fileId) {
          _this.preStandardDocfileList = [
            {
              applyId: formData.id,
              name: formData.preStandardDoc.fileName,
              url: formData.preStandardDoc.fileId,
              protoFileId: formData.preStandardDoc.protoFileId,
            },
          ];
        }
      }
      if (formData.preAppendixes != null) {
        formData.preAppendixes.forEach((element) => {
          _this.preAppendixesfileList.push({
            name: element.fileName,
            url: element.fileId,
            protoFileId: element.protoFileId
          });
        });
      }
    },
    async getDistributeList (versionId) {
      let _this = this
      let distributeList = []
      //培训
      let res = await listDistribute({ versionId: versionId, neType: 'print' })
      if (res.data && res.data.length > 0) {
        _this.formData.trainType = _this.trainType
        for (const item of res.data) {
          let distribute = {
            nums: 1,
            receiveUserName: item.receiveUserName,
            receiveNickName: item.receiveNickName,
            receiveUserDeptId: item.receiveUserDeptId,
            receiveUserDept: item.receiveUserDept,
            type: item.type,
            category: 'train'
          }
          if (item.type === 'dept') {
            let num1 = await getNumByDeptId(item.receiveUserDeptId)
            distribute.nums = num1.data
          } else if (item.type === 'company') {
            let num2 = await getNumByTenantId(item.receiveUserDeptId)
            distribute.nums = num2.data
          }
          distributeList.push(distribute)
        }
      }
      //分发
      let res1 = await listPrintGroup({ versionId: versionId })
      if (res1.data && res1.data.length > 0) {
        _this.formData.yNDistribute = _this.yes
        _this.formData.distributeType = 'person'
        for (const item of res1.data) {
          let distribute = {
            nums: item.nums,
            receiveUserName: item.receiveUserName,
            receiveNickName: item.receiveNickName,
            receiveUserDeptId: item.receiveUserDeptId,
            receiveUserDept: item.receiveUserDept,
            type: 'person',
            category: 'print'
          }
          distributeList.push(distribute)
        }
      }
      _this.distributeList = distributeList
      console.log(distributeList)
    },
    handelConfirm (list, type) {
      let trains = []
      list.forEach(item => {
        trains.push({
          fileIds: item.url,
          fileName: item.name,
          userName: this.userInfo.userName,
          deptId: this.userInfo.deptId,
          docId: this.formData.docId,
          applyId: this.formData.id,
          type: type
        })
      })
      let data = {
        applyId: this.formData.id,
        type: type,
        trains: trains
      }
      updateModifyApplyTrainList(data);
    },
    getNextVersion () {
      getNextVersion({ version: this.formData.versionValue, docClass: this.formData.docClass }).then(res => {
        this.formData.versionValue = res.data
      })
    },
    getQueryUserProjectList () {
      let _this = this
      queryUserProjectList().then(res => {
        _this.projectList = res.data
      })
    },
    rest () {
      let _this = this
      _this.activeName = "info"
    },
    async initStatus () {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.$t(`file_handle.change_auditing`)
      if (!_this.batchStatus) {
        if (_this.nodeShow('whether_train')) {
          let funCondition = _this.nodeFunCondition('whether_train')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.yNTrain) {
              _this.formData.yNTrain = funCondition.limitValue
            }
            _this.trainStatus = funCondition.validate
          }
        }
        if (_this.nodeShow('whether_customer_record')) {
          let funCondition = _this.nodeFunCondition('whether_customer_record')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.whetherCustomer) {
              _this.formData.whetherCustomer = funCondition.limitValue
            }
            _this.customerStatus = funCondition.validate
          }
        }
        _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
      }
    },
    procInstInfoAndStatus (procInstId) {
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
        } else {
          _this.pListData = { procInstId: procInstId }
        }
        _this.getExtAttributeModel()
      });
    },
    nodeShow (code) {
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      } else {
        return false
      }
    },
    nodeFunCondition (code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      } else {
        return undefined
      }
    },
    // 获取文件类型的流程配置
    async getByUpDocClassAndBizType (docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass, _this.pButton)
      _this.procDefKey = data && data.flowKey ? data.flowKey : "";
      _this.getWorkflowprocesskey()
    },
    getWorkflowprocesskey () {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      } else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel () {
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId && curActDefId) {
        _this.getNodeDetailInfo()
        getExtAttributeModel(
           procDefId,
           curActDefId
         ).then((res) => {
           console.log("扩展属性====>", res);
           let kuozhanshujuBool = {}
           let kuozhanshuju = {}
           res.data.forEach(item=>{
             if (item.objType==='Boolean') {
               kuozhanshujuBool[item.objKey] = item.objValue
             } else {
               kuozhanshuju[item.objKey] = item.objValue
             }
           })
           _this.kuozhanshujuBool = kuozhanshujuBool;
           _this.kuozhanshuju = kuozhanshuju;
          this.$refs.dynamic.setKuozhanshuju(res.data.length>0?_this.kuozhanshuju:{})
         }).finally(()=>{
           _this.loading = false
         });
      } else {
        _this.kuozhanshujuBool = {}
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModelBool (val) {
      if (this.kuozhanshujuBool && this.kuozhanshujuBool !== {}) {
        let obj = this.kuozhanshujuBool[val]
        return !!obj && obj === 'true'
      } else {
        return false
      }
    },
    attributeModel (val) {
      return this.kuozhanshuju[val]
    },
    getNodeDetailInfo () {
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData && curActDefId && _this.formData.docClass) {
        getInfo(_this.formData.docClass, _this.pButton, curActDefId).then(res => {
          let nodeDetail = {}
          res.data.forEach(item => {
            nodeDetail[item.code] = true
          })
          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    beforeUpload (file) {
      let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isLt1M = file.size / 1024 / 1024 < 10;
      if (!isLt1M) {
        this.$message.error(this.$t(`file_handle.change_upload_limit`));
        return false;
      }
      if (this.uploadType.includes(fileType)) {
        return true;
      } else {
        this.$message({
          message: this.$t(`file_handle.change_upload_only`) + this.uploadType.toString() + this.$t(`file_handle.change_format`),
          type: "warning",
        });
        return false;
      }
    },
    closeAS () {
      this.viewShow = false;
    },
    close () {
      this.$emit("close")
    },
    handlePreview (id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    //不需要验证必填的保存
    async saveForm (type) {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      if (_this.editStatus) {
        formData.recordStatus = "draft";
        if (_this.batchStatus) {
          formData.notUpdateTitle = true
        }
      } else {
        formData.onlyEdit = true
      }
      if(type){
        formData.onlyEdit = false
      }
      //变更操作（新增、修订、作废）的提交和保存的类型区分统一使用recordStatus字段（draft：草稿, doing:进行中，done:已完成，deleted:作废）
      formData.docLinks = [];
      if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
        _this.formData.docLinks = _this.$refs.linkFile.dataList;
      }
      formData.noteLinks = [];
      if (_this.$refs.linkNote && _this.$refs.linkNote.dataList) {
        formData.noteLinks = _this.$refs.linkNote.dataList;
      }
      formData.noteDocLinks = [];
      if (_this.$refs.linkDoc && _this.$refs.linkDoc.dataList) {
        formData.noteDocLinks = _this.$refs.linkDoc.dataList;
      }
      if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
        formData.recordLinks = _this.$refs.linkRecord.dataList;
      }
      if (_this.standardDocfileList != "") {
        formData.standardDoc = {
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url,
          protoFileId: _this.standardDocfileList[0].protoFileId,
        }
      } else {
        formData.standardDoc = { docName: '' }
      }
      formData.appendixes = [];
      _this.appendixesfileList.forEach((element) => {
        formData.appendixes.push({ fileId: element.url, docName: element.name, status: 1,protoFileId: element.protoFileId });
      });
      formData.remarkDoc = [];
      if (_this.$refs.distributeBox) {
        formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          formData[boxSetting[item]] = boxFormData[item]
        }
      }
      if (formData.id) {
        let res = await updateModifyApply(formData)
        if (res.code === 200) {
          this.$modal.msgSuccess(_this.$t(`file_handle.change_save_succ`));
          _this.loading = false
          if (_this.batchStatus) {
            this.$emit("close", formData, _this.dataListIndex)
          }
        }
      } else {
        if (_this.batchStatus) {
          _this.loading = false
          this.$emit("close", formData, _this.dataListIndex)
          return
        }
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.docName.trim(),
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          order: _this.pListData.actDefOrder,
          review: false,
          type: _this.applyType
        };
        formData.editStatus = _this.editStatus
        let res = await addModifyApply(formData)
        if (res.code === 200) {
          _this.$modal.msgSuccess(_this.$t(`file_handle.change_succ`));
          _this.formData.id = res.data.businessKey;
          _this.procInstId = res.data.procInstId
          _this.procInstInfoAndStatus(res.data.procInstId)
          _this.loading = false
        }
      }
    },
    handleBackFlowToOne () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`);
          }
        },
      }).then(({ value }) => {
        _this.loading = true
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.docName.trim(),
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName,
              },
              bizType: _this.pButton,
              review: true,
              applyStatus: false,
              status: 'draft',
              type: _this.applyType,
              mark: _this.mark,
              order: 0,
            };
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break;
            }
          }
          _this.close(true);
        })
      })
    },
    deleteForm () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`);
          }
        },
      }).then(({ value }) => {
        _this.loading = true
        let formData = {
          id: _this.formData.id,
          bpmClientInputModel: {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_curComment: value,
            },
            order: _this.order,
            type: _this.applyType,
            mark: _this.mark,
            review: true,
            applyStatus: false,
          },
          recordStatus: 'cancel',
          editStatus: false
        }
        addModifyApply(formData).then((res) => {
          if (res.code === 200) {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`));
            this.close(true);
          }
        });
      })
    },
    transferForm () {
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData, _this.formData.id, _this.applyType, _this.order, _this.pButton)
    },
    // 提交
    async submitForm () {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      //审核
      if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
        if (_this.formSubmit.pass === undefined) {
          _this.$modal.msgError(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`));
          return;
        }
        // 验证是否填写了审核意见
        if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel + _this.$t(`doc.this_dept_comments`));
          return;
        }
      }
      //培训记录
      if (_this.nodeShow('page_oper_add_train_record')) {
        let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.trains && _this.trains.length > 0)) {
            _this.$modal.msgError(_this.$t(`doc.this_dept_pls_upload_train_file`));
            return true;
          }
        }
      }
      //客户封面
      if (_this.nodeShow('page_oper_add_customer_cover') && _this.formData.whetherCustomer === _this.yes && _this.formSubmit.pass !== false) {
        let funCondition = _this.nodeFunCondition('page_oper_add_customer_cover')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.cover && _this.cover.length > 0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`) + _this.$t(`doc.customer_cover`) + '！');
            return true;
          }
        }
      }
      //客户记录
      if (_this.nodeShow('add_customer_record') && _this.formData.whetherCustomer === _this.yes && _this.formSubmit.pass !== false) {
        let funCondition = _this.nodeFunCondition('add_customer_record')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.$refs.customerBox && _this.$refs.customerBox.dataList && _this.$refs.customerBox.dataList.length > 0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`) + _this.$t(`doc.customer_record`) + '！');
            return true;
          }
        }
      }
      //签名
      if (_this.nodeShow('log_title')) {
        // 如果是编制环节校验编制人
        if (_this.editStatus) {
          let res = await validateByUserName({ userCode: _this.formData.userName })
          if (!res.data) {
            _this.$modal.msgError(_this.$t(`doc.user_organizer_validate`));
            return true;
          }
        }
        let res = await validateByUserName({ userCode: _this.userInfo.userName })
        if (!res.data) {
          _this.$modal.msgError(_this.$t(`doc.user_signature_validate`));
          return true;
        }
      }
      let dialogVisible = true
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs["elFormCustomer"]) {
        let valid = await _this.$refs["elFormCustomer"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs["validateForm"]) {
        let validateValid = await _this.$refs["validateForm"].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      // 验证分发和培训表单
      if (!!_this.$refs["distributeBox"]) {
        let distributeForm = _this.$refs["distributeBox"].$refs["elFormDistribute"]
        if (!!distributeForm) {
          let validateValid = await distributeForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      if (!!_this.$refs["distributeBox"]) {
        let trainForm = _this.$refs["distributeBox"].$refs["elFormTrain"]
        if (!!trainForm) {
          let validateValid = await trainForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      if (!!_this.$refs["distributeBox"]) {
        let valid = await _this.$refs["distributeBox"].validateForm()
        if (!valid) {
          dialogVisible = false
          return
        }
      }
      let {valid, data} = await this.$refs.filePushConfig.validateAndGetData();
      //校验
      if (await _this.validate() || !valid) {
        return
      }
      _this.formData.filePush=data

      _this.loading = true;
      _this.formData.docLinks = [];
      if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
        _this.$refs.linkFile.dataList.forEach((element) => {
          _this.formData.docLinks.push({
            // linkId: element.linkId,
            fileId: element.fileId,
            docId: element.docId,
            versionId: element.versionId,
            versionValue: element.versionValue,
            docName: element.docName,
            docClass: element.docClass,
            status: 1,
          });
        });
      }
      _this.formData.noteLinks = [];
      if (_this.$refs.linkNote && _this.$refs.linkNote.dataList) {
        _this.formData.noteLinks = _this.$refs.linkNote.dataList;
      }
      _this.formData.noteDocLinks = [];
      if (_this.$refs.linkDoc && _this.$refs.linkDoc.dataList) {
        _this.formData.noteDocLinks = _this.$refs.linkDoc.dataList;
      }
      if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
        _this.formData.recordLinks = _this.$refs.linkRecord.dataList;
      }
      // console.log("this.formData.standardDoc="+JSON.stringify(this.formData.standardDoc));
      if (_this.standardDocfileList != "") {
        this.formData.standardDoc = {
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url,
          status: 1,
          protoFileId: _this.standardDocfileList[0].protoFileId,
        }
      } else {
        this.formData.standardDoc = { docName: '' }
      }
      this.formData.appendixes = [];
      this.appendixesfileList.forEach((element) => {
        this.formData.appendixes.push({ fileId: element.url,docName:element.name, status: 1,protoFileId: element.protoFileId });
      });
      this.formData.remarkDoc = [];
      if (_this.$refs.distributeBox) {
        _this.formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          _this.formData[boxSetting[item]] = boxFormData[item]
        }
      }
      _this.jointReviewRedirect()
      await _this.setPresetUserList()
      this.dialogVisible = true;
      this.loading = false;
    },
    async setPresetUserList () {
      let _this = this
      if (_this.nodeShow('preset_countersign')) {
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition.groupId) {
          let users = [];
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item => {
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept,
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 下个环节预选直属部门领导
      if (_this.nodeShow('next_set_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getLeader(_this.userInfo.userName, _this.formData.deptId)
            user = res.data
          } else {
            let res = await getLeader(_this.userInfo.userName, _this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      if (_this.nodeShow('next_set_division_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_division_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getDivisionLeader(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDivisionLeader(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      //设置流程待选任意为文控
      if (_this.nodeShow('set_flow_select_list')) {
        let funCondition = _this.nodeFunCondition('set_flow_select_list')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getDocManagersByDeptId(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDocManagersByDeptId(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = []
            user.forEach(item => {
              users.push({
                userName: item.userName,
                nickName: item.nickName,
                deptId: item.deptId,
                deptName: item.dept.deptName
              })
            })
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (_this.formData.presetUserList.length > 0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if (_this.nodeShow('cdxmd') && _this.batch) {
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition && funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({
            batch: _this.batch,
            nextDefId: _this.pListData.curActDefId,
            havaDetail: true
          })
          let nodeCode = ""
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({
              userName: item.sender,
              nickName: item.nickName,
              deptId: item.senderDeptId,
              deptName: item.deptName
            })
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode === nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.isTrain = _this.formData.yNTrain
      _this.searchQuery.isCustomer = _this.formData.whetherCustomer
      _this.searchQuery.ext5 = _this.formData.ext5
      _this.searchQuery.pass = _this.formSubmit.pass
      _this.searchQuery.batch = !!_this.batch
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')) {
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition && funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围  过滤出没有预选人员的环节
          hideNodeCode = funCondition.nodeCode.filter(item => !defaultStaff.some(dpu => item === dpu.nodeCode && dpu.users && JSON.parse(dpu.users).length > 0))
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode && hideNodeCode.length === length) {
            hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length - hideNodeCode.length) <= limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length === 0) {
            defaultStaff.forEach(item => {
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
          }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect () {
      let _this = this
      if (_this.nodeShow('hscdx') || _this.nodeShow('wgcdx')) {
        getRecordbyPorcInstId(_this.procInstId).then(res => {
          if (res.data.length === 1) {
            let query = {
              docClass: _this.formData.docClass,
              bizType: _this.pButton,
              code: "cdxmd",
              batch: _this.batch,
            }
            getRedirectDefId(query).then(res1 => {
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length === 1) {
                  let next = res1.data.find(item => item.nextDefId === funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          } else {
            _this.isLast = false
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect (val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if (val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validate () {
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')) {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(_this.formData.docClass, _this.pButton, _this.formData.presetUserList, nodeCode)
        if (bool) {
          return true;
        }
      }
      if (_this.editStatus) {
        if (!_this.formData.id) {
          let res1 = await selectStatusByDocId({ versionId: _this.formData.versionId, notInDraft: false })
          if (res1.data != 0) {
            _this.$modal.msgWarning(res1.msg);
            return true;
          }
        }
      }
      return false;
    },
    // 签章生效
    async handleSignEffective () {
      let self = this
      if ('E' === self.formData.isSignature) {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }
      //生成封面
      if (self.nodeShow('top_btn_file_cover')) {
        if (self.formData.isSignature !== 'C') {
          self.$modal.alert("温馨提示：请先执行【生成封面】");
          return false;
        }
      } else {
        //设置了生成封面 就不用验证了
        if (self.nodeShow('top_btn_generate_code')) {
          if (self.formData.recordLinks.some(item => !item.docId)) {
            self.$modal.alert(self.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`));
            return false;
          }
        }
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      signEffective(self.formData.id, 'effective').then((res) => {
        if (res.code === 200) {
          self.$modal.alert(self.$t(`file_handle.change_signature_text3`));
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'E'
        } else {
          self.$modal.alert(res.msg);
        }
        self.loading = false
      });
    },
    async handleCoverEffective () {
      let self = this
      // 验证设置生效时间
      if (self.nodeShow('publish_setup_time') && self.formData.setupTime == null) {
        self.$modal.alert(self.$t(`doc.this_dept_set_effective_date`));
        return false;
      }
      //不用重复生成封面
      /* if ('C' === self.formData.isSignature) {
         self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
         return false;
       }*/
      //验证生成编号
      if (self.nodeShow('top_btn_generate_code')) {
        if (self.formData.recordLinks.some(item => !item.docId)) {
          self.$modal.alert(self.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`));
          return false;
        }
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      coverEffective(self.formData.id, 'cover').then((res) => {
        if (res.code === 200) {
          self.$modal.msgSuccess(self.$t(`doc.this_dept_operation_succ`));
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'C'
        } else {
          self.$modal.msgError(res.msg);
        }
        self.loading = false
      });
    },
    // 执行发布（推送文件到文件台账）
    async handlePublish () {
      let self = this
      if (self.nodeShow('top_btn_setup_time') && self.formData.isSignature !== 'E') {
        self.$modal.alert(self.$t(`doc.this_dept_pls_effect_signature_relase`));
        return false;
      }
      if (self.nodeShow('recovery_confirm') && self.formData.recoveryConfirm !== self.yes) {
        self.$modal.alert(self.$t(`doc.recovery_confirm_validate`));
        return false;
      }
      if (self.nodeShow('set_dept_receiver')) {
        if (!!self.$refs["distributeBox"]) {
          let valid = await self.$refs["distributeBox"].validateForm()
          if (!valid) {
            return
          }
        }
      }
      self.$modal.confirm(self.$t(`doc.this_dept_confirm_release`) + '《' + self.formData.docName + '》，' + self.$t(`doc.this_dept_file_effect_push_account`)).then(function () {
        // 执行发布的签章
        self.loading = true
        signEffective(self.formData.id, 'publish').then((res) => {
          if (res.code === 200) {
            self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text6`));
            self.formData.encryptFileId = res.data
            self.formData.isSignature = 'Y'
            self.pushDialogVisible = false
            self.handleWorkflowSubmit('publish');
          } else {
            self.$modal.alert(res.msg);
          }
          self.loading = false
        });
      })
    },
    // 发布二次确认
    setupStartTime () {
      let _this = this
      _this.setupTime = _this.formData.setupTime
      if (!_this.setupTime) {
        _this.setupTime = parseTime(new Date())
      }
      _this.pushDialogVisible = true
    },
    // 发布二次确认
    pushCommit () {
      let _this = this
      if (!_this.setupTime) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_set_effective_date`))
        return
      }
      if (_this.formData.setupTime !== _this.setupTime) {
        let form = {
          id: _this.formData.id,
          setupTime: _this.setupTime,
          isSignature: 'N',
          onlyEdit: true
        }
        updateModifyApply(form).then((res) => {
          if (res.code === 200) {
            _this.formData.isSignature = form.isSignature
            _this.formData.setupTime = form.setupTime
            _this.pushDialogVisible = false
          }
        });
      } else {
        _this.pushDialogVisible = false
      }
    },
    //提交流程
    handleWorkflowSubmit (invokeFrom) {
      let _this = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      let nextData = undefined
      if (typeof (invokeFrom) == 'object') {
        nextData = _this.$refs.prochild.handleWorkflowSubmit();
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        nextData = {
          wf_nextActDefId: 'end',
          wf_nextActDefName: '结束',
          direction: true,
          wf_receivers: [],
          fields: {},
        }
      }
      if (!nextData) {
        return;
      }
      // 显示加载中
      _this.detailLoading = true
      _this.flowStepLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_nextActDefName: nextData.wf_nextActDefName,
            fields: nextData.fields
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction,
        };
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {

            model: {
              wf_procTitle:  _this.formData.docName.trim(),
              wf_nextActDefId: nextData.wf_nextActDefId,
              wf_procDefId: _this.pListData.procDefId,
              wf_procDefKey: _this.procDefKey,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_receivers: nextData.wf_receivers,
              wf_curActDefName: _this.pListData.actDefName,
              wf_curActDefId: _this.pListData.actDefId,
              wf_nextActDefName: nextData.wf_nextActDefName,
              fields: nextData.fields
            },
            order: _this.pListData.actDefOrder,
            review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
            applyStatus: _this.formSubmit.pass,
            type: _this.applyType,
            mark: _this.mark,
            direction: nextData.direction,
          };
        }
        if (_this.nodeShow('log_title')) {
          let funCondition = _this.nodeFunCondition('log_title')
          if (funCondition&&funCondition.limitValue) {
            formData.bpmClientInputModel.title = funCondition.limitValue
          }
        }
        if (nextData.wf_nextActDefId === 'end') {
          //办结
          formData.recordStatus = 'done'
          formData.bpmClientInputModel.jointReview = false
        } else {
          //进行中
          formData.recordStatus = 'doing'
          formData.bpmClientInputModel.jointReview= nextData.multi
        }
        if(_this.nodeShow('hscdx')||_this.nodeShow('wgcdx')){
          formData.bpmClientInputModel.batch = _this.batch
          formData.bpmClientInputModel.redirectDefId=_this.redirectDefId
          formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
          if (_this.redirectOrder) {
            formData.bpmClientInputModel.order = _this.redirectOrder
          }
          formData.bpmClientInputModel.isLast = _this.isLast
        }
        formData.editStatus = _this.editStatus
        formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader')|| _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
        formData.customerEdit = _this.nodeShow('whether_customer_record')
        if (_this.nodeShow('online_edit')) {
          formData.bpmClientInputModel.onlineType = 'online_edit'
        }else if (_this.nodeShow('online_revision')){
          formData.bpmClientInputModel.onlineType = 'online_revision'
        }else if (_this.nodeShow('online_annotation')) {
          formData.bpmClientInputModel.onlineType = 'online_annotation'
        }
        if(_this.nodeShow('file_range_preview_edit') || _this.nodeShow('distribution_edit')){
          formData.editStatus = true
          formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        }
        addModifyApply(formData).then((res) => {
          if (res.code===200) {
            //  _this.$modal.msgSuccess("提交成功");
            _this.$message({
                message: '流程提交成功',//提示的信息
                type:'success',　　//类型是成功
                duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
                onClose:()=>{
                  _this.detailLoading = false
                  _this.flowStepLoading = false
                  _this.dialogVisible = false;
                  _this.close();
                }
            });
          }
        });
    },
    resetForm () {
      this.$refs["elForm"].resetFields();
    },
    async settingDocClassId (val) {
      let _this = this
      await settingDocClassId(val).then((response) => {
        _this.docClassData=response.data
        if (response.data && response.data.fileList != null) {
          let mobanwenjian = []
          response.data.fileList.forEach(item => {
            mobanwenjian.push({
              name: item.fileName,
              url: item.id
            })
          })
          this.mobanwenjian = mobanwenjian
        } else {
          this.mobanwenjian = []
        }
      });
    },
    normalizerDept (node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal (data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    handleMonitor () {
      this.monitorDrawerVisible = true;

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId);
      });
    },
    handelpbohuiqicaoren () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_reject_text`);
          }
        },
      }).then(async ({ value }) => {
        _this.loading = true
        // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
        let backarr = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_businessKey: _this.formData.id,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_procDefId: _this.pListData.procDefId,
            wf_curComment: value,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_receivers: [
              {
                receiveUserId: _this.formData.userName,
                receiveUserOrgId: _this.formData.deptId
              }
            ],
          },
          order: 0,
          review: true,
          applyStatus: false,
          type: _this.applyType,
          step: _this.attributeModel("step"),
          bizType: _this.pButton
        };
        workflowbacktostart(backarr).then((response) => {
          this.$modal.msgSuccess(_this.$t(`file_handle.change_reject_succ`));
          _this.close();
        }).finally(() => {
          _this.loading = false
        });
      })
    },
    async shengchengbianhao () {
      let _this = this
      if (_this.editStatus) {
        await _this.saveForm()
      }
      _this.shenchenbianhao = true;
      let fileIds = _this.formData.recordLinks && _this.formData.recordLinks.length > 0 ? _this.formData.recordLinks.map(item => item.fileId) : null
      let recordIdDisabled = []
      _this.formData.recordLinks.forEach(item => {
        if (item.isDeleted != 0 || item.status != 1 || _this.formData.recordLinks.filter(record => record.docId && item.docId === record.docId).length > 1) {
          recordIdDisabled.push(item.fileId)
        }
      })
      _this.recordIdDisabled = recordIdDisabled
      _this.$nextTick(() => {
        _this.$refs.docIdBox.init(_this.formData.id, fileIds);
      })
    },
    setDocId (docId) {
      let _this = this
      if (_this.formData.docId !== docId) {
        _this.formData.docId = docId
        _this.setSignatureStatus()
      }
    },
    setRecordDocId (recordLinks) {
      let _this = this
      recordLinks.forEach((element, i) => {
        _this.formData.recordLinks.forEach((val, ix) => {
          if (val.fileId === element.busId) {
            _this.formData.recordLinks[ix].docId = element.newNo;
          }
        });
      });
    },
    handleCloseChange () {
      this.dealDrawerShow = false
    },
    handleDeal (formData) {
      let _this = this
      _this.dealDrawerShow = true;
      _this.$nextTick(() => {
        console.log(_this.formData)
        _this.$refs.dealDrawer.init({ type: formData.invokeType, preChangeCode: formData.invokeId });
      });
    },
    fileUpdate (field) {
      let _this = this
      //文件重新上传 需要重新签章
      _this.setSignatureStatus()
      _this.$refs.elForm.validateField(field)
    },
    setSignatureStatus () {
      let _this = this
      if (_this.formData.id && _this.formData.isSignature !== 'N') {
        _this.formData.isSignature = 'N'
        updateModifyApply({
          id: _this.formData.id,
          isSignature: _this.formData.isSignature,
          onlyEdit: true
        })
      }
    },
    getDeptList () {
      let self = this
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
      treeselect({ status: 0 }).then(response => {
        let data = self.sortDataById(response.data);
        this.deptOptions2 = data
      });
    },
    sortDataById (data) {
      // 按id排序函数
      function sortById (a, b) {
        return parseInt(a.weight) - parseInt(b.weight)
      }
      // 递归排序子级
      function sortChildren (node) {
        if (node.children && node.children.length > 0) {
          node.children.sort(sortById);
          node.children.forEach(child => {
            sortChildren(child);
          });
        }
      }
      // 初始排序
      data.sort(sortById);
      // 对每个节点递归排序子级
      data.forEach(node => {
        sortChildren(node);
      });
      return data;
    },
    getCompanyDataList () {
      let _this = this
      getCompanyList({}).then(res => {
        _this.companyList = res.data
      })
    },
    selectPresetUser () {
      let _this = this
      _this.$nextTick(() => {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null, null, _this.formData.docClass, _this.pButton, _this.formData.presetUserList, nodeCode)
      })
    },
    selectHandlePresetUser (source, index, data) {
      let _this = this
      _this.$set(_this.formData, "presetUserList", data)
    },
    handleSelect () {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.userList.init(null, null, null)
      })
    },
    handleSubmitUser (source, index, user) {
      this.formData.nickName = user.nickName;
      this.formData.userName = user.userName;
      this.formData.deptId = user.dept.deptId;
      this.formData.deptName = user.dept.deptName;
    },
    versionSearchInit (source) {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.versionList.init(source, null, false)
      })
    },
    versionSelectHandle (source, index, data) {
      if (source === 'up') {
        this.formData.upVersionId = data.versionId
        this.formData.upDocName = data.docName
        this.formData.parentDocId = data.docId
        this.$refs.elForm.validateField("upVersionId");
      } else {
        this.formData.programVersionId = data.versionId
        this.formData.programDocName = data.docName
        this.formData.programDocId = data.docId
      }
    },
    getByDocClass (docClass) {
      let _this = this
      _this.loading = true
      getFormRuleRecursive(docClass).then(res => {
        this.formRuleList = []
        if (res.data && res.data.ruleDetails) {
          let data = JSON.parse(res.data.ruleDetails)
          this.formRuleList = data
          _this.loading = false
        }
      }).finally(err => {
        _this.loading = false
      })
    },
    initDefaultValue (value, defaultValue) {
      let _this = this
      if (defaultValue) {
        let key = defaultValue.split('@')
        if (key.length > 1) {
          _this.$set(_this.formData, value, _this.formData[key[1]].toString())
        } else {
          _this.$set(_this.formData, value, defaultValue)
        }
      }
    },
    handelRecoveryConfirm () {
      let _this = this
      updateById({ id: _this.formData.id, recoveryConfirm: _this.formData.recoveryConfirm })
    },
    //唯一序号、是否显示
    showStatistics (index, bool) {
      if (bool) {
        if (!this.quantity.includes(index)) {
          this.quantity.push(index)
        }
      }
      return bool
    },
    handleCompareFile () {
      let formData = {
        id: this.formData.id,
        docId: this.formData.docId,
        fileId: this.standardDocfileList[0].url
      }
      this.detailLoading = true
      compareFile(formData).then((res) => {
        if (res.code === 200) {
          if (res.data != null) {
            this.compareHandlePreview(res.data)
          }
        }
      }).catch(() => {
        this.detailLoading = false
      });
    },
    compareHandlePreview (id) {
      this.viewId = id;
      this.$refs.viewRef.compareHandleOpenView(this.standardDocfileList[0].url, id);
      this.viewShow = true;
      this.detailLoading = false
    },
    //从消息待办跳转来的
    isShowSubmit () {
      let invokerFormMsg = this.$route.query.invokerForm
      //当_this.procDefKey为空，并且来源为我的消息
      if ((this.procDefKey == null || this.procDefKey == undefined || this.procDefKey == '') && invokerFormMsg != null && invokerFormMsg == 'systemMsg') {
        return false
      }
      return true
    },
    messageListener (event) {
      let _this = this
      console.log('收到子页面的消息:', event)
      if (event && event.data && event.data.text) {
        let data = JSON.parse(event.data.text)
        console.log('收到子页面的消息:', data)
        if (data.ntkoData) {
          let value = data.ntkoData.FunctionArgs
          if (data.ntkoData.parentExecutionFunction === 'savaFile' && value.length<=3) {
            console.log('保存主文件')
            _this.standardDocfileList = [
              {
                url: value[0],
                name: value[1],
                protoFileId: value[2]
              }
            ]
          } else if (data.ntkoData.parentExecutionFunction === 'saveAppendix' && value.length<=3) {
            console.log('保存附件')
            _this.appendixesfileList.forEach(item => {
              if (item.protoFileId === value[2]) {
                item.url = value[0]
                item.name = value[1]
              }
            })
          }
          if((data.ntkoData.parentExecutionFunction === 'savaFile' || data.ntkoData.parentExecutionFunction === 'saveAppendix') && value.length<=3){
            _this.saveForm('edit')
          }
          if(value.length>3){
            console.log("关闭")
            _this.closeEdit(value[1])
          }
        }
      }
    },
    closeEdit(data){
      releaseStatus(data).then((response) => {
        console.log(response)
      }).finally(() => {
        this.loading = false
      })
    }
  },
};
</script>
<style scoped>
.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

body {
  background: #fff;
  font-size: 14px;
  color: #333;
  margin: 0;
  padding: 0;
  min-width: 1200px;
}
@media screen and (max-width: 1200px) {
  .el-drawer {
    overflow-x: auto;
    overflow-x: auto !important;
    width: 100% !important;
  }
  body .el-drawer .el-drawer__header {
    min-width: 1200px;
  }
  body .el-drawer .el-drawer__body {
    min-width: 1200px;
  }
  .phpage .el-drawer .el-drawer__header {
    min-width: 100px;
  }
  .phpage .el-drawer .el-drawer__body {
    min-width: 100px;
  }
}
</style>
