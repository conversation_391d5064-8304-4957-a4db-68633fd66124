<template>
  <div class="document_change_add" v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_temp_file_dept_review`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="pListData&&pListData.procInstId" @click="handleMonitor">{{
            $t(`doc.this_dept_process_monitor`)
          }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus" @click="selectPresetUser">
          {{ $t(`file_handle.change_select_people`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_cancel')&&pListData&&pListData.procInstId&&workflowStatus" type="danger"
                   @click="deleteForm" v-dbClick
        >{{ $t(`file_handle.change_revoke`) }}
        </el-button>
        <el-button v-if="pListData&&pListData.procInstId&&workflowStatus" @click="showVerify({type:'transfer'})" v-dbClick>
          {{ transferStatus ? $t(`file_handle.transfer_return`) : $t(`file_handle.transfer`) }}
        </el-button>
        <el-button
          v-if="pListData&&pListData.procInstId&&!workflowStatus&&'doing'===formData.status&&formData.createBy===userInfo.userName&&backFlowToOneStatus"
          type="danger" @click="showVerify({type:'backFlow'})" v-dbClick
        >{{ $t(`file_handle.change_withdraw`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus" @click="showVerify({type:'turnDown'})"
                   type="danger"
        >{{ $t(`file_handle.change_reject_to_preparer`) }}
        </el-button>
        <el-button v-if="(editStatus||workflowStatus)&&isShowSubmit()" type="primary" @click="submitForm" v-dbClick>
          {{ $t(`doc.this_dept_annex`) }}
        </el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{
            $t(`doc.this_dept_save`)
          }}
        </el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="pListData&&pListData.procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
          <div class="cell-btn">
            <button
              type="button"
              @click="handleSelectFile()"
              class="el-button blue el-button--default"
              v-if="editStatus"
            >
              <span>{{ $t(`doc.this_dept_select_file`) }}</span>
            </button>
          </div>
        </div>
        <div class="el-card gray-card is-always-shadow">
          <div class="el-card__body">
            <el-table :data="formData.itemList">
              <el-table-column :label="$t(`doc.this_dept_file_type`)" align="left" prop="docClass"
                               :formatter="formatterDocClass"
              >
              </el-table-column>
              <el-table-column :label="$t(`doc.this_dept_file_name`)" align="left" prop="docName">
              </el-table-column>
              <el-table-column :label="$t(`doc.this_dept_file_code`)" align="left" prop="docId"/>
              <el-table-column
                :label="$t(`doc.this_dept_file_versions2`)"
                align="left"
                prop="versionValue"
              />
              <el-table-column
                :label="$t(`doc.this_dept_staffing_dept`)"
                align="left"
                prop="deptName"
              />
              <el-table-column
                :label="$t(`doc.this_dept_staffs`)"
                align="left"
                prop="userName"
                v-if="!workflowStatus||nodeShow('jielun')"
              >
                <template slot-scope="scope">
                  <el-input
                    v-if="nodeShow('jielun')&&workflowStatus"
                    style="width: 80%; margin-right: 10px"
                    readonly
                    v-model.trim="scope.row.nickName"
                    :placeholder="$t(`doc.this_dept_select`)"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="handleSelect('itemList',scope.$index,scope.row.deptId)"
                    ></el-button>
                  </el-input>
                  <span v-else>{{ scope.row.nickName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t(`file_handle.change_result`)"
                align="left"
                v-if="!workflowStatus||nodeShow('jielun')||nodeShow('jielun_chakan')"
                prop="userName"
              >
                <template slot-scope="scope">
                  <el-select
                    style="width: 100%"
                    v-if="nodeShow('jielun')&&workflowStatus"
                    v-model="scope.row.reviewAction"
                    :placeholder="$t(`doc.this_dept_pls_select`)"
                  >
                    <el-option
                      v-for="item in actionTypeoptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <span v-else>{{ formatterPassOptions(scope.row.reviewAction) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t(`doc.company_dept_reason`)"
                align="left"
                v-if="!workflowStatus||nodeShow('jielun')||nodeShow('jielun_chakan')"
                prop="reason"
              >
                <template slot-scope="scope">
                  <el-input
                    v-if="nodeShow('jielun')&&workflowStatus"
                    style="width: 100%"
                    v-model.trim="scope.row.reason"
                  >
                  </el-input>
                  <span v-else>{{ scope.row.reason }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t(`doc.this_dept_operation`)"
                align="left"
                class-name="small-padding fixed-width"
                v-if="editStatus"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(formData.itemList,scope.$index)"
                  >{{ $t(`doc.this_dept_delete`) }}
                  </el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_appli_info`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row>
              <!--              <el-col :span="12">
                              <el-form-item :label="$t(`doc.this_dept_title`)+`:`" prop="applyTitle">
                                <el-input
                                  v-if="editStatus"
                                  v-model="formData.applyTitle"
                                  :placeholder="$t(`doc.this_dept_insert_name`)"
                                  maxlength="50"
                                  clearable
                                  style="width: 80%; margin-right: 10px"
                                >
                                </el-input>
                                <sapn v-else>{{formData.applyTitle}}</sapn>
                              </el-form-item>
                            </el-col>-->
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_title`) + `:`" prop="applyTitle">
                  <sapn>{{ formData.applyTitle }}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptId">
                  <sapn>{{ formData.deptName }}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_claimant`)+`:`" prop="userName">
                  <sapn>{{ formData.nickName }}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_date`)" prop="createTime">
                  <sapn>{{ formData.createTime }}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_appli_reason`)+`:`" prop="reason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model="formData.reason"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    maxlength="1000"
                    show-word-limit
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus">
        <div class="card-head">
          <div class="cell-title">{{ submitLabel }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_conclusion`)" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass" @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)+':'">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId="pListData.procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading=flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :selected="nodeShow('default_selected')"
        :userListStatus="!nodeShow('user_list')"
        :order="order"
        :searchQuery="searchQuery"
        :pListData="pListData"
        :hideNodeCode="hideNodeCode"
        :defaultStaff="defaultStaff"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="showVerify({type:'flowSubmit'})"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <version-list
      style="overflow:auto;"
      :selectValidate="true"
      :dataType="formData.dataType"
      :classTypeList="classTypeList"
      :versionData="formData.itemList"
      @selectHandle="versionSelectHandle"
      :selectableFun="selectableFun"
      ref="versionList"
    ></version-list>
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>
    <preset-user ref="presetUser" @selectHandle="selectHandlePresetUser"></preset-user>
    <transfer-flow ref="transferFlow" @close="close"></transfer-flow>
    <identity-verify
      :visible.sync="verifyVisible"
      :business-params="currentParams"
      @business-execute="handleBusinessExecute"
      @verify-cancel="handleCancel"
    />
  </div>
</template>
<script>
import { settingDocClassList } from '@/api/file_settings/type_settings'
import IdentityVerify from './add_import/identityVerify.vue';
import processcode from '@/views/workflowList/processcode/index.vue'
import WorkflowLogs from '@views/workflowList/workflowLogs/index.vue'
import { settingDocClassId } from '@/api/file_settings/type_settings'
import {
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, getRecordbyPorcInstId, getRedirectDefId, workflowbacktostart, backFlowToOne
} from '@/api/my_business/workflow'
// PDF本地文件预览
import { queryUserProjectList } from '@/api/system/project'
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import { getWorkflowApplyLog, selectApplySerial, selectStatusByDocId } from '@/api/my_business/workflowApplyLog'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import { addReviewApply, getReviewApplyByBpmnId, updateReviewApply } from '@/api/document_account/reviewApply'
import { getInfo, getInfoByType, getInfoByTypeDetail } from '@/api/setting/docClassFlowNodeDetail'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import { listPresetUser } from '@/api/setting/presetUser'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import { getByUpDocClassAndBizType } from '@/api/setting/docClassFlow'
import { getDocManagersByDeptId, getLeader, getDivisionLeader } from '@/api/system/user'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import { listDistributeGroupDetail } from '@/api/setting/distributeGroupDetail'

export default {
  dicts: ['business_status'],
  components: {
    IdentityVerify,
    TransferFlow,
    UserList,
    VersionList,
    processcode,
    WorkflowLogs,
    PresetUser
  },
  name: 'Add_doc',
  props: ['dataType', 'data'],
  data() {
    return {
      currentType: '',
      currentParams: null,
      verifyVisible: false,
      applyType: 'ls_review_doc_apply',
      classTypeList: undefined,
      backFlowToOneStatus: true,
      transferStatus: false,
      order: 0,
      searchQuery: {},
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      isLast: true,
      docClass: undefined,
      hideNodeCode: [],
      defaultStaff: undefined,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      open: false,
      docClassList: [],
      appendixsList: [],
      remarkfileList: [],
      changeFactor: [],
      actionTypeoptions: [
        { value: 'keep', label: this.$t(`doc.this_dept_file_status_quo`) },
        // { value: "extension", label: "文件有效期延期" },
        { value: 'update', label: this.$t(`file_set.type_revise`) },
        { value: 'disuse', label: this.$t(`file_set.type_repeal`) }
      ],
      submitLabel: undefined,
      isProject: false,
      docIdData: {},
      jiluliData: [],
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) }
      ],
      formSubmit: { summary: '', actionType: '', pass: undefined },
      pButton: 'ls_review',
      isSummary: false,
      projectList: [],
      project: { id: '', name: '' },
      activeName: 'info',
      procDefKey: undefined,
      processData: {},
      viewId: '',
      userInfo: JSON.parse(sessionStorage.getItem('USER_INFO')),
      viewShow: false,
      active: 4,
      activeIndex: '1',
      uploadType: ['doc', 'docx', 'ppt', 'xlsx', 'pdf', 'jpg', 'png'],
      monitorDrawerVisible: false,
      formData: {
        id: undefined,
        applyTitle: undefined,
        deptId: undefined,
        userName: undefined,
        reason: undefined,
        status: undefined,
        createTime: undefined,
        itemList: [],
        presetUserList: [],
        presetUserEdit: false,
        batch: undefined
      },
      rules: {
        /*applyTitle: [
          { required: true, message: this.$t(`doc.this_dept_insert_title`), trigger: "blur" },
        ],*/
        projectId: [
          { required: true, message: this.$t(`doc.this_dept_select_project`), trigger: 'blur' }
        ],
        pass: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: 'blur' }
        ],
        reason: [
          { required: true, message: this.$t(`doc.this_dept_insert_appli_reason`), trigger: 'blur' }
        ]
      },
      kuozhanshuju: {},
      field117Action: '',
      action: '/dms-admin/process/file/local_upload',
      appendixesfileList: [],
      standardDocfileList: [],
      menuitem: '1',
      classLevelOptions: [],
      summary: '',
      pListData: {},
      editStatus: false,
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading: false,
      nodeDetail: {},
      nodeDetailList: [],
      batch: undefined
    }
  },
  computed: {},
  watch: {
    docClass(val) {
      let _this = this
      if (val) {
        _this.getNodeDetailInfo()
        if (!_this.procInstId) {
          _this.getByUpDocClassAndBizType(val)
        }
      }
    },
    'formData.dataType'(val) {
      let _this = this
      _this.isProject = val === 'project'
    },
    'formData.itemList'(val) {
      let _this = this
      if (_this.editStatus) {
        _this.updateGenerateApplyTitle()
      }
    },
    data(val) {
      if (val) {
        this.init(val)
      }
    }
  },
  async created() {
    let response1 = await this.getConfigKey('back_flow_to_one')
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true'
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    showVerify(invokeFrom) {
      this.currentParams = invokeFrom
      // 判断是否需要身份验证
      if (this.nodeShow('authentication')) {
        this.verifyVisible = true
      } else {
        // 不需要验证时直接执行业务逻辑
        this.handleBusinessExecute(this.currentParams)
      }
    },

    // 执行业务逻辑
    handleBusinessExecute(params) {
      switch(params.type) {
        case 'flowSubmit':
          this.handleWorkflowSubmit(params)
          break
        case 'transfer':
          this.transferForm(params)
          break
        case 'backFlow':
          this.handleBackFlowToOne(params)
          break
        case 'publish':
          this.handlePublish(params)
          break
        case 'turnDown':
          this.handelpbohuiqicaoren(params)
          break
      }
    },
    handleExport2() {
      this.$refs.pdfView.init()
    },
    async init(row) {
      let _this = this
      _this.rest()
      _this.loading = true
      _this.formData.dataType = _this.dataType
      _this.classTypeList = row.classTypeList
      _this.batch = row.batch
      _this.getQueryUserProjectList()
      if (row.preChangeCode) {
        let res = await getWorkflowApplyLog(row.preChangeCode)
        row.procInstId = res.data.procInstId
      }
      if (row.order) {
        _this.order = row.order
      }
      //是否编辑模式
      _this.$nextTick(() => {
        if (row && row.procInstId) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          _this.getDetail(_this.procInstId)
        } else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.getSettingDocClassTreeseList()
          // _this.getByUpDocClassAndBizType();
        }
      })
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      getReviewApplyByBpmnId(procInstId).then(async(res) => {
        let formData = res.data
        let res3 = await listPresetUser({ bizId: formData.id })
        formData.presetUserList = res3.data
        _this.formData = formData
        _this.docClass = formData.itemList[0].docClass
        _this.getSettingDocClassTreeseList()
      }).finally(() => {
        _this.detailLoading = false
      })
    },
    getQueryUserProjectList() {
      let _this = this
      queryUserProjectList().then(res => {
        _this.projectList = res.data
      })
    },
    rest() {
      let _this = this
      _this.activeName = 'info'
      _this.formData = {
        id: undefined,
        applyTitle: undefined,
        applyType: this.pButton,
        deptId: this.userInfo.deptId,
        userName: this.userInfo.userName,
        deptName: this.userInfo.dept.deptName,
        nickName: this.userInfo.nickName,
        reason: undefined,
        status: undefined,
        createTime: _this.parseTime(new Date()),
        dataType: undefined,
        itemList: [],
        presetUserList: [],
        presetUserEdit: false,
        batch: undefined
      }
    },
    procInstInfoAndStatus(procInstId) {
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
        } else {
          _this.pListData = { procInstId: procInstId }
        }
        _this.getExtAttributeModel()
      })
    },
    nodeShow(code) {
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      } else {
        return false
      }
    },
    selectPresetUser() {
      let _this = this
      _this.$nextTick(() => {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null, null, null, _this.pButton, _this.formData.presetUserList, nodeCode)
      })
    },
    selectHandlePresetUser(source, index, data) {
      let _this = this
      _this.$set(_this.formData, 'presetUserList', data)
    },
    async initStatus() {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.$t(`file_handle.change_auditing`)
      _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
    },
    async getByUpDocClassAndBizType(docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass, _this.pButton)
      _this.procDefKey = data && data.flowKey ? data.flowKey : ''
      _this.getWorkflowprocesskey()
    },
    getNodeDetailInfo() {
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData && curActDefId && _this.docClass) {
        getInfo(_this.docClass, _this.pButton, curActDefId).then(res => {
          let nodeDetail = {}
          res.data.forEach(item => {
            nodeDetail[item.code] = true
          })
          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    getWorkflowprocesskey() {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data
            this.getExtAttributeModel()
          })
        })
      } else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
      }
    },
    getExtAttributeModel() {
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId && curActDefId) {
        _this.getNodeDetailInfo()
        // getExtAttributeModel(
        //   procDefId,
        //   curActDefId
        // ).then((res) => {
        //   console.log("扩展属性====>", res);
        //   let kuozhanshuju = {}
        //   res.data.forEach(item=>{
        //     kuozhanshuju[item.objKey] = item.objValue
        //   })
        //   _this.kuozhanshuju = kuozhanshuju;
        // _this.editStatus =_this.nodeShow('bianji')&&_this.workflowStatus
        // _this.submitLabel = _this.nodeShow('pizhun')?_this.$t(`file_handle.change_approve`):_this.$t(`file_handle.change_auditing`)
        // }).finally(()=>{
        _this.loading = false
        // });
      } else {
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModel(val) {
      if (this.kuozhanshuju && this.kuozhanshuju !== {}) {
        let obj = this.kuozhanshuju[val]
        return obj ? obj === 'true' : false
      } else {
        return false
      }
    },

    close() {
      this.viewShow = false
      this.$emit('close')
    },
    handlePreview(id) {
      this.viewId = id
      this.$refs.viewRef.handleOpenView(id)
      this.viewShow = true
    },
    //不需要验证必填的保存
    async saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t('doc.this_dept_no_process_setting'))
        return
      }
      /* if (!_this.formData.applyTitle) {
         _this.$message.warning(_this.$t(`doc.this_dept_title_not_null`));
         return;
       }*/
      if (!_this.formData.applyTitle) {
        await _this.generateApplyTitle()
      }
      if (!_this.formData.itemList || _this.formData.itemList.length < 1) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_select_file`))
        return
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.recordStatus = 'draft'
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader') || _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_leader') || _this.nodeShow('next_set_division_leader')
      if (formData.id) {
        updateReviewApply(formData).then((res) => {
          if (res.code === 200) {
            _this.$message({
              message: '保存成功',//提示的信息
              type: 'success',　　//类型是成功
              duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose: () => {
                _this.loading = false
              }
            })
          }
        })
      } else {
        let wf_receivers = []
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        })
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.applyTitle,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId
          },
          order: _this.pListData.actDefOrder,
          type: _this.applyType,
          review: false
        }
        formData.editStatus = _this.editStatus
        addReviewApply(formData).then((res) => {
          if (res.code === 200) {
            _this.formData.id = res.data.businessKey
            _this.procInstId = res.data.procInstId
            _this.procInstInfoAndStatus(res.data.procInstId)
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type: 'success',　　//类型是成功
              duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose: () => {
                _this.loading = false
              }
            })
          }
        })
      }
    },
    jointReviewRedirect() {
      let _this = this
      if (_this.nodeShow('hscdx')) {
        getRecordbyPorcInstId(_this.procInstId).then(res => {
          //会审只剩最后一个环节
          if (res.data.length === 1) {
            let query = {
              docClass: _this.docClass,
              bizType: _this.pButton,
              code: 'cdxmd',
              batch: _this.batch
            }
            getRedirectDefId(query).then(res1 => {
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length === 1) {
                  let next = res1.data.find(item => item.nextDefId === funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          }else{
            _this.isLast = false
          }
        })
      }
    },
    transferForm() {
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData, _this.formData.id, _this.applyType, _this.order, _this.pButton)
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
        return
      }
      let dialogVisible = true
      //审核
      if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
        if (_this.formSubmit.pass === undefined) {
          _this.$modal.msgError(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
          return
        }
        // 验证是否填写了审核意见
        if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel + _this.$t(`doc.this_dept_comments`))
          return
        }
      }
      if (!!_this.$refs['elForm']) {
        let valid = await _this.$refs['elForm'].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs['validateForm']) {
        let validateValid = await _this.$refs['validateForm'].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      if (await _this.validate()) {
        return
      }
      _this.jointReviewRedirect()
      this.loading = true
      await _this.setPresetUserList()
      this.dialogVisible = true
      this.loading = false
    },
    async setPresetUserList() {
      let _this = this
      if (_this.nodeShow('preset_countersign')) {
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition.groupId) {
          let users = []
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item => {
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 下个环节预选直属部门领导
      if (_this.nodeShow('next_set_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getLeader(_this.userInfo.userName, _this.formData.deptId)
            user = res.data
          } else {
            let res = await getLeader(_this.userInfo.userName, _this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      if (_this.nodeShow('next_set_division_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_division_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getDivisionLeader(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDivisionLeader(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      //设置流程待选任意为文控
      if (_this.nodeShow('set_flow_select_list')) {
        let funCondition = _this.nodeFunCondition('set_flow_select_list')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getDocManagersByDeptId(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDocManagersByDeptId(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = []
            user.forEach(item => {
              users.push({
                userName: item.userName,
                nickName: item.nickName,
                deptId: item.deptId,
                deptName: item.dept.deptName
              })
            })
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (_this.formData.presetUserList.length > 0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if (_this.nodeShow('cdxmd') && _this.batch) {
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition && funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({
            batch: _this.batch,
            nextDefId: _this.pListData.curActDefId,
            havaDetail: true
          })
          let nodeCode = ''
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({
              userName: item.sender,
              nickName: item.nickName,
              deptId: item.senderDeptId,
              deptName: item.deptName
            })
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode === nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.pass = _this.formSubmit.pass
      _this.searchQuery.batch = !!_this.batch
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')) {
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition && funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围  过滤出没有预选人员的环节
          hideNodeCode = funCondition.nodeCode.filter(item => !defaultStaff.some(dpu => item === dpu.nodeCode && dpu.users && JSON.parse(dpu.users).length > 0))
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode && hideNodeCode.length === length) {
            hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length - hideNodeCode.length) <= limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length === 0) {
            defaultStaff.forEach(item => {
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
          }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    nodeFunCondition(code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      } else {
        return undefined
      }
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`file_handle.change_pass`)
      if (val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`file_handle.change_no_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validate() {
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')) {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(null, _this.pButton, _this.formData.presetUserList, nodeCode)
        if (bool) {
          return true
        }
      }
      if (!_this.formData.itemList || _this.formData.itemList.length < 1) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_select_file`))
        return true
      }
      if (_this.nodeShow('jielun')) {
        if (_this.formData.itemList.some(item => !item.reviewAction)) {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_select_review_conclusion`))
          return true
        }
      }
      if (_this.editStatus) {
        for (let item of _this.formData.itemList) {
          if (!item.id) {
            let res1 = await selectStatusByDocId({ versionId: item.versionId, notInDraft: false })
            if (res1.data != 0) {
              _this.$modal.msgWarning(res1.msg)
              validate = true
            }
          }
        }
      }
      return false
    },

    //提交表单和流程数据
    async handleWorkflowSubmit(invokeFrom) {
      let _this = this
      let wf_receivers = []
      let wf_nextActDefId = null
      let wf_nextActDefName = null
      if (typeof (invokeFrom) == 'object') {
        if (_this.$refs.prochild.receiveUserList.length < 1 && _this.$refs.prochild.nextData.actDefType !== 'endEvent') {
          _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`))
          return
        }
        _this.$refs.prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
            receiveUserId: element.id,
            receiveUserOrgId: element.parentId
          })
        })
        wf_nextActDefId = _this.$refs.prochild.nextData.actDefId
        wf_nextActDefName = _this.$refs.prochild.nextData.actDefName
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
        wf_nextActDefName = '结束'
      }

      if (!_this.formData.applyTitle) {
        await _this.generateApplyTitle()
      }
      let formData = JSON.parse(JSON.stringify(_this.formData))

      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.applyTitle,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_nextActDefId: wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: wf_nextActDefName
          },
          order: _this.pListData.actDefOrder,
          applyStatus: _this.formSubmit.pass,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          type: _this.applyType
        }
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {
          type: _this.applyType,
          model: {
            wf_procTitle: _this.formData.applyTitle,
            wf_nextActDefId: wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: wf_nextActDefName
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass
        }
      }
      if (_this.$refs.prochild.nextData.actDefType === 'endEvent') {
        //办结
        formData.recordStatus = 'done'
        formData.bpmClientInputModel.jointReview = false
      } else {
        //进行中
        formData.recordStatus = 'doing'
        formData.bpmClientInputModel.jointReview = _this.$refs.prochild.nextData.multi
      }
      if (_this.nodeShow('hscdx')) {
        formData.bpmClientInputModel.batch = _this.batch
        formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
        formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
        if (_this.redirectOrder) {
          formData.bpmClientInputModel.order = _this.redirectOrder
        }
        formData.bpmClientInputModel.isLast = _this.isLast
      }
      formData.editStatus = _this.editStatus || _this.nodeShow('jielun')
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader') || _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
      addReviewApply(formData).then((res) => {
        if (res.code === 200) {
          _this.$message({
            message: _this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false
              _this.close()
            }
          })
        }
      })
    },
    handleSelect(source, index, deptId) {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.userList.init(source, index, deptId)
      })
    },
    getSettingDocClassTreeseList() {
      let query = { classStatus: '1', dataType: this.formData.dataType, classTypeList: this.classTypeList }
      settingDocClassList(query).then(
        (response) => {
          this.docClassList = JSON.parse(JSON.stringify(response.rows))
          this.classLevelOptions = this.handleTree(response.rows, 'id', 'parentClassId')
        }
      )
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children
      }
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal(data) {
      const result = []
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums
          })
          let child = data.children
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i])
            }
          }
        }
        loop(item)
      })
      return result
    },
    handleMonitor() {
      this.monitorDrawerVisible = true

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId)
      })
    },
    handelpbohuiqicaoren() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_reject_text`)
          }
        }
      }).then(async({ value }) => {
        _this.loading = true
        // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
        let backarr = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_businessKey: _this.formData.id,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_procTitle: _this.formData.applyTitle,
            wf_procDefId: _this.pListData.procDefId,
            wf_curComment: value,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_receivers: [
              {
                receiveUserId: _this.formData.userName,
                receiveUserOrgId: _this.formData.deptId
              }
            ]
          },
          order: 0,
          review: true,
          applyStatus: false,
          type: _this.applyType,
          step: _this.order,
          bizType: _this.pButton
        }
        workflowbacktostart(backarr).then((response) => {
          this.$modal.msgSuccess(_this.$t(`file_handle.change_reject_succ`))
          _this.close()
        }).finally(() => {
          _this.loading = false
        })
      })
    },
    handleBackFlowToOne() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`)
          }
        }
      }).then(({ value }) => {
        _this.loading = true
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.applyTitle,
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName
              },
              bizType: _this.pButton,
              review: true,
              applyStatus: false,
              status: 'draft',
              type: _this.applyType,
              mark: _this.mark,
              order: 0
            }
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break
            }
          }
          _this.close(true)
        })
      })
    },
    deleteForm() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`)
          }
        }
      }).then(({ value }) => {
        _this.loading = true
        let formData = {
          id: _this.formData.id,
          bpmClientInputModel: {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_curComment: value
            },
            order: _this.order,
            type: _this.applyType,
            review: true,
            applyStatus: false
          },
          recordStatus: 'cancel',
          editStatus: false
        }
        addReviewApply(formData).then((res) => {
          if (res.code === 200) {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`))
            this.close(true)
          }
        })
      })
    },
    onProjectChange(val) {
      let _this = this
      _this.formData.projectId = val.id
      _this.formData.projectName = val.name
      _this.formData.invokeId = val.id
    },
    handleSelectFile() {
      this.$nextTick(() => {
        this.$refs.versionList.init(null, null, true, 'Y')
      })
    },
    selectableFun(row, index, versionData, selectInfoData) {
      if (versionData.findIndex(item => item.docId === row.docId) > -1) {
        return false
      }
      let data = versionData.concat(selectInfoData)
      if (data.length > 0) {
        return data[0].docClass === row.docClass
      } else {
        return true
      }
    },
    async versionSelectHandle(source, index, data, callback) {
      let _this = this
      if (data.length < 1) {
        this.$modal.msg(this.$t(`doc.this_dept_select_file`))
        callback(false)
      } else {
        let bool = true
        for (const item of data) {
          let res = await selectStatusByDocId({ versionId: item.versionId, notInDraft: false })
          if (res.data != 0 && res.data != _this.formData.id) {
            _this.$modal.msgWarning(res.msg)
            bool = false
          }
        }
        callback(bool)
        if (bool) {
          for (const item of data) {
            this.formData.itemList.push({
              docName: item.docName,
              docId: item.docId,
              docClass: item.docClass,
              versionId: item.versionId,
              versionValue: item.versionValue,
              userName: item.userName,
              deptId: item.deptId,
              nickName: item.nickName,
              deptName: item.deptName
            })
          }
          this.docClass = data[0].docClass
        }
        this.open = false
      }
    },
    userSelectHandle(source, index, user) {
      this.formData[source][index].nickName = user.nickName
      this.formData[source][index].deptName = user.dept.deptName
      this.formData[source][index].userName = user.userName
      this.formData[source][index].deptId = user.deptId
    },
    handleDelete(dataList, index) {
      dataList.splice(index, 1)
    },
    formatterDocClass(row, column, cellValue, index) {
      let _this = this
      if (_this.docClassList) {
        let item = _this.docClassList.find(item => item.id === cellValue)
        return item ? item.className : cellValue
      }
      return cellValue
    },
    formatterPassOptions(value) {
      let _this = this
      if (value) {
        let option = _this.actionTypeoptions.find(item => item.value === value)
        return option ? option.label : value
      }
      return ''
    },
    /**
     * 生成标题
     * @returns {Promise<void>}
     */
    async generateApplyTitle() {
      let _this = this
      await selectApplySerial(_this.pButton).then((res) => {
        _this.applyTitleSerial = res.data
        _this.formData.applyTitle = _this.formData.deptName + '-' + _this.formData.itemList.length + '份' + '-' + _this.applyTitleSerial
      })
    },
    updateGenerateApplyTitle() {
      if (this.formData.applyTitle) {
        let parts = this.formData.applyTitle.split('-')
        let deptNameDash = parts[0]
        let applyTitleSerialDash = parts[parts.length - 1]
        this.formData.applyTitle = deptNameDash + '-' + this.formData.itemList.length + '份' + '-' + applyTitleSerialDash
      }
    },
    //从消息待办跳转来的
    isShowSubmit() {
      let invokerFormMsg = this.$route.query.invokerForm
      //当_this.procDefKey为空，并且来源为我的消息
      if ((this.procDefKey == null || this.procDefKey == undefined || this.procDefKey == '') && invokerFormMsg != null && invokerFormMsg == 'systemMsg') {
        return false
      }
      return true
    }
  }
}
</script>
<style scoped>
.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

</style>
