<template>
  <div class="document_change_add"
       v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file`) }}{{$t(`doc.this_dept_cancel`)}}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="procInstId"
                   @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus&&!batchStatus"
                   @click="selectPresetUser">{{ $t(`file_handle.change_select_people`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_cancel')&&procInstId&&workflowStatus&&!batchStatus"
                   type="danger"
                   @click="deleteForm"
                   v-dbClick>{{ $t(`file_handle.change_revoke`) }}</el-button>
        <el-button v-if="procInstId&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'transfer'})"
                   v-dbClick>{{ transferStatus?$t(`file_handle.transfer_return`):$t(`file_handle.transfer`) }}</el-button>
        <el-button v-if="procInstId&&!workflowStatus&&'doing'===formData.processStatus&&formData.createBy===userInfo.userName&&backFlowToOneStatus&&!batchStatus"
                   type="danger"
                   @click="showVerify({type:'backFlow'})"
                   v-dbClick>{{$t(`file_handle.change_withdraw`)}}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'turnDown'})"
                   type="danger">{{ $t(`file_handle.change_reject_to_preparer`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_generate_code')&&workflowStatus&&!batchStatus"
                   @click="shengchengbianhao()"
                   type="primary">{{ $t(`file_handle.change_generate_num`) }}</el-button>
        <!-- 【签章生效】和【执行发布】一般出现在发布环节 -->
        <el-button v-if="nodeShow('top_btn_setup_time')&&workflowStatus&&!batchStatus"
                   @click="handleSignEffective()"
                   type="primary">{{ $t(`file_handle.change_signature_effc`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_publish_file')&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'publish'})"
                   type="primary">{{ $t(`file_handle.change_execute_release`) }}</el-button>
        <!-- 提交按钮在 非【执行发布】环节出现 -->
        <el-button v-if="!nodeShow('top_btn_publish_file') &&workflowStatus&&!batchStatus&&isShowSubmit()"
                   type="primary"
                   @click="submitForm"
                   v-dbClick>
          {{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button v-if="editStatus"
                   type="primary"
                   @click="saveForm"
                   v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="procInstId"
             v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)"
                   name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)"
                   name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body"
         v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elForm"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15"
                    v-if="isProject">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_project`)+`:`"
                              prop="projectId">
                  <span>{{formData.projectName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" ">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_types`)"
                              prop="docClass">
                  <span>{{docClassData.className}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_type`) + `:`"
                              prop="changeType">
                  <span>{{$t(`doc.this_dept_cancel`)}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`)+`:`"
                              prop="upVersionId">
                  <span>{{formData.upDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)"
                              prop="parentDocId">
                  <span>{{formData.parentDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)"
                              prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)"
                              prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)"
                              prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staffing_depts`)"
                              prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staff`)"
                              prop="userName">
                  <span>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`"
                              prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
              <!--      变更流程来的流程显示流程截止日期      -->
              <el-col :span="12"
                      v-if="showStatistics('invokeType', formData.invokeType&&formData.invokeType.includes('change'))">
                <el-form-item :label="$t(`doc.this_dept_deadline`)"
                              prop="deadline">
                  <span>{{parseTime(formData.deadline, "{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <dynamic-form ref="dynamic"
                          :formRuleList="formRuleList"
                          :formData="formData"
                          :editStatus="false"
                          :deptOptions2="deptOptions2"
                          :dict="dict" />
            <!----------------------------------根据文件分类，编制时新增字段 ------------------------------------------------>

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.whether_retain`)+':'"
                              prop="whetherRetain">
                  <el-radio-group v-if="editStatus&&formData.docStatus==='1'"
                                  v-model.trim="formData.whetherRetain">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.whetherRetain" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item v-if="formData.whetherRetain===yes"
                              :label="$t(`doc.retain_deadline`)+`:`"
                              prop="retainDeadline">
                  <el-date-picker v-if="editStatus"
                                  v-show="formData.whetherRetain===yes"
                                  v-model="formData.retainDeadline"
                                  align="right"
                                  type="date"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  :placeholder="$t(`doc.this_dept_select_date`)">
                  </el-date-picker>
                  <span v-else
                        v-show="formData.whetherRetain===yes">{{parseTime(formData.retainDeadline, "{y}-{m}-{d}")}}</span>
                </el-form-item>
                <el-form-item v-else
                              label=" "
                              prop=" ">
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_temp`)">
                  <div class="link-box bzlink-box">
                    <span v-if="mobanwenjian != ''"
                          style="color: #385bb4; cursor: pointer"
                          @click="handlePreview(mobanwenjian[0].id)">{{ mobanwenjian[0].fileName }}</span>
                    <span v-if="mobanwenjian != ''"
                          style="color: #385bb4; cursor: pointer; margin-left: 10px"
                          @click="
                        handelefileLocalDownload(
                          mobanwenjian[0].id,
                          mobanwenjian[0].fileName
                        )
                      ">下载</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_source`)+`:`"
                              prop="invokeId">
                  <span style="color: #385bb4; cursor: pointer"
                        @click="handleDeal(formData)">{{formData.preChangeCode}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_effec_ver`)+`:`">
                  <div class="link-box bzlink-box">
                    <span v-if="isEmpty(formData.preStandardDoc)"
                          style="color: #385bb4; cursor: pointer"
                          @click="handlePreview(formData.preStandardDoc.fileId)">{{ formData.preStandardDoc.fileName }}</span>
                    <span v-show="(formData.classType!==classTypeRecord&&checkPermi(['doc:file:download']))||(formData.classType===classTypeRecord&&checkPermi(['record:file:download']))"
                          v-if="editStatus&&isEmpty(formData.preStandardDoc)"
                          style="color: #385bb4; cursor: pointer; margin-left: 10px"
                          @click="
                        handelefileLocalDownload(
                          formData.preStandardDoc.fileId,
                          formData.preStandardDoc.fileName
                        )
                      ">下载</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_effec_ver`)+`:`"
                              prop="">
                  <div v-for="(item, i) in formData.preAppendixes"
                       class="link-box bzlink-box"
                       :key="i">
                    <span style="color: #385bb4; margin-left: 10px; cursor: pointer"
                          :key="i"
                          @click="handlePreview(item.fileId)">{{ item.fileName }}
                    </span>
                    <span :key="i"
                          v-show="(formData.classType!==classTypeRecord&&checkPermi(['doc:file:download']))||(formData.classType===classTypeRecord&&checkPermi(['record:file:download']))"
                          v-if="editStatus"
                          style="color: #385bb4; cursor: pointer; margin-left: 10px"
                          @click="
                        handelefileLocalDownload(item.fileId, item.fileName)
                      ">{{ $t(`doc.this_dept_download`) }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="formData.classType===classTypeForeign">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`) + `:`"
                              prop="compliance">
                  <span>{{formData.compliance}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`) + `:`"
                              prop="changeReason">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.changeReason"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`) +`:`"
                              prop="content">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.content"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_content`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`"
                              prop="remark">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.remark"
                            type="textarea"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="(trains&&trains.length>0)||(workflowStatus&&nodeShow('page_oper_add_train_record'))">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`"
                              prop="remark">
                  <fileUpload v-model.trim="trains"
                              :editStatus="workflowStatus&&nodeShow('page_oper_add_train_record')"
                              limit="100"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','ppts','bmp','jpg','png','svg','tif','gif']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'train')" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="nodeShow('recovery_confirm')||formData.processStatus==='done'">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.recovery_confirm`)+`:`"
                              prop="recoveryConfirm">
                  <el-radio-group v-if="nodeShow('recovery_confirm')"
                                  @input="handelRecoveryConfirm"
                                  v-model.trim="formData.recoveryConfirm">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.recoveryConfirm" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <!-- 文件推送配置 -->
      <FilePushConfig
        v-if="nodeShow('file_push')"
        :doc-class-id="formData.docClass"
        :data-type="formData.dataType"
        :docId="formData.docId"
        show-push-file
        :data="formData.filePush"

        :required="getValidateByCode('file_push')&&editStatus"

        ref="filePushConfig"
      />

      <div class="news-card"
           v-if="nodeShow('whether_customer_record')||formData.whetherCustomer">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.customer_record`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormCustomer"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.whether_customer_records`)+`:`"
                              prop="whetherCustomer">
                  <el-radio-group v-if="workflowStatus&&customerStatus"
                                  v-model.trim="formData.whetherCustomer">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.whetherCustomer" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.whetherCustomer===yes">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_cover`)+`:`"
                              prop="customerCover">
                  <fileUpload v-model.trim="cover"
                              :editStatus="workflowStatus&&nodeShow('page_oper_add_customer_cover')"
                              :down-status="workflowStatus&&nodeShow('add_customer_record')"
                              limit="1"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'cover')" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_approval_record`)+`:`"
                              prop="customerRecord">
                  <customer-box ref="customerBox"
                                :applyId="formData.id"
                                :versionValue="formData.versionValue"
                                :editStatus="workflowStatus&&nodeShow('add_customer_record')&&formData.whetherCustomer!==no"></customer-box>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card"
           v-if="nodeShow('whether_train')||!!formData.yNTrain">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_train`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormTrain"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_or_not`)+`:`"
                              prop="yNTrain">
                  <el-radio-group v-if="workflowStatus&&trainStatus"
                                  v-model.trim="formData.yNTrain">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.yNTrain" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="el-card news-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs v-model.trim="activeIndex"
                   class="news-tabs">
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeNote&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_master_file`) }}</span>
              <link-doc :editStatus="editStatus"
                        v-show="activeIndex  == '1'"
                        :dataList="formData.noteDocLinks"
                        ref="linkDoc"
                        :dataType="formData.dataType"></link-doc>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeDoc&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note :editStatus="false"
                         :status="false"
                         v-show="activeIndex  == '1'"
                         :dataList="formData.noteLinks"
                         ref="linkNote"
                         :dataType="formData.dataType"></link-note>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeDoc&&!classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record ref="linkRecord"
                           :disuseStatus="true"
                           :editStatus="editStatus"
                           :dataList="formData.recordLinks"
                           v-show="activeIndex  == '1'"
                           :dataType="formData.dataType"></link-record>
            </el-tab-pane>
            <el-tab-pane name="2"
                         v-if="formData.classType===classTypeDoc">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file :editStatus="false"
                         v-show="activeIndex  == '2'"
                         :dataList="formData.docLinks"
                         ref="linkFile"
                         :dataType="formData.dataType"></link-file>
            </el-tab-pane>
            <el-tab-pane name="3">
              <span slot="label">{{ $t(`doc.this_dept_file_history`) }}</span>
              <el-card class="gray-card">
                <historicalVersion @handleClick="handleDeal"
                                   :detatailsData="formData"
                                   v-show="activeIndex  == '3'"></historicalVersion>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <div class="news-card"
           v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!batchStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="validateForm"
                   :model="formSubmit"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="200px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+'结论:'"
                              prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"
                                  @input="commentItemSelect">
                    <el-radio v-for="dict in passoptions"
                              :key="dict.value"
                              :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+'意见:'">
                  <el-input v-model="formSubmit.summary"
                            type="textarea"
                            :placeholder="'请输入'+submitLabel+'意见'"
                            maxlength="200"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId="procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员 -->
    <el-dialog title="选择下一环节及人员"
               v-if="dialogVisible"
               :visible.sync="dialogVisible"
               width="60%"
               append-to-body
               v-loading=flowStepLoading
               :close-on-click-modal="false"
               :close-on-press-escape="false">
      <processcode ref="prochild"
                   :selected="nodeShow('default_selected')"
                   :userListStatus="!nodeShow('user_list')"
                   :order="order"
                   :defaultStaff="defaultStaff"
                   :hideNodeCode="hideNodeCode"
                   :searchQuery="searchQuery"
                   :pListData="pListData"
                   :isSummary="isSummary"
                   :hiddenUserBox="nodeShow('wgcdx')"></processcode>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary"
                   v-dbClick
                   @click="showVerify({type:'flowSubmit'})">{{ $t(`doc.this_dept_annex`) }}</el-button>
      </span>
    </el-dialog>
    <monitor-drawer v-if="monitorDrawerVisible"
                    ref="monitorDrawer"></monitor-drawer>
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="closeAS">
    </as-pre-view>
    <!-- PDF文件预览组件 -->
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="close"></as-pre-view>
    <el-drawer :wrapperClosable='false'
               :visible.sync="dealDrawerShow"
               :append-to-body="true"
               direction="rtl"
               size="90%"
               :with-header="false"
               :show-close="false"
               modal-append-to-body
               :destroy-on-close="true">
      <div style="width:100%; height:100%;overflow: hidden">
        <workflow-router ref="dealDrawer"
                         @closeDrawer="handleCloseChange"></workflow-router>
      </div>
    </el-drawer>
    <preset-user ref="presetUser"
                 @selectHandle="selectHandlePresetUser"></preset-user>
    <transfer-flow ref="transferFlow"
                   @close="close"></transfer-flow>
    <identity-verify
      :visible.sync="verifyVisible"
      :business-params="currentParams"
      @business-execute="handleBusinessExecute"
      @verify-cancel="handleCancel"
    />
  </div>
</template>
<script>
import historicalVersion from "./add_import/historicalVersion";
import { processFileLocalUpload } from "@/api/commmon/file";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import { getDocManagersByDeptId, getLeader, getDivisionLeader } from '@/api/system/user'
import { settingDocClassList } from "@/api/file_settings/type_settings";
import { listDept, treeselect } from '@/api/system/dept'
import {
  addModifyApply,
  getModifyApply,
  updateModifyApply,
  linkLoglistlink,
  getInfoByBpmnId, getDocNoByApplyId, getRecordDocNoByLinkId, updateById, queryModifyApplyTrain
} from '@/api/file_processing/modifiyApply'
import { signEffective } from "@/api/file_processing/fileSignature";
import { settingDocClassId } from "@/api/file_settings/type_settings";
import { getByUpDocClassAndBizType } from "@/api/setting/docClassFlow";
import { getInfo } from "@/api/setting/docClassFlowNodeDetail";
import { modifyApplyLinklist, standardGetDetail } from "@/api/document_account/standard";
import {
  workflowSubmit,
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, workflowbacktostart, getRecordbyPorcInstId, getRedirectDefId, backFlowToOne, workflowTransfer
} from '@/api/my_business/workflow'
import mixin from "@/layout/mixin/Commmon.js";
import { getModifyApplyTrainList, updateModifyApplyTrainList } from '@/api/my_business/modifyApplyTrain'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import WorkflowRouter from '@views/workflowList/workflowRouter.vue'
import { selectStatusByDocId, selectStatusRecord } from '@/api/my_business/workflowApplyLog'
import { listPresetUser } from '@/api/setting/presetUser'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import { checkPermi } from '@/utils/permission'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import { getInfoBy } from '@/api/setting/docClassSetting'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import LinkDoc from '@views/workflowList/addWorkflow/add_import/linkDoc.vue'
import { getFormRuleRecursive } from '@/api/setting/formRule'
import { dictLanguage } from '../../../utils/ruoyi'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import CustomerBox from '@views/workflowList/addWorkflow/add_import/customerBox.vue'
import RelationPlanNo from '@/components/RelationPlanNo.vue'
import { listDistributeGroupDetail } from '@/api/setting/distributeGroupDetail'
import DynamicForm from '@/views/components/DynamicForm.vue'
import IdentityVerify from "@views/workflowList/addWorkflow/add_import/identityVerify.vue";
import FilePushConfig from '@/components/FilePushConfig'

export default {
  dicts: ['sys_yes_no', 'tenant_list', "file_type", "file_status", "series_code", "face_option", "institutional"],
  components: {
    IdentityVerify,
    RelationPlanNo,
    CustomerBox,
    TransferFlow,
    LinkDoc,
    LinkNote,
    PresetUser,
    WorkflowRouter,
    LinkFile,
    LinkRecord,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs,
    DynamicForm,
    FilePushConfig
  },
  name: "Add_doc",
  props: ["dataType", 'data'],
  mixins: [mixin],
  data () {
    return {
      currentType: '',
      currentParams: null,
      verifyVisible: false,
      applyType: 'disuse_doc',
      dataListIndex: undefined,
      order: 0,
      yes: 'Y',
      no: 'N',
      hideNodeCode: [],
      defaultStaff: undefined,
      classTypeRecordMN: true,
      backFlowToOneStatus: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      searchQuery: {},
      shlkPath: process.env.VUE_APP_SHLK_PATH,
      procInstId: undefined,
      dealDrawerShow: false,
      submitLabel: undefined,
      isProject: false,
      docIdData: {},
      jiluliData: [],
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: undefined },
      pButton: 'DISUSE',
      isSummary: false,
      title: this.$t(`doc.this_dept_add_file`),
      activeName: "info",
      nodeDetail: {},
      nodeDetailList: [],
      procDefKey: undefined,
      processData: {},
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible: false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      isLast: true,
      deptOptions2: [],
      quantity: [],
      formRuleList: [],
      formData: {
        invokeType: '',
        docStatus: undefined,
        whetherRetain: undefined,
        retainDeadline: undefined,
        whetherCustomer: 'Y',
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: undefined, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        noteDocLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        step: undefined,
        presetUserList: [],
        batch: undefined,
        productLine: undefined,
        process: undefined,
        productType: undefined,
        haveLinkFile: undefined,
        custodyDeptId: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        factorys: null,
        recoveryConfirm: undefined,
        yNTrain: undefined,
        ext1: undefined,
        ext2: undefined,
        ext3: undefined,
        ext4: undefined,
        ext5: undefined,
        ext6: undefined,
        ext7: undefined,
        ext8: undefined,
        ext9: undefined,
        ext10: undefined,
        ext11: undefined,
        ext12: undefined,
        ext13: undefined,
        ext14: undefined,
        ext15: undefined,
        ext16: undefined,
        ext17: undefined,
        ext18: undefined,
        ext19: undefined,
        ext20: undefined,
        internalDocId: null,
      },
      rules: {
        whetherRetain: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.whether_retain`) + '!', trigger: "blur,change" },
        ],
        retainDeadline: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.retain_deadline`) + '!', trigger: "blur,change" },
        ],
        whetherCustomer: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`) + '!', trigger: "blur,change" },
        ],
        pass: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur" },
        ],
        docClass: [
          { required: true, message: this.$t(`doc.this_dept_preparer`), trigger: "blur" },
        ],
        docName: [
          {
            required: true,
            message: this.$t(`doc.this_dept_file_name_not_null`),
            trigger: "blur,change",
          },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_file_name_more_long`),
          }
        ],
        versionValue: [
          { required: true, message: this.$t(`doc.this_dept_insert_ver`), trigger: "blur" },
        ],
        yNTrain: [
          { required: true, message: this.$t(`doc.this_dept_select_is_train`), trigger: "blur,change" },
        ],
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_reason_more_long`),
          },
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_content_more_long`),
          },
        ],
        internalDocId: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`) + this.$t(`doc.this_dept_internal_file_number`),
            trigger: "blur,change",
          }
        ]

      },
      kuozhanshuju: {},
      kuozhanshujuBool: {},
      field117Action: "",
      action: "/dms-admin/process/file/local_upload",
      trains: [],
      cover: [],
      summary: "",
      pListData: {},
      editStatus: false,
      trainStatus: false,
      customerStatus: false,
      transferStatus: false,
      projectList: [],
      project: { id: '', name: '' },
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading: false,
      userinfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      isShowPart: false,
      isCustomerShow: false,
      isDeviceShow: false,
      batch: undefined,
      docClassData: {},
    };
  },
  computed: {
    batchStatus () {
      return !!this.formData.batchId
    }
  },
  watch: {
    "formData.docClass" (val) {
      let _this = this
      if (val) {
        _this.getNodeDetailInfo()
        if (!_this.procInstId) {
          _this.getByUpDocClassAndBizType(val)
        }
      }
    },
    "formData.dataType" (val) {
      let _this = this
      _this.isProject = val === 'project'
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  async created () {
    let response = await this.getConfigKey("record.doc.type")
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
    let response1 = await this.getConfigKey("back_flow_to_one")
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true';
  },
  mounted () {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    // 根据code获取validate值
    getValidateByCode(code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        try {
          const funCondition = JSON.parse(nodeDetail.funCondition)
          return funCondition.validate
        } catch (error) {
          console.error('解析funCondition失败:', error)
          return undefined
        }
      } else {
        return undefined
      }
    },
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    showVerify(invokeFrom) {
      this.currentParams = invokeFrom
      // 判断是否需要身份验证
      if (this.nodeShow('authentication')) {
        this.verifyVisible = true
      } else {
        // 不需要验证时直接执行业务逻辑
        this.handleBusinessExecute(this.currentParams)
      }
    },

    // 执行业务逻辑
    handleBusinessExecute(params) {
      switch(params.type) {
        case 'flowSubmit':
          this.handleWorkflowSubmit(params)
          break
        case 'transfer':
          this.transferForm(params)
          break
        case 'backFlow':
          this.handleBackFlowToOne(params)
          break
        case 'publish':
          this.handlePublish(params)
          break
        case 'turnDown':
          this.handelpbohuiqicaoren(params)
          break
      }
    },
    dictLanguage,
    checkPermi,
    init (row) {
      let _this = this
      _this.rest()
      _this.loading = true
      _this.order = row.order ? row.order : 0
      _this.batch = row.batch
      _this.mark = row.mark
      _this.getDeptList()
      //是否编辑模式
      this.$nextTick(() => {
        if (row && (!!row.procInstId || !isNaN(row.dataListIndex))) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          if (row.batchId) {
            _this.formData = row
            _this.dataListIndex = row.dataListIndex
            if (_this.formData.classType !== _this.classTypeDoc && _this.formData.classType !== _this.classTypeNote) {
              _this.activeIndex = '3'
            }
            _this.settingDocClassId(_this.formData.docClass);
            _this.getByDocClass(_this.formData.docClass);
            if (row.id) {
              _this.getModifyApplyTrain('cover', 'cover')
              _this.getModifyApplyTrain('trains', 'train')
            }
          } else {
            _this.getDetail(_this.procInstId)
          }
        } else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.getStandardDetail(row)
          // this.getWorkflowprocesskey();
        }
      });
    },
    getStandardDetail (query) {
      let _this = this
      _this.detailLoading = true
      standardGetDetail(query).then(async (res) => {
        let formData = res.data;
        //关联文件
        let res1 = await linkLoglistlink({ linkType: "REF_DOC", versionId: formData.versionId })
        formData.docLinks = res1.data
        //关联记录
        let res2 = await linkLoglistlink({ linkType: "RECORD", versionId: formData.versionId })
        formData.recordLinks = res2.data
        //关联记录
        let res3 = await linkLoglistlink({ linkType: "NOTE", versionId: formData.versionId, status: '1' })
        formData.noteLinks = res3.data

        let res4 = await linkLoglistlink({ linkType: "NOTE_DOC", versionId: formData.versionId, status: '1' })
        formData.noteDocLinks = res4.data

        formData.id = undefined
        formData.changeType = _this.pButton;
        formData.presetUserList = []
        if (formData.classType !== _this.classTypeDoc && formData.classType !== _this.classTypeNote) {
          _this.activeIndex = '3'
        }
        formData.docStatus = formData.status
        formData.whetherCustomer = undefined
        formData.whetherRetain = _this.no
        formData.filePush.pushFile=formData.ext8==='Y'

        _this.formData = formData
        if (!_this.formData.userName) {
          this.$set(this.formData, 'nickName', _this.userinfo.nickName)
          this.$set(this.formData, 'userName', _this.userInfo.userName)
        }
        _this.settingDocClassId(_this.formData.docClass);

        //查询物料是否展示
        this.getByDocClass(formData.docClass);
      }).finally(() => {
        _this.detailLoading = false
      });
    },
    async settingDocClassId (val) {
      let _this = this
      await settingDocClassId(val).then((response) => {
        _this.docClassData = response.data
        if (response.data && response.data.fileList != null) {
          _this.mobanwenjian = response.data.fileList;
        } else {
          _this.mobanwenjian = []
        }
      });
    },
    getDetail (procInstId) {
      let _this = this
      _this.detailLoading = true
      getInfoByBpmnId(procInstId).then(async (res) => {
        let formData = res.data;
        //console.log("查询文件变更操作申请详细", res);
        //关联文件
        let res1 = await linkLoglistlink({ linkType: "REF_DOC", versionId: formData.versionId })
        formData.docLinks = res1.data
        //关联记录
        // let res2 = await linkLoglistlink({ linkType: "RECORD", versionId: formData.versionId })
        // formData.recordLinks = res2.data
        let res2 = await modifyApplyLinklist({ applyId: formData.id, linkType: "RECORD" })
        formData.recordLinks = res2.rows;
        let res3 = await listPresetUser({ bizId: formData.id })
        formData.presetUserList = res3.data
        let res4 = await modifyApplyLinklist({ applyId: formData.id, linkType: "NOTE" })
        formData.noteLinks = res4.rows;

        let res5 = await modifyApplyLinklist({ applyId: formData.id, linkType: "NOTE_DOC" })
        formData.noteDocLinks = res5.rows;

        if (formData.classType !== _this.classTypeDoc) {
          _this.activeIndex = '3'
        }

        if (formData.docStatus !== '1') {
          formData.whetherRetain = _this.no
        }
        formData.filePush.pushFile=formData.ext8==='Y'

        _this.formData = formData
        _this.getModifyApplyTrain('cover', 'cover')
        _this.getModifyApplyTrain('trains', 'train')
        _this.getSettingDocClassTreeseList();
        if (_this.formData.docClass) {
          _this.settingDocClassId(_this.formData.docClass);
        }
        //查询物料是否展示
        this.getByDocClass(formData.docClass);
      }).finally(() => {
        _this.detailLoading = false
      });
    },
    getModifyApplyTrain (source, type) {
      let _this = this
      let query = {
        applyId: _this.formData.id,
        type: type,
        isDeleted: 0
      }
      let list = []
      queryModifyApplyTrain(query).then(res => {
        if (res.data) {
          res.data.forEach(item => {
            list.push({
              url: item.fileIds,
              name: item.files[0].fileName
            })
          })
        }
        _this.$set(_this, source, list)
      })
    },
    rest () {
      let _this = this
      _this.activeName = "info"
    },
    procInstInfoAndStatus (procInstId) {
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
        } else {
          _this.pListData = { procInstId: procInstId }
        }
        _this.getExtAttributeModel()
      });
    },
    handelConfirm (list, type) {
      let trains = []
      list.forEach(item => {
        trains.push({
          fileIds: item.url,
          fileName: item.name,
          userName: this.userInfo.userName,
          deptId: this.userInfo.deptId,
          docId: this.formData.docId,
          applyId: this.formData.id,
          type: type
        })
      })
      let data = {
        applyId: this.formData.id,
        type: type,
        trains: trains
      }
      updateModifyApplyTrainList(data);
    },
    nodeShow (code) {
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      } else {
        return false
      }
    },
    nodeFunCondition (code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      } else {
        return undefined
      }
    },
    // 获取文件类型的流程配置
    async getByUpDocClassAndBizType (docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass, _this.pButton)
      _this.procDefKey = data && data.flowKey ? data.flowKey : "";
      _this.getWorkflowprocesskey()
    },
    getWorkflowprocesskey () {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      } else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel () {
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId && curActDefId) {
        _this.getNodeDetailInfo()
         getExtAttributeModel(
           procDefId,
           curActDefId
         ).then((res) => {
           console.log("扩展属性====>", res);
           let kuozhanshujuBool = {}
           let kuozhanshuju = {}
           res.data.forEach(item=>{
             if (item.objType==='Boolean') {
               kuozhanshujuBool[item.objKey] = item.objValue
             } else {
               kuozhanshuju[item.objKey] = item.objValue
             }
           })
           _this.kuozhanshujuBool = kuozhanshujuBool;
           _this.kuozhanshuju = kuozhanshuju;
           this.$refs.dynamic.setKuozhanshuju(res.data.length>0?_this.kuozhanshuju:{})
         }).finally(()=>{
         _this.loading = false
         });
      } else {
        _this.kuozhanshujuBool = {}
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModelBool (val) {
      if (this.kuozhanshujuBool && this.kuozhanshujuBool !== {}) {
        let obj = this.kuozhanshujuBool[val]
        return !!obj && obj === 'true'
      } else {
        return false
      }
    },
    attributeModel (val) {
      return this.kuozhanshuju[val]
    },
    getNodeDetailInfo () {
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData && curActDefId && _this.formData.docClass) {
        getInfo(_this.formData.docClass, _this.pButton, curActDefId).then(res => {
          let nodeDetail = {}
          res.data.forEach(item => {
            nodeDetail[item.code] = true
          })
          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    async initStatus () {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.$t(`file_handle.change_auditing`)
      if (!_this.batchStatus) {
        if (_this.nodeShow('whether_train')) {
          let funCondition = _this.nodeFunCondition('whether_train')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.yNTrain) {
              _this.formData.yNTrain = funCondition.limitValue
            }
            _this.trainStatus = funCondition.validate
          }
        }
        if (_this.nodeShow('whether_customer_record')) {
          let funCondition = _this.nodeFunCondition('whether_customer_record')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.whetherCustomer) {
              _this.formData.whetherCustomer = funCondition.limitValue
            }
            _this.customerStatus = funCondition.validate
          }
        }
        _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
      }
    },
    closeAS () {
      this.viewShow = false;
    },
    close () {
      this.$emit("close")
    },
    handlePreview (id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    //不需要验证必填的保存
    async saveForm () {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      if (_this.editStatus) {
        formData.recordStatus = "draft";
        if (_this.batchStatus) {
          formData.notUpdateTitle = true
        }
      } else {
        formData.onlyEdit = true
      }
      //变更操作（新增、修订、作废）的提交和保存的类型区分统一使用recordStatus字段（draft：草稿, doing:进行中，done:已完成，deleted:作废）
      formData.docLinks = [];
      if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
        _this.$refs.linkFile.dataList.forEach((element) => {
          formData.docLinks.push({
            // linkId: element.linkId,
            fileId: element.fileId,
            docId: element.docId,
            versionId: element.versionId,
            versionValue: element.versionValue,
            docName: element.docName,
            docClass: element.docClass,
            status: 2,
          });
        });
      }
      formData.noteLinks = [];
      if (_this.$refs.linkNote && _this.$refs.linkNote.dataList) {
        let noteLinks = _this.$refs.linkNote.dataList;
        noteLinks.forEach(item => {
          item.status = 2
        })
        formData.noteLinks = noteLinks;
      }
      formData.noteDocLinks = [];
      if (_this.$refs.linkDoc && _this.$refs.linkDoc.dataList) {
        let noteDocLinks = _this.$refs.linkDoc.dataList;
        noteDocLinks.forEach(item => {
          item.status = 2
        })
        formData.noteDocLinks = noteDocLinks;
      }
      if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
        formData.recordLinks = _this.$refs.linkRecord.dataList;
      }
      if (formData.id) {
        let res = await updateModifyApply(formData)
        if (res.code === 200) {
          this.$modal.msgSuccess(_this.$t(`file_handle.change_save_succ`));
          _this.loading = false
          if (_this.batchStatus) {
            this.$emit("close", formData, _this.dataListIndex)
          }
        }
      } else {
        if (_this.batchStatus) {
          _this.loading = false
          this.$emit("close", formData, _this.dataListIndex)
          return
        }
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.docName,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          order: _this.pListData.actDefOrder,
          review: false,
          type: _this.applyType
        };
        formData.editStatus = _this.editStatus
        addModifyApply(formData).then((res) => {
          if (res.code === 200) {
            _this.$modal.msgSuccess(_this.$t(`file_handle.change_succ`));
            _this.formData.id = res.data.businessKey;
            _this.procInstId = res.data.procInstId
            _this.procInstInfoAndStatus(res.data.procInstId)
            _this.loading = false
          }
        });
      }
    },
    handleBackFlowToOne () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`);
          }
        },
      }).then(({ value }) => {
        _this.loading = true
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.docName.trim(),
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName,
              },
              bizType: _this.pButton,
              review: true,
              applyStatus: false,
              status: 'draft',
              type: _this.applyType,
              mark: _this.mark,
              order: 0,
            };
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break;
            }
          }
          _this.close(true);
        })
      })
    },
    deleteForm () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`);
          }
        },
      }).then(({ value }) => {
        _this.loading = true
        let formData = {
          id: _this.formData.id,
          bpmClientInputModel: {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_curComment: value,
            },
            order: _this.order,
            type: _this.applyType,
            mark: _this.mark,
            review: true,
            applyStatus: false,
          },
          recordStatus: 'cancel',
          editStatus: false
        }
        addModifyApply(formData).then((res) => {
          if (res.code === 200) {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`));
            this.close(true);
          }
        });
      })
    },
    transferForm () {
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData, _this.formData.id, _this.applyType, _this.order, _this.pButton)
    },
    // 提交
    async submitForm () {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      //审核
      if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
        if (_this.formSubmit.pass === undefined) {
          _this.$modal.msgError(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`));
          return;
        }
        // 验证是否填写了审核意见
        if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel + _this.$t(`doc.this_dept_comments`));
          return;
        }
      }
      //培训记录
      if (_this.nodeShow('page_oper_add_train_record')) {
        let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.trains && _this.trains.length > 0)) {
            _this.$modal.msgError(_this.$t(`doc.this_dept_pls_upload_train_file`));
            return true;
          }
        }
      }
      //客户封面
      if (_this.nodeShow('page_oper_add_customer_cover') && _this.formData.whetherCustomer === _this.yes && _this.formSubmit.pass !== false) {
        let funCondition = _this.nodeFunCondition('page_oper_add_customer_cover')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.cover && _this.cover.length > 0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`) + _this.$t(`doc.customer_cover`) + '！');
            return true;
          }
        }
      }
      //客户记录
      if (_this.nodeShow('add_customer_record') && _this.formData.whetherCustomer === _this.yes && _this.formSubmit.pass !== false) {
        let funCondition = _this.nodeFunCondition('add_customer_record')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.$refs.customerBox && _this.$refs.customerBox.dataList && _this.$refs.customerBox.dataList.length > 0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`) + _this.$t(`doc.customer_record`) + '！');
            return true;
          }
        }
      }
      let dialogVisible = true
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs["elFormCustomer"]) {
        let valid = await _this.$refs["elFormCustomer"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs["validateForm"]) {
        let validateValid = await _this.$refs["validateForm"].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      let {valid, data} = await this.$refs.filePushConfig.validateAndGetData();
      //校验
      if (await _this.validate() || !valid) {
        return
      }
      _this.formData.filePush=data

      this.loading = true;
      this.formData.docLinks = [];
      if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
        let docLinks = _this.$refs.linkFile.dataList;
        docLinks.forEach(item => {
          item.status = 2
        })
        _this.formData.docLinks = docLinks
      }
      _this.formData.noteLinks = [];
      if (_this.$refs.linkNote && _this.$refs.linkNote.dataList) {
        let noteLinks = _this.$refs.linkNote.dataList;
        noteLinks.forEach(item => {
          item.status = 2
        })
        _this.formData.noteLinks = noteLinks
      }

      _this.formData.noteDocLinks = [];
      if (_this.$refs.linkDoc && _this.$refs.linkDoc.dataList) {
        let noteDocLinks = _this.$refs.linkDoc.dataList;
        noteDocLinks.forEach(item => {
          item.status = 2
        })
        _this.formData.noteDocLinks = noteDocLinks
      }
      if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
        this.formData.recordLinks = this.$refs.linkRecord.dataList;
      }
      _this.jointReviewRedirect()
      await _this.setPresetUserList()
      this.dialogVisible = true;
      this.loading = false;
    },
    async setPresetUserList () {
      let _this = this
      if (_this.nodeShow('preset_countersign')) {
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition.groupId) {
          let users = [];
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item => {
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept,
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 下个环节预选直属部门领导
      if (_this.nodeShow('next_set_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getLeader(_this.userInfo.userName, _this.formData.deptId)
            user = res.data
          } else {
            let res = await getLeader(_this.userInfo.userName, _this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      if (_this.nodeShow('next_set_division_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_division_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getDivisionLeader(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDivisionLeader(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      //设置流程待选任意为文控
      if (_this.nodeShow('set_flow_select_list')) {
        let funCondition = _this.nodeFunCondition('set_flow_select_list')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined;
          if (funCondition.validate) {
            let res = await getDocManagersByDeptId(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDocManagersByDeptId(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = []
            user.forEach(item => {
              users.push({
                userName: item.userName,
                nickName: item.nickName,
                deptId: item.deptId,
                deptName: item.dept.deptName
              })
            })
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1);
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (this.formData.presetUserList.length > 0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if (_this.nodeShow('cdxmd') && _this.batch) {
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition && funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({
            batch: _this.batch,
            nextDefId: _this.pListData.curActDefId,
            havaDetail: true
          })
          let nodeCode = ""
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({
              userName: item.sender,
              nickName: item.nickName,
              deptId: item.senderDeptId,
              deptName: item.deptName
            })
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode === nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.isTrain = _this.formData.yNTrain
      _this.searchQuery.isCustomer = _this.formData.whetherCustomer
      _this.searchQuery.ext5 = _this.formData.ext5
      _this.searchQuery.pass = _this.formSubmit.pass
      _this.searchQuery.batch = !!_this.batch
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')) {
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition && funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围  过滤出没有预选人员的环节
          hideNodeCode = funCondition.nodeCode.filter(item => !defaultStaff.some(dpu => item === dpu.nodeCode && dpu.users && JSON.parse(dpu.users).length > 0))
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode && hideNodeCode.length === length) {
            hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length - hideNodeCode.length) <= limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length === 0) {
            defaultStaff.forEach(item => {
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
          }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect () {
      let _this = this
      if (_this.nodeShow('hscdx') || _this.nodeShow('wgcdx')) {
        getRecordbyPorcInstId(_this.procInstId).then(res => {
          if (res.data.length === 1) {
            let query = {
              docClass: _this.formData.docClass,
              bizType: _this.pButton,
              code: "cdxmd",
              batch: _this.batch,
            }
            getRedirectDefId(query).then(res1 => {
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length === 1) {
                  let next = res1.data.find(item => item.nextDefId === funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          } else {
            _this.isLast = false
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect (val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if (val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validate () {
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')) {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(_this.formData.docClass, _this.pButton, _this.formData.presetUserList, nodeCode)
        if (bool) {
          return true;
        }
      }
      if (!_this.formData.id) {
        let res1 = await selectStatusByDocId({ versionId: _this.formData.versionId, notInDraft: false })
        if (res1.data != 0) {
          _this.$modal.msgWarning(res1.msg);
          validate = true
        }
      }
      return false;
    },
    // 签章生效
    handleSignEffective () {
      let self = this
      if (self.formData.docId == null) {
        self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`));
        return false;
      }
      if (self.formData.isSignature == 'E' || self.formData.isSignature == 'Y') {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      signEffective(self.formData.id, 'effective').then((res) => {
        if (res.code === 200) {
          self.$modal.alert(self.$t(`file_handle.change_signature_text3`));
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'E'
        } else {
          self.$modal.alert(res.msg);
        }
        self.loading = false
      });
    },
    // 执行发布（推送文件到文件台账）
    handlePublish () {
      let self = this
      if (self.nodeShow('recovery_confirm') && self.formData.recoveryConfirm !== self.yes) {
        self.$modal.alert(self.$t(`doc.recovery_confirm_validate`));
        return false;
      }
      /*
      if(self.formData.docId == null) {
        self.$modal.alert("温馨提示：请先执行【生成编号】，然后执行【签章生效】，最后再【执行发布】。");
        return false;
      } else if(self.formData.isSignature == 'N') {
        self.$modal.alert("温馨提示：请先执行【签章生效】，最后再【执行发布】。");
        return false;
      }
      */
      self.$modal.confirm(self.$t(`file_handle.change_signature_text4`) + self.formData.docName + '》，' + self.$t(`file_handle.change_signature_text5`)).then(function () {
        // 执行发布的签章
        self.loading = true
        signEffective(self.formData.id, 'publish').then((res) => {
          if (res.code === 200) {
            self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text6`));
            self.formData.encryptFileId = res.data
            self.formData.isSignature = 'Y'
            self.handleWorkflowSubmit('publish');
          } else {
            self.$modal.alert(res.msg);
          }
          self.loading = false
        });
      }).then(() => {
        // 取消操作
      }).catch(() => { });
    },
    //提交流程
    handleWorkflowSubmit (invokeFrom) {
      let _this = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      let nextData = undefined
      if (typeof (invokeFrom) == 'object') {
        nextData = _this.$refs.prochild.handleWorkflowSubmit();
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        nextData = {
          wf_nextActDefId: 'end',
          wf_nextActDefName: '结束',
          direction: true,
          wf_receivers: [],
          fields: {},
        }
      }
      if (!nextData) {
        return;
      }
      // 显示加载中
      _this.detailLoading = true
      _this.flowStepLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.docName,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: nextData.wf_nextActDefName,
            fields: nextData.fields
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction,
        };
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {
          model: {
            wf_procTitle: _this.formData.docName,
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: nextData.wf_nextActDefName,
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction,
        };
      }
      if (nextData.wf_nextActDefId === 'end') {
        //办结
        formData.recordStatus = 'done'
        formData.bpmClientInputModel.jointReview = false
      } else {
        //进行中
        formData.recordStatus = 'doing'
        formData.bpmClientInputModel.jointReview = nextData.multi
      }
      if (_this.nodeShow('hscdx') || _this.nodeShow('wgcdx')) {
        formData.bpmClientInputModel.batch = _this.batch
        formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
        formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
        if (_this.redirectOrder) {
          formData.bpmClientInputModel.order = _this.redirectOrder
        }
        formData.bpmClientInputModel.isLast = _this.isLast
      }
      formData.editStatus = _this.editStatus
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader') || _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
      formData.customerEdit = _this.nodeShow('whether_customer_record')
      if(_this.nodeShow('file_range_preview_edit') || _this.nodeShow('distribution_edit')){
        formData.editStatus = true
        formData.distributeList = _this.$refs.distributeBox.getDistributeList()
      }
      addModifyApply(formData).then((res) => {
        if (res.code === 200) {
          //  _this.$modal.msgSuccess("提交成功");
          _this.$message({
            message: this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.detailLoading = false
              _this.flowStepLoading = false
              _this.dialogVisible = false;
              _this.close();
            }
          });
        }
      });
    },
    resetForm () {
      this.$refs["elForm"].resetFields();
    },
    getSettingDocClassTreeseList () {
      let query = { classStatus: "1", dataType: this.formData.dataType, openPurview: true }
      if (this.formData.classType === this.classTypeForeign) {
        query.neClassType = undefined
        query.classType = this.classTypeForeign
      } else {
        query.neClassType = this.classTypeForeign
        query.classType = undefined
      }
      settingDocClassList(query).then(
        (res) => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          this.classLevelOptions = this.handleTree(res.rows, "id", "parentClassId");
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer (node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizerDept (node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal (data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    handleMonitor () {
      this.monitorDrawerVisible = true;

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId);
      });
    },
    handelpbohuiqicaoren () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return this.$t(`file_handle.change_fill_reject_text`);
          }
        },
      })
        .then(async ({ value }) => {
          _this.loading = true
          // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
          let backarr = {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_businessKey: _this.formData.id,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_procTitle: _this.formData.docName,
              wf_procDefId: _this.pListData.procDefId,
              wf_curComment: value,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_receivers: [
                {
                  receiveUserId: _this.formData.userName,
                  receiveUserOrgId: _this.formData.deptId
                }
              ],
            },
            review: true,
            applyStatus: false,
            type: _this.applyType,
            step: _this.attributeModel("step"),
            bizType: _this.pButton
          };
          workflowbacktostart(backarr).then((response) => {
            this.$modal.msgSuccess("file_handle.change_reject_succ");
            _this.close();
          }).finally(() => {
            _this.loading = false
          });
        })
    },
    shengchengbianhao () {
      let _this = this
      _this.jiluliushuihao = [];
      getDocNoByApplyId(_this.formData.id).then((res) => {
        _this.formData.docId = res.data.docId;
        _this.docIdData = res.data;
        if (_this.formData.recordLinks != null) {
          var linids = [];
          _this.formData.recordLinks.forEach((element, i) => {
            linids.push(element.fileId);
          });
          if (linids != "") {
            getRecordDocNoByLinkId({
              fileIds: linids.join(","),
              applyId: _this.formData.id,
            }).then((res) => {
              _this.jiluliData = res.data;
              res.data.forEach((element, i) => {
                _this.formData.recordLinks.forEach((val, ix) => {
                  if (val.linkId == element.linkId) {
                    _this.formData.recordLinks[ix].docId = element.docId;
                  }
                });
                _this.jiluliushuihao.push(element);
              });
            });
          }
        }
      });

      _this.shenchenbianhao = true;
    },
    onProjectChange (val) {
      let _this = this
      _this.formData.projectId = val.id
      _this.formData.projectName = val.name
    },
    handleCloseChange () {
      this.dealDrawerShow = false
    },
    handleDeal (formData) {
      let _this = this
      _this.dealDrawerShow = true;
      _this.$nextTick(() => {
        console.log(_this.formData)
        _this.$refs.dealDrawer.init({ type: formData.invokeType, preChangeCode: formData.invokeId });
      });
    },
    selectPresetUser () {
      let _this = this
      _this.$nextTick(() => {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null, null, _this.formData.docClass, _this.pButton, _this.formData.presetUserList, nodeCode)
      })
    },
    selectHandlePresetUser (source, index, data) {
      let _this = this
      _this.$set(_this.formData, "presetUserList", data)
    },
    getByDocClass (docClass) {
      let _this = this
      _this.loading = true
      getFormRuleRecursive(docClass).then(res => {
        this.formRuleList = []
        if (res.data && res.data.ruleDetails) {
          let data = JSON.parse(res.data.ruleDetails)
          _this.formRuleList = data
          _this.loading = false
        }
      }).finally(err => {
        _this.loading = false
      })
    },
    initDefaultValue (value, defaultValue) {
      let _this = this
      if (defaultValue) {
        let key = defaultValue.split('@')
        if (key.length > 1) {
          _this.$set(_this.formData, value, _this.formData[key[1]].toString())
        } else {
          _this.$set(_this.formData, value, defaultValue)
        }
      }
    },
    handelRecoveryConfirm () {
      let _this = this
      updateById({ id: _this.formData.id, recoveryConfirm: _this.formData.recoveryConfirm })
    },
    //唯一序号、是否显示
    showStatistics (index, bool) {
      if (bool) {
        if (!this.quantity.includes(index)) {
          this.quantity.push(index)
        }
      }
      return bool
    },
    sortDataById (data) {
      // 按id排序函数
      function sortById (a, b) {
        return parseInt(a.weight) - parseInt(b.weight)
      }
      // 递归排序子级
      function sortChildren (node) {
        if (node.children && node.children.length > 0) {
          node.children.sort(sortById);
          node.children.forEach(child => {
            sortChildren(child);
          });
        }
      }
      // 初始排序
      data.sort(sortById);
      // 对每个节点递归排序子级
      data.forEach(node => {
        sortChildren(node);
      });
      return data;
    },
    getDeptList () {

      let self = this
      treeselect({ status: 0 }).then(response => {
        this.deptOptions2 = self.sortDataById(response.data)
      });
    },
    getDictLabel (dictValue, dictType) {
      const dictItem = this.dict.type[dictType].find(item => item.value === dictValue);
      return dictItem ? this.dictLanguage(dictItem) : dictValue;
    },
    //从消息待办跳转来的
    isShowSubmit () {
      let invokerFormMsg = this.$route.query.invokerForm
      //当_this.procDefKey为空，并且来源为我的消息
      if ((this.procDefKey == null || this.procDefKey == undefined || this.procDefKey == '') && invokerFormMsg != null && invokerFormMsg == 'systemMsg') {
        return false
      }
      return true
    }

  }
};
</script>
<style scoped>
.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}
</style>
