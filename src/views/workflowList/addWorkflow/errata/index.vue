<template>
  <div class="document_change_add"
       v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file`)+$t(`doc.this_dept_errata`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="formData&&formData.procInstId"
                   @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button type="primary"
                   @click="submitForm">{{ $t('doc.this_dept_annex') }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="formData&&formData.procInstId"
             v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)"
                   name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)"
                   name="log"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_edit_record`)"
                   name="edit"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body"
         v-if="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elForm"
                   :model="formData"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_file_type`)+`:`"
                              prop="docClass">
                  <span>{{docClassData.className}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" "></el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`)+`:`"
                              prop="upVersionId">
                  <span>{{formData.upDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)"
                              prop="parentDocId">
                  <span>{{formData.parentDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_file_name`)+`:`"
                              prop="docName">
                  <el-input v-if="editStatus"
                            v-model.trim="formData.docName"
                            :placeholder="$t(`doc.this_dept_insert_name`)"
                            clearable
                            :style="{ width: '100%' }"
                            maxlength="50"
                            show-word-limit
                            :disabled="disabled"></el-input>
                  <span v-else>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_file_id`)+`:`"
                              prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_file_ver`)+`:`"
                              prop="versionValue">
                  <el-input v-if="editStatus"
                            v-model.trim="formData.versionValue"
                            :placeholder="$t(`doc.this_dept_insert_ver`)"
                            clearable
                            :style="{ width: '100%' }"
                            maxlength="50"
                            show-word-limit></el-input>
                  <span v-else>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_preparation_dept`)+`:`"
                              prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_preparer`)+`:`"
                              prop="userName">
                  <span>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`"
                              prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <dynamic-form :formRuleList="formRuleList"
                          :formData="formData"
                          :editStatus="editStatus"
                          :deptOptions2="deptOptions"
                          :dict="dict" />

            <!----------------------------------根据文件分类，编制时新增字段 ------------------------------------------------>

            <!--            <el-row gutter="15" v-if="formData.classType===classTypeForeign">-->
            <!--              <el-col :span="24">-->
            <!--                <el-form-item :label="$t(`doc.this_dept_file_effective_date`)+`:`" prop="fileEffectiveDate">-->
            <!--                  <span>{{ parseTime(formData.fileEffectiveDate) }}</span>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->

            <!--              <el-col :span="24">-->
            <!--                <el-form-item :label="$t(`doc.this_dept_revise_date`)+`:`" prop="revisionDate">-->
            <!--                  <span>{{ parseTime(formData.revisionDate) }}</span>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->

            <!--            <el-row gutter="15">-->
            <!--              <el-col :span="24">-->
            <!--                <el-form-item :label="$t(`file_set.type_temp`)+`:`">-->
            <!--                  <div class="link-box bzlink-box">-->
            <!--                    <span-->
            <!--                      v-if="mobanwenjian != ''"-->
            <!--                      style="color: #385bb4; cursor: pointer"-->
            <!--                      @click="handlePreview(mobanwenjian[0].id)"-->
            <!--                    >{{ mobanwenjian[0].fileName }}</span-->
            <!--                    >-->
            <!--                    <span-->
            <!--                      v-if="mobanwenjian != ''"-->
            <!--                      style="color: #385bb4; cursor: pointer; margin-left: 10px"-->
            <!--                      @click="-->
            <!--                        handelefileLocalDownload(-->
            <!--                          mobanwenjian[0].id,-->
            <!--                          mobanwenjian[0].fileName-->
            <!--                        )-->
            <!--                      "-->
            <!--                    >{{ $t(`doc.this_dept_download`) }}</span-->
            <!--                    >-->
            <!--                  </div>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_ver`)+`:`">
                  <fileUpload :editStatus="editStatus"
                              v-model.trim="docFile"
                              :downStatus="true"
                              limit="1"
                              :fileType="['docx','doc','xls','xlsx','pdf','ppt','pptx']"
                              v-slot="{fileId,fileName,isOffice,protoFileId}"
                              :isShowTip="false">
                    <online-edit v-if="isOffice"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener">{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                  </fileUpload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_annexes_ver`)+`:`"
                              prop="">
                  <fileUpload :editStatus="editStatus"
                              v-model.trim="appendixFileList"
                              :downStatus="true"
                              :isfileType="false"
                              :limit="100"
                              v-slot="{fileId,fileName,isOffice,protoFileId}"
                              :isShowTip="false">
                    <online-edit v-if="isOffice"
                                 cbType="saveAppendix"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener">{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                  </fileUpload>
                  <!-- </div> -->
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_effective_date`)+`:`"
                              prop="startDate">
                  <span>{{parseTime(formData.startDate,"{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_release_date`)+`:`"
                              prop="releaseTime">
                  <span>{{parseTime(formData.releaseTime,"{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="status!=='1'">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_cancel_date`)+`:`"
                              prop="endDate">
                  <span>{{formData.endDate}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" ">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.classType===classTypeForeign">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`)+`:`"
                              prop="compliance">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model.trim="formData.compliance"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_compliancy`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`)+`:`"
                              prop="changeReason">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model.trim="formData.changeReason"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="500"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`)+`:`"
                              prop="content">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model.trim="formData.content"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_content`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="500"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`"
                              prop="remark">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.remark"
                            type="textarea"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`"
                              prop="remark">
                  <fileUpload v-model.trim="trains"
                              :editStatus="userInfo.userName===formData.userName&&status==='1'"
                              limit="100"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'train')" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <!-- 文件推送配置 -->
      <FilePushConfig
        v-if="settingDetail['ext8']"
        :doc-class-id="formData.docClass"
        :data-type="formData.dataType"
        :isReadOnly="!editStatus"
        :doc-id="formData.docId"
        :data="formData.filePush"
        :required="getValidateByCode('ext8')"
        ref="filePushConfig"
      />

      <div class="news-card"
           v-if="formData.whetherCustomer">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.customer_record`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormTrain"
                   :model="formData"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.whether_customer_records`)+`:`"
                              prop="whetherCustomer">
                  <dict-tag :options="dict.type.sys_yes_no"
                            :value="formData.whetherCustomer" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.whetherCustomer===yes">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_cover`)+`:`"
                              prop="customerCover">
                  <fileUpload v-model.trim="cover"
                              :editStatus="false"
                              limit="1"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts']"
                              :isShowTip="false" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_approval_record`)+`:`"
                              prop="customerRecord">
                  <customer-box ref="customerBox"
                                :versionId="formData.versionId"
                                :down-status="false"></customer-box>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <distribute-view-box ref="distributeViewBox"></distribute-view-box>

      <div class="el-card news-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs v-model.trim="activeIndex"
                   class="news-tabs">
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeNote&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_master_file`) }}</span>
              <link-doc :editStatus="false"
                        :status="false"
                        v-show="activeIndex  == '1'"
                        :dataList="formData.docLinks"
                        ref="linkDoc"
                        :dataType="formData.dataType"></link-doc>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeDoc&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note :editStatus="false"
                         :status="false"
                         v-show="activeIndex  == '1'"
                         :dataList="formData.noteLinks"
                         ref="linkNote"
                         :dataType="formData.dataType"></link-note>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-if="formData.classType===classTypeDoc&&!classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record ref="linkRecord"
                           :status="false"
                           :editStatus="false"
                           v-show="activeIndex == '1'"
                           :dataList="formData.recordLinks"
                           :dataType="formData.dataType"></link-record>
            </el-tab-pane>
            <el-tab-pane name="2"
                         v-if="formData.classType===classTypeDoc">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file :editStatus="false"
                         v-show="activeIndex == '2'"
                         ref="linkFile"
                         :dataList="formData.fileLinks"
                         :dataType="formData.dataType"></link-file>
            </el-tab-pane>
            <el-tab-pane name="3">
              <span slot="label">{{ $t(`doc.this_dept_file_history`) }}</span>
              <el-card class="gray-card">
                <historicalVersion :detatailsData="formData"
                                   @handleClick="handleDeal"
                                   v-show="activeIndex == '3'"></historicalVersion>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div v-if="activeName==='log'">
      <workflow-logs :procInstId="formData.procInstId"></workflow-logs>
    </div>
    <div v-show="activeName==='edit'">
      <EditRecord :version-id="formData.versionId"
                  :isVisible="activeName==='edit'"
                  :docFile="docFile"
                  :appendixes="appendixFileList"></EditRecord>
    </div>
    <monitor-drawer v-if="monitorDrawerVisible"
                    ref="monitorDrawer"></monitor-drawer>
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="closeAS"></as-pre-view>
    <DealDrawer v-if="dealDrawerShow"
                ref="dealDrawer"
                @closeDrawer="handleCloseChange"></DealDrawer>
    <!-- PDF文件预览组件 -->
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="close"></as-pre-view>
  </div>
</template>
<script>
import historicalVersion from "../add_import/historicalVersion";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {compareFileByFileId, linkLoglistlink, queryModifyApplyTrain} from '@/api/file_processing/modifiyApply'
import {settingDocClassId} from "@/api/file_settings/type_settings";
import {standardGetDetail} from "@/api/document_account/standard";
import {getFormRuleRecursive} from "@/api/setting/formRule"
// PDF本地文件预览
import {updateModifyApplyTrainList} from '@/api/my_business/modifyApplyTrain'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import DealDrawer from '@/components/DealDrawer/index.vue'
import {checkPermi} from '@/utils/permission'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import LinkDoc from '@views/workflowList/addWorkflow/add_import/linkDoc.vue'
import {treeselect} from '@/api/system/dept'
import CustomerBox from '@views/workflowList/addWorkflow/add_import/customerBox.vue'
import RelationPlanNo from '@/components/RelationPlanNo.vue'
import {updateVersion} from '@/api/document_account/version'
import {addErrataVersion} from '@/api/process/errataVersion'
import DistributeViewBox from '@views/workflowList/addWorkflow/add_import/distributeViewBox.vue'
import DynamicForm from '@/views/components/DynamicForm.vue'
import OnlineEdit from '@viewscomponents/onlineEdit/index.vue'
import EditRecord from '@views/workflowList/addWorkflow/add_import/editRecord.vue'
import {releaseStatus} from "@/api/system/fileEditingDetailLog";
import FilePushConfig from '@/components/FilePushConfig'

export default {
  components: {
    EditRecord,
    OnlineEdit,
    DistributeViewBox,
    RelationPlanNo,
    CustomerBox,
    LinkDoc,
    LinkNote,
    DealDrawer,
    LinkRecord,
    LinkFile,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs,
    DynamicForm,
    FilePushConfig
  },
  dicts: ['sys_yes_no', "file_status", "series_code", "face_option", "institutional", "file_type"],
  name: "Detail",
  props: ["dataType", 'data'],
  data () {
    return {
      settingDetail: {},
      docFile: [],
      appendixFileList: [],
      docClassData: {},
      yes: 'Y',
      no: 'N',
      classTypeRecordMN: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      dealDrawerShow: false,
      shlkPath: process.env.VUE_APP_SHLK_PATH,
      status: undefined,
      trains: [],
      cover: [],
      customerRecordList: [],
      pButton: undefined,
      activeName: "info",
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible: false,
      quantity: [],
      formRuleList: [],
      deptOptions: [],
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: { fileName: '' }, //编制文件
        fileLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        docLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        partRemark: null,
        factorys: null,
        customerCode: null,
        deviceCode: null,
        deviceName: null,
        internalDocId: undefined,
      },
      editStatus: true,
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      isShowPart: false,
      isCustomerShow: false,
      isDeviceShow: false,
      isShelfLifeShow: false,
      isShowProductVersion: false,
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  async created () {
    let response = await this.getConfigKey("record.doc.type")
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
  },
  mounted () {
    if (this.$route.query.docId &&
      this.$route.query.versionId
    ) {
      let param = {
        docId: this.$route.query.docId,
        versionId: this.$route.query.versionId
      }
      this.init(param)
    }
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    // 根据code获取validate值
    getValidateByCode(code) {
      let node = this.formRuleList.find(item => item.value === code);
      if (!node) {
        return false;
      }
      return node?.required === 'Y'
    },
    handleCompareFile (basicFileId1,basicFileId2) {
      let formData = {
        basicFileId1: basicFileId1,
        basicFileId2: basicFileId2,
        businessId: this.formData.id
      }
      this.detailLoading = true
      compareFileByFileId(formData).then((res) => {
        if (res.code === 200) {
          if (res.data != null) {
            this.compareHandlePreview(res.data)
          }
        }
      }).catch(() => {
        this.detailLoading = false
      });
    },
    compareHandlePreview (id) {
      this.viewId = id;
      this.$refs.viewRef.compareHandleOpenView(this.appendixFileList[0].url, id);
      this.viewShow = true;
      this.detailLoading = false
    },
    checkPermi,
    init (row) {
      let _this = this
      _this.rest()
      _this.status = row.status
      _this.getDetail(row)
      _this.getDeptList()
      _this.$nextTick(() => {
        _this.$refs.distributeViewBox.init(row.versionId)
      })
    },
    getDetail (query) {
      let _this = this
      _this.detailLoading = true
      standardGetDetail(query).then(async (res) => {
        let formData = res.data;
        _this.getDocClassById(formData.docClass)
        linkLoglistlink({ linkType: "DOC", versionId: formData.versionId }).then(res4 => {
          _this.docFile = res4.data.map(item => { return { name: item.fileName, url: item.fileId, protoFileId: item.protoFileId } })
        })
        linkLoglistlink({ linkType: "APPENDIX", versionId: formData.versionId }).then(res5 => {
          _this.appendixFileList = res5.data.map(item => { return { name: item.fileName, url: item.fileId, protoFileId: item.protoFileId } })
        })

        //关联文件
        let res1 = await linkLoglistlink({ linkType: "REF_DOC", versionId: formData.versionId })
        formData.fileLinks = res1.data
        if (_this.classTypeRecordMN) {
          //关联记录
          if (formData.classType === _this.classTypeDoc) {
            let res3 = await linkLoglistlink({ linkType: "NOTE", versionId: formData.versionId })
            if (_this.status === "1") {
              formData.noteLinks = res3.data.filter(item => item.status === 1)
            } else {
              formData.noteLinks = res3.data
            }
          }
          if (formData.classType === _this.classTypeNote) {
            let res3 = await linkLoglistlink({ linkType: "NOTE_DOC", versionId: formData.versionId })
            if (_this.status === "1") {
              formData.docLinks = res3.data.filter(item => item.status === 1)
            } else {
              formData.docLinks = res3.data
            }
          }
        } else {
          //关联记录
          let res2 = await linkLoglistlink({ linkType: "RECORD", versionId: formData.versionId })
          if (_this.status === "1") {
            formData.recordLinks = res2.data.filter(item => item.status === 1)
          } else {
            formData.recordLinks = res2.data
          }
        }
           // 确保 filePush 对象存在，如果为 null 则初始化
        if (!formData.filePush) {
          formData.filePush = {}
        }
        formData.filePush.pushFile = formData.ext8 === 'Y'


        formData.type = _this.formData.type
        formData.changeType = _this.pButton;
        if (formData.classType !== _this.classTypeDoc && formData.classType !== _this.classTypeNote) {
          _this.activeIndex = '3'
        }
        _this.formData = formData
        _this.getModifyApplyTrain('cover', 'cover')
        _this.getModifyApplyTrain('trains', 'train')
        this.getByDocClass(formData.docClass)
      }).finally(() => {
        _this.detailLoading = false
      });
    },
    getDocClassById (val) {
      let _this = this
      settingDocClassId(val).then((response) => {
        _this.docClassData = response.data
        if (response.data && response.data.fileList != null) {
          this.mobanwenjian = response.data.fileList;
        } else {
          this.mobanwenjian = []
        }
      });
    },
    getModifyApplyTrain (source, type) {
      let _this = this
      let query = {
        versionId: _this.formData.versionId,
        type: type,
        isDeleted: 0
      }
      let list = []
      queryModifyApplyTrain(query).then(res => {
        if (res.data) {
          res.data.forEach(item => {
            list.push({
              url: item.fileIds,
              name: item.files[0].fileName
            })
          })
        }
        _this.$set(_this, source, list)
      })
    },
    handleCloseChange () {
      this.dealDrawerShow = false
    },
    handleDeal (row) {
      this.dealDrawerShow = true;
      let url = this.shlkPath + '/#/workflow?type=' + row.invokeType + '&preChangeCode=' + row.invokeId
      this.$nextTick(() => {
        this.$refs.dealDrawer.init(url);
      });
    },
    rest () {
      let _this = this
      _this.activeName = "info"
    },
    closeAS () {
      this.viewShow = false;
    },
    close () {
      this.$emit("close")
    },
    handlePreview (id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    handelConfirm (list, type) {
      let trains = []
      list.forEach(item => {
        trains.push({
          fileIds: item.url,
          fileName: item.name,
          userName: this.userInfo.userName,
          deptId: this.userInfo.deptId,
          docId: this.formData.docId,
          versionId: this.formData.versionId,
          type: type
        })
      })
      let data = {
        versionId: this.formData.versionId,
        type: type,
        trains: trains
      }
      updateModifyApplyTrainList(data);
    },
    handleMonitor () {
      this.monitorDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.formData.procInstId);
      });
    },
    async submitForm () {
      let _this = this

      let {valid, data} = await this.$refs.filePushConfig.validateAndGetData();
      //校验
      if (!valid) {
        return
      }

      _this.$confirm(_this.$t(`file_handle.submit_text`), _this.$t(`file_handle.change_tip`), {
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        type: "warning",
      })
        .then(() => {
          _this.loading = true
          let formData = JSON.parse(JSON.stringify(_this.formData))
          formData.filePush = data
          let docFile = _this.restLink(formData, _this.docFile[0], 'DOC')
          formData.fileId = docFile.fileId
          formData.linkList = _this.appendixFileList.map(item => _this.restLink(formData, item, 'APPENDIX'))
          formData.linkList.push(docFile);
          addErrataVersion(formData).then(res => {
            _this.$message({
              message: _this.$t(`doc.this_dept_sub_succ`),//提示的信息
              type: 'success',　　//类型是成功
              duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            });
          }).finally(() => {
            _this.loading = false
          })
        })
    },
    restLink (formData, file, linkType) {
      console.log(file)
      return {
        fileId: file.url,
        fileName: file.name,
        linkType: linkType,
        linkCode: formData.docId,
        versionId: formData.versionId,
        versionValue: formData.versionValue,
        docClass: formData.docClass,
        status: formData.status,
        startDate: formData.startDate,
        releaseTime: formData.startDate,
        endDate: formData.startDate,
        protoFileId: file.protoFileId,
      }
    },
    getByDocClass (docClass) {
      let _this = this
      _this.loading = true
      getFormRuleRecursive(docClass).then(res => {
        let settingDetail = {}
        _this.formRuleList = []
        if (res.data && res.data.ruleDetails) {
          let data = JSON.parse(res.data.ruleDetails)
          data.forEach(item => {
            settingDetail[item.value] = item.show === 'Y'
          })
          _this.formRuleList = data

          _this.settingDetail = settingDetail
        }
      }).finally(err => {
        _this.loading = false
      })
    },
    getDeptList () {
      // deptLevel = 2 只显示组织层级2级以内的节点
      let self = this
      treeselect({ status: 0 }).then(response => {
        this.deptOptions = response.data
      });
    },
    //唯一序号、是否显示
    showStatistics (index, bool) {
      if (bool) {
        if (!this.quantity.includes(index)) {
          this.quantity.push(index)
        }
      }
      return bool
    },
    saveForm () {
      let _this = this
      _this.loading = true
      let formData = { id: this.formData.versionId, ext20: this.formData.ext20 }

      updateVersion(formData).then(res => {
        _this.$message({
          message: _this.$t(`file_handle.change_save_succ`),//提示的信息
          type: 'success',　　//类型是成功
          duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
        });
      }).finally(() => {
        _this.loading = false
      })
    },
    messageListener (event) {
      let _this = this
      console.log('收到子页面的消息:', event)
      if (event && event.data && event.data.text) {
        let data = JSON.parse(event.data.text)
        console.log('收到子页面的消息:', data)
        if (data.ntkoData) {
          let value = data.ntkoData.FunctionArgs
          if (data.ntkoData.parentExecutionFunction === 'savaFile' && value.length<=3) {
            console.log('保存主文件')
            _this.docFile = [
              {
                url: value[0],
                name: value[1],
                protoFileId: value[2]
              }
            ]
          } else if (data.ntkoData.parentExecutionFunction === 'saveAppendix' && value.length<=3) {
            console.log('保存附件')
            _this.appendixFileList.forEach(item => {
              if (item.protoFileId === value[2]) {
                item.url = value[0]
                item.name = value[1]
              }
            })
          }
          if(value.length>3){
            console.log("关闭")
            _this.closeEdit(value[1])
          }
        }
      }
    },
    closeEdit(data){
      releaseStatus(data).then((response) => {
        console.log(response)
      }).finally(() => {
        this.loading = false
      })
    }
  },

};
</script>
<style scoped>
.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}
</style>
