<template>
  <div class="document_change_add" v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file_loss`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="pListData&&pListData.procInstId" @click="handleMonitor">{{
            $t(`doc.this_dept_process_monitor`)
          }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus" @click="selectPresetUser">
          {{ $t(`file_handle.change_select_people`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_cancel')&&pListData&&pListData.procInstId&&workflowStatus" type="danger"
                   @click="deleteForm" v-dbClick
        >{{ $t(`file_handle.change_revoke`) }}
        </el-button>
        <el-button v-if="pListData&&pListData.procInstId&&workflowStatus" @click="showVerify({type:'transfer'})" v-dbClick>
          {{ transferStatus ? $t(`file_handle.transfer_return`) : $t(`file_handle.transfer`) }}
        </el-button>
        <el-button
          v-if="pListData&&pListData.procInstId&&!workflowStatus&&'doing'===formData.status&&formData.createBy===userInfo.userName&&backFlowToOneStatus"
          type="danger" @click="showVerify({type:'backFlow'})" v-dbClick
        >{{ $t(`file_handle.change_withdraw`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus" @click="showVerify({type:'turnDown'})"
                   type="danger"
        >{{ $t(`file_handle.change_reject_to_preparer`) }}
        </el-button>
        <el-button v-if="(editStatus||workflowStatus)&&isShowSubmit()" type="primary" @click="submitForm" v-dbClick>
          {{ $t(`doc.this_dept_annex`) }}
        </el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{
            $t(`doc.this_dept_save`)
          }}
        </el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="pListData&&pListData.procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_appli_info`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_title`)+`:`" prop="applyTitle">
                  <sapn>{{ formData.applyTitle }}</sapn>
                </el-form-item>
                <!--                <el-form-item :label="$t(`doc.this_dept_title`)+`:`" prop="applyTitle">
                                  <el-input
                                    v-if="editStatus"
                                    v-model="formData.applyTitle"
                                    :placeholder="$t(`doc.this_dept_insert_name`)"
                                    maxlength="50"
                                    clearable
                                    style="width: 80%; margin-right: 10px"
                                  >
                                  </el-input>
                                  <sapn v-else>{{formData.applyTitle}}</sapn>
                                </el-form-item>-->
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptId">
                  <sapn>{{ formData.deptName }}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_claimant`)+`:`" prop="userName">
                  <sapn>{{ formData.nickName }}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_date`)" prop="applyTime">
                  <sapn>{{ formData.applyTime }}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <sapn>{{ formData.docName }}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_code`)" prop="docId">
                  <sapn>{{ formData.docId }}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <sapn>{{ formData.versionValue }}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_type`)" prop="docClass">
                  <sapn>{{ formatterDocClass(null, null, formData.docClass, null) }}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <!--            <el-row>-->
            <!--              <el-col :span="24">-->
            <!--                <el-form-item label="是否外发:" prop="isPuttingOut">-->
            <!--                  <el-radio-group  v-if="editStatus" v-model.trim="formData.isPuttingOut">-->
            <!--                    <el-radio-->
            <!--                      v-for="(item, index) in dict.type.sys_yes_no"-->
            <!--                      :key="index"-->
            <!--                      :label="item.value"-->
            <!--                    >{{ item.label }}</el-radio>-->
            <!--                  </el-radio-group>-->
            <!--                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.isPuttingOut"/>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_appli_reason`)+`:`" prop="reason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model="formData.reason"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_loss_impact`)+`:`" prop="effect">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model.trim="formData.effect"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_post_precaution`)+`:`" prop="precaution">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model.trim="formData.precaution"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_loss_file_info`) }}</div>
          <div class="cell-btn">
            <button
              type="button"
              @click="handleSelectFile()"
              class="el-button blue el-button--default"
              v-if="editStatus"
            >
              <span>{{ $t(`doc.this_dept_select_file`) }}</span>
            </button>
          </div>
        </div>
        <div class="el-card gray-card is-always-shadow">
          <div class="el-card__body">
            <el-table :data="formData.itemList">
              <el-table-column :label="$t(`doc.this_dept_distribute_info`)" align="left" prop="code"
                               :formatter="formatterCode"
              ></el-table-column>
              <el-table-column :label="$t(`doc.this_dept_sign_dept`)" align="left" prop="receiveUserDept"
              ></el-table-column>
              <el-table-column :label="$t(`doc.this_dept_signatory`)" align="left" prop="receiveNickName"/>
              <el-table-column :label="$t(`doc.this_dept_sign_date`)" align="left" prop="receiveTime"/>
              <el-table-column :label="$t(`doc.this_dept_status`)" align="left" prop="status"
                               :formatter="formatterStatus"
              />
              <el-table-column
                :label="$t(`doc.this_dept_operation`)"
                align="left"
                class-name="small-padding fixed-width"
                v-if="editStatus"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(formData.itemList,scope.$index)"
                  >{{ $t(`doc.this_dept_delete`) }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div class="news-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus">
        <div class="card-head">
          <div class="cell-title">{{ submitLabel }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`file_handle.change_result`)+':'" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass" @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)+':'">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId="pListData.procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading=flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :selected="nodeShow('default_selected')"
        :userListStatus="!nodeShow('user_list')"
        :order="order"
        :searchQuery="searchQuery"
        :pListData="pListData"
        :hideNodeCode="hideNodeCode"
        :defaultStaff="defaultStaff"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="showVerify({type:'flowSubmit'})"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <el-drawer
      :visible.sync="drawerShow"
      direction="rtl"
      size="60%"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      modal-append-to-body
      append-to-body
      :destroy-on-close="true"
    >
      <distribute-list ref="distributeList" :title="$t(`doc.this_dept_select_loss_file`)" @close="handleCloseChange"
                       @submit="handleDistributeList"
      ></distribute-list>
    </el-drawer>
    <preset-user ref="presetUser" @selectHandle="selectHandlePresetUser"></preset-user>
    <transfer-flow ref="transferFlow" @close="close"></transfer-flow>
    <identity-verify
      :visible.sync="verifyVisible"
      :business-params="currentParams"
      @business-execute="handleBusinessExecute"
      @verify-cancel="handleCancel"
    />
  </div>
</template>
<script>
import { settingDocClassList } from '@/api/file_settings/type_settings'
import IdentityVerify from './add_import/identityVerify.vue';
import processcode from '@/views/workflowList/processcode/index.vue'
import WorkflowLogs from '@views/workflowList/workflowLogs/index.vue'
import {
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, workflowbacktostart, getRecordbyPorcInstId, getRedirectDefId, backFlowToOne
} from '@/api/my_business/workflow'
// PDF本地文件预览
import { getWorkflowApplyLog, selectApplySerial } from '@/api/my_business/workflowApplyLog'
import DistributeList from '@views/workflowList/addWorkflow/add_import/distributeList.vue'
import { addLostApply, getLostApplyByBpmnId, updateLostApply } from '@/api/process/lostApply'
import { listLostApplyItem } from '@/api/process/lostApplyItem'
import { getByUpDocClassAndBizType } from '@/api/setting/docClassFlow'
import { getInfo } from '@/api/setting/docClassFlowNodeDetail'
import { addReviewApply } from '@/api/document_account/reviewApply'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import { listPresetUser } from '@/api/setting/presetUser'
import { getDocManagersByDeptId, getLeader, getDivisionLeader } from '@/api/system/user'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import { listDistributeGroupDetail } from '@/api/setting/distributeGroupDetail'

export default {
  dicts: ['sys_yes_no'],
  components: {
    IdentityVerify,
    TransferFlow,
    PresetUser,
    DistributeList,
    processcode,
    WorkflowLogs
  },
  name: 'Additional_doc',
  props: ['data'],
  data() {
    return {
      currentType: '',
      currentParams: null,
      verifyVisible: false,
      applyType: 'lost_doc',
      backFlowToOneStatus: true,
      transferStatus: false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      isLast: true,
      hideNodeCode: [],
      defaultStaff: [],
      order: 0,
      pButton: 'lost',
      drawerShow: false,
      searchQuery: {},
      deptList: [],
      companyList: [],
      deptOptions: [],
      docClassList: [],
      submitLabel: undefined,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) }
      ],
      formSubmit: { summary: '', actionType: '', pass: undefined },
      isSummary: false,
      activeName: 'info',
      nodeDetail: [],
      procDefKey: undefined,
      viewId: '',
      userInfo: JSON.parse(sessionStorage.getItem('USER_INFO')),
      viewShow: false,
      monitorDrawerVisible: false,
      formData: {
        id: undefined,
        applyTitle: undefined,
        deptId: undefined,
        userName: undefined,
        deptName: undefined,
        nickName: undefined,
        versionId: undefined,
        docId: undefined,
        docName: undefined,
        versionValue: undefined,
        docClass: undefined,
        reason: undefined,
        status: undefined,
        applyTime: undefined,
        isPuttingOut: undefined,
        effect: undefined,
        precaution: undefined,
        itemList: [],
        presetUserList: []
      },
      rules: {
        effect: [
          { required: true, message: this.$t(`doc.this_dept_insert_loss_impact`), trigger: 'blur' }
        ],
        precaution: [
          { required: true, message: this.$t(`doc.this_dept_insert_post_precaution`), trigger: 'blur' }
        ],
        isPuttingOut: [
          { required: true, message: this.$t(`doc.this_dept_select_whether_outgoing`), trigger: 'blur' }
        ],
        /*applyTitle: [
          { required: true, message: this.$t(`doc.this_dept_insert_title`), trigger: "blur" },
        ],*/
        pass: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: 'blur' }
        ],
        reason: [
          { required: true, message: this.$t(`doc.this_dept_insert_appli_reason`), trigger: 'blur' }
        ]
      },
      kuozhanshuju: {},
      pListData: {},
      editStatus: false,
      workflowStatus: false,
      dialogVisible: false,
      loading: false,
      detailLoading: false,
      flowStepLoading: false,
      batch: undefined
    }
  },
  computed: {},
  watch: {
    data(val) {
      if (val) {
        this.init(val)
      }
    },
    'formData.itemList'(val) {
      let _this = this
      if (_this.editStatus) {
        _this.updateGenerateApplyTitle()
      }
    }
  },
  async created() {
    let response1 = await this.getConfigKey('back_flow_to_one')
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true'
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    showVerify(invokeFrom) {
      this.currentParams = invokeFrom
      // 判断是否需要身份验证
      if (this.nodeShow('authentication')) {
        this.verifyVisible = true
      } else {
        // 不需要验证时直接执行业务逻辑
        this.handleBusinessExecute(this.currentParams)
      }
    },

    // 执行业务逻辑
    handleBusinessExecute(params) {
      switch(params.type) {
        case 'flowSubmit':
          this.handleWorkflowSubmit(params)
          break
        case 'transfer':
          this.transferForm(params)
          break
        case 'backFlow':
          this.handleBackFlowToOne(params)
          break
        case 'publish':
          this.handlePublish(params)
          break
        case 'turnDown':
          this.handelpbohuiqicaoren(params)
          break
      }
    },
    handleExport2() {
      this.$refs.pdfView.init()
    },
    async init(row) {
      let _this = this
      _this.rest()
      _this.loading = true
      let { data } = await getByUpDocClassAndBizType(null, _this.pButton)
      _this.procDefKey = data && data.flowKey ? data.flowKey : ''
      if (row.preChangeCode) {
        let res = await getWorkflowApplyLog(row.preChangeCode)
        row.procInstId = res.data.procInstId
      }
      if (row.order) {
        _this.order = row.order
      }
      if (row.batch) {
        _this.batch = row.batch
      }
      //是否编辑模式
      _this.$nextTick(() => {
        if (row && row.procInstId) {
          let procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(procInstId)
          _this.getDetail(procInstId)
        } else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.formData.docClass = row.docClass
          _this.formData.docId = row.docId
          _this.formData.docName = row.docName
          _this.formData.versionValue = row.versionValue
          _this.formData.versionId = row.versionId
          _this.getSettingDocClassTreeseList()
          _this.getWorkflowprocesskey()
        }
      })
    },
    formatterCode(row, column, cellValue, index) {
      if (row.code < 10) {
        return '0' + row.code
      }
      return row.code
    },
    formatterStatus(row) {
      if (row.status === 'lost') {
        return this.$t(`doc.this_dept_loss`)
      } else if (row.status === 'recovery') {
        return this.$t(`file_handle.print_recovered`)
      } else {
        return this.$t(`file_handle.print_unrecover`)
      }
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      getLostApplyByBpmnId(procInstId).then(async(res) => {
        let formData = res.data
        _this.formData = formData
        _this.getSettingDocClassTreeseList()
        _this.getItemList(formData.id)
        _this.getPresetUserList()
      }).finally(() => {
        _this.detailLoading = false
      })
    },
    getPresetUserList() {
      let _this = this
      listPresetUser({ bizId: _this.formData.id }).then(res => {
        _this.formData.presetUserList = res.data
      })
    },
    getItemList(applyId) {
      listLostApplyItem({ applyId: applyId }).then(res => {
        this.$set(this.formData, 'itemList', res.data)
      })
    },
    rest() {
      let _this = this
      _this.activeName = 'info'
      _this.formData = {
        id: undefined,
        applyTitle: undefined,
        deptId: this.userInfo.deptId,
        userName: this.userInfo.userName,
        deptName: this.userInfo.dept.deptName,
        nickName: this.userInfo.nickName,
        versionId: undefined,
        docId: undefined,
        docName: undefined,
        versionValue: undefined,
        docClass: undefined,
        reason: undefined,
        status: undefined,
        applyTime: _this.parseTime(new Date()),
        isPuttingOut: undefined,
        effect: undefined,
        precaution: undefined,
        itemList: [],
        presetUserList: []
      }
    },
    procInstInfoAndStatus(procInstId) {
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.pListData = res
        } else {
          _this.pListData = { procInstId: procInstId }
        }
        _this.getExtAttributeModel()
      })
    },
    getWorkflowprocesskey() {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data
            this.getExtAttributeModel()
          })
        })
      } else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
      }
    },
    getExtAttributeModel() {
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId && curActDefId) {
        _this.getNodeDetailInfo()
        // getExtAttributeModel(
        //   procDefId,
        //   curActDefId
        // ).then((res) => {
        //   console.log("扩展属性====>", res);
        //   let kuozhanshuju = {}
        //   res.data.forEach(item=>{
        //     kuozhanshuju[item.objKey] = item.objValue
        //   })
        //   _this.kuozhanshuju = kuozhanshuju;
        // }).finally(()=>{
        _this.loading = false
        // });
      } else {
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    getNodeDetailInfo() {
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData && curActDefId) {
        getInfo(null, _this.pButton, curActDefId).then(res => {
          let nodeDetail = {}
          res.data.forEach(item => {
            nodeDetail[item.code] = true
          })
          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    async initStatus() {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.$t(`file_handle.change_auditing`)
      _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
    },
    nodeShow(code) {
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      } else {
        return false
      }
    },
    nodeFunCondition(code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      } else {
        return undefined
      }
    },
    attributeModel(val) {
      if (this.kuozhanshuju && this.kuozhanshuju !== {}) {
        let obj = this.kuozhanshuju[val]
        return obj ? obj === 'true' : false
      } else {
        return false
      }
    },

    close() {
      this.viewShow = false
      this.$emit('close')
    },
    //不需要验证必填的保存
    async saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
        return
      }
      if (!_this.formData.applyTitle) {
        await _this.generateApplyTitle()
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.recordStatus = 'draft'
      formData.editStatus = _this.editStatus
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader') || _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
      if (formData.id) {
        updateLostApply(formData).then((res) => {
          if (res.code === 200) {
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type: 'success',　　//类型是成功
              duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose: () => {
                _this.loading = false
              }
            })
          }
        })
      } else {
        let wf_receivers = []
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        })
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.applyTitle,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId
          },
          order: _this.pListData.actDefOrder,
          type: _this.applyType,
          review: false
        }
        formData.editStatus = _this.editStatus
        addLostApply(formData).then((res) => {
          if (res.code === 200) {
            _this.formData.id = res.data.businessKey
            _this.procInstInfoAndStatus(res.data.procInstId)
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type: 'success',　　//类型是成功
              duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose: () => {
                _this.loading = false
              }
            })
          }
        })
      }
    },
    transferForm() {
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData, _this.formData.id, _this.applyType, _this.order, _this.pButton)
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
        return
      }
      let dialogVisible = true
      //审核
      if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
        if (_this.formSubmit.pass === undefined) {
          _this.$modal.msgError(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
          return
        }
        // 验证是否填写了审核意见
        if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel + _this.$t(`doc.this_dept_comments`))
          return
        }
      }
      if (!!_this.$refs['elForm']) {
        let valid = await _this.$refs['elForm'].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs['validateForm']) {
        let validateValid = await _this.$refs['validateForm'].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      if (await _this.validate()) {
        return
      }
      _this.jointReviewRedirect()
      _this.loading = true
      await _this.setPresetUserList()
      _this.dialogVisible = true
      _this.loading = false
    },
    async setPresetUserList() {
      let _this = this
      if (_this.nodeShow('preset_countersign')) {
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition.groupId) {
          let users = []
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item => {
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 下个环节预选直属部门领导
      if (_this.nodeShow('next_set_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getLeader(_this.userInfo.userName, _this.formData.deptId)
            user = res.data
          } else {
            let res = await getLeader(_this.userInfo.userName, _this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      if (_this.nodeShow('next_set_division_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_division_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getDivisionLeader(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDivisionLeader(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      //设置流程待选任意为文控
      if (_this.nodeShow('set_flow_select_list')) {
        let funCondition = _this.nodeFunCondition('set_flow_select_list')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getDocManagersByDeptId(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDocManagersByDeptId(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = []
            user.forEach(item => {
              users.push({
                userName: item.userName,
                nickName: item.nickName,
                deptId: item.deptId,
                deptName: item.dept.deptName
              })
            })
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (_this.formData.presetUserList.length > 0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if (_this.nodeShow('cdxmd') && _this.batch) {
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition && funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({
            batch: _this.batch,
            nextDefId: _this.pListData.curActDefId,
            havaDetail: true
          })
          let nodeCode = ''
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({
              userName: item.sender,
              nickName: item.nickName,
              deptId: item.senderDeptId,
              deptName: item.deptName
            })
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode === nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.pass = _this.formSubmit.pass
      _this.searchQuery.batch = !!_this.batch
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')) {
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition && funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围  过滤出没有预选人员的环节
          hideNodeCode = funCondition.nodeCode.filter(item => !defaultStaff.some(dpu => item === dpu.nodeCode && dpu.users && JSON.parse(dpu.users).length > 0))
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode && hideNodeCode.length === length) {
            hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length - hideNodeCode.length) <= limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length === 0) {
            defaultStaff.forEach(item => {
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
          }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect() {
      let _this = this
      //会审重定向
      if (_this.nodeShow('hscdx')) {
        getRecordbyPorcInstId(_this.pListData.procInstId).then(res => {
          //待办只剩最后一个
          if (res.data.length === 1) {
            let query = {
              docClass: null,
              bizType: _this.pButton,
              code: 'cdxmd',
              batch: _this.batch
            }
            getRedirectDefId(query).then(res1 => {
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length === 1) {
                  let next = res1.data.find(item => item.nextDefId === funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          }else{
            _this.isLast = false
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if (val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validate() {
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      if (!_this.formData.itemList || _this.formData.itemList.length < 1) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_select_file`))
        return true
      }
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')) {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(null, _this.pButton, _this.formData.presetUserList, nodeCode)
        if (bool) {
          return true
        }
      }
      return false
    },

    //提交表单和流程数据
    async handleWorkflowSubmit(invokeFrom) {
      let _this = this

      let wf_receivers = []
      let wf_nextActDefId = null
      let wf_nextActDefName = null
      if (typeof (invokeFrom) == 'object') {
        if (_this.$refs.prochild.receiveUserList.length < 1 && _this.$refs.prochild.nextData.actDefType !== 'endEvent') {
          _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`))
          return
        }
        _this.$refs.prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
            receiveUserId: element.id,
            receiveUserOrgId: element.parentId
          })
        })
        wf_nextActDefId = _this.$refs.prochild.nextData.actDefId
        wf_nextActDefName = _this.$refs.prochild.nextData.actDefName
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
        wf_nextActDefName = '结束'
      }
      if (!_this.formData.applyTitle) {
        await _this.generateApplyTitle()
      }
      let formData = JSON.parse(JSON.stringify(_this.formData))

      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.applyTitle,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_nextActDefId: wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: wf_nextActDefName
          },
          order: _this.pListData.actDefOrder,
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun')
        }
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {
          type: _this.applyType,
          model: {
            wf_procTitle: _this.formData.applyTitle,
            wf_nextActDefId: wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefId: _this.pListData.actDefId,
            wf_curActDefName: _this.pListData.actDefName,
            wf_nextActDefName: wf_nextActDefName
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass
        }
      }
      if (_this.$refs.prochild.nextData.actDefType === 'endEvent') {
        //办结
        formData.recordStatus = 'done'
        formData.bpmClientInputModel.jointReview = false
      } else {
        //进行中
        formData.recordStatus = 'doing'
        formData.bpmClientInputModel.jointReview = _this.$refs.prochild.nextData.multi
      }
      if (_this.nodeShow('hscdx')) {
        formData.bpmClientInputModel.batch = _this.batch
        formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
        formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
        if (_this.redirectOrder) {
          formData.bpmClientInputModel.order = _this.redirectOrder
        }
        formData.bpmClientInputModel.isLast = _this.isLast
      }
      formData.editStatus = _this.editStatus
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader') || _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
      addLostApply(formData).then((res) => {
        if (res.code === 200) {
          _this.$message({
            message: this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false
              _this.close()
            }
          })
        }
      })
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: '1', neClassType: 'foreign' }).then(res => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          this.docClassTree = this.handleTree(res.rows, 'id', 'parentClassId')
        }
      )
    },
    handleMonitor() {
      this.monitorDrawerVisible = true
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId)
      })
    },
    handelpbohuiqicaoren() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_reject_text`)
          }
        }
      }).then(async({ value }) => {
        _this.loading = true
        // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
        let backarr = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_businessKey: _this.formData.id,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_procTitle: _this.formData.applyTitle,
            wf_procDefId: _this.pListData.procDefId,
            wf_curComment: value,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_receivers: [
              {
                receiveUserId: _this.formData.userName,
                receiveUserOrgId: _this.formData.deptId
              }
            ]
          },
          order: 0,
          review: true,
          applyStatus: false,
          type: _this.applyType,
          step: _this.order,
          bizType: _this.pButton
        }
        workflowbacktostart(backarr).then((response) => {
          this.$modal.msgSuccess(_this.$t(`file_handle.change_reject_succ`))
          _this.close()
        }).finally(() => {
          _this.loading = false
        })
      })
    },
    selectPresetUser() {
      let _this = this
      _this.$nextTick(() => {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null, null, null, _this.pButton, _this.formData.presetUserList, nodeCode)
      })
    },
    handleBackFlowToOne() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`)
          }
        }
      }).then(({ value }) => {
        _this.loading = true
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.applyTitle,
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName
              },
              bizType: _this.pButton,
              review: true,
              applyStatus: false,
              status: 'draft',
              type: _this.applyType,
              mark: _this.mark,
              order: 0
            }
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break
            }
          }
          _this.close(true)
        })
      })
    },
    deleteForm() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`)
          }
        }
      }).then(({ value }) => {
        _this.loading = true
        let formData = {
          id: _this.formData.id,
          bpmClientInputModel: {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_curComment: value
            },
            order: _this.order,
            type: _this.applyType,
            review: true,
            applyStatus: false
          },
          recordStatus: 'cancel',
          editStatus: false
        }
        addLostApply(formData).then((res) => {
          if (res.code === 200) {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`))
            this.close(true)
          }
        })
      })
    },
    handleSelectFile() {
      this.drawerShow = true
      this.$nextTick(() => {
        this.$refs.distributeList.init(this.formData.versionId, this.formData.itemList)
      })
    },
    handleCloseChange() {
      this.drawerShow = false
    },
    handleDistributeList(dataList) {
      this.handleCloseChange()
      if (dataList) {
        dataList.forEach(item => {
          this.formData.itemList.push({
            distributeId: item.id,
            code: item.code,
            receiveUserName: item.receiveUserName,
            receiveNickName: item.receiveNickName,
            receiveUserDeptId: item.receiveUserDeptId,
            receiveUserDept: item.receiveUserDept,
            receiveTime: item.receiveTime,
            status: item.status
          })
        })
      }
    },
    handleDelete(dataList, index) {
      dataList.splice(index, 1)
    },
    formatterDocClass(row, column, cellValue, index) {
      let _this = this
      if (_this.docClassList) {
        let item = _this.docClassList.find(item => item.id === cellValue)
        return item ? item.className : cellValue
      }
      return cellValue
    },
    selectHandlePresetUser(source, index, data) {
      let _this = this
      _this.$set(_this.formData, 'presetUserList', data)

    },
    /**
     * 生成申请标题
     * @returns {Promise<void>}
     */
    async generateApplyTitle() {
      let _this = this
      await selectApplySerial('lost').then((res) => {
        _this.applyTitleSerial = res.data
        _this.formData.applyTitle = _this.formData.deptName + '-' + _this.formData.itemList.length + '份' + '-' + _this.applyTitleSerial
      })
    },
    /**
     * 更新申请标题
     */
    updateGenerateApplyTitle() {
      if (this.formData.applyTitle) {
        let parts = this.formData.applyTitle.split('-')
        let deptNameDash = parts[0]
        let applyTitleSerialDash = parts[parts.length - 1]
        this.formData.applyTitle = deptNameDash + '-' + this.formData.itemList.length + '份' + '-' + applyTitleSerialDash
      }
    },
    //从消息待办跳转来的
    isShowSubmit() {
      let invokerFormMsg = this.$route.query.invokerForm
      //当_this.procDefKey为空，并且来源为我的消息
      if ((this.procDefKey == null || this.procDefKey == undefined || this.procDefKey == '') && invokerFormMsg != null && invokerFormMsg == 'systemMsg') {
        return false
      }
      return true
    }
  }
}
</script>
<style scoped>
.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

</style>
