<!--
 * @Description: 身份验证组件
 * @Author: deng<PERSON><PERSON><PERSON>
 * @Date: 2024-04-02
 * @LastEditors: deng<PERSON>jie
 * @LastEditTime: 2024-04-02
 -->
<template>
  <div>
    <el-dialog
      :title="$t(`doc.identity_verify_check`)"
      :visible.sync="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="80px">
        <el-form-item :label="$t(`doc.identity_verify_password`)" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            :placeholder="$t(`doc.identity_verify_password_placeholder`)"
            show-password
            @keyup.enter.native="handleVerify"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">{{$t(`file_set.type_cancel`)}}</el-button>
        <el-button type="primary" v-dbClick @click="handleVerify" :loading="loading">{{$t(`file_set.type_confim`)}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { verifyIdentity } from '@/api/system/user'

export default {
  name: 'IdentityVerify',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 成功回调方法
    successCallback: {
      type: Function,
      default: () => {}
    },
    // 取消回调方法
    cancelCallback: {
      type: Function,
      default: () => {}
    },
    // 业务参数
    businessParams: {
      type: [Object, Array, String, Number],
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        password: ''
      },
      rules: {
        password: [
          { required: true, message: this.$t(`doc.identity_verify_password_placeholder`), trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    // 验证身份
    handleVerify(invokeFrom) {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            await verifyIdentity(this.form)
            this.dialogVisible = false
            // 验证成功后，触发业务逻辑执行事件
            this.$emit('business-execute', this.businessParams)
          } catch (error) {
            console.log(error)
            this.$message.error(this.$t(`doc.identity_verify_check_error`))
          } finally {
            this.loading = false
          }
        }
      })
    },
    // 取消验证
    handleCancel() {
      this.dialogVisible = false
      // 执行取消回调
      this.cancelCallback()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
