<template>
  <el-dialog
    :title="$t(`doc.this_dept_gen_doc_code`)"
    :visible.sync="visible"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <el-alert
      v-if="!docIdDisabled"
      :title="$t(`doc.this_dept_adjust_doc_code`)"
      type="warning">
    </el-alert>
    <el-table
      :data="docIdData"
      v-loading="loading"
      max-height="400"
      border
    >
      <el-table-column
        :label="$t(`doc.this_dept_file_names`)"
        align="left"
        prop="docName"/>
      <el-table-column
        :label="$t(`doc.this_dept_file_codes`)"
        align="left"
        prop="docId"
      >
        <template slot-scope="scope">
          <el-input
            v-model.trim="scope.row.newNo"
            :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_file_codes`)"
            :style="{ width: '100%' }"
            maxlength="50"
          ></el-input>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{$t(`doc.this_dept_abolish`)}}</el-button>
      <el-button @click="updateDocId">{{$t(`doc.this_dept_confirm`)}}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getDocNoListByApplyId,
  updateDocIdList,
} from '@/api/file_processing/modifiyApply'
    export default {
      name: "DocIdListBox",
      props: {
        docIdDisabled: {
          type: Boolean,
          default:  false,
        },
        recordIdDisabled: {
          type: Array,
          default: ()=>[],
        }
      },
      data() {
          return {
              loading:false,
              visible: false,
              docIdData: [],
              applyIdList:undefined
          };
        },
        methods: {
            init(applyIdList) {
                let _this = this
                _this.applyIdList = applyIdList
                _this.visible = true
                _this.getDocId(applyIdList)
            },
            getDocId(applyIdList){
              let _this =this
              _this.loading =true
              getDocNoListByApplyId(applyIdList).then((res) => {
                res.data.forEach(item=>{
                  item.newNo = item.docId
                  item.oldNo = item.docId
                })
                _this.docIdData = res.data;
                _this.$emit("setDocId",res.data)
              }).finally(()=>{
                _this.loading = false
              });
            },
            updateDocId(){
              let _this = this
              let msg = ''
              if (_this.docIdData.some(item=>{
                 if (_this.docIdData.findIndex(data=>data.busId!==item.busId&&data.newNo===item.newNo)>-1){
                   msg = "【"+item.newNo+"】"+_this.$t(`doc.this_dept_doc_code_exist`)
                   return true
                 }
                 if (!item.newNo) {
                   msg = _this.$t(`doc.this_dept_doc_code_no_null`)
                   return true
                 }
              })){
                _this.$modal.msgWarning(msg)
                return
              }
              updateDocIdList(_this.docIdData).then(res=>{
                _this.$emit("setDocId",_this.docIdData)
                _this.visible = false
              })
            },
        }
    }
</script>
