<template>
 <el-dialog
   :title="$t(`file_handle.transfer`)"
   :visible.sync="visible"
   width="40%"
   :close-on-click-modal="false"
   append-to-body>
    <div class="el-card__body">
      <el-form :model="formData" ref="queryForm"  v-show="showSearch" label-width="100px">
        <el-form-item :label="$t(`doc.designated_personnel`)" prop="userName">
          <el-input style="width: 80%; margin-right: 10px" readonly v-model.trim="formData.nickName" :placeholder="$t(`doc.this_dept_pls_select`)">
            <el-button slot="append" icon="el-icon-search" @click="handleSelect"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
   <div slot="footer">
     <el-button @click="visible = false">{{ $t(`file_set.type_cancel`) }}</el-button>
     <el-button type="primary" @click="selectHandle(true)">{{ $t(`file_set.type_confim`) }}</el-button>
   </div>
   <user-list ref="userList" @selectHandle="handleSubmitUser"></user-list>
   <identity-verify
     v-if="verifyVisible"
     :visible.sync="verifyVisible"
     :success-callback="currentCallback"
     :cancel-callback="handleCancel"
   />
 </el-dialog>
</template>

<script>
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import { workflowTransfer } from '@/api/my_business/workflow'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import IdentityVerify from "@views/workflowList/addWorkflow/add_import/identityVerify.vue";

export default {
  name: "TransferFlow",
  components: { UserList,IdentityVerify },
  data() {
    return {
      currentCallback: () => {},
      verifyVisible: false,
      source: undefined,
      index: undefined,
      fileType: undefined,
      visible: false,
      roleKey: undefined,
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      multiple: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      dataList: [],
      multipleSelection: [],
      // 查询参数
      formData: {
        userName: undefined,
        nickName: undefined,
        deptId: undefined,
        deptName: undefined,
      },
      pListData: {},
      id: undefined,
      type: undefined,
      order: undefined,
      bizType: undefined,
      transferList: [],
      authentication:false
    };
  },
  methods: {
    showVerify() {
      if(this.authentication){
        console.log(1)
        this.currentCallback = this.selectHandle(true)
        this.verifyVisible = true
      }else{
        console.log(2)
        this.selectHandle(true)
      }
    },
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    init(pListData,id,type,order,bizType,authentication) {
      let _this = this
      _this.authentication = authentication
      _this.id = id
      _this.type = type
      _this.order = order
      _this.bizType = bizType
      _this.pListData = pListData
      if(_this.transferList&&_this.transferList.length>0){
        _this.formData = {
            userName: _this.transferList[0].sender,
            nickName: _this.transferList[0].nickName,
            deptId: _this.transferList[0].senderDeptId,
            deptName: _this.transferList[0].deptName,
        }
        _this.visible = false
        _this.selectHandle(null)
      }else {
        _this.visible = true
      }
    },
    async getTransferStatus(pListData) {
      let _this = this
      let res = await listWorkflowLog({
        actInstId: pListData.curActInstId,
        actStatus: 'transfer',
        transfer: true,
        havaDetail: true
      })
      _this.transferList = res.rows
      return _this.transferList&&_this.transferList.length>0
    },
    selectHandle(transfer){
      let _this = this
      let msg = transfer?_this.$t(`file_handle.transfer_text`)+'【'+_this.formData.nickName+'】?':_this.$t(`file_handle.transfer_return_text`)+'【'+_this.formData.nickName+'】?'
      _this.$modal.confirm(msg).then(()=> {
          const loading = this.$loading({
            lock: true,
          });
          let wf_receivers = [{
            receiveUserId: _this.formData.userName,
            receiveUserOrgId: _this.formData.deptId,
          }];
        let bpmClientInputModel = {
            model: {
              wf_businessKey: _this.id,
              wf_procDefKey: _this.pListData.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_nextActDefName: _this.pListData.curActDefName,
              wf_nextActDefId: _this.pListData.curActDefId,
              wf_receivers: wf_receivers,
            },
            transfer: transfer,
            type: _this.type,
            order: _this.order,
            bizType: _this.bizType
          }
          workflowTransfer(bpmClientInputModel).then((res) => {
            if (res.code === 200) {
              _this.$modal.msgSuccess(_this.$t(`doc.this_dept_operation_succ`));
              _this.visible = false
              _this.$emit("close");
            }
          }).finally(()=>{
            loading.close();
          });
        })
    },
    handleSelect() {
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(null,null,null)
      })
    },
    handleSubmitUser(source,index,user) {
      this.formData.nickName = user.nickName;
      this.formData.userName = user.userName;
      this.formData.deptId = user.dept.deptId;
      this.formData.deptName = user.dept.deptName;
    },
  }
};
</script>
