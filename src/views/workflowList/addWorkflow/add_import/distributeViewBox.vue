<template>
  <div class="news-card">
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="elForm"
        size="medium"
        label-position="right"
        label-width="150px"
      >
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item :label="$t(`doc.this_dept_distribute_to`)+`:`" prop="distributeList">
              <el-card class="gray-card table-card no-padding">
                <el-table :data="dataList" border max-height="500">
                  <el-table-column
                    :label="$t(`doc.this_dept_receive_dept`)"
                    align="center"
                    prop="receiveUserDept"
                  >
                  </el-table-column>
                  <el-table-column
                    :label="$t(`doc.this_dept_receive_user`)"
                    align="center"
                    prop="receiveNickName"
                  >
                  </el-table-column>
                  <el-table-column
                    :label="$t(`doc.this_print_paper`)"
                    align="center"
                    prop="printPaperType"
                  >
                    <template slot-scope="scope">
                      <dict-tag :options="dict.type.print_paper_type" :value="scope.row.printPaperType"/>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t(`doc.this_dept_print_copies`)"
                    align="center"
                    prop="nums"
                  >
                  </el-table-column>
                </el-table>
              </el-card>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item :label="$t(`doc.purview_target`)+`:`" prop="distributeList">
              <el-card class="gray-card table-card no-padding">
                <span v-if="trainCompanyDataList.length>0">{{ $t(`file_set.type_company`) }}</span>
                <el-table v-if="trainCompanyDataList.length>0" :data="trainCompanyDataList" border max-height="500">
                  <el-table-column
                    :label="$t(`file_set.type_company`)"
                    align="center"
                    prop="receiveUserDept"
                  >
                  </el-table-column>
                </el-table>
                <span v-if="trainDeptDataList.length>0">{{ $t(`dicts.sys_distribute_type_dept`) }}</span>
                <el-table v-if="trainDeptDataList.length>0" :data="trainDeptDataList" border max-height="500">
                  <el-table-column
                    :label="$t(`doc.this_dept_dept`)"
                    align="center"
                    prop="receiveUserDept"
                  >
                  </el-table-column>
                </el-table>
                <span v-if="trainPersonDataList.length>0">{{ $t(`file_set.type_personal`) }}</span>
                <el-table v-if="trainPersonDataList.length>0" :data="trainPersonDataList" border max-height="500">
                  <el-table-column
                    :label="$t(`file_set.type_personal`)"
                    align="center"
                    prop="receiveNickName"
                  >
                  </el-table-column>
                  <el-table-column
                    :label="$t(`doc.this_dept_dept`)"
                    align="center"
                    prop="receiveUserDept"
                  >
                  </el-table-column>
                </el-table>
              </el-card>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
<script>

import { listDistribute, listPrintGroup } from '@/api/process/distribute'

export default {
  name: "DistributeViewBox",
  dicts: ['print_paper_type'],
  data() {
    return {
      dataList: [],
      trainDataList: [{}],
      trainDeptDataList: [],
      trainPersonDataList: [],
      trainCompanyDataList: [],
      setting: {},
    }
  },
  methods:{
    init(versionId){
      this.getDistributeList(versionId)
      this.getPrintGroupList(versionId)
    },
    getDistributeList(versionId){
      let _this = this
      //培训
      listDistribute({versionId:versionId,neType: 'print'}).then(res=>{
        let trainDeptDataList=  []
        let trainPersonDataList = []
        let trainCompanyDataList = []
        res.data.forEach(item=>{
          if (item.type === 'dept'){
            trainDeptDataList.push(item)
          }else if (item.type === 'person'){
            trainPersonDataList.push(item)
          }else if (item.type === 'company'){
            trainCompanyDataList.push(item)
          }
        })
        _this.trainDeptDataList = trainDeptDataList
        _this.trainPersonDataList = trainPersonDataList
        _this.trainCompanyDataList = trainCompanyDataList
      })
    },
    getPrintGroupList(versionId){
      let _this = this
      listPrintGroup({versionId:versionId}).then(res=>{
        _this.dataList = res.data
      })
    }
  }
}
</script>
