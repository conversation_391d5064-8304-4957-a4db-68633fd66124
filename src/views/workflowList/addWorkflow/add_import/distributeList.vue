<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{title}}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" @click="submit">{{ $t(`file_handle.change_confirm`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="68px"
          >
            <el-row type="flex">
              <el-col :span="12">
                <el-form-item label="" prop="searchValue">
                  <el-input
                    v-model.trim="queryParams.searchValue"
                    :placeholder="$t(`doc.this_dept_insert_keyword`)"
                    clearable
                    size="small"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                  >{{ $t(`doc.this_dept_search`) }}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
        </el-form>
        <el-card class="gray-card table-card no-padding">
          <el-table :data="dataList" border @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              :selectable="selectable"
              width="55"
              align="left"
            />
            <el-table-column
              :label="$t(`doc.this_dept_distribute_info`)"
              align="center"
              prop="code"
              :formatter="formatterCode"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_dept`)"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_signatory`)"
              align="center"
              prop="receiveNickName"
              width="200"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_date`)"
              align="center"
              prop="receiveTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_status`)"
              align="center"
              prop="status"
              :formatter="formatterStatus"
            >
            </el-table-column>
          </el-table>
        </el-card>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { pageDistribute } from '@/api/process/distribute'
export default {
  name: "DistributeList",
  props: ["title"],
  data() {
    return {
      viewId: "",
      selectInfoData: [],
      dataList: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {},
      queryParams: {
        searchValue: undefined,
        type:'print',
        versionId: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      detailLoading: false,
      selectedList: []
    };
  },
  computed: {},
  methods: {
    init(versionId,selectedList){
      let _this = this
      _this.selectedList = selectedList
      _this.queryParams.versionId = versionId
      _this.handleQuery()
    },
    handleQuery(){
      this.queryParams.pageNum = 1
      this.getList()
    },
    getList(){
      let _this = this
      _this.detailLoading = true
      pageDistribute(this.queryParams).then(res=>{
          _this.dataList = res.rows
          _this.total = res.total;
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    formatterStatus(row){
      if (row.status==='lost')  {
        return '丢失'
      }else if (row.status==='recovery') {
        return '已回收'
      }else {
        return '未回收'
      }
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    handleSelectionChange(selection) {
      this.selectInfoData = selection;
    },
    selectable(row, index){
      if (this.selectedList&&this.selectedList.length>0) {
        return !this.selectedList.find(item=>item.code===row.code)
      }else {
        return true
      }
    },
    submit(){
      this.$emit("submit", this.selectInfoData)
    }
  },
};
</script>
