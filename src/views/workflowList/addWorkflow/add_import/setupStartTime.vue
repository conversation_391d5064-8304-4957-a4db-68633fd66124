<template>
  <el-dialog :title="$t(`doc.this_dept_tip`)"
             :visible.sync="visible"
             width="35%"
             v-loading="loading"
             :close-on-click-modal="false"
             :close-on-press-escape="false"
             append-to-body>
    <div>
        <span>{{ $t(`doc.this_dept_set_effective_date`) }}：
           <el-date-picker
             v-model="setupTime"
             value-format="yyyy-MM-dd HH:mm:ss"
             type="date"
             :placeholder="$t(`doc.this_dept_select_effective_date`)">
          </el-date-picker>
        </span>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
      <el-button type="primary" @click="pushCommit">{{ $t(`doc.this_dept_confirm`) }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { onlyEditModifyApply } from '../../../../api/file_processing/modifiyApply'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: "SetupStartTime",
  props: {
    batchId:{
      type: String,
      default: undefined
    },
    id:{
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      loading:false,
      visible: false,
      setupTime: undefined,
      oldSetupTime: undefined
    };
  },
  methods: {
    init(setupTime){
      let _this = this
      _this.visible = true
      _this.oldSetupTime = setupTime
      if (!setupTime) {
        setupTime = parseTime(new Date())
      }
      _this.setupTime = setupTime
    },
    pushCommit(){
      let _this = this
      if(!_this.setupTime){
        _this.$modal.msgError(_this.$t(`doc.this_dept_set_effective_date`))
        return
      }
      if (_this.oldSetupTime !== _this.setupTime) {
        let form = {
          id: _this.id,
          batchId: _this.batchId,
          setupTime: _this.setupTime,
          isSignature: 'N',
          onlyEdit: true
        }
        onlyEditModifyApply(form).then((res) => {
          if (res.code===200) {
            _this.visible = false
            _this.$emit('updateStartTime',_this.setupTime)
          }
        });
      }else {
        _this.visible = false
      }
    },
  }
}
</script>
