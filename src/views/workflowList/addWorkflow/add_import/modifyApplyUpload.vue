<template>
  <div>
    <el-button style="float: right;margin-left: 10px" @click="handleImport">{{$t('doc.import_file_information')}}</el-button>
    <el-button style="float: right" @click="handleExport">{{$t('doc.download_file_information')}}</el-button>
    <el-button style="float: right" @click="handleUploadFile" v-if="uploadStatus">{{ $t('doc.this_dept_upload_folder') }}</el-button>
    <input v-show="false" ref="fileRef" id="fileFolder" type="file" @change="fileChange" webkitdirectory multiple="multiple" />
    <input v-show="false" ref="fileImport" type="file" @change="handleImportFileChange" />
    <div v-if="progressShow" class="mask"></div>
    <div v-if="progressShow" class="maskprogress">
      <div style="
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
        ">
        <el-progress type="dashboard" :percentage="percentage" :color="colors"></el-progress>
        <br />
        <div style="
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
          ">
          <div style="color: #ffff">{{ $t('sys_mgr.dispose_uploading') }}...</div>
          <br />
          <div style="color: #ffff">
            （{{ $t('sys_mgr.dispose_already_upload') }}{{ filesNumber }}/{{ $t('sys_mgr.dispose_total') }}{{ filesLength }}）
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { modifyApplyImport, modifyApplyUpload } from '../../../../api/file_processing/modifiyApply'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver';
import { getNextVersion } from '../../../../api/setting/versionRuleDetail'
import { distributeGroupByDocClass } from '../../../../api/setting/distributeGroup'
export default {
  name: "ModifyApplyUpload",
  props:{
    formData:{
      type: Object,
      default: ()=>{},
    },
    fileType: {
      type: Array,
      default: () => ["docx", "doc", "xls", "ppt", "txt", "pdf", "jpg", "png"],
    },
    uploadStatus: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      yes: 'Y',
      dataForm: {},
      versionValue: "",
      distributeList: [],
      upLoadData: {},
      upLoadDataList: [],
      cgNum: 0,
      sbNum: 0,
      percentage: 0,
      sbNumMsg: [],
      filesLength: 0,
      filesNumber: 0,
      progressShow:false,
      colors: [
        { color: "#007dff", percentage: 20 },
        { color: "#007dff", percentage: 40 },
        { color: "#007dff", percentage: 60 },
        { color: "#007dff", percentage: 80 },
        { color: "#007dff", percentage: 100 },
      ],
    };
  },
  methods: {
    init(){
      console.log("初始化")
      let _this = this
      getNextVersion({version:"",docClass:_this.formData.docClass}).then(res=>{
        _this.versionValue = res.data
      })
      // distributeGroupByDocClass(_this.formData.docClass).then(res=>{
      //   let formData = {
      //     trainType: '',
      //     distributeType: 'person',
      //     isDistribute: 'N',
      //   }
      //   let distributeList = []
      //   res.data.forEach(item => {
      //     if (item.type === 'print') {
      //       formData.distributeType = item.scopeType
      //       if (item.itemList && item.itemList.length > 0) {
      //         formData.isDistribute = _this.yes
      //         distributeList.push(...item.itemList)
      //       }
      //     } else if (item.type === 'purview') {
      //       formData.trainType = item.scopeType
      //       if (item.itemList && item.itemList.length > 0) {
      //         distributeList.push(...item.itemList)
      //       }
      //     }
      //   })
      //   _this.dataForm = formData
      //   _this.distributeList = distributeList
      // })
    },
    handleUploadFile(){
      let _this = this
      if (!_this.formData.docClass) {
        _this.$modal.msg( _this.$t('doc.this_dept_select_type'));
      } else {
        _this.upLoadData = {}
        _this.upLoadDataList = []
        document.getElementById("fileFolder").value = null;
        _this.$refs.fileRef.dispatchEvent(new MouseEvent("click"));
      }
    },
    async fileChange(e) {
      this.cgNum = 0;
      this.sbNum = 0;
      this.percentage = 0;
      this.sbnummsg = [];
      let files = e.target.files;
      this.filesLength = files.length;
      this.filesNumber = 0;
      let fileSper = Math.floor( 100 / files.length * 100)/100;
      this.progressShow = true;
      if (this.filesLength == 0) {
        this.progressShow = false;
        this.$modal.msg(this.$t('sys_mgr.dispose_upload_text',[0,0]));
        return
      }
      for (const file of files) {
        await this.doAsyncOperation(file, fileSper);
      }
      this.$refs.fileRef.value = null;
    },
    async doAsyncOperation(file, fileSper) {
      let _this = this
      return new Promise((resolve, reject) => {
        // 异步操作代码
        var testMsg = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase();
        if (!_this.fileType.includes(testMsg)) {
          this.sbNum = this.sbNum + 1;
          this.sbNumMsg.push({path: file.webkitRelativePath,msg:this.$t('doc.documents_only',[_this.fileType.join('/')])})
          this.progressShowClose(fileSper)
          resolve()
          return
        }
        let formData = {
          versionValue: _this.versionValue,
          docClass:_this.formData.docClass,
          batchId:_this.formData.batchId,
          dataType:_this.formData.dataType,
          classType:_this.formData.classType,
          changeType:_this.formData.changeType,
          userName:_this.formData.userName,
          nickName:_this.formData.nickName,
          deptId: _this.formData.deptId,
          deptName:_this.formData.deptName,
          applyTime: _this.formData.applyTime,
          yNTrain:_this.formData.yNTrain,
          whetherCustomer: _this.formData.whetherCustomer,
          docName: file.name,
          yNDistribute: _this.dataForm.isDistribute,
          distributeType: _this.dataForm.distributeType,
          trainType: _this.dataForm.trainType,
          // distributeList: _this.distributeList
        }
        let linkType = 'DOC'
        let index = file.webkitRelativePath.indexOf(_this.$t(`doc.appendix`))
        if (index>-1) {
          linkType = 'APPENDIX'
          let path = file.webkitRelativePath.substring(0,index);
          if (_this.upLoadData[path]){
            formData.applyId=_this.upLoadData[path]
          }else {
            this.sbNum = this.sbNum + 1;
            this.sbNumMsg.push({path:file.webkitRelativePath,msg:_this.$t(`doc.main_file_not_found`)})
            this.progressShowClose(fileSper)
            resolve()
            return
          }
        }
        let formDatafile = new FormData();
        formDatafile.append("file", file);
        formDatafile.append("formData",JSON.stringify(formData));
        formDatafile.append("linkType", linkType);
        modifyApplyUpload(formDatafile).then((res) => {
            if (res.data) {
              this.cgNum = this.cgNum + 1;
              if ('DOC'===linkType) {
                formData.id = res.data.applyId
                formData.appendixes = []
                formData.standardDoc = res.data
                _this.upLoadDataList.push(formData)
                _this.upLoadData[file.webkitRelativePath.replace(file.name,'')] = formData.id
              }else {
                let data = _this.upLoadDataList.find(item=>item.id === res.data.applyId)
                data.appendixes.push(res.data)
              }
            }else {
              this.sbNumMsg.push({path:file.webkitRelativePath,msg:res.msg})
            }
            this.progressShowClose(fileSper,_this.upLoadDataList)
            resolve()
          })
          .catch((error) => {
            this.sbNum = this.sbNum + 1;
            this.sbNumMsg.push({path:file.webkitRelativePath,msg:error.toString()})
            this.progressShowClose(fileSper)
            resolve()
          });
      });
    },
    progressShowClose(fileSper) {
      let _this = this
      _this.filesNumber = _this.filesNumber + 1;
      _this.percentage =Math.floor((_this.percentage + fileSper)*100)/100;
      if (_this.percentage > 100) {
        _this.percentage = 100;
      }
      if (_this.filesNumber == _this.filesLength) {
        _this.progressShow = false;
        if (_this.sbNum == 0) {
          _this.$message.success(_this.$t('sys_mgr.dispose_upload_text',[ _this.cgNum,0]));
        } else {
          _this.$message({
            showClose: true,
            dangerouslyUseHTMLString: true,
            duration: 0,
            message:_this.$t('sys_mgr.dispose_upload_text',[ _this.cgNum,_this.sbNum])+
              "<br/>"+
              _this.$t('sys_mgr.dispose_upload_text_1'),
            type: "warning",
          });
          if (_this.sbNumMsg.length>0) {
            const headers = [_this.$t('doc.path'), _this.$t('sys_mgr_log.operlog_err_msg')];
            const ws_data = [headers, ..._this.sbNumMsg.map(item => [item.path, item.msg])];
            _this.exportToExcel(ws_data,_this.$t('doc.this_dept_upload_folder')+_this.$t('sys_mgr_log.operlog_err_msg')+'.xlsx');
          }
        }
        if (_this.upLoadDataList&&_this.upLoadDataList.length>0) {
          _this.$emit('upLoadDataList',_this.upLoadDataList,()=>{})
        }
        document.getElementById("fileFolder").value = null;
        _this.cgNum = 0;
        _this.sbNum = 0;
        _this.progressShow = false;
      }
    },
    exportToExcel(ws_data,name) {
      const ws = XLSX.utils.aoa_to_sheet(ws_data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([wbout], { type: 'application/octet-stream' });
      saveAs(blob, name)
    },
    handleExport() {
      let _this = this
      _this.$emit('upLoadDataList',null,()=>{
        _this.download(
          "/process/modifyApply/batch/export",
          {
            batchId: _this.formData.batchId
          },
          _this.$t('doc.import_file_information')+`_${new Date().getTime()}.xlsx`
        );
      })
    },
    handleImport(){
      this.$refs.fileImport.dispatchEvent(new MouseEvent("click"));
    },
    handleImportFileChange(e){
      let file = e.target.files[0];
      const formImportFile = new FormData();
      formImportFile.append("file", file);
      if (file != undefined) {
        this.loading = true;
        modifyApplyImport(formImportFile).then((res) => {
          this.loading = false;
          if (res.data.initFileMsg.length > 0) {
            this.$message({
              showClose: true,
              dangerouslyUseHTMLString: true,
              duration: 0,
              message: this.$t('doc.modification_failures',[res.data.initFileMsg.length])+'，'+this.$t('sys_mgr.dispose_upload_text_1'),
              type: "warning",
            });
            const headers = [this.$t('sys_mgr_log.operlog_err_msg')];
            const ws_data = [headers, ...res.data.initFileMsg.map(item => [item])];
            this.exportToExcel(ws_data,this.$t('doc.download_file_information')+this.$t('sys_mgr_log.operlog_err_msg')+'.xlsx');
          } else {
            this.$message.success(this.$t('doc.this_dept_operation_succ'));
          }
          this.$refs.fileImport.value = null;
          this.$emit('dataRefresh')
        });
      }
    }
  }
};
</script>
<style scoped  lang="scss">
.mask {
  background-color: rgb(0, 0, 0);
  opacity: 0.6;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
}

.maskprogress {
  position: fixed;
  top: 50%;
  left: 50%;
  margin: -100px 0 0 -100px;
  z-index: 9999999;
  .el-progress__text {
    color: #ffff;
  }
}
</style>
