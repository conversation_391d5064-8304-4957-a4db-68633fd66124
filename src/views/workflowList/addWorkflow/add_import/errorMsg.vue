<template>
  <el-dialog :title="$t(`doc.this_dept_tip`)"
             :visible.sync="visible"
             width="45%"
             v-loading="loading"
             :close-on-click-modal="false"
             :close-on-press-escape="false"
             append-to-body>
    <el-table :data="dataList" border max-height="400">
      <el-table-column
        :label="$t(`doc.this_dept_file_names`)"
        prop="docName"
      >
      </el-table-column>
      <el-table-column
        :label="$t(`doc.this_dept_info_content`)"
        prop="msg"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: "ErrorMsg",
  props: {
  },
  data() {
    return {
      loading:false,
      visible: false,
      dataList: []
    };
  },
  methods: {
    init(dataList){
      let _this = this
      _this.visible = true
      _this.dataList = dataList
    },
  }
}
</script>
