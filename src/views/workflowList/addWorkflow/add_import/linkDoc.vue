<template>
  <div class="app-container">
<!--    <el-form-->
<!--      :model="queryParams"-->
<!--      ref="queryForm"-->
<!--      :inline="true"-->
<!--      v-show="showSearch"-->
<!--      label-width="68px"-->
<!--      class="top"-->
<!--    >-->
<!--      <el-row type="flex">-->
<!--        <el-col :span="24" style="text-align: right" v-if="editStatus">-->
<!--          <el-button plain @click="handleAdd()">{{ $t(`doc.this_dept_new_add`) }}</el-button>-->
<!--        </el-col>-->
<!--      </el-row>-->
<!--    </el-form>-->
    <el-card class="gray-card">
      <el-table
        v-loading="loading"
        :data="dataList"
      >
        <el-table-column :label="$t(`myItem.borrow_file_type`)" align="left" prop="docClass" :formatter="formatterDocClass">
        </el-table-column>
        <el-table-column :label="$t(`myItem.borrow_file_name`)" align="left" prop="docName">
          <template slot-scope="scope">
            <span
              @click="handlePreview(scope.row.fileId)"
              style="color: #0144bb; cursor: pointer"
            >{{ scope.row.docName }}</span
            >
          </template>
        </el-table-column>
        <el-table-column :label="$t(`myItem.borrow_file_id`)" align="left" prop="docId" />
        <el-table-column :label="$t(`myItem.borrow_file_ver`)" align="left" prop="versionValue" />
        <el-table-column :label="$t(`doc.this_dept_current_file_status`)" align="left" prop="isDeleted" v-if="status">
          <template slot-scope="scope">
            <span v-if="scope.row.isDeleted == 0">{{ $t(`doc.this_dept_pending`) }}</span>
            <span v-if="scope.row.isDeleted == 1">{{ $t(`doc.this_dept_in_effect`) }}</span>
            <span v-if="scope.row.isDeleted == 2">{{ $t(`doc.this_dept_lost_effect`) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t(`doc.this_dept_post_release_status`)" align="left" prop="status" v-if="status">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 1">{{ $t(`doc.this_dept_take_effect`) }}</span>
            <span v-if="scope.row.status == 2">{{ $t(`doc.this_dept_Invalid`) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t(`doc.this_dept_effective_date`)" align="left" prop="startDate" v-if="!status">
          <template slot-scope="scope">
            <span>{{parseTime(scope.row.startDate,"{y}-{m}-{d}")}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t(`doc.this_dept_release_date`)" align="left" prop="releaseTime" v-if="!status">
          <template slot-scope="scope">
            <span>{{parseTime(scope.row.releaseTime,"{y}-{m}-{d}")}}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(`doc.this_dept_operation`)"
          align="left"
          class-name="small-padding fixed-width"
          v-if="editStatus"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handlezuofei(dataList,scope.$index)"
              v-if="scope.row.status==1&&scope.row.isDeleted==1"
            >{{ $t(`doc.this_dept_cancel`) }}</el-button
            >
            <el-button
              type="text"
              @click="handlecexiao(dataList,scope.$index)"
              v-if="scope.row.isDeleted!=1||scope.row.status!=1"
            >{{ $t(`file_handle.change_revoke`) }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <version-list
      style="overflow:auto;"
      classType="DOC"
      :dataType="dataType"
      :versionData="dataList"
      ref="versionList"
      @selectHandle="versionSelectHandle"
    ></version-list>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close">
    </as-pre-view>
  </div>
</template>

<script>
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import { settingDocClassList } from '@/api/file_settings/type_settings'

export default {
  name: "LinkNote",
  components: {
    VersionList,
  },
  props:{
    dataType: {
      type: String,
    },
    dataList:{
      type: Array,
      default: ()=>[]
    },
    editStatus:{
      type: Boolean,
      default: true,
    },
    status:{
      type: Boolean,
      default: true,
    },
    updateStatus:{
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      viewShow: undefined,
      viewId: undefined,
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: undefined,
      },
      docClassList: [],
      docClassTree: [],
    };
  },
  mounted() {
    if (this.docClassList) {
      this.getDocClassList()
    }
  },
  watch:{
    dataType(val){
      this.getDocClassList()
    },
  },
  methods: {
    getDocClassList(){
      // 获取文件分类集合
      if (!!this.dataType) {
        settingDocClassList({classStatus: "1", dataType:this.dataType,neClassType:'foreign'}).then(res => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
        });
      }
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      let item = _this.docClassList.find(item=>item.id===cellValue)
      return item?item.className:cellValue
    },
    handlePreview(id, source, linkType, mode) {
      console.log('预览文件id', id);
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id, source, linkType, mode);
      this.viewShow = true;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.$nextTick(()=>{
        this.$refs.versionList.init(null,null,true)
      })
    },
    versionSelectHandle(source,index,data){
      let selectInfoData = data;
      let dataList = []
      selectInfoData.forEach((element, i) => {
        dataList.push({
          linkId: undefined,
          fileId: element.fileId,
          docId: element.docId,
          versionId: element.versionId,
          versionValue: element.versionValue,
          docName: element.docName,
          docClass: element.docClass,
          startDate: element.startDate,
          releaseTime: element.releaseTime,
          isDeleted: '0',
          status: '1',
        })
      });
      if (this.dataList == null) {
        this.dataList = [];
      }
      this.dataList.push(...dataList)
    },
    handlezuofei(dataList,index) {
      let data = dataList[index]
      if (data.isDeleted != 1) {
        this.$modal.msg(this.$t(`doc.this_dept_record_not_update_or_cancel`));
      } else if (data.status == 2) {
        this.$modal.msg(this.$t(`doc.this_dept_record_yet_cancel`));
      } else {
        data.status = 2
      }
    },
    handlecexiao(dataList,index) {
      let data = dataList[index]
      if (data.isDeleted==1) {
        //生效中的改回原来的状态
        data.status = 1
      } else {
        dataList.splice(index,1)
      }
    },
  },
};
</script>
<style lang="scss">
.top {
  margin-bottom: 10px;

  .switch {
    color: #409eff;
  }
}
</style>
