<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
      <el-radio-group v-model="isCollapse" style="margin-bottom: 20px;">
        <el-radio-button :label="false">{{ $t('doc.this_dept_current_ver') }}</el-radio-button>
        <el-radio-button v-if="appendixes.length>0" :label="true">{{ $t('doc.this_dept_annexes_ver') }}</el-radio-button>
      </el-radio-group>
    <el-card class="gray-card table-card" >
      <el-row>
        <el-col :span="4" v-show="isCollapse">
          <el-table
            :show-header="false"
            :data="appendixes"
            border
            ref="appendix"
            @row-click="handleClick"
            show-overflow-tooltip
            highlight-current-row>
            <el-table-column
              prop="name">
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="isCollapse?20:24">
          <el-timeline :reverse="true" style="margin: 20px">
            <el-timeline-item
              v-for="(activity, index) in dataList"
              :key="index"
              placement="top"
              :timestamp="activity.createTime"
            >
              <el-row class="row">
                <el-col :span="4">操作类型</el-col>
                <el-col :span="20" style="padding-left: 10px"><dict-tag :options="dict.type.online_type" :value="activity.type"/></el-col>
              </el-row>
              <el-row class="row">
                <el-col :span="4">操作人</el-col>
                <el-col :span="20" style="padding-left: 10px">{{ activity.createName }}</el-col>
              </el-row>
              <el-row class="row">
                <el-col :span="4">流程环节</el-col>
                <el-col :span="20" style="padding-left: 10px">{{ activity.actDefName }}</el-col>
              </el-row>
              <el-row class="row">
                <el-col :span="4">编辑文件</el-col>
                <el-col :span="20"><online-edit :fileId="activity.fileId" :fileName="activity.fileName" type="2">{{ activity.fileName }}</online-edit></el-col>
              </el-row>
            </el-timeline-item>
          </el-timeline>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { listEditLog } from '@/api/process/editLog'
import OnlineEdit from '@viewscomponents/onlineEdit/index.vue'

export default {
  name: 'EditRecord',
  dicts:['online_type'],
  components: { OnlineEdit },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    applyId: {
      type: String,
      default: undefined
    },
    versionId: {
      type: String,
      default: undefined
    },
    docFile: {
      type: Array,
      default: () => []
    },
    appendixes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      radioValue: undefined,
      protoFileId: undefined,
      isCollapse: false,
      loading: true,
      dataList: []
    }
  },
  watch: {
    isVisible(val) {
      if (val) {
        if (!this.protoFileId && this.docFile.length > 0) {
          this.protoFileId = this.docFile[0].protoFileId
        }
        this.getList()
      }
    },
    isCollapse(val){
      if (val) {
        this.protoFileId = this.appendixes[0].protoFileId
        this.$refs.appendix.setCurrentRow(this.appendixes[0])
      }else {
        this.protoFileId = this.docFile[0].protoFileId
      }
      this.getList()
    }
  },
  methods: {
    getList() {
      let _this = this
      _this.loading = true
      if ((_this.applyId || _this.versionId) && _this.protoFileId) {
        listEditLog({
          applyId: _this.applyId,
          versionId: _this.versionId,
          protoFileId: _this.protoFileId
        }).then(response => {
          _this.dataList = response.rows
          _this.loading = false
        })
      }
    },
    handleClick(row){
      if (row.protoFileId!==this.protoFileId) {
        this.protoFileId = row.protoFileId
        this.getList()
      }
    }
  }
}
</script>
<style scoped>
.row {
  margin-bottom: 15px; /* 调整上下间隔 */
}
</style>
