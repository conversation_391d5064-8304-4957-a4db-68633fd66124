<template>
  <div>
    <el-button v-if="editStatus" type="primary" @click="handleAdd()">{{ $t(`doc.this_dept_new_add`) }}</el-button>
    <el-table border width="100%" :data="dataList" v-loading="loading">
      <el-table-column :label="$t(`doc.this_dept_file_versions2`)" align="left" prop="versionValue" width="100">
      </el-table-column>
      <el-table-column :label="$t(`doc.customer_record`)" align="left" prop="pass" width="100">
        <template slot-scope="scope">
          <span :style="{color:passList[scope.row.pass].color}">{{ passList[scope.row.pass].label }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t(`doc.customer_feedback`)" align="left" prop="remark">
        <template slot-scope="scope">
          <el-input
            class="fujian"
            readonly
            resize="none"
            v-model="scope.row.remark"
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 4 }"
            :style="{ width: '100%' }"
            maxlength="5000"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column :label="$t(`doc.this_dept_attach`)" align="left" prop="fileList" width="300">
        <template slot-scope="scope">
          <div
            v-for="(item, i) in scope.row.files"
            class="link-box bzlink-box"
            :key="i"
          >
            <span style="color: #385bb4; margin-left: 10px; cursor: pointer" :key="i" @click="handlePreview(item.id)">{{ item.fileName }}</span>
            <span v-if="downStatus" :key="i" style="color: #385bb4; cursor: pointer; margin-left: 10px" @click="handelefileLocalDownload(item.id, item.fileName)">{{ $t(`doc.this_dept_download`) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t(`file_set.version_create_time`)" align="left" prop="createTime" width="180">
      </el-table-column>
      <el-table-column v-if="editStatus" :label="$t(`doc.this_dept_operation`)" align="left" width="100">
        <template slot-scope="scope">
          <el-button
            circle
            type="danger"
            @click="handleDel(scope.row)"
          >{{ $t(`doc.this_dept_delete`) }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      destroy-on-close
      :title="$t(`doc.this_dept_new_add`)"
      :visible.sync="open"
      v-loading="dialogLoading"
      :close-on-click-modal="false"
      width="800px"
      append-to-body
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formData"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item :label="$t(`doc.this_dept_file_versions2`)+`:`" prop="versionValue">
          <span>{{formData.versionValue}}</span>
        </el-form-item>
        <el-form-item :label="$t(`doc.customer_record`)+`:`" prop="pass">
          <el-radio-group v-model.trim="formData.pass">
            <el-radio
              v-for="dict in passBoolOptions"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`doc.customer_feedback`)+`:`" prop="remark">
          <el-input
            resize="none"
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 4 }"
            :style="{ width: '100%' }"
            maxlength="5000"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t(`doc.this_dept_attach`)+`:`" prop="fileIds">
            <fileUpload
              v-model.trim="fileList"
              limit="100"
              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','ppts']"
              :isShowTip="false"
              @input="fileUpdate"
            />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="cancel">{{ $t(`file_set.type_cancel`) }}</el-button>
        <el-button type="primary" @click="handelConfirm()">{{ $t(`file_set.type_confim`) }}</el-button>
      </div>
    </el-dialog>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
  </div>
</template>
<script>
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import SelectionBox from '@viewscomponents/selectionBox/index.vue'
import { addModifyApplyTrain, updateModifyApplyTrain } from '@/api/my_business/modifyApplyTrain'
import { queryModifyApplyTrain } from '@/api/file_processing/modifiyApply'
import { fileLocalDownload } from '@/api/commmon/file'

export default {
  name: "CustomerBox",
  components: { SelectionBox, UserList },
  dicts: ['sys_yes_no'],
  props:{
    applyId: {
      type: String,
      default: undefined,
    },
    versionValue:{
      type: String,
      default: undefined,
    },
    editStatus: {
      type: Boolean,
      default: false,
    },
    downStatus:{
      type: Boolean,
      default: true,
    },
    versionId: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      loading: false,
      viewShow: false,
      viewId: undefined,
      dataList: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      open: false,
      dialogLoading: false,
      passBoolOptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      passList: { 'true':{label:this.$t(`doc.this_dept_pass`),color:'#70B603'},'false':{label:this.$t(`doc.this_dept_not_pass`),color:'#D9001B'}},
      customerData:{
        createTime: undefined,
        type: 'customer',
        pass: undefined,
        remark: undefined,
      },
      yes: 'Y',
      formData:{
        versionValue: undefined,
        applyId: undefined,
        fileIds: undefined,
        createTime: undefined,
        type: 'customer',
        pass: undefined,
        remark: undefined,
      },
      fileList: [],
      rules:{
        pass: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.customer_record`), trigger: "blur,change" },
        ],
        remark: [
          { required: true, message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.customer_feedback`), trigger: "blur,change" },
        ],
        fileIds: [
          { required: true, message: this.$t(`sys_mgr_log.user_signature_upload_text1`)+this.$t(`doc.this_dept_attach`), trigger: "blur,change" },
        ],
      },
      setting: {},
    }
  },
  mounted() {
    this.$nextTick(()=>{
      this.getList()
    })
  },
  methods:{
    cancel() {
      this.open = false;
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    close(){
      this.viewShow = false;
    },
    // 表单重置
    reset() {
      this.formData = {
        applyId: this.applyId,
        versionValue: this.versionValue,
        userName: this.userInfo.userName,
        deptId: this.userInfo.deptId,
        fileIds: undefined,
        createTime: undefined,
        type: 'customer',
        pass: undefined,
        remark: undefined,
      };
      this.fileList = [];
    },
    handleAdd(source,data){
      this.reset();
      this.open = true
    },
    fileUpdate(field){
      this.$set(this.formData,'fileIds',field.map(item=>item.url).join(','))
    },
    handleDel(row){
      let _this = this
      _this.$modal
        .confirm(_this.$t('file_set.version_delete_or_not'))
        .then(function () {
          _this.loading = true
          let data = {
            id: row.id,
            isDeleted: 1
          }
          return updateModifyApplyTrain(data);
        })
        .then(() => {
          _this.getList();
          _this.$modal.msgSuccess(_this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    handelConfirm(list,type) {
      let _this = this
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          addModifyApplyTrain(_this.formData).then(res=>{
            this.open = false
            this.getList()
          })
        }
      });
    },
    getList(){
      let _this = this
      if (_this.applyId||_this.versionId) {
        let data = {
          applyId: _this.applyId,
          versionId: _this.versionId,
          type: 'customer',
          isDeleted: 0
        }
        _this.loading = true
        queryModifyApplyTrain(data).then(res=>{
          _this.dataList = res.data
        }).finally(()=>{
          _this.loading = false
        })
      }
    },
    handelefileLocalDownload(id, name) {
      fileLocalDownload(id).then((res) => {
        //console.log("file", res);
        this.saveFile(res, name);
      });
    },
    saveFile(data, name) {
      try {
        const blobUrl = window.URL.createObjectURL(data);
        // console.log('bo',blobUrl);
        const a = document.createElement("a");
        a.style.display = "none";
        a.download = name;
        a.href = blobUrl;
        a.click();
      } catch (e) {
        alert("保存文件出错");
      }
    },
  }
}
</script>
<style scoped>
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
</style>
