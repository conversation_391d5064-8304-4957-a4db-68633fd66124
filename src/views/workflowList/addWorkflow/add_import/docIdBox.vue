<template>
  <el-dialog
    :title="$t(`doc.this_dept_gen_doc_code`)"
    :visible.sync="visible"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <el-alert
      v-if="!docIdDisabled||recordIdDisabled.length!==recordLinks.length"
      :title="$t(`doc.this_dept_adjust_doc_code`)"
      type="warning">
    </el-alert>
    <span>{{ $t(`doc.this_dept_master_file`) }}</span>
    <div style="margin: 5px 10px">
        <span>{{ $t(`myItem.borrow_file_id`) }}：
           <span v-if="docIdDisabled">{{docIdData.docId}}</span>
           <el-input v-else v-model="docIdData.docId" maxlength="50" style="width: 300px"></el-input>
        </span>
    </div>
    <span v-if="recordLinks&&recordLinks.length>0">{{ $t(`doc.this_dept_record_file`) }}</span>
    <div style="margin: 5px 10px" v-for="(val, i) in recordLinks" :key="i">
        <span>{{ $t(`myItem.borrow_file_id`) }}：
          <span v-if="recordIdDisabled.includes(val.busId)">{{val.oldNo}}</span>
           <el-input v-else v-model="val.newNo" maxlength="50" style="width: 300px"></el-input>
        </span>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t(`doc.this_dept_close`) }}</el-button>
      <el-button @click="updateDocId">{{ $t(`file_handle.change_confirm`) }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getDocNoByApplyId,
  getRecordDocNoByLinkId,
  updateDocId,
  updateRecordDocId
} from '@/api/file_processing/modifiyApply'
    export default {
      name: "DocIdBox",
      props: {
        docIdDisabled: {
          type: Boolean,
          default:  false,
        },
        recordIdDisabled: {
          type: Array,
          default: ()=>[],
        }
      },
      data() {
          return {
              visible: false,
              docIdData: {},
              recordLinks: [],
              docId: undefined,
              applyId:undefined
          };
        },
        methods: {
            init(applyId,fileIds) {
                let _this = this
                _this.applyId = applyId
                _this.visible = true
                _this.getDocId(applyId,fileIds)
            },
            getDocId(applyId,fileIds){
              let _this =this
              getDocNoByApplyId(applyId).then((res) => {
                _this.docId = res.data.docId;
                _this.$emit("setDocId",_this.docId)
                _this.docIdData = res.data;
                if (fileIds) {
                  getRecordDocNoByLinkId({
                    fileIds: fileIds.join(","),
                    applyId: applyId,
                    docId: _this.docId,
                  }).then((res) => {
                    res.data.forEach(res=>{
                      res.oldNo = res.docId
                      res.newNo = res.docId
                      res.busId = res.linkId
                    })
                    _this.recordLinks = res.data;
                    _this.$emit("setRecordDocId",_this.recordLinks)
                  });
                }
              });
            },
            updateDocId(){
              let _this = this
              if (!_this.docIdData.docId) {
                _this.$modal.msgWarning(_this.$t(`doc.this_dept_doc_code_no_null`))
                return
              }
              // if (!_this.docId) {
              //   _this.$modal.msgWarning("请停止操作，联系系统管理员维护！")
              //   return
              // }
              let data = {
                newNo: _this.docIdData.docId,
                oldNo: _this.docId,
                busId: _this.applyId
              }
              if (!_this.docIdDisabled) {
                updateDocId(data).then(res=>{
                  _this.$emit("setDocId",_this.docIdData.docId)
                })
              }
              if (_this.recordLinks&&_this.recordLinks.length>0) {
                updateRecordDocId(_this.recordLinks).then(res=>{
                  _this.visible = false
                  _this.$emit("setRecordDocId",_this.recordLinks)
                })
              }else {
                _this.visible = false
              }
            },
        }
    }
</script>
