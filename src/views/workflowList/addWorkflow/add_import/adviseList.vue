/**
 * 意见列表组件
 * <AUTHOR>
 * @date 2024-03-21
 */
<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <span>{{ $t(`doc.this_dept_file_advise_list`) }}</span>
      <div class="cell-right">
        <el-button plain @click="handleExport">{{ $t(`doc.exterior_dept_export`) }}</el-button>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="adviseList"
      style="width: 100%">
      <el-table-column
        prop="createName"
        :label="$t(`doc.this_advise_createName`)"
        width="120">
      </el-table-column>
      <el-table-column
        prop="summary"
        :label="$t(`doc.this_advise_advise_summary`)"
        min-width="300">
      </el-table-column>
      <el-table-column
        prop="createTime"
        :label="$t(`doc.this_advise_submit_time`)"
        width="180">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </el-card>
</template>

<script>
import { listFileAdvise } from "@/api/file_processing/fileAdvise";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: 'AdviseList',
  props: {
    versionId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // 意见列表数据
      adviseList: [],
      total: 0,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        versionId: undefined
      }
    }
  },
  watch: {
    versionId: {
      handler(val) {
        if (val) {
          this.queryParams.versionId = val
          this.getList()
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "process/fileAdvise/export",
        {
          ...this.queryParams,
        },
        `文件建议_${parseTime(new Date(), "{y}{m}{d}")}.xlsx`
      );
    },
    // 获取意见列表
    getList() {
      this.loading = true
      listFileAdvise(this.queryParams).then(response => {
        this.adviseList = response.rows
        this.total = response.total
        // 通知父组件更新意见数量
        this.$emit('update-advise-count', this.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 分页大小改变
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    // 页码改变
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>

<style scoped>
.pagination-container {
  padding: 15px 0;
  text-align: right;
}
</style> 