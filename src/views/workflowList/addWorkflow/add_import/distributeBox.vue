<template>
  <div>
    <div class="news-card" v-show="displayOne===undefined||displayOne==='print'" v-if="showWhetherDistribute">
      <div class="card-head">
        <div class="cell-title">{{ $t(`doc.this_dept_distribute`) }}</div>
      </div>
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elFormDistribute"
          :model="formData"
          size="medium"
          label-position="right"
          label-width="150px"
        >
          <el-row gutter="15" v-if="!hideIsDistribute">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_distribute_or_not`)+`:`" prop="isDistribute"
                            :rules="[{required: whetherDistributeCondition.validate, message:$t(`doc.this_dept_pls_select`), trigger: ['blur','change']}]">
                <el-radio-group  v-if="distributionEdit" v-model.trim="formData.isDistribute">
                  <el-radio
                    v-for="(item, index) in dict.type.sys_yes_no"
                    :key="index"
                    :label="item.value"
                  >{{ dictLanguage(item) }}</el-radio>
                </el-radio-group>
                <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.isDistribute"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15" v-if="formData.isDistribute===yes">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.invalid_external_distribution_scope`)+`:`" prop="distributeType" :rules="[{required: true, message:this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.invalid_external_distribution_scope`), trigger: ['blur','change']}]">
                <el-select
                  v-if="distributionEdit"
                  disabled
                  :placeholder="$t(`doc.invalid_external_distribution_scope`)"
                  v-model.trim="formData.distributeType"
                  onchange="distributeChange"
                >
                  <el-option
                    v-for="dict in dict.type.sys_distribute_type"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
                <dict-tag v-else :options="dict.type.sys_distribute_type" :value="formData.distributeType"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15" v-if="formData.isDistribute===yes">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_distribute_to`)+`:`" prop="distributeList">
                <el-card class="gray-card table-card no-padding" v-show="formData.distributeType.includes('company')">
                  <el-button v-if="distributionEdit" type="primary"
                             @click="selectionBoxInit('companyDataList',companyDataList,companyList,{title:$t(`file_set.type_select_company`),id:'id',label:'tenantName',valueId:'receiveUserDeptId',valueLabel:'receiveUserDept',valueModel:restData(1,'company','print'),notDel:[]},undefined)">
                    {{ $t(`file_set.type_select_company`) }}
                  </el-button>
                  <el-form class="form" size="mini" :model="companyDataList" ref="distributeForm">
                    <el-table :data="companyDataList" border max-height="500">
                      <el-table-column
                        :label="$t(`file_set.type_company`)"
                        align="center"
                        prop="receiveUserDept"
                      >
                      </el-table-column>
                      <el-table-column
                        :label="$t(`doc.this_dept_print_copies`)"
                        align="center"
                        prop="nums"
                      >
                        <template slot-scope="scope">
                          <el-form-item  class="no-margin-bottom"  v-if="editStatus" :prop="scope.$index + '.nums'" :rules="numRules">
                            <el-input-number v-if="editStatus" v-model="scope.row.nums" precision="0" :min="1" :max="999"></el-input-number>
                          </el-form-item>
                          <span v-else>{{scope.row.nums}}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-if="distributionEdit"
                        :label="$t(`myItem.msg_operation`)"
                        align="center"
                        :show-overflow-tooltip="true"
                      >
                        <template slot-scope="scope">
                          <el-button
                            circle
                            type="danger"
                            @click="handleDel('companyDataList',scope.$index)"
                          >{{ $t(`doc.this_dept_delete`) }}</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form>
                </el-card>
                <el-card class="gray-card table-card no-padding" v-show="formData.distributeType.includes('dept')">
                  <el-button v-if="distributionEdit" type="primary"
                             @click="selectionBoxInit('deptDataList',deptDataList,deptOptions,{title:$t(`doc.this_dept_select_dept`),id:'deptId',label:'deptName',valueId:'receiveUserDeptId',valueLabel:'receiveUserDept',valueModel:restData(1,'dept','print'),anySelect:true},deptList)">
                    {{ $t(`doc.this_dept_select_dept`) }}
                  </el-button>
                  <el-form class="form" size="mini" :model="deptDataList" ref="distributeForm">
                    <el-table :data="deptDataList" border max-height="500">
                      <el-table-column
                        :label="$t(`doc.this_dept_dept`)"
                        align="center"
                        prop="receiveUserDept"
                      >
                      </el-table-column>
                      <el-table-column
                        :label="$t(`doc.this_dept_people`)"
                        v-if="setDeptReceiver||!workflowStatus"
                        align="center"
                        prop="receiveNickName"
                        :show-overflow-tooltip="true"
                      >
                        <template slot-scope="scope">
                          <el-input v-if="setDeptReceiver" style="width: 80%; margin-right: 10px" readonly v-model.trim="scope.row.receiveNickName" :placeholder="$t(`doc.this_dept_pls_select`)">
                            <el-button slot="append" icon="el-icon-search" @click="userSearchInit('deptDataList',scope.$index,null,false,null)"></el-button>
                          </el-input>
                          <span v-else>{{scope.row.receiveNickName}}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        :label="$t(`doc.this_dept_print_copies`)"
                        align="center"
                        prop="nums"
                      >
                        <template slot-scope="scope">
                          <el-form-item class="no-margin-bottom" v-if="editStatus" :prop="scope.$index + '.nums'" :rules="numRules">
                            <el-input-number v-if="editStatus" v-model="scope.row.nums" precision="0" :min="1" :max="999"></el-input-number>
                          </el-form-item>
                          <span v-else>{{scope.row.nums}}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-if="distributionEdit"
                        :label="$t(`myItem.msg_operation`)"
                        align="center"
                        :show-overflow-tooltip="true"
                      >
                        <template slot-scope="scope">
                          <el-button
                            circle
                            type="danger"
                            @click="handleDel('deptDataList',scope.$index)"
                          >{{ $t(`doc.this_dept_delete`) }}</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form>
                </el-card>
                <el-card class="gray-card table-card no-padding" v-show="formData.distributeType.includes('person')">
                  <el-button v-if="distributionEdit" type="primary"
                             @click="userSearchInit('personDataList',null,null,true,personDataList.map(item=>item.receiveUserName))">
                    {{ $t(`doc.this_dept_select_user`) }}
                  </el-button>
                  <el-form class="form" size="mini" :model="personDataList" ref="distributeForm">
                    <el-table :data="personDataList" border max-height="500">
                      <el-table-column
                        :label="$t(`doc.this_dept_dept`)"
                        align="center"
                        prop="receiveUserDept"
                      >
                      </el-table-column>
                      <el-table-column
                        :label="$t(`doc.this_dept_people`)"
                        align="center"
                        prop="receiveNickName"
                        :show-overflow-tooltip="true"
                      >
                      </el-table-column>
                      <el-table-column
                        :label="$t(`doc.this_print_paper`)"
                        align="center"
                        prop="printPaperType"
                        :show-overflow-tooltip="true"
                      >
                        <template slot-scope="scope">
                          <el-select
                            v-if="distributionEdit"
                            :placeholder="$t(`doc.this_print_paper`)"
                            v-model.trim="scope.row.printPaperType"
                          >
                            <el-option
                              v-for="dict in dict.type.print_paper_type"
                              :key="dict.value"
                              :label="dictLanguage(dict)"
                              :value="dict.value"
                            >
                            </el-option>
                          </el-select>
                          <dict-tag v-else :options="dict.type.print_paper_type" :value="scope.row.printPaperType"/>
                        </template>
                      </el-table-column>
                      <el-table-column
                        :label="$t(`doc.this_dept_print_copies`)"
                        align="center"
                        prop="nums"
                      >
                        <template slot-scope="scope">
                          <el-form-item  class="no-margin-bottom"  v-if="distributionEdit" :prop="scope.$index + '.nums'" :rules="numRules">
                            <el-input-number v-if="distributionEdit" v-model="scope.row.nums" precision="0" :min="1" :max="999"></el-input-number>
                          </el-form-item>
                          <span v-else>{{scope.row.nums}}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        :label="$t(`myItem.msg_operation`)"
                        v-if="distributionEdit"
                        align="center"
                        :show-overflow-tooltip="true"
                      >
                        <template slot-scope="scope">
                          <el-button
                            circle
                            type="danger"
                            @click="handleDel('personDataList',scope.$index)"
                          >{{ $t(`doc.this_dept_delete`) }}</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form>
                </el-card>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
    <div class="news-card" v-if="!trainAlike&&showPurview"
         v-show="displayOne===undefined||displayOne==='purview'">
      <div class="card-head">
        <div class="cell-title">{{ $t(`doc.purview`) }}</div>
      </div>
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elFormTrain"
          :model="formData"
          size="medium"
          label-position="right"
          label-width="150px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.preview_range`)+`:`" prop="trainType"
                            :rules="[{required: fileRangePreviewEdit, message:$t(`doc.this_dept_pls_select`)+$t(`doc.preview_range`), trigger: ['blur','change']}]">
                <el-select
                  v-if="fileRangePreviewEdit"
                  :placeholder="$t(`doc.preview_range`)"
                  v-model.trim="formData.trainType"
                >
                  <el-option
                    v-for="dict in dict.type.sys_distribute_type"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
                <dict-tag v-else :options="dict.type.sys_distribute_type" :value="formData.trainType"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.preview_target`)+`:`" prop="distributeList">
                <el-card class="gray-card table-card no-padding" v-show="formData.trainType.includes('company')">
                  <el-button v-if="fileRangePreviewEdit" type="primary" @click="selectionBoxInit('trainCompanyDataList',trainCompanyDataList,companyList,{title:$t(`file_set.type_select_company`),id:'id',label:'tenantName',valueId:'receiveUserDeptId',valueLabel:'receiveUserDept',valueModel:restData(0,'company','train'),notDel:[]},undefined)">{{ $t(`file_set.type_select_company`) }}</el-button>
                  <el-table :data="trainCompanyDataList" border max-height="500">
                    <el-table-column
                      :label="$t(`file_set.type_company`)"
                      align="center"
                      prop="receiveUserDept"
                    >
                    </el-table-column>
                    <el-table-column
                      :label="$t(`doc.number_of_objects`)"
                      align="center"
                      prop="nums"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="fileRangePreviewEdit"
                      :label="$t(`myItem.msg_operation`)"
                      align="center"
                      :show-overflow-tooltip="true"
                    >
                      <template slot-scope="scope">
                        <el-button
                          circle
                          type="danger"
                          @click="handleDel('trainCompanyDataList',scope.$index)"
                        >{{ $t(`doc.this_dept_delete`) }}</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <el-card class="gray-card table-card no-padding" v-show="formData.trainType.includes('dept')">
                  <el-button v-if="fileRangePreviewEdit" type="primary" @click="selectionBoxInit('trainDeptDataList',trainDeptDataList,deptOptions,{title:$t(`doc.this_dept_select_dept`),id:'deptId',label:'deptName',valueId:'receiveUserDeptId',valueLabel:'receiveUserDept',valueModel:restData(1,'dept','train'),anySelect:true,FSLink:true},deptList)">{{ $t(`doc.this_dept_select_dept`) }}</el-button>
                  <el-table :data="trainDeptDataList" border max-height="500">
                    <el-table-column
                      :label="$t(`doc.this_dept_dept`)"
                      align="center"
                      prop="receiveUserDept"
                    >
                    </el-table-column>
                    <el-table-column
                      :label="$t(`doc.number_of_objects`)"
                      align="center"
                      prop="nums"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="fileRangePreviewEdit"
                      :label="$t(`myItem.msg_operation`)"
                      align="center"
                      :show-overflow-tooltip="true"
                    >
                      <template slot-scope="scope">
                        <el-button
                          circle
                          type="danger"
                          @click="handleDel('trainDeptDataList',scope.$index)"
                        >{{ $t(`doc.this_dept_delete`) }}</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <el-card class="gray-card table-card no-padding" v-show="formData.trainType.includes('person')">
                  <el-button v-if="fileRangePreviewEdit" type="primary" @click="userSearchInit('trainPersonDataList',null,null,true,trainPersonDataList.map(item=>item.receiveUserName))">{{$t(`doc.this_dept_select_user`)}}</el-button>
                  <el-table :data="trainPersonDataList" border max-height="500">
                    <el-table-column
                      :label="$t(`doc.this_dept_dept`)"
                      align="center"
                      prop="receiveUserDept"
                    >
                    </el-table-column>
                    <el-table-column
                      :label="$t(`doc.this_dept_people`)"
                      align="center"
                      prop="receiveNickName"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                    <el-table-column
                      :label="$t(`doc.number_of_objects`)"
                      align="center"
                      prop="nums"
                    >
                    </el-table-column>
                    <el-table-column
                      :label="$t(`myItem.msg_operation`)"
                      v-if="fileRangePreviewEdit"
                      align="center"
                      :show-overflow-tooltip="true"
                    >
                      <template slot-scope="scope">
                        <el-button
                          circle
                          type="danger"
                          @click="handleDel('trainPersonDataList',scope.$index)"
                        >{{ $t(`doc.this_dept_delete`) }}</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>
    <selection-box ref="selectionBox" @selectHandle="selectBoxHandle"></selection-box>
  </div>
</template>
<script>
import {getNumByDeptId, getNumByTenantId} from '@/api/system/user'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import SelectionBox from '@viewscomponents/selectionBox/index.vue'
import {updateModifyApplyDistribute} from '@/api/my_business/modifyApplyDistribute'
import {distributeGroupByDocClass} from '../../../../api/setting/distributeGroup'

export default {
  name: "DistributeBox",
  components: { SelectionBox, UserList },
  dicts: ['sys_distribute_type','sys_yes_no', 'print_paper_type'],
  props:{
    editStatus: {
      type: Boolean,
      default: false,
    },
    workflowStatus: {
      type: Boolean,
      default: false,
    },
    setDeptReceiver: {
      type: Boolean,
      default: false,
    },
    distributeList: {
      type: Array,
      default: ()=>[]
    },
    companyList: {
      type: Array,
      default: ()=>[]
    },
    deptList: {
      type: Array,
      default: ()=>[]
    },
    deptOptions: {
      type: Array,
      default: ()=>[]
    },
    trainAlike:{
      type: Boolean,
      default: false,
    },
    displayOne:{
      type: String,
      default: undefined,
    },
    hideIsDistribute:{
      type: Boolean,
      default: false,
    },
    nodeDetailList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 缓存节点显示状态，避免重复计算
    nodeVisibility() {
      const visibility = {};
      // 如果节点存在于 nodeDetailList 中，就表示该节点应该显示
      this.nodeDetailList.forEach(node => {
        visibility[node.code] = true;
      });
      return visibility;
    },
    // 具体节点的显示状态
    showWhetherDistribute() {
      return this.nodeVisibility['whether_distribute'] || false;
    },
    showPurview() {
      return this.nodeVisibility['purview'] || false;
    },
    // 分发编辑
    distributionEdit() {
      return this.nodeVisibility['distribution_edit'] || false
    },
    // 文件预览权限范围编辑
    fileRangePreviewEdit() {
      return this.nodeVisibility['file_range_preview_edit'] || false
    },
    // 节点功能条件缓存
    nodeConditions() {
      const conditions = {};
      console.log(this.nodeDetailList)
      this.nodeDetailList.forEach(node => {
        if (node.funCondition) {
          try {
            conditions[node.code] = JSON.parse(node.funCondition);
          } catch (e) {
            conditions[node.code] = {validate: false};
          }
        } else {
          conditions[node.code] = {validate: false};
        }
      });
      return conditions;
    },
    // 具体节点的功能条件
    whetherDistributeCondition() {
      return this.nodeConditions['whether_distribute'] || {validate: false};
    },
    purviewCondition() {
      return this.nodeConditions['purview'] || {validate: false};
    }
  },
  data() {
    return {
      yes: 'Y',
      deptDataList: [],
      personDataList: [],
      companyDataList: [],
      trainDeptDataList: [],
      trainPersonDataList: [],
      trainCompanyDataList: [],
      formData:{
        trainType: '',
        distributeType: 'person',
        isDistribute: 'N',
      },
      setting: {},
      numRules: [{ required: true, message: '打印份数不能为空', trigger:  ['blur', 'change'] }],
    }
  },
  watch:{
    distributeList(val){
      let _this = this
      let deptDataList=  []
      let personDataList = []
      let companyDataList = []
      let trainDeptDataList=  []
      let trainPersonDataList = []
      let trainCompanyDataList = []
      val.forEach(item=>{
        if (item.category === 'print') {
          //分发
          if (item.type === 'dept'){
            deptDataList.push(item)
          }else if (item.type === 'person'){
            personDataList.push(item)
          }else if (item.type === 'company'){
            companyDataList.push(item)
          }
        } else {
          //培训
          if (item.type === 'dept'){
            trainDeptDataList.push(item)
          }else if (item.type === 'person'){
            trainPersonDataList.push(item)
          }else if (item.type === 'company'){
            trainCompanyDataList.push(item)
          }
        }
      })
      _this.deptDataList = deptDataList
      _this.personDataList = personDataList
      _this.companyDataList = companyDataList
      _this.trainDeptDataList = trainDeptDataList
      _this.trainPersonDataList = trainPersonDataList
      _this.trainCompanyDataList = trainCompanyDataList
    },
    nodeDetailList: {
      handler(newVal, oldVal) {
        this.checkNodeVisibility();
      },
      deep: true, // 深度监听数组内部元素的变化
      immediate: true // 立即执行一次回调函数
    }
  },
  mounted() {
    debugger
    if (this.hideIsDistribute){
      this.formData.isDistribute=this.yes
    }
  },
  methods:{
    checkNodeVisibility() {
      // 检查节点可见性的逻辑
      if (this.hideIsDistribute) {
        this.formData.isDistribute = this.yes;
      }

      // 根据节点详情更新表单状态
      if (this.whetherDistributeCondition && this.whetherDistributeCondition.limitValue) {
        this.formData.isDistribute = this.whetherDistributeCondition.limitValue;
      }

      if (this.purviewCondition && this.purviewCondition.limitValue) {
        this.formData.trainType = this.purviewCondition.limitValue;
      }
    },
    // 保留原方法以兼容性，但建议使用计算属性
    nodeShow(code) {
      // 检查节点是否存在于 nodeDetailList 中
      return this.nodeDetailList.some(node => node.code === code);
    },
    getNodeDetail(code) {
      let _this = this
      return _this.nodeDetailList.find(item => item.code === code)
    },
    nodeFunCondition(code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      } else {
        return {validate: false}
      }
    },
    distributeChange(){
      this.deptDataList = []
      this.personDataList = []
      this.companyDataList = []
    },
    trainChange(){
      this.trainDeptDataList = []
      this.trainPersonDataList = []
      this.trainCompanyDataList = []
    },
    async validateForm(){
      // 验证分发和培训表单
      let validateValid= true
      let distributeForm = this.$refs["elFormDistribute"]
      if (!!distributeForm) {
        validateValid = await distributeForm.validate()
      }
      let trainForm = this.$refs["elFormTrain"]
      if (!!trainForm) {
        validateValid = await trainForm.validate()
      }
      if(this.formData.isDistribute===this.yes) {
        validateValid = this.deptDataList.length > 0 || this.personDataList.length > 0 || this.companyDataList.length > 0;
        if(!validateValid){
          this.$message.error(this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.this_dept_distribute_to`))
          return validateValid
        }
      }
      // 文件预览权限范围
      if (!this.trainAlike && this.showPurview) {
        console.log(this.purviewCondition.validate)
        console.log(this.nodeConditions)
        if (this.fileRangePreviewEdit) {
          validateValid = this.trainDeptDataList.length > 0 || this.trainPersonDataList.length > 0 || this.trainCompanyDataList.length > 0;
          if (!validateValid) {
            this.$message.error(this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.preview_target`))
          }
        }
      }
      if (this.setDeptReceiver) {
        if(this.deptDataList.some(item=>!item.receiveNickName||!item.receiveUserName)) {
          this.$message.error(this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.this_dept_receive_user`));
          return false;
        }
      }
      return validateValid;
    },
    init(formData,setting,reset){
      if (!reset) {
        this.setting = setting
      }
      let form = JSON.parse(JSON.stringify(this.formData))
      for (let item in setting) {
        let value = formData[setting[item]]
        if (value||reset) {
          form[item] = value
        }
      }
      this.formData = form
    },
    restData(nums,type,category){
      let printPaperType = category==='print'?'plain_paper':undefined
      return  {
        id: undefined,
        nums: nums,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
        type: type,
        category: category,
        printPaperType: printPaperType
      }
    },
    selectionBoxInit(label,selectedList,dataList,settings,treeList) {
      this.$refs.selectionBox.init(label,undefined,selectedList,dataList,settings,treeList)
    },
    userSearchInit(source,index,roleKey,multiple,selectList){
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(source,index,null,roleKey,multiple,selectList,undefined)
      })
    },
    userSelectHandle(source,index,userList){
      if ('deptDataList'=== source ) {
        this.$set(this[source][index],'receiveUserName',userList.userName)
        this.$set(this[source][index],'receiveNickName',userList.nickName)
        if (this[source][index].id) {
          updateModifyApplyDistribute(this[source][index])
        }
      }else {
        let category = source === 'trainPersonDataList'?'train':'print'
        if (userList&&userList.length>0) {
          for (let i=0;i<userList.length;i++) {
            let user = userList[i]
            let data = this.restData(1,'person',category)
            data.receiveUserName = user.userName
            data.receiveNickName = user.nickName
            data.receiveUserDeptId = user.dept.deptId
            data.receiveUserDept = user.dept.deptName
            this[source].push(data)
          }
        }
      }
    },
    async selectBoxHandle(label, index, selectedList, valueList) {
      if (label === 'trainDeptDataList') {
        for (const item of valueList) {
          let res = await getNumByDeptId(item.receiveUserDeptId)
          item.nums = res.data
        }
      } else if (label === 'trainCompanyDataList') {
        for (const item of valueList) {
          let res = await getNumByTenantId(item.receiveUserDeptId)
          item.nums = res.data
        }
      }
      this.$set(this, label, valueList)
    },
    handleDel(source,index){
      this[source].splice(index,1)
    },
    getDistributeList(){
      let distributeList = []
      let _this = this
      if (_this.formData.isDistribute===_this.yes) {
        if (_this.displayOne===undefined||_this.displayOne==='print'){
          if (_this.formData.distributeType==='company') {
            distributeList.push(..._this.companyDataList)
          }
          if (_this.formData.distributeType.includes('dept')) {
            distributeList.push(..._this.deptDataList)
          }
          if (_this.formData.distributeType.includes('person')) {
            distributeList.push(..._this.personDataList)
          }
        }
      }
      if (!_this.trainAlike && this.showPurview) {
        if (_this.displayOne===undefined||_this.displayOne==='purview') {
          if (_this.formData.trainType === 'company') {
            distributeList.push(..._this.trainCompanyDataList)
          }
          if (_this.formData.trainType.includes('dept')) {
            distributeList.push(..._this.trainDeptDataList)
          }
          if (_this.formData.trainType.includes('person')) {
            distributeList.push(..._this.trainPersonDataList)
          }
        }
      }else {
        _this.formData.trainType = _this.formData.distributeType
        let trainDistributeList = JSON.parse(JSON.stringify(distributeList))
        trainDistributeList.forEach(item=>{
          item.id = null
          item.category = 'train'
        })
        distributeList.push(...trainDistributeList)
      }
      return distributeList
    },
    async initDistributeGroup(docClass,setting,reset) {
      let _this = this
      let formData = {
        trainType: '',
        distributeType: 'person',
        isDistribute: 'N',
      }
      if (!reset) {
        _this.setting = setting
      }
      let distributeList = []
      let res = await distributeGroupByDocClass(docClass)
      res.data.forEach(item => {
        if (item.type === 'print') {
          formData.distributeType = item.scopeType
          if (item.itemList && item.itemList.length > 0) {
            formData.isDistribute = _this.yes
            distributeList.push(...item.itemList)
          }
        } else if (item.type === 'purview') {
          formData.trainType = item.scopeType
          if (item.itemList && item.itemList.length > 0) {
            distributeList.push(...item.itemList)
          }
        }
      })
      _this.formData = formData
      return distributeList
    }
  }
}
</script>
