<!-- 扩展字段对应关系
ext1: 所属部门
ext2: 订单号
ext3: 文件状态
ext4: 计划单号
ext5: 文件类别
ext6: 体系代码
ext7: 封面
ext8: 合同编号
ext9: 文件细分代码
ext10:组织
ext11:文件名称（英）
ext12:外部编号
ext13:项目/客户代码
ext14:修订码
ext15:事业部
-->
<template>
  <div class="document_change_add"
       v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file`) }}{{ $t(`doc.this_dept_new_add`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="procInstId"
                   @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus&&!batchStatus"
                   @click="selectPresetUser">
          {{ $t(`file_handle.change_select_people`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_cancel')&&procInstId&&workflowStatus&&!batchStatus"
                   type="danger"
                   @click="deleteForm"
                   v-dbClick>{{ $t(`file_handle.change_revoke`) }}
        </el-button>
        <el-button v-if="procInstId&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'transfer'})"
                   v-dbClick>
          {{ transferStatus ? $t(`file_handle.transfer_return`) : $t(`file_handle.transfer`) }}
        </el-button>
        <el-button v-if="procInstId&&!workflowStatus&&'doing'===formData.processStatus&&formData.createBy===userInfo.userName&&backFlowToOneStatus&&!batchStatus"
                   type="danger"
                   @click="showVerify({type:'backFlow'})"
                   v-dbClick>{{ $t(`file_handle.change_withdraw`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'turnDown'})"
                   type="danger">{{ $t(`file_handle.change_reject_to_preparer`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_generate_code')&&workflowStatus&&!batchStatus"
                   @click="shengchengbianhao()"
                   type="primary">{{ $t(`file_handle.change_generate_num`) }}
        </el-button>
        <!-- 【生成封面】、【签章生效】和【执行发布】一般出现在发布环节 -->
        <el-button v-if="nodeShow('publish_setup_time')&&workflowStatus&&!batchStatus"
                   @click="setupStartTime()"
                   type="primary">{{ $t(`doc.this_dept_select_effective_date`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_file_cover')&&workflowStatus&&!batchStatus"
                   @click="handleCoverEffective()"
                   type="primary">{{ $t(`file_handle.change_generate_cover`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_setup_time')&&workflowStatus&&!batchStatus"
                   @click="handleSignEffective()"
                   type="primary">{{ $t(`file_handle.change_signature_effc`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_publish_file')&&workflowStatus&&!batchStatus"
                   @click="showVerify({type:'publish'})"
                   type="primary">{{ $t(`file_handle.change_execute_release`) }}
        </el-button>
        <!-- 提交按钮在 非【执行发布】环节出现 -->
        <el-button v-if="!nodeShow('top_btn_publish_file') && workflowStatus&&!batchStatus&&isShowSubmit()"
                   type="primary"
                   @click="submitFormConfirm"
                   v-dbClick>
          {{ $t('doc.this_dept_annex') }}
        </el-button>
        <el-button v-if="editStatus"
                   type="primary"
                   @click="saveFormConfirm"
                   v-dbClick>{{
            $t(`doc.this_dept_save`)
          }}
        </el-button>
        <el-button v-if="isClose"
                   @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="procInstId"
             v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)"
                   name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)"
                   name="log"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_edit_record`)"
                   name="edit"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body"
         v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elForm"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_types`)"
                              prop="docClass">
                  <treeselect v-if="!(procInstId||formData.batchId)"
                              v-model.trim="formData.docClass"
                              :options="classLevelOptions"
                              :disable-branch-nodes="true"
                              :normalizer="normalizer"
                              :searchable="true"
                              :show-count="true"
                              :clearable="false"
                              :noOptionsText="$t(`doc.this_dept_no_data`)"
                              @select="selectDocClass"
                              :placeholder="$t(`doc.this_dept_select_type`)" />
                  <span v-else>{{ docClassData.className }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_type`) + `:`"
                              prop="changeType">
                  <span>{{ $t(`doc.this_dept_new_add`) }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`) + `:`"
                              prop="upVersionId">
                  <el-input v-if="editStatus"
                            v-model.trim="formData.upDocName"
                            :placeholder="$t(`doc.this_dept_pls_select`)"
                            readonly>
                    <el-button slot="append"
                               type="primary"
                               icon="el-icon-more"
                               @click="versionSearchInit('up')"></el-button>
                  </el-input>
                  <span v-else>{{ formData.upDocName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)"
                              prop="parentDocId">
                  <span>{{ formData.parentDocId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <!--文件名称-->
                <el-form-item :label="$t('dicts.form_control_filename')+':'"
                              prop="docName">
                  <el-input v-if="editStatus"
                            v-model="formData.docName"
                            :placeholder="$t(`doc.this_dept_insert_name`)"
                            clearable
                            :style="{ width: '100%' }"
                            maxlength="500"
                            show-word-limit
                            :disabled="disabled"></el-input>
                  <span v-else>{{ formData.docName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" "></el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)"
                              prop="docId">
                  <span style="line-height:34px">{{ formData.docId }}</span>
                  <el-button v-if="editStatus&&!(formData.classType===classTypeRecord&&!formData.parentDocId)&&nodeShow('page_oper_prepare_id')"
                             style="float: right;margin-right: 30px"
                             type="primary"
                             size="mini"
                             @click="prepareIdSelect('formData',formData.classType,formData.docClass,formData.parentDocId)">{{ $t(`doc.this_dept_select_pre_code`) }}
                  </el-button>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)"
                              prop="versionValue">
                  <el-input v-if="editStatus"
                            v-model="formData.versionValue"
                            :placeholder="$t(`doc.this_dept_insert_ver`)"
                            clearable
                            :style="{ width: '100%' }"
                            maxlength="50"
                            show-word-limit></el-input>
                  <span v-else>{{ formData.versionValue }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staff`)"
                              prop="userName">
                  <el-input v-if="editStatus&&!batchStatus"
                            style="width: 80%; margin-right: 10px"
                            readonly
                            v-model.trim="formData.nickName"
                            :placeholder="$t(`doc.this_dept_select`)">
                    <el-button slot="append"
                               icon="el-icon-search"
                               @click="handleSelect"></el-button>
                  </el-input>
                  <span v-else>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staffing_depts`)"
                              prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`"
                              prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" "
                              prop=" "></el-form-item>
              </el-col>
            </el-row>

            <dynamic-form ref="dynamic"
                          :formRuleList="formRuleList"
                          :formData="formData"
                          :editStatus="editStatus"
                          :deptOptions2="deptOptions2"
                          :dict="dict" />
            <!----------------------------------根据文件分类，编制时新增字段 ------------------------------------------------>

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_temp`)">
                  <fileUpload :editStatus="false"
                              v-model.trim="mobanwenjian"
                              :isShowTip="false"
                              :downStatus="true"
                              v-slot="{fileId,fileName,isOffice}">
                    <online-edit v-if="editStatus&&isOffice&&nodeShow('online_edit')"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="standardDocfileList.length>0?standardDocfileList[0].protoFileId:undefined"
                                 :handleMsg="messageListener">{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                  </fileUpload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_source`)+`:`"
                              prop="invokeId">
                  <span style="color: #385bb4; cursor: pointer"
                        @click="handleDeal(formData)">{{ formData.preChangeCode }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_change_ver`)+`:`"
                              prop="standardDocfileList">
                  <!-- <div class="fileuploadbzremark"> -->
                  <fileUpload :editStatus="editStatus"
                              v-model.trim="standardDocfileList"
                              :downStatus="nodeShow('page_oper_file_download')"
                              :coverDownStatus="nodeShow('cover_file_download')"
                              limit="1"
                              @input="fileUpdate('standardDocfileList')"
                              :fileType="['docx','doc','xls','xlsx','pdf','ppt','pptx']"
                              :isShowTip="false"
                              :isMode="nodeShow('page_oper_file_edit')"
                              v-slot="{fileId,fileName,isOffice,protoFileId}">
                    <online-edit v-if="isOffice&&nodeShow('online_edit')"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener">{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_revision')"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener"
                                 type="0"
                                 cbPath="/process/modifyApplyLink/online/update"
                                 :applyId="formData.id">{{$t(`dicts.flow_node_fun_list_online_revision`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_annotation')"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener"
                                 type="1"
                                 cbPath="/process/modifyApplyLink/online/update"
                                 :applyId="formData.id">{{$t(`dicts.flow_node_fun_list_online_annotation`)}}
                    </online-edit>
                  </fileUpload>
                  <!-- </div> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_ver_annex`)+`:`"
                              prop="appendixesfileList">
                  <div class="link-box bzlink-box" v-show="nodeShow('page_oper_file_download') && appendixesfileList && appendixesfileList.length>0">
                    <span style="color: #385bb4; margin-left: 10px; cursor: pointer"></span>
                    <span style="color: #385bb4; margin-left: 10px; cursor: pointer;" @click="handelefileLocalDownloadAll(appendixesfileList)" >
                      下载全部
                    </span>
                  </div>
                  <fileUpload :editStatus="editStatus"
                              v-model.trim="appendixesfileList"
                              :downStatus="nodeShow('page_oper_file_download')"
                              :pdfDownStatus="nodeShow('cover_file_download')"
                              :fileType="[]"
                              :isfileType="false"
                              :limit="100"
                              @input="fileUpdate('appendixesfileList')"
                              :isShowTip="false"
                              v-slot="{fileId,fileName,isOffice,protoFileId}">
                    <online-edit v-if="isOffice&&nodeShow('online_edit')"
                                 cbType="saveAppendix"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener">{{$t(`dicts.flow_node_fun_list_online_edit`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_revision')"
                                 cbType="saveAppendix"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener"
                                 type="0"
                                 cbPath="/process/modifyApplyLink/online/update"
                                 :applyId="formData.id">{{$t(`dicts.flow_node_fun_list_online_revision`)}}
                    </online-edit>
                    <online-edit v-if="isOffice&&nodeShow('online_annotation')"
                                 cbType="saveAppendix"
                                 :fileId="fileId"
                                 :fileName="fileName"
                                 :protoFileId="protoFileId"
                                 :handleMsg="messageListener"
                                 type="1"
                                 cbPath="/process/modifyApplyLink/online/update"
                                 :applyId="formData.id">{{$t(`dicts.flow_node_fun_list_online_annotation`)}}
                    </online-edit>
                  </fileUpload>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="formData.classType===classTypeForeign">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`) + `:`"
                              prop="compliance">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.compliance"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_compliancy`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`)+`:`"
                              prop="changeReason">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.changeReason"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`)+`:`"
                              prop="content">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.content"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert_change_content`)"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            maxlength="1000"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`"
                              prop="remark">
                  <el-input :class="editStatus?'':'fujian'"
                            :readonly="!editStatus"
                            resize="none"
                            v-model="formData.remark"
                            type="textarea"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"
                            show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"
                    v-if="(trains&&trains.length>0)||(workflowStatus&&nodeShow('page_oper_add_train_record'))">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`"
                              prop="remark">
                  <fileUpload v-model.trim="trains"
                              :editStatus="workflowStatus&&nodeShow('page_oper_add_train_record')"
                              limit="100"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'train')" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <!-- 文件推送配置 -->
      <FilePushConfig
        v-if="nodeShow('file_push')"
        :doc-class-id="formData.docClass"
        :data-type="formData.dataType"
        :isReadOnly="!editStatus"
        :data="formData.filePush"
        :required="getValidateByCode('file_push')&&editStatus"
        ref="filePushConfig"
      />

      <div class="news-card"
           v-if="nodeShow('whether_customer_record')||formData.whetherCustomer">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.customer_record`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormTrain"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.whether_customer_records`)+`:`"
                              prop="whetherCustomer">
                  <el-radio-group v-if="workflowStatus&&customerStatus&&!batchStatus"
                                  v-model.trim="formData.whetherCustomer">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}
                    </el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.whetherCustomer" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"
                    v-if="formData.whetherCustomer===yes">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_cover`)+`:`"
                              prop="customerCover">
                  <fileUpload v-model.trim="cover"
                              :editStatus="workflowStatus&&nodeShow('page_oper_add_customer_cover')"
                              :down-status="workflowStatus&&nodeShow('add_customer_record')"
                              limit="1"
                              :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts']"
                              :isShowTip="false"
                              @input="(list)=>handelConfirm(list,'cover')" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_approval_record`)+`:`"
                              prop="customerRecord">
                  <customer-box ref="customerBox"
                                :applyId="formData.id"
                                :versionValue="formData.versionValue"
                                :editStatus="workflowStatus&&nodeShow('add_customer_record')&&formData.whetherCustomer!==no"></customer-box>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card"
           v-if="nodeShow('whether_train')||!!formData.yNTrain">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_train`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="elFormTrain"
                   :model="formData"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="150px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_or_not`)+`:`"
                              prop="yNTrain">
                  <el-radio-group v-if="workflowStatus&&trainStatus&&!batchStatus"
                                  v-model.trim="formData.yNTrain">
                    <el-radio v-for="(item, index) in dict.type.sys_yes_no"
                              :key="index"
                              :label="item.value">{{ dictLanguage(item) }}
                    </el-radio>
                  </el-radio-group>
                  <dict-tag v-else
                            :options="dict.type.sys_yes_no"
                            :value="formData.yNTrain" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <!--分发-->
      <distribute-box v-if="!batchStatus"
                      :editStatus="editStatus"
                      :workflowStatus="workflowStatus"
                      :setDeptReceiver="nodeShow('set_dept_receiver')"
                      :deptList="deptList"
                      :deptOptions="deptOptions"
                      :companyList="companyList"
                      :distributeList="distributeList"
                      :nodeDetailList="nodeDetailList"
                      ref="distributeBox"></distribute-box>

      <div class="el-card news-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs v-model.trim="activeIndex"
                   class="news-tabs">
            <el-tab-pane name="1"
                         v-if="classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note :editStatus="editStatus"
                         v-show="activeIndex  == '1'"
                         :dataList="formData.noteLinks"
                         ref="linkNote"
                         :dataType="formData.dataType"></link-note>
            </el-tab-pane>
            <el-tab-pane name="1"
                         v-else>
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record ref="linkRecord"
                           :dataList="formData.recordLinks"
                           :editStatus="editStatus"
                           v-show="activeIndex  == '1'"
                           :dataType="formData.dataType"></link-record>
            </el-tab-pane>
            <el-tab-pane name="2">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file :editStatus="editStatus"
                         v-show="activeIndex  == '2'"
                         :dataList="formData.docLinks"
                         ref="linkFile"
                         :dataType="formData.dataType"></link-file>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <div class="news-card"
           v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!batchStatus">
        <div class="card-head">
          <div class="cell-title">{{ submitLabel }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form ref="validateForm"
                   :model="formSubmit"
                   :rules="rules"
                   size="medium"
                   label-position="right"
                   label-width="200px">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_conclusion`)"
                              prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"
                                  @input="commentItemSelect">
                    <el-radio v-for="dict in passoptions"
                              :key="dict.value"
                              :label="dict.value">{{ dict.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)">
                  <el-input v-model="formSubmit.summary"
                            type="textarea"
                            :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comment`)"
                            maxlength="200"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                            :style="{ width: '100%' }"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId="procInstId"></workflow-logs>
    </div>
    <div v-show="activeName==='edit'">
      <EditRecord :applyId="formData.id"
                  :isVisible="activeName==='edit'"
                  :docFile="standardDocfileList"
                  :appendixes="appendixesfileList"></EditRecord>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog :title="$t(`doc.this_dept_select_next`)"
               v-if="dialogVisible"
               :visible.sync="dialogVisible"
               width="60%"
               append-to-body
               v-loading=flowStepLoading
               :close-on-click-modal="false"
               :close-on-press-escape="false">
      <processcode ref="prochild"
                   :selected="nodeShow('default_selected')"
                   :userListStatus="!nodeShow('user_list')"
                   :order="order"
                   :searchQuery="searchQuery"
                   :hideNodeCode="hideNodeCode"
                   :defaultStaff="defaultStaff"
                   :pListData="pListData"
                   :isSummary="isSummary"
                   :hiddenUserBox="nodeShow('wgcdx')"></processcode>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary"
                   v-dbClick
                   @click="showVerify({type:'flowSubmit'})">{{ $t(`doc.this_dept_annex`) }}</el-button>
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer v-if="monitorDrawerVisible"
                    ref="monitorDrawer"></monitor-drawer>
    <doc-id-box ref="docIdBox"
                v-if="shenchenbianhao"
                @setDocId="setDocId"
                @setRecordDocId="setRecordDocId"></doc-id-box>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow"
                 :id="viewId"
                 ref="viewRef"
                 @close="close"></as-pre-view>
    <el-drawer :wrapperClosable="false"
               :visible.sync="dealDrawerShow"
               :append-to-body="true"
               direction="rtl"
               size="90%"
               :with-header="false"
               :show-close="false"
               modal-append-to-body
               :destroy-on-close="true">
      <div style="width:100%; height:100%;overflow: hidden">
        <workflow-router ref="dealDrawer"
                         @closeDrawer="handleCloseChange"></workflow-router>
      </div>
    </el-drawer>
    <prepare-doc-id ref="prepareDocId"
                    @selectHandle="handleSubmitDocId"></prepare-doc-id>
    <version-list :dataType="formData.dataType"
                  :classTypeList="[classTypeDoc]"
                  ref="versionList"
                  @selectHandle="versionSelectHandle"></version-list>
    <preset-user ref="presetUser"
                 @selectHandle="selectHandlePresetUser"></preset-user>
    <user-list ref="userList"
               @selectHandle="handleSubmitUser"></user-list>

    <el-dialog :title="$t(`doc.this_dept_tip`)"
               v-if="pushDialogVisible"
               :visible.sync="pushDialogVisible"
               width="35%"
               v-loading="loading"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               append-to-body>
      <div>
        <span>{{ $t(`doc.this_dept_set_effective_date`) }}：
          <el-date-picker v-model="setupTime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          type="date"
                          :placeholder="$t(`doc.this_dept_select_effective_date`)">
          </el-date-picker>
        </span>
      </div>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="pushDialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary"
                   @click="pushCommit">{{ $t(`doc.this_dept_confirm`) }}</el-button>
      </div>
    </el-dialog>
    <transfer-flow ref="transferFlow"
                   @close="close"></transfer-flow>
    <identity-verify
      :visible.sync="verifyVisible"
      :business-params="currentParams"
      @business-execute="handleBusinessExecute"
      @verify-cancel="handleCancel"
    />
  </div>
</template>
<script>
import historicalVersion from './add_import/historicalVersion'
import {fileLocalDownload} from '@/api/commmon/file'
import {settingDocClassId, settingDocClassList} from '@/api/file_settings/type_settings'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import processcode from '@/views/workflowList/processcode/index.vue'
import WorkflowLogs from '@views/workflowList/workflowLogs/index.vue'
import {
  addModifyApply,
  getInfoByBpmnId,
  queryModifyApplyTrain,
  updateModifyApply
} from '@/api/file_processing/modifiyApply'
import {coverEffective, signEffective} from '@/api/file_processing/fileSignature'
import {getByUpDocClassAndBizType} from '@/api/setting/docClassFlow'
import {getInfo} from '@/api/setting/docClassFlowNodeDetail'
import {isExistByName, modifyApplyLinklist} from '@/api/document_account/standard'
import {
  backFlowToOne,
  getExtAttributeModel,
  getRecordbyPorcInstId,
  getRedirectDefId,
  getStartActdef,
  procInstInfoAndStatus,
  workflowbacktostart,
  workflowprocesskey
} from '@/api/my_business/workflow'
// PDF本地文件预览
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import DealDrawer from '@/components/DealDrawer/index.vue'
import WorkflowRouter from '@views/workflowList/workflowRouter.vue'
import DocIdBox from '@views/workflowList/addWorkflow/add_import/docIdBox.vue'
import {checkPermi} from '@/utils/permission'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import PrepareDocId from '@views/workflowList/addWorkflow/add_import/prepareDocId.vue'
import IdentityVerify from './add_import/identityVerify.vue'
import {listPresetUser} from '@/api/setting/presetUser'
import {selectStatusByDocId} from '@/api/my_business/workflowApplyLog'
import {checkNoIsExist} from '@/api/system/standard'
import {listWorkflowLog} from '@/api/my_business/workflowLog'
import {getNextVersion} from '@/api/setting/versionRuleDetail'
import {listDept, treeselect} from '@/api/system/dept'
import {getCompanyList, getDivisionLeader, getDocManagersByDeptId, getLeader} from '@/api/system/user'
import {listModifyApplyDistribute} from '@/api/my_business/modifyApplyDistribute'
import DistributeBox from '@views/workflowList/addWorkflow/add_import/distributeBox.vue'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import {updateModifyApplyTrainList} from '@/api/my_business/modifyApplyTrain'
import {parseTime} from '@/utils/ruoyi'
import {validateByUserName} from '@/api/system/userSignature'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import {checkAnnotation} from '../../../api/process/baseApply'
import {validatePrepareId} from '../../../api/setting/prepareId'
import {getFormRuleRecursive} from '@/api/setting/formRule'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import CustomerBox from '@views/workflowList/addWorkflow/add_import/customerBox.vue'
import {getCodeRuleDetailByDocClass} from '@/api/setting/codeRule'
import RelationPlanNo from '@/components/RelationPlanNo.vue'
import {listDistributeGroupDetail} from '@/api/setting/distributeGroupDetail'
import OnlineEdit from '@viewscomponents/onlineEdit/index.vue'
import EditRecord from '@views/workflowList/addWorkflow/add_import/editRecord.vue'
import DynamicForm from '@/views/components/DynamicForm.vue'
import {releaseStatus} from '@/api/system/fileEditingDetailLog'
import mixin from "@/layout/mixin/Commmon.js"
import FilePushConfig from '@/components/FilePushConfig'

export default {
  dicts: ['sys_yes_no', 'tenant_list', 'file_status', 'series_code', 'face_option', 'institutional', 'file_type'],
  components: {
    IdentityVerify,
    EditRecord,
    OnlineEdit,
    RelationPlanNo,
    CustomerBox,
    TransferFlow,
    LinkNote,
    UserList,
    DistributeBox,
    PrepareDocId,
    VersionList,
    PresetUser,
    DocIdBox,
    WorkflowRouter,
    DealDrawer,
    LinkRecord,
    LinkFile,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs,
    DynamicForm,
    FilePushConfig
  },
  name: 'Add_doc',
  mixins: [mixin],
  props: ['dataType', 'data'],
  data () {
    let validateFileUrl = (rule, value, callback) => {
      if (this.standardDocfileList.length < 1) {
        //我控制了FileList 长度代表文件个数
        callback(new Error(this.$t(`doc.this_dept_to_upload_file`)))
      } else {
        callback()
      }
    }
    let validateDocName = (rule, val, callback) => {
      if (this.editStatus) {
        if (this.formData.docName != undefined && this.formData.docName.trim() != '') {
          isExistByName({ docName: this.formData.docName, applyId: this.formData.id }).then((response) => {
            if (response.data == true) {
              return callback(new Error(this.$t(`doc.this_dept_file_name_exist`)))
            } else {
              callback()
            }
          })
        } else {
          callback(new Error(this.$t(`doc.this_dept_file_name_not_null`)))
        }
      } else {
        callback()
      }
    }
    return {
      currentType: '',
      currentParams: null,
      verifyVisible: false,
      mark: undefined,
      classTypeList: [],
      applyType: 'add_doc',
      dataListIndex: undefined,
      order: 0,
      watchStatus: false,
      leader: undefined,
      trains: [],
      cover: [],
      hideNodeCode: [],
      yes: 'Y',
      no: 'N',
      distributeList: [],
      deptList: [],
      companyList: [],
      deptOptions: [],
      deptOptions2: [],
      defaultStaff: undefined,
      classTypeRecordMN: true,
      backFlowToOneStatus: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      searchQuery: {},
      shlkPath: process.env.VUE_APP_SHLK_PATH,
      dealDrawerShow: false,
      submitLabel: undefined,
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) }
      ],
      formSubmit: { summary: '', actionType: '', pass: undefined },
      pButton: 'ADD',
      isSummary: false,
      title: this.$t(`doc.this_dept_add_file`),
      project: { id: '', name: '' },
      activeName: 'info',
      nodeDetail: {},
      nodeDetailList: [],
      procDefKey: undefined,
      processData: {},
      viewId: '',
      userInfo: JSON.parse(sessionStorage.getItem('USER_INFO')),
      viewShow: false,
      active: 4,
      activeIndex: '1',
      uploadType: ['doc', 'docx', 'ppt', 'xlsx', 'pdf', 'jpg', 'png'],
      monitorDrawerVisible: false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      isLast: true,
      quantity: [],
      formRuleList: [],
      formData: {
        invokeType: '',
        whetherCustomer: undefined,
        distributeList: [],
        classType: undefined,
        projectId: undefined,
        projectName: undefined,
        docClass: undefined,
        changeType: undefined,
        docName: '',
        versionValue: '',
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: this.$t(`doc.initial_release`),
        content: this.$t(`doc.initial_release`),
        trainDept: undefined,
        dataType: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: { fileName: '' }, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        preStandardDoc: '',
        preAppendixes: '',
        preChangeCode: undefined,
        step: undefined,
        presetUserList: [],
        batch: undefined,
        yNDistribute: '',
        distributeType: '',
        yNTrain: '',
        trainType: '',
        custodyDeptId: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        setupTime: undefined,
        factorys: null,
        internalDocId: undefined,
        ext1: undefined,
        ext2: undefined,
        ext3: undefined,
        ext4: undefined,
        ext5: undefined,
        ext6: undefined,
        ext7: undefined,
        ext8: undefined,
        ext9: undefined,
        ext10: undefined,
        ext11: undefined,
        ext12: undefined,
        ext13: undefined,
        ext14: undefined,
        ext15: undefined,
        ext16: undefined,
        ext17: undefined,
        ext18: undefined,
        ext19: undefined,
        ext20: undefined,
        batchId: undefined
      },
      rules: {
        whetherCustomer: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`) + this.$t(`doc.whether_customer_records`) + '!',
            trigger: 'blur,change'
          }
        ],
        fileEffectiveDate: [
          { required: true, message: this.$t(`doc.this_dept_select_effective_date`), trigger: 'blur,change' }
        ],
        yNTrain: [
          { required: true, message: this.$t(`doc.this_dept_select_is_train`), trigger: 'blur,change' }
        ],
        yNDistribute: [
          { required: true, message: this.$t(`doc.this_dept_select_is_distribute`), trigger: 'blur,change' }
        ],
        projectId: [
          { required: true, message: this.$t(`doc.this_dept_select_project`), trigger: 'blur,change' }
        ],
        pass: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: 'blur,change' }
        ],
        docClass: [
          {
            required: true,
            message: this.$t(`doc.this_dept_preparer`),
            trigger: 'change'
          }
        ],
        upVersionId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_select_up_file`),
            trigger: 'blur,change'
          }
        ],
        docName: [
          {
            max: 1000,
            message: this.$t(`doc.this_dept_file_name_more_long`)
          },
          { validator: validateDocName, trigger: 'blur' }
        ],
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: 'blur,change' },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_reason_more_long`)
          }
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: 'blur,change' },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_content_more_long`)
          }
        ],
        standardDocfileList: [
          {
            required: true,
            validator: validateFileUrl,
            trigger: ['blur', 'change', 'input']
          }
        ],
        internalDocId: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`) + this.$t(`doc.this_dept_internal_file_number`),
            trigger: 'blur,change'
          }
        ],
        ext1: [{
          required: true,
          message: this.$t('dicts.form_control_ext1') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext2: [{
          required: true,
          message: this.$t('dicts.form_control_ext2') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext3: [{
          required: true,
          message: this.$t('dicts.form_control_ext3') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext4: [{
          required: true,
          message: this.$t('dicts.form_control_ext4') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext5: [{
          required: true,
          message: this.$t('dicts.form_control_ext5') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext6: [{
          required: true,
          message: this.$t('dicts.form_control_ext6') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext7: [{
          required: true,
          message: this.$t('dicts.form_control_ext7') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext8: [{
          required: true,
          message: this.$t('dicts.form_control_ext8') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext9: [{
          required: true,
          message: this.$t('dicts.form_control_ext9') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext10: [{
          required: true,
          message: this.$t('dicts.form_control_ext10') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext11: [{
          required: true,
          message: this.$t('dicts.form_control_ext11') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext12: [{
          required: true,
          message: this.$t('dicts.form_control_ext12') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext13: [{
          required: true,
          message: this.$t('dicts.form_control_ext13') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext14: [{
          required: true,
          message: this.$t('dicts.form_control_ext14') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext15: [{
          required: true,
          message: this.$t('dicts.form_control_ext15') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext16: [{
          required: true,
          message: this.$t('dicts.form_control_ext1') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext17: [{
          required: true,
          message: this.$t('dicts.form_control_ext1') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext18: [{
          required: true,
          message: this.$t('dicts.form_control_ext1') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext19: [{
          required: true,
          message: this.$t('dicts.form_control_ext1') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }],
        ext20: [{
          required: false,
          message: this.$t('dicts.form_control_ext1') + this.$t('doc.this_dept_not_null'),
          trigger: 'blur,change'
        }]
      },
      kuozhanshujuBool: {},
      kuozhanshuju: {},
      field117Action: '',
      action: '/dms-admin/process/file/local_upload',
      appendixesfileList: [],
      standardDocfileList: [],
      classLevelOptions: [],
      docClassList: [],
      summary: '',
      pListData: {},
      editStatus: false,
      trainStatus: false,
      customerStatus: false,
      transferStatus: false,
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading: false,
      procInstId: undefined,
      pushDialogVisible: false,
      setupTime: undefined,
      isShowPart: false,
      isCustomerShow: false,
      isDeviceShow: false,
      isCodeAndTypeShow: false,
      docClassData: {},
      theFirstStatus: false,
      codeRuleDetail: [],
      batch: undefined,
      internalDocIdShow: false,
      distributeSetting: { trainType: 'trainType', distributeType: 'distributeType', isDistribute: 'yNDistribute' },
      isClose: true //关闭按钮控制
    }
  },
  computed: {
    batchStatus () {
      return !!this.formData.batchId
    }
  },
  watch: {
    'formData.docId' (val, oldVal) {
      let _this = this
      let recordLinks = _this.$refs.linkRecord
      if (_this.editStatus && recordLinks && recordLinks.dataList.length > 0) {
        _this.formData.recordLinks = []
        _this.$message.warning(_this.$t(`doc.this_dept_master_code_change_record_clear`))
      }
    },
    'formData.docClass' (val, oldVal) {
      let _this = this
      if (val !== oldVal && oldVal && _this.formData.docId) {
        _this.formData.docId = ''
      }
      if (val) {
        _this.getNodeDetailInfo()
        if (!_this.procInstId) {
          _this.getNextVersion()
          if (!_this.batchStatus) {
            _this.$refs.distributeBox.initDistributeGroup(val, _this.distributeSetting).then(res => {
              _this.distributeList = res
            })
          }
          _this.getByUpDocClassAndBizType(val)
        }
        _this.getDocClassById(val)
      } else {
        this.mobanwenjian = []
      }
    },
    'formData.classType' (val) {
      if (val !== this.classTypeRecord) {
        this.formData.upVersionId = undefined
        this.formData.upDocName = undefined
        this.formData.parentDocId = undefined
      }
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    }
  },
  async created () {
    let response = await this.getConfigKey('record.doc.type')
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true'
    let response1 = await this.getConfigKey('back_flow_to_one')
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true'

    const invokerForm = this.$route.query.invokerForm
    if (invokerForm && invokerForm == 'systemMsg') {
      this.isClose = false
    }
  },
  mounted () {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    showVerify(invokeFrom) {
      this.currentParams = invokeFrom
      // 判断是否需要身份验证
      if (this.nodeShow('authentication')) {
        this.verifyVisible = true
      } else {
        // 不需要验证时直接执行业务逻辑
        this.handleBusinessExecute(this.currentParams)
      }
    },

    // 执行业务逻辑
    handleBusinessExecute(params) {
      switch(params.type) {
        case 'flowSubmit':
          this.handleWorkflowSubmit(params)
          break
        case 'transfer':
          this.transferForm(params)
          break
        case 'backFlow':
          this.handleBackFlowToOne(params)
          break
        case 'publish':
          checkAnnotation({ id: this.formData.id, batchId:''}).then(async (res) => {
            if(!res.data || res.data.length == 0){
              this.handlePublish(params)
            }else{
              this.$message.warning(this.$t(`doc.file_exist_record`))
            }
          })
          break
        case 'turnDown':
          this.handelpbohuiqicaoren(params)
          break
      }
    },
    handleCancel() {
      // 取消验证后的处理
      this.verifyVisible = false
    },
    init (row) {
      let _this = this
      _this.theFirstStatus = true
      _this.getDeptList()
      _this.getCompanyDataList()
      _this.loading = true
      _this.order = row.order ? row.order : 0
      _this.batch = row.batch
      _this.mark = row.mark
      _this.classTypeList = row.classTypeList
      //是否编辑模式
      _this.$nextTick(async () => {
        if (row && (!!row.procInstId || !isNaN(row.dataListIndex))) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          if (row.batchId) {
            _this.setFileList(row)
            _this.formData = row
            _this.dataListIndex = row.dataListIndex
            _this.getByDocClass(_this.formData.docClass)
            // _this.initDistributeBox(_this.formData.distributeList)
            _this.getModifyApplyTrain('cover', 'cover')
            _this.getModifyApplyTrain('trains', 'train')
          } else {
            _this.getDetail(_this.procInstId)
          }
        } else {
          _this.workflowStatus = true
          _this.editStatus = true
          _this.rest(row.userInfo)
          if (row.batchId) {
            _this.formData.batchId = row.batchId
            if (row.docClass) {
              _this.formData.docClass = row.docClass
              _this.formData.whetherCustomer = row.whetherCustomer
              _this.formData.yNTrain = row.yNTrain
              _this.formData.applyTime = row.applyTime
              _this.getByDocClass(row.docClass)
            }
          } else {
            _this.getSettingDocClassTreeseList()
          }
          // _this.getWorkflowprocesskey();
          // _this.$nextTick(()=> {
          //   if (_this.$refs.distributeBox) {
          //     _this.$refs.distributeBox.init(_this.formData, _this.distributeSetting)
          //   }
          // })
          _this.loading = false
        }
      })

      setTimeout(() => {
        if (_this.formData.factorys == null || _this.formData.factorys == '' || _this.formData.factorys == undefined) {
          let tenantItem = this.dict.type.tenant_list.find(v => window.location.href.includes(v.raw.remark))
          if (tenantItem && tenantItem.value) {
            _this.formData.factorys = tenantItem.value
          }
        }
      }, 1000)
    },

    setFileList (formData) {
      let _this = this
      if (formData.standardDoc) {
        if (formData.standardDoc.fileId) {
          _this.standardDocfileList = [
            {
              applyId: formData.id,
              name: formData.standardDoc.docName,
              url: formData.standardDoc.fileId,
              protoFileId: formData.standardDoc.protoFileId
            }
          ]
        }
      }
      if (formData.appendixes != null) {
        formData.appendixes.forEach((element) => {
          _this.appendixesfileList.push({
            name: element.docName,
            url: element.fileId,
            protoFileId: element.protoFileId
          })
        })
      }
    },
    async getByUpDocClassAndBizType (docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass, _this.pButton)
      _this.procDefKey = data && data.flowKey ? data.flowKey : ''
      _this.getWorkflowprocesskey()
    },
    getDetail (procInstId) {
      let _this = this
      _this.detailLoading = true
      getInfoByBpmnId(procInstId).then(async (res) => {
        let formData = res.data
        let res1 = await modifyApplyLinklist({ applyId: formData.id, linkType: 'REF_DOC' })
        formData.docLinks = res1.rows
        let res2 = await modifyApplyLinklist({ applyId: formData.id, linkType: 'RECORD' })
        formData.recordLinks = res2.rows
        let res3 = await listPresetUser({ bizId: formData.id })
        formData.presetUserList = res3.data
        let res4 = await modifyApplyLinklist({ applyId: formData.id, linkType: 'NOTE' })
        formData.noteLinks = res4.rows
        _this.setFileList(formData)
        _this.project = {
          id: formData.projectId,
          name: formData.projectName
        }
        formData.filePush.pushFile=formData.ext8==='Y'

        _this.docClass = formData.docClass
        _this.formData = formData
        _this.getDistributeList(formData.id)
        _this.getModifyApplyTrain('cover', 'cover')
        _this.getModifyApplyTrain('trains', 'train')
        //查询物料是否展示
        this.getByDocClass(formData.docClass)
      }).finally(() => {
        _this.detailLoading = false
      })
    },
    getModifyApplyTrain (source, type) {
      let _this = this
      let query = {
        applyId: _this.formData.id,
        type: type,
        isDeleted: 0
      }
      let list = []
      queryModifyApplyTrain(query).then(res => {
        if (res.data) {
          res.data.forEach(item => {
            list.push({
              url: item.fileIds,
              name: item.files[0].fileName
            })
          })
        }
        _this.$set(_this, source, list)
      })
    },
    handelConfirm (list, type) {
      let trains = []
      list.forEach(item => {
        trains.push({
          fileIds: item.url,
          fileName: item.name,
          userName: this.userInfo.userName,
          deptId: this.userInfo.deptId,
          docId: this.formData.docId,
          applyId: this.formData.id,
          type: type
        })
      })
      let data = {
        applyId: this.formData.id,
        type: type,
        trains: trains
      }
      updateModifyApplyTrainList(data)
    },
    getDistributeList (applyId) {
      let _this = this
      listModifyApplyDistribute({ applyId: applyId }).then(res => {
        _this.initDistributeBox(res.data)
      })
    },
    initDistributeBox (distributeList) {
      let _this = this
      _this.distributeList = distributeList
      _this.$nextTick(() => {
        if (_this.$refs.distributeBox) {
          _this.$refs.distributeBox.init(_this.formData, _this.distributeSetting)
        }
      })
    },
    getNextVersion () {
      getNextVersion({ version: '', docClass: this.formData.docClass }).then(res => {
        if (res.data) {
          this.formData.versionValue = res.data
        }
      })
    },
    rest (userInfo) {
      let _this = this
      _this.activeName = 'info'
      let formData = {
        invokeType: '',
        whetherCustomer: undefined,
        projectId: undefined,
        projectName: undefined,
        docClass: undefined,
        dataType: _this.dataType,
        changeType: _this.pButton,
        docName: '',
        versionValue: '',
        docId: undefined,
        // deptId: _this.userInfo.dept.deptId,
        // 部门对应的组织架构一级部门ID
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: this.$t(`doc.initial_release`),
        content: this.$t(`doc.initial_release`),
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: { fileName: '' }, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        preStandardDoc: '',
        preAppendixes: '',
        preChangeCode: undefined,
        upVersionId: undefined,
        upDocName: undefined,
        classType: 'DOC',
        presetUserList: [],
        step: undefined,
        batch: undefined,
        yNDistribute: 'N',
        distributeType: '',
        distributeList: [],
        yNTrain: undefined,
        trainType: '',
        custodyDeptId: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        setupTime: undefined,
        ext1: undefined,
        ext2: undefined,
        ext3: undefined,
        ext4: undefined,
        ext5: undefined,
        ext6: undefined,
        ext7: undefined,
        ext8: undefined,
        ext9: undefined,
        ext10: undefined,
        ext11: undefined,
        ext12: undefined,
        ext13: undefined,
        ext14: undefined,
        ext15: undefined,
        ext16: undefined,
        ext17: undefined,
        ext18: undefined,
        ext19: undefined,
        ext20: undefined,
        batchId: undefined
      }
      if (userInfo) {
        formData.deptId = userInfo.deptId
        formData.deptName = userInfo.deptName
        formData.userName = userInfo.userName
        formData.nickName = userInfo.nickName
      }
      _this.formData = formData
    },
    async initStatus () {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.$t(`file_handle.change_auditing`)
      if (_this.nodeShow('top_btn_generate_code')) {
        _this.getCodeRuleDetailList()
      }
      if (!_this.batchStatus) {
        if (_this.nodeShow('whether_train')) {
          let funCondition = _this.nodeFunCondition('whether_train')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.yNTrain) {
              _this.formData.yNTrain = funCondition.limitValue
            }
            _this.trainStatus = funCondition.validate
          }
        }
        if (_this.nodeShow('whether_customer_record')) {
          let funCondition = _this.nodeFunCondition('whether_customer_record')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.whetherCustomer) {
              _this.formData.whetherCustomer = funCondition.limitValue
            }
            _this.customerStatus = funCondition.validate
          }
        }
        _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
      }
    },
    getCodeRuleDetailList () {
      let _this = this
      let query = {
        docClass: _this.formData.docClass,
        ruleTypeList: ['FORM', 'DICT']
      }
      getCodeRuleDetailByDocClass(query).then(res => {
        let codeRuleDetail = []
        for (let item of res.data) {
          let keys = item.ruleValue.split('@')
          if ('classCode' !== keys[0]) {
            if (keys.length > 1) {
              codeRuleDetail.push(keys[1])
            } else {
              if ('deptCode' === keys[0]) {
                codeRuleDetail.push('deptId')
              } else {
                codeRuleDetail.push(keys[0])
              }
            }
          }
        }
        _this.codeRuleDetail = codeRuleDetail
        //增加监听
        codeRuleDetail.forEach(item => {
          _this.$watch('formData.' + item, (newVal, oldVal) => {
            if (newVal !== oldVal && _this.formData.docId) {
              _this.watchStatus = true
            }
          })
        })
      })
    },
    procInstInfoAndStatus (procInstId) {
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
        } else {
          _this.pListData = { procInstId: procInstId }
          this.workflowStatus = false
        }
        _this.getExtAttributeModel()
      })
    },
    nodeShow (code) {
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      } else {
        return false
      }
    },
    nodeFunCondition (code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      } else {
        return undefined
      }
    },

    // 根据code获取validate值
    getValidateByCode(code) {
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item => item.code === code)
      if (nodeDetail && nodeDetail.funCondition) {
        try {
          const funCondition = JSON.parse(nodeDetail.funCondition)
          return funCondition.validate
        } catch (error) {
          console.error('解析funCondition失败:', error)
          return undefined
        }
      } else {
        return undefined
      }
    },
    getWorkflowprocesskey () {
      let _this = this
      // _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data
            this.getExtAttributeModel()
          })
        })
      } else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
      }
    },
    getExtAttributeModel () {
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId && curActDefId) {
        _this.getNodeDetailInfo()
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {
          console.log("扩展属性====>", res);
          let kuozhanshujuBool = []
          let kuozhanshuju = []
          res.data.forEach(item=>{
            if (item.objType==='Boolean') {
              kuozhanshujuBool[item.objKey] = item.objValue
            } else {
              kuozhanshuju[item.objKey] = item.objValue
            }
          })
          _this.kuozhanshujuBool = kuozhanshujuBool;
          _this.kuozhanshuju = kuozhanshuju;
          this.$refs.dynamic.setKuozhanshuju(res.data.length>0?_this.kuozhanshuju:{})
        }).finally(()=>{
          _this.loading = false
        });
      } else {
        _this.kuozhanshujuBool = {}
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModelBool (val) {
      if (this.kuozhanshujuBool && this.kuozhanshujuBool !== {}) {
        let obj = this.kuozhanshujuBool[val]
        return !!obj && obj === 'true'
      } else {
        return false
      }
    },
    attributeModel (val) {
      return this.kuozhanshuju[val]
    },
    getNodeDetailInfo () {
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData && curActDefId && _this.formData.docClass) {
        getInfo(_this.formData.docClass, _this.pButton, curActDefId).then(res => {
          let nodeDetail = {}
          res.data.forEach(item => {
            nodeDetail[item.code] = true
          })

          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    close () {
      this.viewShow = false
      // 判断浏览器中有没有invokerForm并且值为system则调用window.close关闭当前页面
      const invokerForm = this.$route.query.invokerForm
      if (invokerForm && invokerForm == 'system') {
        window.close() // 如果窗口是通过脚本打开的，尝试关闭
        /*if (window.opener) {
          window.close(); // 如果窗口是通过脚本打开的，尝试关闭
        } else {
          this.$message.warning('请手动关闭浏览器')
        }*/
      } else {
        this.$emit('close')
      }
    },
    handlePreview (id) {
      this.viewId = id
      this.$refs.viewRef.handleOpenView(id)
      this.viewShow = true
    },
    handelefileLocalDownload (id, name) {
      fileLocalDownload(id).then((res) => {
        //console.log("file", res);
        this.saveFile(res, name)
      })
    },
    saveFile (data, name) {
      try {
        const blobUrl = window.URL.createObjectURL(data)
        // console.log('bo',blobUrl);
        const a = document.createElement('a')
        a.style.display = 'none'
        a.download = name
        a.href = blobUrl
        a.click()
      } catch (e) {
        alert('保存文件出错')
      }
    },
    saveFormConfirm () {
      let _this = this
      if (_this.watchStatus) {
        _this.watchStatus = false
        _this.$confirm(_this.$t(`file_handle.watch_status_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: 'warning'
        })
          .then(() => {
            _this.saveForm()
          })
      } else {
        _this.saveForm()
      }
    },
    //不需要验证必填的保存
    async saveForm (type) {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
        return
      }
      if (await _this.validateCoincide()) {
        return
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      if (_this.editStatus) {
        formData.recordStatus = 'draft'
        if (_this.batchStatus) {
          formData.notUpdateTitle = true
        }
      } else {
        formData.onlyEdit = true
      }
      if(type){
        formData.onlyEdit = false
      }
      //变更操作（新增、修订、作废）的提交和保存的类型区分统一使用recordStatus字段（draft：草稿, doing:进行中，done:已完成，deleted:作废）
      formData.docLinks = []
      if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
        formData.docLinks = _this.$refs.linkFile.dataList
      }
      formData.noteLinks = []
      if (_this.$refs.linkNote && _this.$refs.linkNote.dataList) {
        formData.noteLinks = _this.$refs.linkNote.dataList
      }
      if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
        formData.recordLinks = _this.$refs.linkRecord.dataList
      }
      if (_this.standardDocfileList != '') {
        formData.standardDoc = {
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url,
          protoFileId: _this.standardDocfileList[0].protoFileId,
          status: 1
        }
      } else {
        formData.standardDoc = { docName: '' }
      }
      formData.appendixes = []
      _this.appendixesfileList.forEach((element) => {
        formData.appendixes.push({
          fileId: element.url,
          docName: element.name,
          status: 1,
          protoFileId: element.protoFileId
        })
      })
      formData.remarkDoc = []
      if (_this.$refs.distributeBox) {
        formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          formData[boxSetting[item]] = boxFormData[item]
        }
      }
      if (_this.batchStatus) {
        _this.loading = false
        this.$emit('close', formData, _this.dataListIndex)
        return
      }
      if (formData.id) {
        let res = await updateModifyApply(formData)
        if (res.code === 200) {
          _this.$message({
            message: _this.$t(`file_handle.change_save_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.loading = false
            }
          })
        }
      } else {
        let wf_receivers = []
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        })
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.docName.trim(),
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId
          },
          order: _this.pListData.actDefOrder,
          review: false,
          type: _this.applyType
        }
        formData.editStatus = _this.editStatus
        let res = await addModifyApply(formData)
        if (res.code === 200) {
          _this.formData.id = res.data.businessKey
          _this.procInstId = res.data.procInstId
          _this.procInstInfoAndStatus(res.data.procInstId)
          _this.$message({
            message: _this.$t(`file_handle.change_save_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.loading = false
            }
          })
        }
      }
    },
    handleBackFlowToOne () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`)
          }
        }
      }).then(({ value }) => {
        _this.loading = true
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.docName.trim(),
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName
              },
              bizType: _this.pButton,
              review: true,
              applyStatus: false,
              status: 'draft',
              type: _this.applyType,
              mark: _this.mark,
              order: 0
            }
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break
            }
          }
          _this.close(true)
        })
      })
    },
    deleteForm () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`)
          }
        }
      }).then(({ value }) => {
        _this.loading = true
        let formData = {
          id: _this.formData.id,
          bpmClientInputModel: {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procInstId: _this.pListData.procInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_curComment: value
            },
            order: _this.order,
            type: _this.applyType,
            mark: _this.mark,
            review: true,
            applyStatus: false
          },
          recordStatus: 'cancel',
          editStatus: false
        }
        addModifyApply(formData).then((res) => {
          if (res.code === 200) {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`))
            this.close(true)
          }
        })
      })
    },
    transferForm () {
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData, _this.formData.id, _this.applyType, _this.order, _this.pButton,_this.nodeShow('authentication'))
    },
    async submitFormConfirm() {
      let _this = this
      if (_this.watchStatus) {
        _this.watchStatus = false
        _this.$confirm(_this.$t(`file_handle.watch_status_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: 'warning'
        })
          .then(() => {
            _this.submitForm()
          })
      } else {
        _this.submitForm()
      }
    },
    // 提交
    async submitForm () {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`))
        return
      }
      let dialogVisible = true
      //审核
      if (_this.nodeShow('shenhe') || _this.nodeShow('pizhun')) {
        if (_this.formSubmit.pass === undefined) {
          _this.$modal.msgError(_this.submitLabel + _this.$t(`file_handle.change_result_not_null`))
          return
        }
        // 验证是否填写了审核意见
        if (!_this.formSubmit.pass && _this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`) + _this.submitLabel + _this.$t(`doc.this_dept_comments`))
          return
        }
      }
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')) {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(_this.formData.docClass, _this.pButton, _this.formData.presetUserList, nodeCode)
        if (bool) {
          return true
        }
      }

      //培训记录
      if (_this.nodeShow('page_oper_add_train_record')) {
        let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.trains && _this.trains.length > 0)) {
            _this.$modal.msgError(_this.$t(`doc.this_dept_pls_upload_train_file`))
            return true
          }
        }
      }
      //客户封面
      if (_this.nodeShow('page_oper_add_customer_cover') && _this.formData.whetherCustomer === _this.yes && _this.formSubmit.pass !== false) {
        let funCondition = _this.nodeFunCondition('page_oper_add_customer_cover')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.cover && _this.cover.length > 0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`) + _this.$t(`doc.customer_cover`) + '！')
            return true
          }
        }
      }
      //客户记录
      if (_this.nodeShow('add_customer_record') && _this.formData.whetherCustomer === _this.yes && _this.formSubmit.pass !== false) {
        let funCondition = _this.nodeFunCondition('add_customer_record')
        if (!funCondition || (funCondition && funCondition.validate)) {
          if (!(_this.$refs.customerBox && _this.$refs.customerBox.dataList && _this.$refs.customerBox.dataList.length > 0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`) + _this.$t(`doc.customer_record`) + '！')
            return true
          }
        }
      }
      //签名
      if (_this.nodeShow('log_title')) {
        // 如果是编制环节校验编制人
        if (_this.editStatus) {
          let res = await validateByUserName({ userCode: _this.formData.userName })
          if (!res.data) {
            _this.$modal.msgError(_this.$t(`doc.user_organizer_validate`))
            return true
          }
        }
        let res = await validateByUserName({ userCode: _this.userInfo.userName })
        if (!res.data) {
          _this.$modal.msgError(_this.$t(`doc.user_signature_validate`))
          return true
        }
      }
      if (!!_this.$refs['elForm']) {
        let valid = await _this.$refs['elForm'].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs['validateForm']) {
        let validateValid = await _this.$refs['validateForm'].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      // 验证分发和培训表单
      if (!!_this.$refs['distributeBox']) {
        let distributeForm = _this.$refs['distributeBox'].$refs['elFormDistribute']
        if (!!distributeForm) {
          let validateValid = await distributeForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      let trainForm = _this.$refs['elFormTrain']
      if (!!trainForm) {
        let validateValid = await trainForm.validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }

      if (!!_this.$refs['distributeBox']) {
        let valid = await _this.$refs['distributeBox'].validateForm()
        if (!valid) {
          dialogVisible = false
          return
        }
      }
      //验证生成编号
      if (_this.nodeShow('top_btn_generate_code')) {
        if (_this.formData.docId == null &&_this.formSubmit.pass === 'pass') {
          _this.$modal.alert(_this.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`))
          return false
        }
        let res = await checkNoIsExist({ newNo: _this.formData.docId, busId: _this.formData.id })
        if (res.data) {
          _this.$modal.msgError(_this.formData.docId + _this.$t(`doc.this_dept_doc_code_exist`))
          return false
        }
      }
      // if (await _this.validateCoincide()){
      //   return
      // }
      let {valid, data} = await this.$refs.filePushConfig.validateAndGetData();
      //校验
      if (await _this.validate() || !valid) {
        return
      }
      _this.formData.filePush=data
      _this.loading = true
      _this.formData.docLinks = []
      if (_this.$refs.linkFile && _this.$refs.linkFile.dataList) {
        _this.formData.docLinks = _this.$refs.linkFile.dataList
      }
      _this.formData.noteLinks = []
      if (_this.$refs.linkNote && _this.$refs.linkNote.dataList) {
        _this.formData.noteLinks = _this.$refs.linkNote.dataList
      }
      if (_this.$refs.linkRecord && _this.$refs.linkRecord.dataList) {
        _this.formData.recordLinks = _this.$refs.linkRecord.dataList
        //验证记录文件编号
        if (!_this.classTypeRecordMN && _this.formData.recordLinks.some(item => !item.docId)) {
          _this.$modal.alert(_this.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`))
          return false
        }
      }
      if (_this.standardDocfileList != '') {
        this.formData.standardDoc = {
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url,
          protoFileId: _this.standardDocfileList[0].protoFileId
        }
      }
      this.formData.appendixes = []
      this.appendixesfileList.forEach((element) => {
        this.formData.appendixes.push({
          fileId: element.url,
          docName: element.name,
          status: 1,
          protoFileId: element.protoFileId
        })
      })
      this.formData.remarkDoc = []
      if (_this.$refs.distributeBox) {
        _this.formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          _this.formData[boxSetting[item]] = boxFormData[item]
        }
      }
      _this.jointReviewRedirect()
      //流程提交选项干预
      await _this.setPresetUserList()
      this.dialogVisible = true
      this.loading = false
    },
    async setPresetUserList () {
      let _this = this
      if (_this.nodeShow('preset_countersign')) {
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0 && funCondition.groupId) {
          let users = []
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item => {
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 部门分管领导
      if (_this.nodeShow('next_set_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getLeader(_this.userInfo.userName, _this.formData.deptId)
            user = res.data
          } else {
            let res = await getLeader(_this.userInfo.userName, _this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      if (_this.nodeShow('next_set_division_leader')) {
        let funCondition = _this.nodeFunCondition('next_set_division_leader')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getDivisionLeader(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDivisionLeader(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = [{
              userName: user.userName,
              nickName: user.nickName,
              deptId: user.deptId,
              deptName: user.dept.deptName
            }]
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      //设置流程待选任意为文控
      if (_this.nodeShow('set_flow_select_list')) {
        let funCondition = _this.nodeFunCondition('set_flow_select_list')
        if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length > 0) {
          let user = undefined
          if (funCondition.validate) {
            let res = await getDocManagersByDeptId(_this.formData.deptId)
            user = res.data
          } else {
            let res = await getDocManagersByDeptId(_this.userInfo.deptId)
            user = res.data
          }
          if (user) {
            let users = []
            user.forEach(item => {
              users.push({
                userName: item.userName,
                nickName: item.nickName,
                deptId: item.deptId,
                deptName: item.dept.deptName
              })
            })
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          } else {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.findIndex(item => item.nodeCode === nodeCode)
              if (preset > -1) {
                _this.formData.presetUserList.splice(preset, 1)
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (_this.formData.presetUserList.length > 0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if (_this.nodeShow('cdxmd') && _this.batch) {
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition && funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({
            batch: _this.batch,
            nextDefId: _this.pListData.curActDefId,
            havaDetail: true
          })
          let nodeCode = ''
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({
              userName: item.sender,
              nickName: item.nickName,
              deptId: item.senderDeptId,
              deptName: item.deptName
            })
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode === nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.isTrain = _this.formData.yNTrain
      _this.searchQuery.isCustomer = _this.formData.whetherCustomer
      _this.searchQuery.ext5 = _this.formData.ext5
      _this.searchQuery.pass = _this.formSubmit.pass
      _this.searchQuery.batch = !!_this.batch
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')) {
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition && funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围  过滤出没有预选人员的环节
          hideNodeCode = funCondition.nodeCode.filter(item => !defaultStaff.some(dpu => item === dpu.nodeCode && dpu.users && JSON.parse(dpu.users).length > 0))
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode && hideNodeCode.length === length) {
            hideNodeCode = hideNodeCode.filter(code => !funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue) && (length - hideNodeCode.length) > limitValue) {
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length - hideNodeCode.length) <= limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode && hideNodeCode.length !== length && hideNodeCode.length === 0) {
            defaultStaff.forEach(item => {
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
          }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect () {
      let _this = this
      if (_this.nodeShow('hscdx') || _this.nodeShow('wgcdx')) {
        getRecordbyPorcInstId(_this.procInstId).then(res => {
          //会审只剩最后一个环节
          if (res.data.length === 1) {
            let query = {
              docClass: _this.formData.docClass,
              bizType: _this.pButton,
              code: 'cdxmd',
              batch: _this.batch
            }
            getRedirectDefId(query).then(res1 => {
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition && funCondition.nodeCode && funCondition.nodeCode.length === 1) {
                  let next = res1.data.find(item => item.nextDefId === funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          } else {
            _this.isLast = false
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect (val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if (val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validateCoincide () {
      let _this = this
      let validate = false
      if (_this.editStatus && _this.nodeShow('page_oper_prepare_id') && _this.formData.docId) {
        let res = await validatePrepareId({ businessId: _this.formData.id, docId: _this.formData.docId })
        if (res.data) {
          _this.$modal.msgError(_this.formData.docId + _this.$t(`doc.this_dept_doc_code_exist`))
          validate = true
        }
      }
      return validate
    },
    async validate () {
      let _this = this
      let validate = false
      // 验证文件状态
      if (_this.editStatus && _this.formData.classType === _this.classTypeRecord) {
        let res = await selectStatusByDocId({ versionId: _this.formData.upVersionId, notInDraft: true })
        if (res.data != 0) {
          _this.$modal.msgWarning(res.msg)
          return true
        }
      }
      return false
    },
    async handleCoverEffective () {
      let self = this
      // 验证设置生效时间
      if (self.nodeShow('publish_setup_time') && self.formData.setupTime == null) {
        self.$modal.alert(self.$t(`doc.this_dept_set_effective_date`))
        return false
      }
      //不用重复生成封面
      /*if ('C' === self.formData.isSignature) {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }*/
      //验证生成编号
      if (self.nodeShow('top_btn_generate_code')) {
        if (self.formData.docId == null) {
          self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`))
          return false
        }
        let res = await checkNoIsExist({ newNo: self.formData.docId, busId: self.formData.id })
        if (res.data) {
          self.$modal.msgError(self.formData.docId + self.$t(`doc.this_dept_doc_code_exist`))
          return false
        }
        if (self.formData.recordLinks.some(item => !item.docId)) {
          self.$modal.alert(self.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`))
          return false
        }
      }

      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`))
      self.loading = true
      coverEffective(self.formData.id, 'cover').then((res) => {
        if (res.code === 200) {
          self.$modal.msgSuccess(self.$t(`doc.this_dept_operation_succ`))
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'C'
        } else {
          self.$modal.msgError(res.msg)
        }
        self.loading = false
      })
    },
    // 签章生效
    async handleSignEffective () {
      let self = this
      if ('E' === self.formData.isSignature) {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`))
        return false
      }
      //生成封面
      if (self.nodeShow('top_btn_file_cover')) {
        if (self.formData.isSignature !== 'C') {
          self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_generate_cover`))
          return false
        }
      } else {
        //设置了生成封面 就不用验证了
        if (self.nodeShow('top_btn_generate_code')) {
          if (self.formData.docId == null) {
            self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`))
            return false
          }
          let res = await checkNoIsExist({ newNo: self.formData.docId, busId: self.formData.id })
          if (res.data) {
            self.$modal.msgError(self.formData.docId + self.$t(`doc.this_dept_doc_code_exist`))
            return false
          }
          if (self.formData.recordLinks.some(item => !item.docId)) {
            self.$modal.alert(self.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`))
            return false
          }
        }
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`))
      self.loading = true
      signEffective(self.formData.id, 'effective').then((res) => {
        if (res.code === 200) {
          self.$modal.alert(self.$t(`file_handle.change_signature_text3`))
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'E'
        } else {
          self.$modal.alert(res.msg)
        }
        self.loading = false
      })
    },
    setupStartTime () {
      let _this = this
      _this.setupTime = _this.formData.setupTime
      if (!_this.setupTime) {
        _this.setupTime = parseTime(new Date())
      }
      _this.pushDialogVisible = true
    },
    // 发布二次确认
    pushCommit () {
      let _this = this
      if (!_this.setupTime) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_set_effective_date`))
        return
      }
      if (_this.formData.setupTime !== _this.setupTime) {
        let form = {
          id: _this.formData.id,
          setupTime: _this.setupTime,
          isSignature: 'N',
          onlyEdit: true
        }
        updateModifyApply(form).then((res) => {
          if (res.code === 200) {
            _this.formData.isSignature = form.isSignature
            _this.formData.setupTime = form.setupTime
            _this.pushDialogVisible = false
          }
        })
      } else {
        _this.pushDialogVisible = false
      }
    },
    // 执行发布（推送文件到文件台账）
    async handlePublish () {
      let self = this
      if (self.nodeShow('top_btn_setup_time') && self.formData.isSignature !== 'E') {
        self.$modal.alert(self.$t(`doc.this_dept_pls_effect_signature_relase`))
        return false
      }
      if (self.nodeShow('set_dept_receiver')) {
        if (!!self.$refs['distributeBox']) {
          let valid = await self.$refs['distributeBox'].validateForm()
          if (!valid) {
            return
          }
        }
      }
      self.$modal.confirm(self.$t(`doc.this_dept_confirm_release`) + '《' + self.formData.docName + '》，' + self.$t(`doc.this_dept_file_effect_push_account`)).then(function () {
        // 执行发布的签章
        self.loading = true
        signEffective(self.formData.id, 'publish').then((res) => {
          if (res.code === 200) {
            self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text6`))
            self.formData.encryptFileId = res.data
            self.formData.isSignature = 'Y'
            self.pushDialogVisible = false
            self.handleWorkflowSubmit('publish')
          } else {
            self.$modal.alert(res.msg)
          }
          self.loading = false
        })
      })
    },
    //提交表单和流程数据
    async handleWorkflowSubmit (invokeFrom) {
      let _this = this
      if (await _this.validateCoincide()) {
        return
      }
      console.log(typeof (invokeFrom))
      let formData = JSON.parse(JSON.stringify(_this.formData))
      let nextData = undefined
      if (typeof (invokeFrom) == 'object') {
        nextData = _this.$refs.prochild.handleWorkflowSubmit()
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        nextData = {
          wf_nextActDefId: 'end',
          wf_nextActDefName: '结束',
          direction: true,
          wf_receivers: [],
          fields: {}
        }
      }
      if (!nextData) {
        return
      }
      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: nextData.wf_nextActDefName,
            fields: nextData.fields
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction
        }
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {
          model: {
            wf_procTitle: _this.formData.docName.trim(),
            wf_nextActDefId: nextData.wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: nextData.wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: nextData.wf_nextActDefName,
            fields: nextData.fields
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.applyType,
          mark: _this.mark,
          direction: nextData.direction
        }
      }
      if (_this.nodeShow('log_title')) {
        let funCondition = _this.nodeFunCondition('log_title')
        if (funCondition && funCondition.limitValue) {
          formData.bpmClientInputModel.title = funCondition.limitValue
        }
      }
      if (nextData.wf_nextActDefId === 'end') {
        //办结
        formData.recordStatus = 'done'
        formData.bpmClientInputModel.jointReview = false
      } else {
        //进行中
        formData.recordStatus = 'doing'
        formData.bpmClientInputModel.jointReview = nextData.multi
      }
      if (_this.nodeShow('hscdx') || _this.nodeShow('wgcdx')) {
        formData.bpmClientInputModel.batch = _this.batch
        formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
        formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
        if (_this.redirectOrder) {
          formData.bpmClientInputModel.order = _this.redirectOrder
        }
        formData.bpmClientInputModel.isLast = _this.isLast
      }
      formData.editStatus = _this.editStatus
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader') || _this.nodeShow('set_flow_select_list') || _this.nodeShow('next_set_division_leader')
      formData.customerEdit = _this.nodeShow('whether_customer_record')
      if (_this.nodeShow('online_edit')) {
        formData.bpmClientInputModel.onlineType = 'online_edit'
      } else if (_this.nodeShow('online_revision')) {
        formData.bpmClientInputModel.onlineType = 'online_revision'
      } else if (_this.nodeShow('online_annotation')) {
        formData.bpmClientInputModel.onlineType = 'online_annotation'
      }
      if(_this.nodeShow('file_range_preview_edit') || _this.nodeShow('distribution_edit')){
        formData.editStatus = true
        formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        console.log(formData.distributeList)
      }
      //return
      addModifyApply(formData).then((res) => {
        if (res.code === 200) {
          _this.$message({
            message: _this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false
              _this.close()
            }
          })
        }
      })
    },
    getDocClassById (val) {
      let _this = this
      settingDocClassId(val).then((response) => {
        _this.docClassData = response.data
        if (response.data && response.data.fileList != null) {
          let mobanwenjian = []
          response.data.fileList.forEach(item => {
            mobanwenjian.push({
              name: item.fileName,
              url: item.id
            })
          })
          this.mobanwenjian = mobanwenjian
        } else {
          this.mobanwenjian = []
        }
      })
    },
    async getSettingDocClassTreeseList () {
      let _this = this
      let query = {
        classStatus: '1',
        dataType: _this.formData.dataType,
        classTypeList: _this.classTypeList,
        openPurview: true
      }
      await settingDocClassList(query).then(
        (res) => {
          _this.docClassList = JSON.parse(JSON.stringify(res.rows))
          let docClassList = res.rows
          //文件类型权限设置 菜单权限设置
          docClassList = docClassList.filter(item => {
            return item.purview && checkPermi(['doc:class:' + item.permission])
          })
          _this.classLevelOptions = _this.handleTree(docClassList, 'id', 'parentClassId')
        }
      )
    },
    /** 转换菜单数据结构 */
    normalizer (node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children
      }
    },
    normalizerDept (node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      }
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal (data) {
      const result = []
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums
          })
          let child = data.children
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i])
            }
          }
        }
        loop(item)
      })
      return result
    },
    handleMonitor () {
      this.monitorDrawerVisible = true
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.procInstId)
      })
    },
    handelpbohuiqicaoren () {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return _this.$t(`file_handle.change_fill_reject_text`)
          }
        }
      })
        .then(async ({ value }) => {
          _this.loading = true
          // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
          let backarr = {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_businessKey: _this.formData.id,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_procTitle: _this.formData.docName.trim(),
              wf_procDefId: _this.pListData.procDefId,
              wf_curComment: value,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_receivers: [
                {
                  receiveUserId: _this.formData.userName,
                  receiveUserOrgId: _this.formData.deptId
                }
              ]
            },
            order: 0,
            review: true,
            applyStatus: false,
            type: _this.applyType,
            step: _this.attributeModel('step'),
            bizType: _this.pButton
          }
          workflowbacktostart(backarr).then((response) => {
            this.$modal.msgSuccess(_this.$t(`file_handle.change_reject_succ`))
            _this.close()
          }).finally(() => {
            _this.loading = false
          })
        })
    },
    async shengchengbianhao () {
      let _this = this
      if (_this.editStatus && !_this.formData.docId) {
        if (_this.codeRuleDetail) {
          let bool = false
          for (let item of _this.codeRuleDetail) {
            if (_this.rules[item] && _this.rules[item][0].required && !_this.formData[item]) {
              _this.$modal.msgWarning(_this.rules[item][0].message)
              bool = true
            }
          }
          if (bool) {
            return
          }
        }
        await _this.saveForm()
      }
      _this.shenchenbianhao = true
      let fileIds = _this.formData.recordLinks && _this.formData.recordLinks.length > 0 ? _this.formData.recordLinks.map(item => item.fileId) : null
      _this.$nextTick(() => {
        _this.$refs.docIdBox.init(_this.formData.id, fileIds)
      })
    },
    setDocId (docId) {
      let _this = this
      if (_this.formData.docId !== docId) {
        _this.formData.docId = docId
        _this.setSignatureStatus()
      }
    },
    setRecordDocId (recordLinks) {
      let _this = this
      recordLinks.forEach((element, i) => {
        _this.formData.recordLinks.forEach((val, ix) => {
          if (val.fileId === element.busId) {
            _this.formData.recordLinks[ix].docId = element.newNo
          }
        })
      })
    },
    prepareIdSelect (source, codeType, docClass, parentDocId) {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.prepareDocId.init(source, null, _this.formData.userName, _this.formData.dataType, codeType, docClass, parentDocId, null, _this.oldDocId)
      })
    },
    handleSubmitDocId (source, index, data) {
      let _this = this
      if (_this.formData.docId !== data.docId) {
        _this.$set(_this.formData, 'docId', data.docId)
        _this.$set(_this.formData, 'docClass', data.docClass)
        _this.$set(_this.formData, 'classType', data.codeType)
      }
    },
    versionSearchInit (source) {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.versionList.init(source, null, false)
      })
    },
    versionSelectHandle (source, index, data) {
      if (source === 'up') {
        this.formData.upVersionId = data.versionId
        this.formData.upDocName = data.docName
        this.formData.parentDocId = data.docId
        this.$refs.elForm.validateField('upVersionId')
      }
    },
    selectDocClass (node) {
      this.formData.classType = node.classType
      this.formData.docClass = node.id
      this.formData.dataType = node.dataType
      this.$refs.elForm.validateField('docClass')
      //查询物料、工厂的字段是否开启
      this.getByDocClass(node.id)
    },
    getByDocClass (docClass) {
      let _this = this
      _this.loading = true
      getFormRuleRecursive(docClass).then(res => {
        let formRuleList = []
        if (res.data && res.data.ruleDetails) {
          let data = JSON.parse(res.data.ruleDetails)
          _this.formRuleList = data
          _this.loading = false
        }
      }).finally(err => {
        _this.loading = false
      })
    },
    initDefaultValue (value, defaultValue) {
      let _this = this
      if (defaultValue) {
        let key = defaultValue.split('@')
        if (key.length > 1) {
          _this.$set(_this.formData, value, _this.formData[key[1]].toString())
        } else {
          _this.$set(_this.formData, value, defaultValue)
        }
      }
    },
    selectPresetUser () {
      let _this = this
      _this.$nextTick(() => {
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null, null, _this.formData.docClass, _this.pButton, _this.formData.presetUserList, nodeCode)
      })
    },
    selectHandlePresetUser (source, index, data) {
      let _this = this
      _this.$set(_this.formData, 'presetUserList', data)
    },
    handleCloseChange () {
      this.dealDrawerShow = false
    },
    handleDeal (formData) {
      let _this = this
      _this.dealDrawerShow = true
      _this.$nextTick(() => {
        _this.$refs.dealDrawer.init({ type: formData.invokeType, preChangeCode: formData.invokeId })
      })
    },
    fileUpdate (field) {
      let _this = this
      //文件重新上传 需要重新签章
      _this.setSignatureStatus()
      _this.$refs.elForm.validateField(field)
    },
    setSignatureStatus () {
      let _this = this
      if (_this.formData.id && _this.formData.isSignature !== 'N') {
        _this.formData.isSignature = 'N'
        updateModifyApply({
          id: _this.formData.id,
          isSignature: _this.formData.isSignature,
          onlyEdit: true
        })
      }
    },
    sortDataById (data) {
      // 按id排序函数
      function sortById (a, b) {
        return parseInt(a.weight) - parseInt(b.weight)
      }

      // 递归排序子级
      function sortChildren (node) {
        if (node.children && node.children.length > 0) {
          node.children.sort(sortById)
          node.children.forEach(child => {
            sortChildren(child)
          })
        }
      }

      // 初始排序
      data.sort(sortById)
      // 对每个节点递归排序子级
      data.forEach(node => {
        sortChildren(node)
      })
      return data
    },
    getDeptList () {
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, 'deptId')
      })
      let self = this
      treeselect({ status: 0 }).then(response => {
        let data = self.sortDataById(response.data)
        this.deptOptions2 = data
      })
    },
    getCompanyDataList () {
      let _this = this
      getCompanyList({}).then(res => {
        _this.companyList = res.data
      })
    },
    restData (nums, type, category) {
      return {
        id: undefined,
        nums: nums,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
        type: type,
        category: category
      }
    },
    handleSelect () {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.userList.init(null, null, null)
      })
    },
    handleSubmitUser (source, index, user) {
      this.formData.nickName = user.nickName
      this.formData.userName = user.userName
      this.formData.deptId = user.dept.deptId
      this.formData.deptName = user.dept.deptName
    },
    //唯一序号、是否显示
    showStatistics (index, bool) {
      if (bool) {
        if (!this.quantity.includes(index)) {
          this.quantity.push(index)
        }
      }
      return bool
    },
    getDictLabel (dictValue, dictType) {
      const dictItem = this.dict.type[dictType].find(item => item.value === dictValue)
      return dictItem ? this.dictLanguage(dictItem) : dictValue
    },
    //从消息待办跳转来的
    isShowSubmit () {
      let invokerFormMsg = this.$route.query.invokerForm
      //当_this.procDefKey为空，并且来源为我的消息
      if ((this.procDefKey == null || this.procDefKey == undefined || this.procDefKey == '') && invokerFormMsg != null && invokerFormMsg == 'systemMsg') {
        return false
      }
      return true
    },
    messageListener (event) {
      let _this = this
      console.log('收到子页面的消息:', event)
      if (event && event.data && event.data.text) {
        let data = JSON.parse(event.data.text)
        console.log('收到子页面的消息:', data)
        if (data.ntkoData) {
          let value = data.ntkoData.FunctionArgs
          if (data.ntkoData.parentExecutionFunction === 'savaFile' && value.length<=3) {
            console.log('保存主文件')
            _this.standardDocfileList = [
              {
                url: value[0],
                name: value[1],
                protoFileId: value[2]
              }
            ]
          } else if (data.ntkoData.parentExecutionFunction === 'saveAppendix' && value.length<=3) {
            console.log('保存附件')
            _this.appendixesfileList.forEach(item => {
              if (item.protoFileId === value[2]) {
                item.url = value[0]
                item.name = value[1]
              }
            })
          }
          if((data.ntkoData.parentExecutionFunction === 'savaFile' || data.ntkoData.parentExecutionFunction === 'saveAppendix') && value.length<=3){
            _this.saveForm('edit')
          }
          if(value.length>3){
            console.log("关闭")
            _this.closeEdit(value[1])
          }
        }
      }
    },
    closeEdit(data){
      releaseStatus(data).then((response) => {
         console.log(response)
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style>
body {
  background: #fff;
  font-size: 14px;
  color: #333;
  margin: 0;
  padding: 0;
  min-width: 1200px;
}

@media screen and (max-width: 1200px) {
  .el-drawer {
    overflow-x: auto;
    overflow-x: auto !important;
    width: 100% !important;
  }

  body .el-drawer .el-drawer__header {
    min-width: 1200px;
  }

  body .el-drawer .el-drawer__body {
    min-width: 1200px;
  }

  .phpage .el-drawer .el-drawer__header {
    min-width: 100px;
  }

  .phpage .el-drawer .el-drawer__body {
    min-width: 100px;
  }
}

.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}
</style>
