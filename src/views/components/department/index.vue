<template>
  <div class="maincss">
    <div class="title-box">
      <div class="draw-title">
        {{ title }}
      </div>
      <el-form
        :model="formSubmit"
        ref="formSubmit"
        class="mt20"
        :rules="formrules"
        label-width="130px"
        v-if="!loading"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <div>待选部门</div>
            <el-tree
              :data="userList"
              :props="userProps"
              @node-click="userNodeClick"
              default-expand-all
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
            </el-tree>
          </el-col>
          <el-col :span="12">
            <div>已选部门</div>
            <div
              class="wordbox"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
              <ul>
                <li v-for="(item, index) in receiveUserList" :key="index">
                  <div class="inli">
                    <i class="el-icon-s-custom"></i>
                    <span>{{ item.deptName }}</span>
                    <i class="el-icon-close" v-if="item.nums==undefined" @click="removeData(item)"></i>
                  </div>
                </li>
              </ul>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import { listDept, listForDist } from "@/api/system/dept";
export default {
  components: {},
  props: ["from_data", "to_data"],
  data() {
    return {
      processList: [],
      userProps: {
        children: "children",
        label: "deptName",
      },
      userList: [],
      receiveUserList: [],
      loading: true,
      nextData: {},
      procDefKey: "",
      actionType: "",
    };
  },
  created() {
    this.getOptionsList();
    this.getChange();
  },
  watch: {
    from_data: {
      handler(newValue, oldValue) {
        this.getOptionsList();
      },
      deep: true,
    },
    to_data: {
      handler(newValue, oldValue) {
        this.getChange();
      },
      deep: true,
    },
    'receiveUserList'(val){
      console.log(val);
    }
  },
  mounted() {},
  methods: {
    getOptionsList() {
      //console.log("this.from_data", this.from_data);
      this.userList = this.from_data;
      this.loading = false;
    },
    getChange() {
      this.receiveUserList = this.to_data;
      console.log("this.toData", this.to_data);
    },
    //点击成员 导入可选成员
    userNodeClick(val) {
      console.log(this.receiveUserList);
      let arr = this.receiveUserList.filter((x) => x.deptId === val.deptId);
      if (arr.length <= 0 && val.children == undefined) {
        this.receiveUserList.push(val);
      }
    },
    //移除成员
    removeData(item) {
      var arr = [];
      this.receiveUserList.forEach((element) => {
        if (element.deptId != item.deptId) {
          arr.push(element);
        }
      });
      this.receiveUserList = arr;
      this.getListDept(this.receiveUserList);
    },
    getListDept(params) {
      listForDist({
        status: 0,
      }).then((response) => {
        var res = [];
        if (params != undefined && params != "" && params != []) {
          res = this.arrayRepeat(params, response.data);
        } else {
          res = response.data;
        }
        this.from_data = this.handleTree(res, "deptId");
      });
    },

    /**
     * 两个数组对象去复
     * @param {*} array1
     * @param {*} array2
     */
    arrayRepeat(array1, array2) {
      var result = [];
      for (var i = 0; i < array2.length; i++) {
        var obj = array2[i];
        var num = obj.deptId;
        var isExist = false;
        for (var j = 0; j < array1.length; j++) {
          var aj = array1[j];
          var n = aj.deptId;
          if (n === num) {
            isExist = true;
            break;
          }
        }
        if (!isExist) {
          result.push(obj);
        }
      }
      return result;
    },
  },
};
</script>
