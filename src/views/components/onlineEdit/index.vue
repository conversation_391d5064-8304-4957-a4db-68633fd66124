<template>
  <span
    v-if="isOffice(fileName)"
    style="color: #385bb4; cursor: pointer; margin-left: 10px"
    @click="handleOnlineEditing()"
  ><slot></slot></span>
  <span
    v-else
    style="margin-left: 10px"
  ><slot></slot></span>
</template>

<script>

import { openViewFileToEdit } from '@/utils/documentView'
import { checkFileEditingStatus, checkBeforeEdit } from '@/api/system/fileEditingDetailLog'

export default {
  name: 'OnlineEdit',
  props: {
    fileId: {
      type: String,
      default: undefined
    },
    fileName: {
      type: String,
      default: undefined
    },
    cbType: {
      type: String,
      default: 'savaFile'
    },
    cbPath: {
      type: String,
      default: undefined
    },
    applyId: {
      type: String,
      default: undefined
    },
    handleMsg: {
      type: Function,
      default: undefined
    },
    type: {
      type: String,
      default: undefined
    },
    protoFileId:{
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      workflowMessageCbAdded: false,
      userInfo: JSON.parse(sessionStorage.getItem('USER_INFO')),
    }
  },
  mounted() {
  },
  activated() {
    this.setupMessageListener()
  },
  created() {
    this.setupMessageListener()
  },
  deactivated() {
    this.removeMessageListener()
  },
  beforeDestroy() {
    this.removeMessageListener()
  },
  methods: {
    isOffice(name){
      let fileType = ['docx','doc','xls','xlsx','ppt','pptx','pdf'];
      if (name.lastIndexOf(".") > -1) {
        let fileExtension = name.slice(name.lastIndexOf(".") + 1).toLowerCase();
        return fileType.includes(fileExtension)
      }else {
        return false
      }
    },
    //建立addEventListener事件
    setupMessageListener() {
      if (!this.workflowMessageCbAdded && this.handleMsg) {
        window.addEventListener('message', this.handleMsg)
        this.workflowMessageCbAdded = true
      }
    },
    //销毁addEventListener事件
    removeMessageListener() {
      console.log(2)
      window.removeEventListener('message', this.handleMsg)
      this.workflowMessageCbAdded = false
    },
    handleOnlineEditing() {
      let webUserName = undefined
      if(this.userInfo){
        webUserName = this.userInfo.userName+'('+this.userInfo.nickName+')'
      }
      let extParam = {
        cbType: this.cbType,
        cbPath: this.cbPath,
        applyId: this.applyId,
        type: this.type,
        protoFileId: this.protoFileId?this.protoFileId:this.fileId,
        webUserName: webUserName
      }
      this.openViewFileToEditBefore(this.fileName, this.fileId, extParam, this.type!==undefined)
    },

    /**
     * 打开文件进行编辑
     * @param {string} fileName 文件名
     * @param {string} fileId 文件ID
     * @param {Object} extParam 扩展参数
     * @param {boolean} isNew 是否新建
     */
     openViewFileToEditBefore(fileName, fileId, extParam, isNew) {
       console.log(fileId)
      const params = {
        fileId: fileId,
        protoFileId: this.protoFileId,
        bizId: this.applyId
      }
      // 检查文件编辑状态
      checkBeforeEdit(
        params,
        // 检查通过，可以编辑
        () => {
          openViewFileToEdit(this.fileName, this.fileId, extParam, this.type!==undefined,this.type,openViewFileToEdit)
        },
        // 检查失败，有人正在编辑
        (editorName) => {
          console.log(editorName)
          this.$message.warning(`文件正在被 ${editorName} 编辑，请稍后再试`)
        }
      )
    }
  }
}
</script>
