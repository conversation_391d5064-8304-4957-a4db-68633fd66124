<template>
  <tree-transfer
    :title="title1"
    :from_data="leftData"
    :to_data="toData"
    :defaultProps="{ label: 'deptName' }"
    @add-btn="add"
    @remove-btn="remove"
    :mode="mode"
    height="340px"
    width="100%"
    filter
    openAll
    pid="parentId"
    node_key="deptId"
  >
  </tree-transfer>
</template>

<script>
import { listDept } from "@/api/system/dept";
import treeTransfer from "el-tree-transfer"; // 引入
export default {
  name: "distributionDepartment",
  components: {
    treeTransfer,
  },
  props: ["huixiandata"],
  data() {
    return {
      distributeDepths: [],
      mode: "transfer", // transfer addressList
      leftData: [],
      toData: [],
      title1: ["可选", "已选"],
    };
  },
  watch:{
     huixiandata(val){
       console.log('huixiandata',val);
     }
  },
  created() {
    //this.toData = this.huixiandata;
    listDept({ status: 0}).then((response) => {
      this.leftData = this.handleTree(response.data, "deptId");
    });
  },
  methods: {
    gethuixiandata(val){
    },
    // 切换模式 现有树形穿梭框模式transfer 和通讯录模式addressList
    changeMode() {
      if (this.mode == "transfer") {
        this.mode = "addressList";
      } else {
        this.mode = "transfer";
      }
    },
    change() {
      let arr = this.deepTraversal(this.postList);
    },
    transfer() {
      this.refreshTable = false;
      console.log("transfer", this.toData);
      this.postList = this.toData;
      this.open = false;
      this.refreshTable = true;
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal(data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    // 监听穿梭框组件添加
    add(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      console.log("fromData:", fromData);
      console.log("toData:", toData);
      console.log("obj:", obj);
    },
    // 监听穿梭框组件移除
    remove(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      console.log("fromData:", fromData);
      console.log("toData:", toData);
      console.log("obj:", obj);
    },
  },
};
</script>
<style lang="scss">
.wl-transfer .transfer-main {
  padding: 0px;
  height: calc(100% - 70px) !important;
}
.rz-transfer {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>
