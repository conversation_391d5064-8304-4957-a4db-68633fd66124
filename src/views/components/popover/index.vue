<template>
  <el-popover
    placement="top-start"
    trigger="hover">
    <span v-if="contentTpye==='string'">{{content}}</span>
    <file-upload
      title=""
      :limit="content.length"
      v-if="contentTpye==='file'"
      :editStatus="false"
      v-model.trim="content"
      :isShowTip="false"
    />
    <i slot="reference" class="el-icon-info"></i>
  </el-popover>
</template>

<script>

export default {
  name: "iPopover",
  props: {
    content: {
    },
    contentTpye:{
      type: String,
      default: 'string',
    }
  },
  data() {
    return {

    };
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style scoped>
 i{
   position: absolute;
   font-size: 20px;
   right: 0px;
   top: 50%;
   transform: translate(-50%, -50%);
 }
</style>
