<!-- 动态表单组件
  1、在此处添加扩展字段，根据formRuleList中的数据进行显示
  2、formList中对象的格式为{"list": "N", "show": "Y", "label": "文件类别", "query": "N", "value": "ext5", "dictType": "file_type", "htmlType": "select", "required": "Y", "defaultValue": "TEST默认值1"}
  3、formList中对象的show字段为"N"时，页面不显示该字段
  4、formList中对象的htmlType字段页面展示
    4.1、formList中对象的htmlType字段为"input"时，显示文本框
    4.2、formList中对象的htmlType字段为"textarea"时，显示文本域
    4.3、formList中对象的htmlType字段为"select"时，显示下拉框,formList中对象的dictType字段为字典类型时，显示对应字段的下拉数据
    4.4、formList中对象的htmlType字段为"radio"时，显示单选框
    4.5、formList中对象的htmlType字段为"checkbox"时，显示复选框
    4.6、formList中对象的htmlType字段为"datetime"时，显示日期控件
    4.7、formList中对象的htmlType字段为"selectUser"时，显示单选选人控件
    4.8、formList中对象的htmlType字段为"selectUserMultiple"时，显示多选选人控件
    4.9、formList中对象的htmlType字段为"imageUpload"时，显示图片上传---暂不支持，后续可拓展
    4.10、formList中对象的htmlType字段为"fileUpload"时，显示文件上传---暂不支持，后续可拓展
    4.9、formList中对象的htmlType字段为"editor"时，显示富文本控件---暂不支持，后续可拓展
  5、formList中对象的required字段为"Y"时，显示必填项
  6、formList中对象的defaultValue字段显示默认值
  7、formList中对象的label字段为前端显示值
  8、formList中对象的value字段为后端接收值
  9、formList中对象的htmlType字段为"tree"时，显示树形控件
  10、formList中对象的maxLength字段为字符长度，有值则显示控制，无值则不控制
  11、formList中对象的htmlType字段为"inputMultiple"时，显示输入可多选数据relation-plan-no组件
-->
<template>
  <el-row>
    <template v-for="(item, index) in formRuleList">
      <el-col :span="12"
              v-if="item.show === 'Y'"
              :key="index">
        <el-form-item :label="$t('dicts.form_control_'+item.value)+':'"
                      :prop="item.value"
                      :rules="item.required === 'Y' || kuozhanshuju[item.value]? [{required: true, message: $t('dicts.form_control_'+item.value) + $t('doc.this_dept_not_null'), trigger: 'blur,change'}] : []">
          <!-- 文本框 -->
          <template v-if="item.htmlType === 'input'">
            <el-input v-if="editStatus || kuozhanshuju[item.value]"
                      v-model.trim="formData[item.value]"
                      :placeholder="$t('doc.this_dept_insert') + $t('dicts.form_control_'+item.value)"
                      clearable
                      :style="{width: '100%'}"
                      :maxlength="item.maxLength || undefined"
                      show-word-limit>
            </el-input>
            <span v-else>{{formData[item.value]}}</span>
          </template>

          <!-- 文本域 -->
          <template v-if="item.htmlType === 'textarea'">
            <el-input v-if="editStatus || kuozhanshuju[item.value]"
                      type="textarea"
                      v-model.trim="formData[item.value]"
                      :placeholder="$t('doc.this_dept_insert') + $t('dicts.form_control_'+item.value)"
                      :rows="3"
                      :maxlength="item.maxLength || undefined"
                      show-word-limit>
            </el-input>
            <span v-else>{{formData[item.value]}}</span>
          </template>

          <!-- 下拉框 -->
          <template v-if="item.htmlType === 'select'">
            <el-select v-if="editStatus || kuozhanshuju[item.value]"
                       v-model.trim="formData[item.value]"
                       :placeholder="$t('doc.this_dept_pls_select') + $t('dicts.form_control_'+item.value)"
                       clearable
                       style="width: 100%">
              <el-option v-for="dict in dict.type[item.dictType]"
                         :key="dict.value"
                         :label="dict.label"
                         :value="dict.value">
              </el-option>
            </el-select>
            <dict-tag v-else
                      :options="dict.type[item.dictType]"
                      :value="formData[item.value]" />
          </template>

          <!-- 单选框 -->
          <template v-if="item.htmlType === 'radio'">
            <el-radio-group v-if="editStatus || kuozhanshuju[item.value]"
                            v-model.trim="formData[item.value]">
              <el-radio v-for="dict in dict.type[item.dictType]"
                        :key="dict.value"
                        :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>
            <dict-tag v-else
                      :options="dict.type[item.dictType]"
                      :value="formData[item.value]" />
          </template>

          <!-- 复选框 -->
          <template v-if="item.htmlType === 'checkbox'">
            <el-checkbox-group v-if="editStatus || kuozhanshuju[item.value]"
                               v-model.trim="formData[item.value]">
              <el-checkbox v-for="dict in dict.type[item.dictType]"
                           :key="dict.value"
                           :label="dict.value">{{dict.label}}</el-checkbox>
            </el-checkbox-group>
            <dict-tag v-else
                      :options="dict.type[item.dictType]"
                      :value="formData[item.value]" />
          </template>

          <!-- 日期控件 -->
          <template v-if="item.htmlType === 'datetime'">
            <el-date-picker v-if="editStatus || kuozhanshuju[item.value]"
                            v-model.trim="formData[item.value]"
                            type="datetime"
                            :placeholder="$t('doc.this_dept_pls_select') + $t('dicts.form_control_'+item.value)"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            style="width: 100%">
            </el-date-picker>
            <span v-else>{{formData[item.value]}}</span>
          </template>

          <!-- 树形控件 -->
          <template v-if="item.htmlType === 'tree'">
            <treeselect :disabled="!editStatus"
                        v-model.trim="formData[item.value]"
                        :options="deptOptions2"
                        :show-count="true"
                        :placeholder="$t('doc.this_dept_pls_select') + $t('dicts.form_control_'+item.value)" />
          </template>
          <!-- 单选/多选选人控件 -->
          <template v-if="item.htmlType === 'selectUserMultiple' || item.htmlType === 'selectUser'">
            <el-input v-if="editStatus || kuozhanshuju[item.value]"
                      v-model.trim="formData[item.value]"
                      :placeholder="$t('doc.this_dept_pls_select') + $t('dicts.form_control_'+item.value)"
                      readonly
                      class="user-select-input">
              <el-button slot="append"
                         icon="el-icon-search"
                         @click="selectHandle(item)">
              </el-button>
            </el-input>
            <span v-else>{{formData[item.value]}}</span>
          </template>

          <!-- 输入可多选数据 -->
          <template v-if="item.htmlType === 'inputMultiple'">
            <relation-plan-no v-if="editStatus || kuozhanshuju[item.value]"
                              :disabled="!editStatus"
                              :value.sync="formData[item.value]"
                              :placeholder="$t('doc.this_dept_pls_select') + $t('dicts.form_control_'+item.value)">
            </relation-plan-no>
            <span v-else>{{formData[item.value]}}</span>
          </template>
        </el-form-item>
      </el-col>
    </template>
    <assign-users ref="assignUsers"
                  @selectHandle="userSelectHandle"></assign-users>
  </el-row>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AssignUsers from '@views/workflowList/addWorkflow/add_import/assignUsers.vue'
import { getUserByUserNames } from '@/api/system/user'
import RelationPlanNo from '@/components/RelationPlanNo.vue'
export default {
  components: {
    Treeselect, AssignUsers, RelationPlanNo
  },
  name: 'DynamicForm',
  props: {
    formRuleList: {
      type: Array,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
    editStatus: {
      type: Boolean,
      default: false,
    },
    dict: {
      type: Object,
      required: true
    },
    deptOptions2: {
      type: Array
    },
  },
  data () {
    return {
      kuozhanshuju: {}
    }
  },
  created () {
    // 可以编辑时清空选择的用户
    this.formRuleList.forEach(item => {
      if (item.htmlType === 'selectUser' || item.htmlType === 'selectUserMultiple') {
        // 如果没有List数据但有value数据,则根据逗号分隔转换成List
        if (this.formData[item.value]) {
          getUserByUserNames(this.formData[item.value]).then(async (res) => {
            // 赋值this.formData[item.value]+list数据,只需要userName、nickName、deptId、deptName这4个字段
            this.$set(this.formData, item.value + 'List', res.data.map(item => ({
              userName: item.userName,
              nickName: item.nickName,
              deptId: item.deptId,
              deptName: item.deptName
            })))
            console.log(JSON.stringify(this.formData[item.value + 'List']))
          })
        }
      }
    })
  },
  watch: {
  },
  methods: {
    setKuozhanshuju(kuozhanshuju){
      this.$set(this,'kuozhanshuju',kuozhanshuju)
    },
    userSelectHandle (source, index, data) {
      let _this = this
      let valueList = []
      if (data && data.length > 0) {
        data.forEach(user => {
          valueList.push({
            userName: user.userName,
            nickName: user.nickName,
            deptId: user.deptId,
            deptName: user.deptName,
          })
        })
      }
      // _this.formData中增加逗号隔开数据,对应动态对应source.value属性
      _this.$set(_this.formData, source.value, valueList.map(item => item.userName).join(','))
      // 将valueList赋值给_this.formData中对应动态对应source.value+List属性，如后台需要使用则需定义source.value+List属性接收
      _this.$set(_this.formData, source.value + 'List', valueList)
    },
    selectHandle (item) {
      let _this = this
      // 判断item.htmlType === 'selectUserMultiple'多选框选中数据
      let isMulti = item.htmlType === 'selectUserMultiple'
      let selected = []
      if (_this.formData[item.value + 'List'] && _this.formData[item.value + 'List'].length > 0) {
        _this.formData[item.value + 'List'].forEach(item => {
          selected.push({
            userName: item.userName,
            nickName: item.nickName,
            deptId: item.deptId,
            deptName: item.deptName,
          })
        })
      }
      _this.$nextTick(() => {
        _this.$refs.assignUsers.init(isMulti ? "选择人员（多选）" : '选择人员（单选）', item, null, selected, isMulti)
      })
    }
  }
}
</script>

<style scoped>
/* 添加样式 */
</style>
