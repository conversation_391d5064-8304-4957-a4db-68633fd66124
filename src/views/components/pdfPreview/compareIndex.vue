<template>
  <div class="drawer" v-loading="loading">
    <div class="drawer-head" style="padding-left:16px;padding-right:16px;background-color: #323639;border-bottom:none;box-shadow: 0px 0px 5px ">
      <div class="cell-title">
        <div>
          <p class="title" style="color: #ffffff">文件预览</p>
        </div>
      </div>
      <div class="cell-btn" style="color: #ffffff;text-align:center">
        <span><el-input-number  :min="1" :max="pageTotal" style="width: 50px" v-model="pageNum" :controls="false" @change="handlePageNum"></el-input-number>/{{pageTotal}}</span>
        <el-divider direction="vertical"></el-divider>
        <el-input-number style="width: 130px" @change="handleChange" v-model="percentage" :min="50" :max="500" :step="10" label="%"></el-input-number>%
        <el-divider direction="vertical"></el-divider>
        <el-button icon="el-icon-refresh-right"  @click="handleRotate">旋 转</el-button>
      </div>
      <div class="cell-btn">
<!--        <el-button type="info" @click="handlePrint" v-hasPermi="['dms:pdf:print']" v-dbClick>打 印</el-button>-->
        <el-button v-if="!!this.data" @click="closeDrawer">关 闭</el-button>
      </div>
    </div>
    <div class="file-flex" id="file-flex"  @scroll="handleScroll">
      <div class="fl" >
        <div class="box" id="box1" @mouseup="handleMouseUp" @mousedown="handleMouseDown1" @mouseout="handleMouseUp" @mousemove="handleMouseMove1">
          <div id="printFrom1" :style="{position: 'relative',left: positionLeft1,width:pdfWidth}">
            <pdf ref="pdf" v-for="item in pageTotal"
                 :rotate="pageRotate"
                 :id="'pdf'+item"
                 :src="src1"
                 :key="item"
                 :page="item"></pdf>
          </div>
        </div>
      </div>
      <div class="fr">
        <div class="box" id="box" @mouseup="handleMouseUp" @mousedown="handleMouseDown" @mouseout="handleMouseUp" @mousemove="handleMouseMove">
          <div id="printFrom" :style="{position: 'relative',left: positionLeft,width:pdfWidth}">
            <pdf ref="pdf" v-for="item in pageTotal"
                 :rotate="pageRotate"
                 :id="'pdf'+item"
                 :src="src"
                 :key="item"
                 :page="item"></pdf>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import pdf from 'vue-pdf'
import printJS from 'print-js'
import { previewPdfWatermark } from '@/api/file_processing/fileSignature'
export default {
  name: "PdfPreview",
  props: {
    data: {
      type: Object,
      default: undefined,
    }
  },
  components:{
    pdf
  },
  data() {
    return {
      pageRotate: 0,
      positionLeft: 0,
      pageNum: 1,
      pdfWidth: '80%',
      percentage: 100,
      loading: false,
      href: "",
      src: "",
      src1:"",
      pageTotal: 0,
      status: false,
      status1: false,
      positionLeft1: 0,
    }
  },
  mounted() {
    if (!this.data||!this.data.pdfId||!this.data.comparePdfId) {
      this.handleOpenView(this.$route.query.comparePdfId,'compare')
      this.handleOpenView(this.$route.query.pdfId,'basics')
    }else {
      this.handleOpenView(this.data.comparePdfId,'compare')
      this.handleOpenView(this.data.pdfId,'basics')
    }
  },
  methods:{
    handlePrint(){
      printJS({
        printable: this.href
      })
    },
    handleOpenView(pdfId,type){
      this.loading = true
      previewPdfWatermark(pdfId).then(stream=>{
        if(stream.type == 'text/xml') {
          // PDF文件找不到了
          this.$modal.alert("fileId="+pdfId+", can not found.")
        } else if(stream.type == 'application/octet-stream') {
          // 正常预览
          const URL = window.URL || window.webkitURL;
          const href = URL.createObjectURL(new Blob([stream], { type: 'application/pdf;charset=utf-8' }))
          this.href= href
          if(type === 'compare'){
            this.src = pdf.createLoadingTask(href)
            // 获取页码
            this.src.promise.then(pdf => this.pageTotal = pdf.numPages).catch(error => {})
          }else{
            this.src1 = pdf.createLoadingTask(href)
            // 获取页码
            this.src1.promise.then(pdf => this.pageTotal = pdf.numPages).catch(error => {})
          }
        }
      }).finally(()=>{
        this.loading = false
      })
    },
    closeDrawer(){
      this.$emit("close")
    },
    handleChange(currentValue, oldValue){
      this.pdfWidth = (80 * currentValue/100).toFixed(1) + "%"
      this.positionLeft = 0
    },
    handleRotate(){
      this.pageRotate += 90
    },
    handleMouseDown(event){
      this.status = true
      console.log(1)
    },
    handleMouseMove(event){
      if (this.status) {
        this.positionLeft = `${parseInt(this.positionLeft) + event.movementX}px`;
      }
    },
    handleMouseUp(event){
      this.status = false
      this.status1 = false
    },
    handleMouseDown1(event){
      this.status1 = true
    },
    handleMouseMove1(event){
      if (this.status1) {
        this.positionLeft1 = `${parseInt(this.positionLeft1) + event.movementX}px`;
      }
    },
    handlePageNum(currentValue, oldValue){
      let offsetTop = document.getElementById('pdf' + currentValue).offsetTop
      document.getElementById('file-flex').scroll({
        top: offsetTop,
      });
    },
    handleScroll(){
      let scrollTop = document.getElementById('file-flex').scrollTop
      for (let index=0; index<this.pageTotal;index++) {
        let id = index+1
        let offsetTop = document.getElementById('pdf' + id).offsetTop
        if (scrollTop<offsetTop) {
          if (this.pageNum!=index) {
            this.pageNum = index
          }
          return
        }
      }
      if (this.pageNum!==this.pageTotal) {
        this.pageNum = parseInt(this.pageTotal)
      }
    },
  }
}
</script>
<style scoped>
  #printFrom{
    margin: 0px auto;
  }
  #box {
    width: 100%;
    height: 100%;
    padding: 6px;
    position: relative;
  }
  #printFrom span{
    margin-top: 6px;
    box-shadow: 0px 0px 5px
  }
  #printFrom1{
    margin: 0px auto;
  }
  #box1 {
    width: 100%;
    height: 100%;
    padding: 6px;
    position: relative;
  }
  #printFrom1 span{
    margin-top: 6px;
    box-shadow: 0px 0px 5px
  }
  .drawer{
    overflow: hidden;
    width: 100%;
    height: 100%;
    background-color: #525659;
  }
</style>
