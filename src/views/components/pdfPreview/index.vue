<template>
  <div class="drawer" v-loading="loading" @mouseup="handleMouseUp">
    <div class="drawer-head" style="padding-left:16px;padding-right:16px;background-color: #323639;border-bottom:none;box-shadow: 0px 0px 5px ">
      <div class="cell-title">
        <div>
          <p class="title" style="color: #ffffff">{{$t(`print.file_preview`)}}</p>
        </div>
      </div>
      <div class="cell-btn" style="color: #ffffff;text-align:center">
        <span><el-input-number  :min="1" :max="pageTotal" style="width: 50px" v-model="pageNum" :controls="false" @change="handlePageNum"></el-input-number>/{{pageTotal}}</span>
        <el-divider direction="vertical"></el-divider>
        <el-input-number style="width: 130px" @change="handleChange" v-model="percentage" :min="50" :max="500" :step="10" label="%"></el-input-number>
        <el-divider direction="vertical"></el-divider>
        <el-button icon="el-icon-refresh-right"  @click="handleRotate">{{$t(`doc.this_dept_revolve`)}}</el-button>
      </div>
      <div class="cell-btn">
        <el-button type="info" @click="handlePrint" v-if="((printData!=null && printStatus) || showPrint) && checkPermi(['dms:pdf:print'])"  v-dbClick>{{$t(`doc.this_dept_printing`)}}</el-button>
        <el-button @click="closeDrawer">{{ $t(`sys_mgr_log.operlog_close`)}}</el-button>
      </div>
    </div>
    <div id="box" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @scroll="handleScroll" style="height: calc(100vh - 66px);">
      <div infinite-scroll-immediate="false" id="printFrom" :style="{position: 'relative',left: positionLeft,width:pdfWidth}" v-infinite-scroll="load">
        <div :id="`pdf${i.index}`" v-for="i in pageTotalList" :key="i.index" style="border-bottom: 5px solid #eaeaea;">
          <pdf :rotate="pageRotate"  :src="i.src"></pdf>
        </div>
      </div>
    </div>
    <print-settings
      ref="printSettings"
      :visible.sync="printSettingsVisible"
      :doc-info="docInfo"
    />
  </div>
</template>

<script>
import { PDFDocument } from 'pdf-lib';
import pdf from 'vue-pdf'
import printJS from 'print-js'
import { previewPdfWatermark } from '@/api/file_processing/fileSignature'
import { addPrintFileDetail } from "../../../api/file_processing/printFileDetail";
import { checkPermi } from '@/utils/permission'
import _ from 'lodash';
import { getConfigKey } from '@/api/system/config'
import PrintSettings from '@/components/PrintSettings'
import { parseTime } from '@/utils/ruoyi'
import watermark from 'watermark-dom'
import {listPrintGroupByFileId} from "@/api/process/distribute";

export default {
  name: "PdfPreview",
  props: {
    data: {
      type: Object,
      default: undefined,
    }
  },
  components: {
    pdf,
    PrintSettings
  },
  data() {
    return {
      pageRotate: 0,
      positionLeft: 0,
      pageSize:5, // PDF每次加载条目熟
      currentPage:0, // 当前PDF所在分页
      fileBuffer:'', // PDF文件流数据
      pageNum: 1,
      pdfWidth: '100%',
      percentage: 100,
      loading: false,
      href: "",
      src: "",
      pageTotal: 0,
      status: false,
      closeStatus: true,
      pageTotalList:[],
      printSettingsVisible: false,
      docInfo: {},
      printStatus:false,
      printData:[],
      showPrint: false
    };
  },
  created() {
    let config = JSON.parse(sessionStorage.getItem("SYS_CONFIG"));
    let sysConfig = config.filter((x) => x.configKey === "sys:config")[0]
    if(sysConfig.configValue.split(',').includes('process:standard:dept:extra')){
      this.showPrint = true
    }
  },
  mounted() {
    if(this.$route){
      this.getDistributeList()
    }
    if (JSON.parse(sessionStorage.getItem('winOpen'))) {
      const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
      const userName = userInfo.userName
      const nickName = userInfo.nickName
      // const loginDate = userInfo.loginDate
      const loginDate = new Date()
      const txt = `${userName},${nickName},${parseTime(loginDate, '{y}-{m}-{d}')}`
      watermark.init({ watermark_txt: txt, watermark_alpha: 0.08, watermark_width: 260, watermark_height: 150, })
    }
    if (!this.data) {
      let fileName = this.$route.query.fileName;
      document.title = fileName;
      this.data = { pdfId: this.$route.query.pdfId};
      this.handleOpenView(this.$route.query.pdfId);
    } else {
      this.closeStatus = true;
      this.handleOpenView(this.data.pdfId);
    }
  },
  beforeDestroy() {
    this.pageTotalList.forEach(i => {
      URL.revokeObjectURL(i.url);
    });
  },
  methods: {
    getDistributeList(){
      let bizId = this.$route.query.bizId
      let fileId = this.$route.query.fileId
      listPrintGroupByFileId({id:bizId,fileId:fileId}).then((res) => {
          this.printData=res.data
          this.printStatus = this.$route && this.$route.query.print && this.$route.query.print === 'true';
      });
    },
    handleOpenView(pdfId){
      this.loading = true
      previewPdfWatermark(pdfId||this.data.pdfId).then(async stream => {
        // 获取请求的URL
        const request = stream.request || stream.responseURL;
        console.log('PDF预览请求URL:', request);

        if (stream.type == 'text/xml') {
          // PDF文件找不到了
          this.$modal.alert("fileId=" + pdfId + ", can not found.")
        } else if (stream.type == 'application/octet-stream') {
          // 正常预览
          const URL = window.URL || window.webkitURL;
          const href = URL.createObjectURL(new Blob([stream], {type: 'application/pdf;charset=utf-8'}))
          this.href = href
          // 获取页码
          pdf.createLoadingTask(href).promise.then(pdf => this.pageTotal = pdf.numPages).catch(error => {
          })
          const arrayBu = await stream.arrayBuffer()
          this.fileBuffer = arrayBu
          const list = await this.splitPdf(arrayBu, 0, 5);
          console.log(list)
          this.splitPdfFormate(list,0)
        }
      }).finally(()=>{
        this.loading = false
      })
    },
    // 处理文件分页
    splitPdfFormate(list,start,callback) {
      const fileList = []
      const promiseList =  list.map((arrayBuffer, index) => {
        return new Promise((resolve,reject) => {
          const url = URL.createObjectURL(new Blob([arrayBuffer], { type: 'application/pdf;charset=utf-8' }));
          let loadingTask = pdf.createLoadingTask(url, {
            withCredentials: false
          });
          loadingTask.promise.then(pdf => {
            fileList.push( {
              total: pdf.numPages,
              src: url,
              index:start+index
            })
            resolve()
          }).catch(()=>{
            reject()
          })

        })
      });
      if(promiseList.length>0){
        Promise.all(promiseList).then(()=>{
          this.pageTotalList =this.pageTotalList.concat(fileList.sort((a, b)=>a.index-b.index))
          console.log(this.pageTotalList)
          if(callback){
            callback()
          }else{
            this.currentPage += 1;
          }
        }).finally(()=>{
          this.loading = false
        })
      }
    },
    checkPermi,
    // 其他方法...
    load: _.debounce(function() {
      this.loading = true;
      const start = this.currentPage * this.pageSize;
      const end = (this.currentPage + 1) * this.pageSize;
      if(end > this.pageTotal){
        this.loading = false;
      }
      console.log(start)
      this.splitPdf(this.fileBuffer, start, end).then((list) => {
        this.splitPdfFormate(list,start)
      });

    }, 300), // 调整防抖时间
    async handlePrint() {
      try {
        const res = await getConfigKey('is_control_print')
        console.log(this.printData)
        if (res.msg === 'true' && this.printData) {
          const contextPath = process.env.VUE_APP_BASE_API
          this.docInfo = {
            docId: this.printData.docId,
            docName: this.printData.docName,
            printCount: this.printData.nums,
            versionId: this.printData.versionId,
            classificationNo: this.printData.code,
            docType: this.printData.className,
            docDistributeId: this.printData.id,
            fileId: this.printData.fileId,
            fileUrl: `/process/file-signature/previewPdfWatermark?pdfFileId=${this.data.pdfId}`
          }
          // 显示打印设置对话框
          this.showPrintSettings()
        } else {
          // 使用原来的打印逻辑
          this.directPrint()
        }
      } catch (error) {
        console.log(error)
        this.$message.error('获取打印设置失败')
        // 发生错误时使用默认打印逻辑
        this.directPrint()
      }
    },

    // 直接打印（原来的逻辑）
    directPrint() {
     // 增加打印明细记录
     printJS({
        printable: this.href
      })
      addPrintFileDetail({ pdfId: this.data.pdfId }).then(res => {

      }).finally(() => {

      })
    },
    // 分段加载PDF
    async splitPdf(pdfBytes,start=0,end=5) {
      const pdfDoc = await PDFDocument.load(pdfBytes);
      const pages = pdfDoc.getPages();
      const pagesList = [];
      const length = end>pages.length?pages.length:end
      for (let i = start; i < length; i++) {
        // 创建一个新的PDF文档
        const newPdf = await PDFDocument.create();
        // 复制当前页面到新的PDF文档
        const [copiedPage] = await newPdf.copyPages(pdfDoc, [i]);
        newPdf.addPage(copiedPage);
        pagesList.push(newPdf);
      }
      return await Promise.all(pagesList.map(page => page.save()))
    },
    closeDrawer() {
      window.close();
      this.$emit("close");
    },
    handleChange(currentValue, oldValue) {
      this.pdfWidth = (80 * currentValue / 100).toFixed(1) + "%";
      this.positionLeft = 0;
    },
    handleRotate() {
      this.pageRotate += 90;
    },
    handleMouseDown(event) {
      this.status = true;
    },
    handleMouseMove(event) {
      if (this.status) {
        this.positionLeft = `${parseInt(this.positionLeft) + event.movementX}px`;
      }
    },
    handleMouseUp(event) {
      this.status = false;
    },
    handlePageNum(currentValue, oldValue){
      const target = document.getElementById(`pdf${currentValue-1}`)
      // 如果存在则直接跳转，不存在，则需要加载完毕才能跳转
      if(target){
        document.getElementById('box').scroll({
          top: target.offsetTop+20,
        });
      }else{
        const start = this.pageSize * this.currentPage;
        const end = Math.ceil(currentValue/this.pageSize) * this.pageSize
        this.splitPdf(this.fileBuffer, start, end).then((list) => {
          this.currentPage = Math.floor(currentValue/this.pageSize)
          this.splitPdfFormate(list,start,()=>{
            this.$nextTick(()=>{
              const target = document.getElementById(`pdf${currentValue-1}`)
              document.getElementById('box').scroll({
                top: target.offsetTop+20,
              });
            })
          })
        });
      }
    },
    handleScroll(){
      // 获取box的滚动高度
      let scrollTop = document.getElementById('box').scrollTop
      // 通过box的滚动高度和每个pdf的高度进行对比，找到当前滚动到的pdf的下标后跳出循环
      const findIndex = this.pageTotalList.findIndex(item=>{
        const itemOffsetTop =  document.getElementById(`pdf${item.index}`)?.offsetTop
        return scrollTop<=itemOffsetTop
      })
      // 如果findIndex !== -1，则说明找到了当前滚动到的pdf的下标，则将当前页码设置为findIndex+1，否则将当前页码设置为当前已渲染的PDF条目数
      if(findIndex !== -1){
        this.pageNum = findIndex
      }else{
        this.pageNum = this.pageTotalList.length
      }
    },
    async showPrintSettings() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      // 获取PrintSettings组件的引用
      const printSettings = this.$refs.printSettings
      if (printSettings && await printSettings.checkPrinters()) {
        this.printSettingsVisible = true
        loading.close();
      }else {
        loading.close();
      }
    }
  }
}
</script>

<style scoped>
#printFrom {
  margin: 0px auto;
}

#box {
  width: 100%;
  height: 100%;
  overflow: auto !important;
  padding: 6px;
  position: relative;
}

#printFrom span {
  margin-top: 6px;
  box-shadow: 0px 0px 5px;
}

.drawer {
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-color: #525659;
}
</style>
