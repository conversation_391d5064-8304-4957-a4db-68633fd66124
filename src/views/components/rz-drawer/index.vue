<template>
  <el-drawer
    :visible.sync="drawer_"
    direction="rtl"
    size="85%"
    modal-append-to-body
    append-to-body
    :show-close="false"
    class="rz-drawer"
    :destroy-on-close="true"
    @close="closeDrawer"
  >
    <span slot="title">
      <div class="rz-drawer-head">
        <div class="rit">
          <div>
            <span class="rz-header-main-title">{{ title }}</span>
            <el-tag
              effect="dark"
              type="success"
              size="mini"
              v-if="state != ''"
              >{{ state }}</el-tag
            >
          </div>
          <div v-if="typetime!=''">
            <span class="rz-header-sub-title"
              >变更类型：新增文件<el-divider direction="vertical"></el-divider
              >编制部门：生产质量部<el-divider direction="vertical"></el-divider
              >编制时间：2021/01/10 11:00</span
            >
          </div>
        </div>
        <div class="left"> 
          <slot name="rzbutton"></slot>
          <!-- <el-button type="primary">分发</el-button>
          <el-button>修改</el-button>
          <el-dropdown>
            <span class="el-dropdown-link">
              <el-button icon="el-icon-more"></el-button>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>暂存</el-dropdown-item>
              <el-dropdown-item>退回</el-dropdown-item>
              <el-dropdown-item>退回起草人</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
      </div>
    </span>

    <slot></slot>
  </el-drawer>
</template>

<script>
export default {
  name: "rzDrawer",
  data() {
    return {};
  },
  mounted() {},
  props: {
    title: {
      type: String,
      default: "",
    },
    state: {
      type: String,
      default: "",
    },
    typetime: {
      type: String,
      default: "",
    },
    drawer: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer;
      },
      set(v) {
        this.$emit("changeDrawer", v);
      },
    },
  },
  watch: {},
  methods: {
    //关闭弹框
    closeDrawer() {
      this.$emit("closeDrawer");
    },
    
  },
};
</script>
<style lang="scss">
.rz-drawer {
  .el-drawer__header {
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
  }
  .rz-drawer-head {
    display: flex;
    justify-content: center;
    align-items: center;
    .rit {
      width: 50%;
      .rz-header-main-title {
        font-size: 16px;
        font-weight: bold;
        color: #225fc7;
      }
      .el-tag {
        margin: 0 10px;
      }
      .rz-header-sub-title {
        font-size: 14px;
        font-weight: normal;
        color: #999;
        margin: 5px 0 0 0;
      }
    }
    .left {
      width: 50%;
      display: flex;
      justify-content: flex-end;
      align-content: center;
      .el-button {
        height: 34px;
        margin-right: 10px;
      }
    }
  }
}
</style>
