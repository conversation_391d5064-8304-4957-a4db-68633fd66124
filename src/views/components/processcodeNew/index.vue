<template>
  <div class="maincss" v-loading="loading">
    <div class="title-box">
      <div class="draw-title">
        {{ title }}
      </div>
      <el-form
        :model="formSubmit"
        ref="formSubmit"
        class="mt20"
        :rules="formrules"
        label-position="top"
        label-width="120px"
        v-if="!loading"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <div>办理步骤</div>
            <el-tree
              :data="processList"
              :props="processProps"
              @node-click="processNodeClick"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
            </el-tree>
          </el-col>
          <!-- 复审业务 -->
          <el-col :span="8" v-if="isjielun">
            <div style="margin-top: 20px">
              <el-radio
                v-model.trim="actionType"
                label="UPDATE"
                border
                size="medium"
                style="margin-bottom: 10px; margin-left: 10px;"
                >文件修订</el-radio
              >
              <el-radio
                v-model.trim="actionType"
                label="EXTENSION"
                border
                size="medium"
                style="margin-bottom: 10px"
                >文件作废</el-radio
              >
              <el-radio
                v-model.trim="actionType"
                label="KEEP"
                border
                size="medium"
                style="margin-bottom: 10px"
                >保持现状</el-radio
              >
              <el-radio
                v-model.trim="actionType"
                label="DISUSE"
                border
                size="medium"
                >有效期延期</el-radio
              >
            </div>
          </el-col>
          <el-col :span="8" v-show="processList[0].actDefType != 'endEvent'">
            <div>待选用户</div>
            <el-tree
              :data="userList"
              :props="userProps"
              @node-click="userNodeClick"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
            </el-tree>
          </el-col>
          <el-col :span="8" v-show="processList[0].actDefType != 'endEvent'">
            <div>已选用户</div>
            <div
              class="wordbox"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
              <ul>
                <li v-for="(item, index) in receiveUserList" :key="index">
                  <div class="inli">
                    <i class="el-icon-s-custom"></i>
                    <span>{{ item.name }}</span>
                    <i class="el-icon-close" @click="removeData(item)"></i>
                  </div>
                </li>
              </ul>
            </div>
          </el-col>
          <el-col :span="24" class="mt10" v-if="istongguo">
            <el-form-item label="是否通过" prop="pass">
              <el-radio-group v-model.trim="pass">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="mt10">
            <el-form-item
              label="是否加入档案："
              prop="joinDoc"
              v-if="formSubmit.actDefId == 'EndEvent_0qwgu4i'"
            >
              <el-radio-group v-model.trim="formSubmit.joinDoc">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isSummary" class="mt10">
            <el-form-item label="审核意见：">
              <el-input
                v-model.trim="formSubmit.summary"
                type="textarea"
                placeholder="请输入审核意见"
                maxlength="200"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  workflowNextactsNew,
  getNextActUsersByNew,
  getExtAttributeModel,
} from "@/api/my_business/workflow";

export default {
  components: {},
  props: {
    isSummary: {
      type: Boolean,
      default: true,
    },
    pListData: {},
    data: {},
    title: {},
    detatailsData: {},
  },
  data() {
    return {
      summary: "",
      formSubmit: { summary: "" },
      processList: [],
      processProps: {
        children: "children",
        label: "actDefName",
      },
      userProps: {
        children: "children",
        label: "name",
      },
      formrules: {
        summary: [{ required: true, message: " ", trigger: "change" }],
        pass: [{ required: true, message: " ", trigger: "change" }],
      },
      receiveUserList: [],
      userList: [],
      loading: true,
      nextData: {},
      procDefKey: "",
      actionType: "",
      wf_actionType: "",
      actDefType: "", //actDefType: "endEvent" 判断流程到哪一步了
      pass: "", //是否通过
      kuozhanshuju: "",
      istongguo: false,
      isjielun: false,
        morendta: undefined,
    };
  },
  created() {
    this.getOptionsList();
    if (this.pListData.procDefKey != "") {
      this.procDefKey = this.pListData.procDefKey;
    }
    //this.$emit("yingchanbohui", false);
  },
  watch: {
    actionType: function (val) {
      // if (val == 1 || val == 2) {
      //   this.wf_actionType = "";
      // } else {
      this.wf_actionType = val;
      // }
    },
    "formSubmit.summary": function (val) {
      this.summary = val;
    },
    pass(val) {
      console.log(val);
    },
  },
  mounted() {},
  methods: {
    getOptionsList() {

      let searchQuery = {
        curActDefId: this.pListData.actDefId,
        procDefId: this.pListData.procDefId,
      };
      workflowNextactsNew(searchQuery)
        .then((res) => {
          this.processList = res.data;
          this.processNodeClick(this.processList[0]);
          this.actDefType = res.data[0].actDefType;
          if (
            this.pListData.businessData.yNTrain &&
            this.pListData.businessData.yNTrain === "1"
          ) {
            let actDefOrder = res.data.filter((x) => x.actDefId === "peixun")[0]
              .actDefOrder;
            let arr2 = res.data.filter((x) => x.actDefOrder <= actDefOrder);
            this.processList = arr2;
          } else {
            this.processList = res.data.filter((x) => x.actDefId !== "peixun");
          }
          if (this.processList.length == 1) {
            this.processNodeClick(this.processList[0]);
          }
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
        });
    },
    //流程节点点击触发 带出可选成员
    processNodeClick(val) {
      let user_info = JSON.parse(sessionStorage.getItem("USER_INFO"));
      if (this.nextData != "") {
        if (val.actDefName == this.nextData.actDefName) {
          return;
        }
      }
      this.nextData = val;
      this.userList = []; //代选
      this.receiveUserList = []; //已选
      let params = {
        procDefId:this.pListData.procDefId,
        userOrgId: user_info.deptId,
        curActDefId: this.pListData.actDefId,
        destActDefId: val.actDefId,
      };
      // if (val.actDefId == "qitabumenhuishen") {
      //   this.$emit("yingchanbohui", false);
      // } else {
      //   this.$emit("yingchanbohui", true);
      // }
      getNextActUsersByNew(params)
        .then((res) => {
          // this.userList = res.data.filter(item => item.id != '100')
          if (
            val.actDefId == "qitabumenhuishen" &&
            this.detatailsData &&
            this.detatailsData.deptIds
          ) {
            let arr = [];
            let deptId = user_info.deptId.toString();
            res.data.forEach((item) => {
              this.detatailsData.deptIds.forEach((item2) => {
                if (item.type === "ORG") {
                  if (item.id === item2.toString() && item.id !== deptId) {
                    arr.push(item);
                  }
                } else {
                  if (
                    item.parentId === item2.toString() &&
                    item.parentId !== deptId
                  ) {
                    arr.push(item);
                  }
                }
              });
            });
            this.userList = this.handleTree(arr, "id");
          } else {
            this.userList = this.handleTree(res.data, "id");
              //如果待选人员里就一个人，就自己加入到已选人员名单里z
            this.rtuserList(this.userList);
            this.userNodeClick(this.morendta);
          }
        })
        .catch((e) => {
          this.loading = false;
        });
    },
    //点击成员 导入可选成员
    userNodeClick(val) {
      if (val.type == "USER") {
        let arr = this.receiveUserList.filter((x) => x.id === val.id);
        if (arr.length <= 0) {
          this.receiveUserList.push(val);
        }
      }
    },
    //移除成员
    removeData(item) {
      var arr = [];
      this.receiveUserList.forEach((element) => {
        if (element.id != item.id) {
          arr.push(element);
        }
      });
      this.receiveUserList = arr;
    },
       rtuserList(data) {
      if (data.length == 1 && data[0].children != undefined) {
        this.rtuserList(data[0].children);
      } else {
        this.morendta = data[0];
        return;
      }
    },
  },
};
</script>
