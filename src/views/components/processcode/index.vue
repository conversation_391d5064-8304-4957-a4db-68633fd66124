<template>
  <div class="maincss" v-loading="loading">
    <div class="title-box">
      <div class="draw-title">
        {{ title }}
      </div>
      <el-form
        :model="formSubmit"
        ref="formSubmit"
        class="mt20"
        :rules="formrules"
        label-position="top"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <div>办理步骤</div>
            <el-tree
              ref="myTree"
              :data="processList"
              :props="processProps"
              node-key="actDefName"
              @node-click="processNodeClick"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
              highlight-current
            >
            </el-tree>
          </el-col>
          <el-col :span="8" v-show="processList[0].actDefType != 'endEvent'">
            <div>待选用户</div>
            <el-tree
              :data="userList"
              :props="userProps"
              :highlight-current="true"
              @node-click="userNodeClick"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
            </el-tree>
          </el-col>
          <el-col :span="8" v-show="processList[0].actDefType != 'endEvent'">
            <div>已选用户</div>
            <div
              class="wordbox"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px"
            >
              <ul>
                <li v-for="(item, index) in receiveUserList" :key="index">
                  <div class="inli">
                    <i class="el-icon-s-custom"></i>
                    <span>{{ item.name }}</span>
                    <i class="el-icon-close" @click="removeData(item)"></i>
                  </div>
                </li>
              </ul>
            </div>
          </el-col>
          <el-col :span="24" class="mt10" v-if="istongguo">
            <el-form-item label="是否通过" prop="pass">
              <el-radio-group v-model.trim="pass">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="mt10">
            <el-form-item
              label="是否加入档案："
              prop="joinDoc"
              v-if="formSubmit.actDefId == 'EndEvent_0qwgu4i'"
            >
              <el-radio-group v-model.trim="formSubmit.joinDoc">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isSummary" class="mt10">
            <el-form-item label="审核意见：">
              <el-input
                v-model.trim="formSubmit.summary"
                type="textarea"
                placeholder="请输入审核意见"
                maxlength="200"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  workflowNextactsNew,
  getNextactuserByPending,
  getExtAttributeModel,
} from "@/api/my_business/workflow";

export default {
  components: {},
  props: {
    isSummary: {
      type: Boolean,
      default: true,
    },
    pListData: {},
    data: {},
    title: {},
    detatailsData: {},
  },
  data() {
    return {
      summary: "",
      formSubmit: { summary: "" },
      processList: [],
      processProps: {
        children: "children",
        label: "actDefName",
      },
      userProps: {
        children: "children",
        label: "name",
      },
      formrules: {
        summary: [{ required: true, message: " ", trigger: "change" }],
        pass: [{ required: true, message: " ", trigger: "change" }],
      },
      receiveUserList: [],
      userList: [],
      loading: true,
      nextData: {},
      procDefKey: "",
      actionType: "",
      wf_actionType: "",
      actDefType: "", //actDefType: "endEvent" 判断流程到哪一步了
      pass: "", //是否通过
      kuozhanshuju: "",
      istongguo: false,
      isjielun: false,
      morendta: undefined,
    };
  },
  created() {
    this.getOptionsList();
    console.log("xuanren", this.pListData);
    if (this.pListData.procDefKey != "") {
      this.procDefKey = this.pListData.procDefKey;
    }
    //this.$emit("yingchanbohui", false);
  },
  watch: {
    actionType: function (val) {
      this.wf_actionType = val;
    },
    "formSubmit.summary": function (val) {
      this.summary = val;
      //console.log(val);
    },
    pass(val) {
      console.log(val);
    },
  },
  mounted() {},
  methods: {
    getOptionsList() {
      let _this = this
      let searchQuery = {
        curActInstId: _this.pListData.curActInstId,
        procDefId: _this.pListData.procDefId,
      };
      workflowNextactsNew(searchQuery)
        .then((res) => {
          _this.processList = res.data;

          _this.processNodeClick(_this.processList[0]);
          _this.actDefType = res.data[0].actDefType;
          if (
            _this.pListData.businessData.yNTrain &&
            _this.pListData.businessData.yNTrain === "1"
          ) {
            let actDefOrder = res.data.filter((x) => x.actDefId === "peixun")[0]
              .actDefOrder;
            let arr2 = res.data.filter((x) => x.actDefOrder <= actDefOrder);
            _this.processList = arr2;
          } else {
            _this.processList = res.data.filter((x) => x.actDefId !== "peixun");
          }
          if (_this.processList.length == 1) {
            _this.processNodeClick(_this.processList[0]);
          }
          _this.loading = false;
          _this.$nextTick(() => {
            console.log(
              "this.processList[0].actDefName",
              _this.processList[0].actDefName
            );
            _this.$refs.myTree.setCurrentKey(_this.processList[0].actDefName);
          });
        })
        .catch((e) => {
          _this.loading = false;
        });
    },
    //流程节点点击触发 带出可选成员
    processNodeClick(val) {
      console.log("业务数据====>", this.pListData);
      getExtAttributeModel(
        this.pListData.procDefId,
        this.pListData.curActDefId
      ).then((res) => {
        console.log("扩展属性====>", res);
        this.kuozhanshuju = res.data;
        if (res.data != "") {
          res.data.forEach((element) => {
            if (element.objKey == "bohui") {
              this.$emit("yingchanbohui", true);
              console.log("bohui111");
            }
            if (element.objKey == "tongguo") {
              //this.istongguo = true;
            }
            if (element.objKey == "jielun") {
              //this.isjielun = true;
            }
          });
        }
      });
      let user_info = JSON.parse(sessionStorage.getItem("USER_INFO"));

      if (this.nextData != "") {
        if (val.actDefName == this.nextData.actDefName) {
          return;
        }
      }
      this.nextData = val;
      console.log("nextData", this.nextData);
      this.userList = []; //代选
      this.receiveUserList = []; //已选
      let params = {
        userOrgId: user_info.deptId,
        curActInstId: this.pListData.curActInstId,
        destActDefId: val.actDefId,
      };
      // if (val.actDefId == "qitabumenhuishen") {
      //   this.$emit("yingchanbohui", false);
      // } else {
      //   this.$emit("yingchanbohui", true);
      // }
      // console.log('this.detatailsData====>', this.detatailsData)
      getNextactuserByPending(params)
        .then((res) => {
          // console.log('res====>', res.data)
          // this.userList = res.data.filter(item => item.id != '100')
          if (
            val.actDefId == "qitabumenhuishen" &&
            this.detatailsData &&
            this.detatailsData.deptIds
          ) {
            let arr = [];
            let deptId = user_info.deptId.toString();
            res.data.forEach((item) => {
              this.detatailsData.deptIds.forEach((item2) => {
                if (item.type === "ORG") {
                  if (item.id === item2.toString() && item.id !== deptId) {
                    arr.push(item);
                  }
                } else {
                  if (
                    item.parentId === item2.toString() &&
                    item.parentId !== deptId
                  ) {
                    arr.push(item);
                  }
                }
              });
            });
            this.userList = this.handleTree(arr, "id");
            console.log("11");
          } else {
            this.userList = this.handleTree(res.data, "id");
            //如果待选人员里就一个人，就自己加入到已选人员名单里z
            this.rtuserList(this.userList);
            this.userNodeClick(this.morendta);
          }
        })
        .catch((e) => {
          this.loading = false;
        });
    },
    //点击成员 导入可选成员
    userNodeClick(val) {
      console.log("val", val);
      if (val.type == "USER") {
        let arr = this.receiveUserList.filter((x) => x.id === val.id);
        if (arr.length <= 0) {
          this.receiveUserList.push(val);
        }
      }
    },
    //移除成员
    removeData(item) {
      var arr = [];
      this.receiveUserList.forEach((element) => {
        if (element.id != item.id) {
          arr.push(element);
        }
      });
      this.receiveUserList = arr;
    },
    ceshi() {
      console.log("11");
      this.$refs.myTree.setCurrentKey(this.processList[0].actDefName);
    },
    rtuserList(data) {
      if (data.length == 1 && data[0].children != undefined) {
        this.rtuserList(data[0].children);
      } else {
        this.morendta = data[0];
        return;
      }
    },
  },
};
</script>
