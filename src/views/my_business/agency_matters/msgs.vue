<template>
  <!-- 我的消息，适配shlk版本 -->
  <div class="app-container news el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane name="0">
          <span slot="label">
            {{ $t('myItem.msg_unread') }}({{
              $store.getters.Number.number ? $store.getters.Number.number : 0
            }})</span
          ></el-tab-pane
        >
        <el-tab-pane :label="$t('myItem.msg_read')" name="1">
          <span slot="label"> {{ $t('myItem.msg_read') }}({{ donetotal ? donetotal : 0 }})</span>
        </el-tab-pane>
      </el-tabs>
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.msgInfo"
                :placeholder="$t('myItem.msg_key_search')"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
<!--              <el-button @click="oneClickRead">{{ $t('myItem.a_confirm_read') }}</el-button>-->
            </div>
            <div class="cell-right"></div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @selection-change="handleSelectionChange"
          :row-class-name="tableRowClassName"
          header-align="left"
        >
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column :label="$t('myItem.msg_content')" align="left" prop="msgInfo">
            <template slot-scope="scope">
              <span style="cursor: pointer; color: #409EFF" @click="jumpAddress(scope.row)">{{ scope.row.msgInfo }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('myItem.handle_receive_time')" align="left" prop="createTime">
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
              }}</span>
            </template>
          </el-table-column>
          <!-- -->
          <el-table-column v-if="activeName == 1" :label="$t('file_count.read_time')" align="left" prop="recoveryTime">
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.recoveryTime, "{y}-{m}-{d} {h}:{i}")
              }}</span>
            </template>
          </el-table-column>
<!--          <el-table-column-->
<!--            :label="$t('myItem.msg_operation')"-->
<!--            v-if="activeName == 0"-->
<!--            align="left"-->
<!--            class-name="small-padding fixed-width"-->
<!--            width="95"-->
<!--            fixed="right"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              <el-button-->
<!--                size="mini"-->
<!--                type="text"-->
<!--                @click="singleRead(scope.row)"-->
<!--                v-if="!scope.row.msgStatus"-->
<!--              >{{ $t('myItem.msg_confirm_read') }}-->
<!--              </el-button>-->
<!--              <span v-else>{{ $t('myItem.msg_read') }}</span>-->
<!--            </template>-->
<!--          </el-table-column>-->
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import { queryNews, readNews } from "@/api/my_business/news";
export default {
  name: "Post",
  dicts: ["sys_normal_disable", "sys_msgType"],
  data() {
    return {
      fileTypeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      donetotal: 0,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        msgInfo: null,
        postName: undefined,
        status: undefined,
        msgType: "msg",
      },
      // 表单参数
      form: {},
      varChangeColor1: true,
      varChangeColor2: false,
      activeName: 0,
      multipleIdList: []
    };
  },
  created() {
    let query = {
      pageNum: 1,
      pageSize: 100,
      classStatus: 1,
    };
    // 查询已读数量
    queryNews({ pageNum: 1, pageSize: 10, msgStatus: "1" }).then((response) => {
      this.donetotal = response.data.total;
    });
    this.queryParams.msgStatus = this.activeName;
    // 获取列表
    this.getList();
  },
  methods: {
    handleFileType(obj) {
      if (obj && this.fileTypeList) {
        let arr = this.fileTypeList.filter((x) => x.id === obj)[0];
        if (arr) {
          return arr.className;
        }
        return obj;
      }
    },
    singleRead(data){
      this.multipleIdList = []
      this.multipleIdList.push(data.id)
      this.readNews(this.multipleIdList)
    },
    readNews(idList) {
      // 确认已读
      this.loading = true
      if (this.$store.getters.Number.number > idList.length) {
        this.$store.commit("SET_NUMBER", this.$store.getters.Number.number - idList.length);
      } else {
        this.$store.commit("SET_NUMBER", "");
      }
      readNews(idList).then((response) => {
        this.$modal.msgSuccess(this.$t('doc.this_dept_operation_succ'));
        this.getList();
        this.donetotal = this.donetotal + 1;
        this.loading = false
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      queryNews(this.queryParams).then((response) => {
        this.postList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
        msgInfo: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.msgInfo = null
      this.resetForm("queryForm");
      this.handleQuery();
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.p7 == this.$t('myItem.msg_read')) {
        //console.log(row.p7);
        return "yidu";
      }
      return "";
    },
    state(tab) {
      this.queryParams.msgStatus = this.activeName;
      this.handleQuery();
    },
    oneClickRead(){
      if(this.multipleIdList.length>0){
        this.readNews(this.multipleIdList)
      }
    },
    handleSelectionChange(selection) {
      this.multipleIdList = []
      for (const selectionKey in selection) {
        this.multipleIdList.push(selection[selectionKey].id)
      }
    },
    normalizeUrlToCurrentOrigin(targetUrl) {
      try {
        // 空值处理
        if (!targetUrl) return '';

        const current = new URL(window.location.href);
        const target = new URL(targetUrl, window.location.href);

        // 不处理非HTTP(S)协议
        if (!['http:', 'https:'].includes(target.protocol)) {
          return target.toString();
        }

        // 如果origin不同则替换
        if (current.origin !== target.origin) {
          return new URL(
            target.pathname + target.search + target.hash,
            current.origin
          ).toString();
        }

        return target.toString();
      } catch (e) {
        console.error('URL处理失败:', e);
        // 解析失败时尝试返回相对路径或原URL
        return targetUrl.startsWith('/')
          ? new URL(targetUrl, window.location.origin).toString()
          : targetUrl;
      }
    },
    jumpAddress(row){
      this.singleRead(row)
      window.open(this.normalizeUrlToCurrentOrigin(row.pcUrl))
    }
  },
};
</script>
<style lang="scss" >
@import "../../../../public/css/poctstyle.css";
</style>
