<template>
  <div class="agency_matters app-container el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane name="taskToDo">
          <span slot="label">
            {{ $t('myItem.handle_pending') }}({{ statustotal == "" ? 0 : statustotal }})</span
          >
        </el-tab-pane>
        <el-tab-pane name="taskDealed">
          <span slot="label"> {{ $t('myItem.handle_done') }}({{ doneTotal }})</span>
        </el-tab-pane>
        <el-tab-pane name="taskFinish">
          <span slot="label"> {{ $t('myItem.handle_finish') }}({{ finishedTotal }})</span>
        </el-tab-pane>
      </el-tabs>
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        class="demo-form-inline"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim.trim="queryParams.title"
                :placeholder="$t('myItem.handle_title_search')"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
            </div>
            <div class="cell-right"></div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item :label="$t(`myItem.handle_process_type`)" label-width="68px">
                <el-select
                  :placeholder="$t(`myItem.handle_process_type`)"
                  clearable
                  v-model.trim="queryParams.procDefKey"
                >
                  <el-option
                    v-for="item in processTypeList"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t(`doc.this_dept_file_code`)">
                <el-input
                  v-model.trim="queryParams.docId"
                  :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_file_code`)"
                  clearable
                  class="input-with-select"
                >
                </el-input>
              </el-form-item>

              <el-form-item :label="$t('myItem.handle_initiator')">
                <el-input
                  v-model.trim="queryParams.startUserName"
                  style="width: 280px"
                  :placeholder="$t(`doc.this_dept_insert`)+$t(`myItem.handle_initiator`)"
                  clearable
                  class="input-with-select"
                >
                </el-input>
              </el-form-item>

              <el-form-item :label="$t(`myItem.handle_receive_time`)">
                <el-date-picker
                  v-model="sendTimeValue"
                  type="datetimerange"
                  style="width: 280px"
                  :picker-options="pickerOptions"
                  :range-separator="$t(`doc.this_dept_to`)"
                  :start-placeholder="$t(`doc.this_dept_start_date`)"
                  :end-placeholder="$t(`doc.this_dept_end_date`)"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  unlink-panels
                  align="right"
                  size="small"
                >
                </el-date-picker>
              </el-form-item>

            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
              <el-button @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
              <el-button @click="boxClass = false">{{ $t('doc.this_dept_abolish') }}</el-button>
            </div>
          </div>
        </div>
      </el-form>

      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          header-align="left"
        >
          <el-table-column
            :label="$t('myItem.handle_process_type')"
            align="left"
            prop="procDefName"
            key="2"
          />
          <el-table-column :label="$t('myItem.handle_process_title')">
            <template slot-scope="scope">
              <span
                @click="handleDeal(scope.row)"
                style="color: #0144bb; cursor: pointer"
              >{{ scope.row.title}}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="queryParams.status===1?$t('myItem.handle_current_stage'):$t('myItem.handle_stage')"
            align="left"
            prop="curActName"
          />
          <el-table-column
            :label="$t(`file_handle.current_act_assignee`)"
            align="left"
            prop="assigneeUserName"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('doc.this_dept_file_code')"
            align="left"
            prop="docId"
            width="200"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('doc.this_dept_file_versions2')"
            align="left"
            prop="versionValue"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('myItem.handle_initiator')"
            align="left"
            prop="startUserName"
            width="130"
          />
          <el-table-column :label="$t('myItem.handle_receive_time')" sortable="custom" prop="sendTime" width="150">
            <template slot-scope="scope">
              <span>{{
                  parseTime(scope.row.sendTime)
                    ? parseTime(scope.row.sendTime).substring(0, 16)
                    : ""
                }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getListData"
      />
    </div>
    <!-- 流程处理抽屉组件 -->
    <DealDrawer v-if="dealDrawerShow" ref="dealDrawer" @closeDrawer="handleCloseChange"></DealDrawer>
  </div>
</template>

<script>
import { getDoing, workflowToDoList } from "@/api/my_business/workflow";
import DealDrawer from "@/components/DealDrawer";
import {queryFlowList} from "@/api/setting/docClassFlow";
import item from "@/layout/components/Sidebar/Item.vue";

export default {
  name: "Post",
  dicts: ["sys_normal_disable", "sys_operterType"],
  components: {
    DealDrawer
  },
  data() {
    return {
      timer: undefined,
      dealDrawerShow: false,
      boxClass: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: "sendTime",
        isAsc: "desc",
        title: "",
        docId: "",
        params: {
          startTime: "",
          endTime: "",
        },
        status: 1,
      },
      // 表单校验
      rules: {
        postName: [
          {
            required: true,
            message: this.$t('file_set.signature_not_null'),
            trigger: "blur",
          },
        ],
        postCode: [
          {
            required: true,
            message: this.$t('file_set.signature_not_null'),
            trigger: "blur",
          },
        ],
        postSort: [
          {
            required: true,
            message: this.$t('file_set.signature_not_null'),
            trigger: "blur",
          },
        ],
      },
      doneTotal: 0,
      finishedTotal: 0,
      taskData: [], // 任务数据
      deptOptions: [],
      drawer: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pListData: {},
      statustotal: "",
      adUpdateDisuseDetails: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      activeName: "taskToDo",
      classLevelOptions: [],
      sendTimeValue: "",
      workflowFocusCbAdded: false,
    };
  },
  watch: {
    sendTimeValue(val) {
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    }
  },
  created() {
    // 初始化获取待办清单
    this.getListData()
    // 获取已办、办结统计页签数量
    this.getOtherActiveCount()
    //
    this.initProcessType()

    this.setupFocusListener()

    this.setupDoingListener()
  },
  activated() {
    this.setupFocusListener()
  },
  deactivated() {
    this.removeFocusListener()
  },
  beforeDestroy() {
    this.removeFocusListener()
  },
  methods: {
    //建立addEventListener事件
    setupDoingListener() {
      if (!this.timer) {
        this.timer = setInterval(this.getDoingByCurActInstId, 2000 );
      }
    },
    //建立addEventListener事件
    setupFocusListener() {
      if (!this.workflowFocusCbAdded) {
        window.addEventListener('focus', this.handleWindowFocus);
        this.workflowFocusCbAdded = true
      }
    },
    //销毁addEventListener事件
    removeFocusListener() {
      window.removeEventListener('focus', this.handleWindowFocus)
      this.workflowFocusCbAdded = false
    },
    handleWindowFocus() {
      this.getListData()
    },
    getDoingByCurActInstId(){
      let list = this.postList.filter(item=>item.doing).map(item=>item.curActInstId)
      if (list.length>0) {
        getDoing(list).then((response) => {
          if (list.some(item=>!response.data[item])){
            this.getListData()
          }
        });
      }
    },
    // 流程处理事件
    handleDeal(row) {
      if (row.doing) {
        this.$message.warning(this.$t('file_handle.process_executed'))
        return
      }
      this.dealDrawerShow = true;
      let flowUrl = this.buildFlowUrl(row)
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(flowUrl)
      } else {
        this.$nextTick(() => {
          this.$refs.dealDrawer.init(flowUrl);
        });
      }
    },
    buildFlowUrl(row) {
      let res = row.url;
      //待办=1、已办=2、办结=3
      res += "&invokeFrom=list&status="+row.status
      return res;
    },
    getOtherActiveCount() {
      // 获取已办统计数量
      this.loading = true
      let params = {
        pageNum: 1,
        pageSize: 10,
        status: 2,
      };
      workflowToDoList(params).then((response) => {
        this.doneTotal = response.total || 0;
        this.loading = false
      });
      // 获取办结统计数量
      this.loading = true
      let params2 = {
        pageNum: 1,
        pageSize: 10,
        status: 3,
      };
      workflowToDoList(params2).then((response) => {
        this.finishedTotal = response.total || 0;
        this.loading = false
      });
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    handleUpdateShow(row) {
      return row.curActDefId.indexOf("faqishenqin") !== -1; //判断是否是第一步
    },
    closeEdit() {
      this.drawer = false;
      this.getListData();
    },
    // closeDrawer() {
    //   this.drawer = false;
    //   this.getListData();
    // },
    /** 查询列表 */
    getListData() {
      this.loading = true;
      this.postList = [];
      this.queryParams.searchType = 'original_bpmn'
      workflowToDoList(this.queryParams).then((response) => {
        if (this.activeName == "taskToDo") {
          // 待办
          this.statustotal = response.total;
          this.$store.commit("SET_THINGNUMBER", response.total || 0);
          this.total = response.total;
        }
        if (this.activeName == "taskDealed") {
          // 已办
          this.doneTotal = response.total;
          this.total = response.total;
        }
        if (this.activeName == "taskFinish") {
          // 办结
          this.finishedTotal = response.total;
          this.total = response.total;
        }

        this.postList = response.rows;
        if (response.rows != null && this.queryParams.searchType != 'original_bpmn') {
          // 非原生流程平台待办集合
          response.rows.forEach((element, index) => {
            if (element.businessData != null) {
              this.postList[index].docName = element.businessData.docName;
            } else {
              this.postList[index].docName = "";
            }
          });
        }
        this.loading = false;
      });
    },
    handleDocName(e) {
      let pdocName = e.docName;
      return pdocName;
    },
    // 取消按钮
    handleCloseChange() {
      this.dealDrawerShow = false
      this.getListData();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getListData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.queryParams.title = "";
      // status: 1,
      this.queryParams.startUserName = null;
      this.queryParams.deptName = null;
      this.queryParams.docName = null;
      this.queryParams.businessData = null;
      this.queryParams.procDefName = null;
      this.queryParams.procDefKey = null;
      this.queryParams.docClass = null;
      this.queryParams.deptId = null;
      this.queryParams.startUserId = null;
      this.queryParams.docId = null;
      this. queryParams.params = {
        startTime: "",
        endTime: "",
      };
      this.sendTimeValue = "";
      this.handleQuery();
    },
    /** 详情按钮操作 */
    handleDetails(row) {

    },
    state(tab) {
      let p = Number(tab.index) + 1;
      if (p == 1) {
        this.varChangeColor1 = true;
        this.varChangeColor2 = false;
      }
      if (p == 2 || p == 3) {
        this.varChangeColor1 = false;
        this.varChangeColor2 = true;
      }
      this.queryParams.status = p;
      this.queryParams.pageNum = 1;
      this.queryParams.title = "";
      this.total = 0;
      this.getListData();
      // this.getshulian();
    },
    /** 初始化流程类型 */
    initProcessType(){
      let _this = this
      queryFlowList({}).then(res=>{
        _this.processTypeList = res.data
      })
    },
  },
};
</script>
<style lang="scss">
@import "../../../../public/css/poctstyle.css";
</style>
