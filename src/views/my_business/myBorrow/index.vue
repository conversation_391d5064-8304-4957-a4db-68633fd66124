<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t('myItem.borrow') }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.searchValue"
                :placeholder="$t('myItem.borrow_search')"
                clearable
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
            </div>
            <div class="cell-right">
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item :label="$t('myItem.borrow_file_status')">
                <el-select
                  :placeholder="$t('myItem.borrow_file_status')"
                  v-model.trim="queryParams.status"
                  clearable
                >
                  <el-option :label="$t('myItem.borrow_in_force')" value="1"></el-option>
                  <el-option :label="$t('myItem.borrow_expired')" value="2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('myItem.borrow_status')" :label-width="columnLangSizeFlag ? '122px' : '68px'">
                <el-select
                  :placeholder="$t('doc.this_dept_change_type')"
                  clearable
                  v-model.trim="queryParams.borrowStatus"
                >
                  <el-option :label="$t('myItem.borrow_borrowing')" value="0"></el-option>
                  <el-option :label="$t('myItem.borrow_out_of_date')" value="1"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
              <el-button @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
              <el-button @click="boxClass = false">{{ $t('doc.this_dept_abolish') }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table header-align="left" v-loading="loading" :data="dataList">
          <el-table-column
            :label="$t('myItem.borrow_file_type')"
            :formatter="formatterDocClass"
            align="left"
            prop="docClass"
            min-width="150"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t('myItem.borrow_file_name')"
            align="left"
            min-width="200"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.borrowStatus=='0'&&scope.row.status == '1'" class="wenjcolor"><a @click="handlePreview(scope.row, 'COMPANY')">{{scope.row.docName}}</a></span>
              <span v-else >{{scope.row.docName}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('myItem.borrow_file_id')"
            align="left"
            min-width="155"
            prop="docId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('myItem.borrow_file_ver')"
            align="left"
            min-width="95"
            prop="versionValue"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('myItem.borrow_preparation_dept')"
            align="left"
            min-width="155"
            prop="deptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('myItem.borrow_preparer')"
            align="left"
            min-width="155"
            prop="nickName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('myItem.borrow_file_status')"
            align="left"
            min-width="155"
            prop="status"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span style="color: red" v-if="scope.row.status == '2'">{{ $t('myItem.borrow_expired') }}</span>
              <span style="color: #09bb25" v-else>{{ $t('myItem.borrow_in_force') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('myItem.borrow_status')"
            align="left"
            min-width="155"
            prop="borrowStatus"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span style="color: red" v-if="scope.row.borrowStatus=='1'">{{ $t('myItem.borrow_out_of_date') }}</span>
              <span style="color: #09bb25" v-else>{{ $t('myItem.borrow_borrowing') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('myItem.borrow_start_time')"
            align="left"
            min-width="155"
            prop="startTime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{parseTime(scope.row.startTime, "{y}-{m}-{d}")}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('myItem.borrow_end_time')"
            align="left"
            min-width="155"
            prop="endTime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{scope.row.isForever===yes?$t('myItem.borrow_long_term_effc'):parseTime(scope.row.endTime, "{y}-{m}-{d}")}}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 添加或修改-->
    </div>
    <as-pre-view
      :visible="viewShow"
      :id="viewId"
      ref="viewRef"
      @close="close"
    >
    </as-pre-view>
  </div>
</template>

<script>
import { settingDocClassList } from "@/api/file_settings/type_settings";
import { myBorrow } from '@/api/process/borrowApply'
export default {
  name: "MyBorrow",
  dicts: [],
  data() {
    return {
      yes: 'Y',
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawerDetails: false,
      changeShow: false,
      dataType: 'stdd',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchValue: null,
        borrowStatus: null,
        status: null,
        borrowUserName: JSON.parse(sessionStorage.getItem("USER_INFO")).userName,
      },
      docClassList: [],
      docClassIdList: [],
      docClassTree: [],
      viewId: undefined,
      viewShow: false,
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getSettingDocClassTreeseList();
  },
  mounted() {
  },
  methods: {
    handlePreview(row, source) {
      this.viewId = row.fileId;
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      if (_this.docClassList) {
        let item = _this.docClassList.find(item=>item.id===cellValue)
        return item?item.className:cellValue
      }
      return cellValue
    },
    getSettingDocClassTreeseList() {
      let _this = this
      settingDocClassList({classStatus: "1", dataType:this.dataType,neClassType:'foreign',openPurview:true}).then(res => {
        this.docClassList = JSON.parse(JSON.stringify(res.rows))
        let data = res.rows.filter(item=>item.purview)
        this.docClassIdList = data.map(item=>item.id)
        this.docClassTree = this.handleTree(JSON.parse(JSON.stringify(data)), "id", "parentClassId")
        this.getList();
      });
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      myBorrow(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.searchValue= null
      this.queryParams.borrowStatus= null,
      this.queryParams.status= null,
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
