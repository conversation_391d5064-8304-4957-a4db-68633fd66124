<template>
  <el-card>
    <el-input
      :placeholder="placeholderText"
      class="input-with-select bg_search"
      v-model.trim="queryParams.nameOrCode"
      @keyup.enter.native="handleQuery"
      clearable
    >
      <el-select v-model="queryParams.searchType" slot="prepend" placeholder="请选择" style="width: 130px;">
        <el-option label="业务字段" value="business"></el-option>
        <el-option label="文件内容" value="content"></el-option>
      </el-select>
      <el-button slot="append" @click="handleQuery">搜索</el-button>
    </el-input>
    <div class="search-tips" v-if="queryParams.searchType === 'business'">
      可搜索：文件编号、文件名称、文件版本号、文件状态、物料编号、文件类型、编制人、内部文件编号
    </div>
    <div class="search-tips" v-if="queryParams.searchType === 'content'">
      请输入要搜索的文件内容关键词
    </div>
    <div class="search_list">
      <div class="total">
        共有<span class="sz">{{total}}</span>条搜索结果（实际可见条数取决于您的访问权限）
      </div>
      <el-table
        header-align="left"
        v-loading="loading"
        :data="dataList"
        highlight-current-row
        @row-click="handleCurrentChange"
      >
        <el-table-column label="" align="left" prop="" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <!-- 添加自定义的类名并定义鼠标光标为手型 -->
            <el-table-row
              class="cursor: pointer"
            >
              <div class="list">
                <div class="title">
                  <span v-html="textRendering(scope.row.docName)">{{scope.row.docName}}</span> <span class="small" v-html="textRendering(scope.row.docId)">{{scope.row.docId}}</span>
                  <el-tag :type="scope.row.status == 1 ? 'success' : 'info'">{{scope.row.status == 1 ? '有效' : '失效'}}</el-tag>
                </div>
                <div class="other">
                  <span class="lab">版本：<span class="blue">{{scope.row.versionValue}}</span> </span>
                  <span class="lab">文件类型：{{scope.row.docClass}}</span>
                  <span class="lab">生效时间：{{parseTime(scope.row.releaseTime, "{y}-{m}-{d}")}}</span>
                  <span class="lab">编制部门：{{scope.row.deptName}}</span>
                </div>
              </div>
            </el-table-row>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"/>

    <el-drawer
      :visible.sync="drawerShow"
      direction="rtl"
      size="90%"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <main-component ref="mainComponent" :code="path+code"  :data="data"  :dataType="dataType" @close="handleCloseChange"></main-component>
    </el-drawer>
  </el-card><!--------el-card--------->

</template>

<script>
import mainComponent from "@/components/mainComponent/index.vue";
import {  listDoc } from '@/api/search_results/searchGlobal'
export default {
  name: "Receive",
  components: {
    mainComponent
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nameOrCode: undefined,
        searchType: 'business',
      },
      searchKeyword: null,
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      dataType: undefined,
    };
  },
  computed: {
    searchQuery() {
      return this.$store.getters.searchQuery;
    },
    placeholderText() {
      return this.queryParams.searchType === 'business'
        ? '请输入文件编号、文件名称、文件版本号、文件状态、物料编号、文件类型、编制人、内部文件编号'
        : '请输入要搜索的文件内容关键词';
    }
  },
  watch: {
    searchQuery(newValue, oldValue) {
      if(newValue != oldValue){
        this.queryParams.nameOrCode = newValue
        this.handleQuery();
      }
    }
  },
  created() {
    if(this.$store.getters.searchQuery){
      this.queryParams.nameOrCode = this.$store.getters.searchQuery
    }
  },
  mounted() {
    this.handleQuery();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      const api = this.queryParams.searchType === 'business' ? listDoc : listDocContent;
      api(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.loading = false;
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0' + row.code;
      }
      return row.code;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.searchKeyword = this.queryParams.nameOrCode; // 设置搜索关键词
      this.getList();
    },
    selectable(row, index){
      return !row.receive;
    },
    textRendering(text){
      let keyword = this.searchKeyword;
      if (keyword) {
        // keyword = this.escapeRegExp(keyword);  // 使用转义后的关键字
        // const encodedKeyword = encodeURI(keyword); // 对关键词进行编码
        // const escapedKeyword = keyword.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');

        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, '<span class="query">$1</span>');
      }
      return text;
    },
    escapeRegExp(value) {
      return value.replace(/ [.*+?^$ {} ()| [\\]\\\\]/g, '\\\\$&');
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      //外来文件直接跳转
      if(row.dataType == 'external'){
        //document_account/external/index
        this.$router.push({path:'/document_account/external/index',query: {content: this.queryParams.nameOrCode, type: row.docClass}})
        return
      }
      _this.handleDetail({type:'detail/index',docId:row.docId,versionId:row.id,flag:'0',status:row.status,dataType:row.dataType})
    },
    handleDetail(row){
      let _this = this
      _this.code = row.type
      _this.data = row
      _this.dataType = row.dataType
      _this.drawerShow = true
    },
    handleCurrentChange(row, event, column){
      this.handleDetails(row)
    },
    handleCloseChange(){
      this.drawerShow = false
      this.getList();
    },
  },
};
</script>

<style scoped>
.input-with-select >>> .el-input-group__prepend {
  background-color: #fff;
}
.search-tips {
  font-size: 12px;
  color: #909399;
  margin: 5px 0;
  padding-left: 10px;
}
</style>
