<template>
  <div class="sso-page" v-loading="loading"></div>
</template>

<script>
// 20230601
// 爱数7集成体系文件系统入口
import { ssoCheck } from "@/api/login";
import { setToken, removeToken } from "@/utils/auth";
import cache from "@/plugins/cache";
export default {
  name: "ssoCheck",
  data() {
    return {
    };
  },
  created() {
    // 开始执行SSO检查
    this.ssoCheck()
  },
  methods: {
    ssoCheck() {
      let self = this
      // 删除令牌和清理会话信息
      removeToken()
      // sessionStorage.setItem("USER_INFO",null)
      // 首先鉴定访问来源
      let client = self.$route.query.client;
      // 爱数用户令牌
      let token = self.$route.query.token;
      // 指定展示页面URL
      let redirect = self.$route.query.redirect;
      if(client != 'as7' || token == undefined || token == '') {
        alert("非法访问")
        return
      }
      self.loading = true;
      ssoCheck(client,token).then((res) => {
        self.loading = false;
        if (res.code === 200 && res.msg == '体系文件用户令牌有效') {
          // SSO验证通过
          //setToken(res.data)
          // 该系统页面将以弹窗方式集成在爱数7网页端，携带参数client=as7，后面的页面将去掉右上角退出登录，且令牌失效的时候跳转至 爱数7网页端登录入口
          cache.session.set('client', client)
          console.log(res.data.rzSsoUrl)
          console.log(res.data.rzUserToken)
          console.log(res.data.rzSsoUrl+`/ssoredirect?&token3=${res.data.rzUserToken}`)
          window.location.href = res.data.rzSsoUrl+`/ssoredirect?&token3=${res.data.rzUserToken}`
        } else if (res.code === 200 && res.msg == '鉴定爱数用户令牌是否有效异常') {
          // AS SSO验证不通过，跳转到SSO提示界面
          cache.session.set('client', client)
          this.$router.push({ path: "/sso_error?data="+ res.data }).catch(() => {});
        } else if (res.code === 200 && res.msg == '体系文件用户令牌生成失败') {
          // DMS服务没有此用户，跳转到SSO提示界面
          cache.session.set('client', client)
          this.$router.push({ path: "/sso_error?data="+ res.data }).catch(() => {});
        } else {
          // SSO验证失败
          alert(res.msg)
        }
      });
    },
  },
};
</script>
