<template>
  <div id="app"  v-loading="loading">
    <el-card>
      <div class="search-box" style="margin-left: 25%">
        <div class="custom-select">
          <el-select
            v-model="queryParams.searchType"
            style="width: 120px; margin-right: 10px">
            <el-option
              v-for="item in searchTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <i class="el-icon-arrow-down custom-arrow"></i>
        </div>
        <el-input clearable style="width: 700px" :placeholder="getPlaceholder()" prefix-icon="el-icon-search" @keyup.enter.native="getSearchLinkLogList()" v-model="queryParams.keyword"> </el-input>
        <el-button type="primary" icon="el-icon-search" @click="getSearchLinkLogList()">{{ $t('search.search_btn') }}</el-button>
      </div>

      <div class="ssjg">
        <div class="fl"><i class="el-icon-info"></i><span class="tit">{{ $t('search.result_tip_1') }} {{total}} {{ $t('search.result_tip_2') }}</span></div>
        <div class="fr"><el-button type="primary" :disabled="this.checkedVersionIds.length == 0" icon="el-icon-download" @click="downloadHandle()" v-hasPermi="['system:search:download']">{{ $t('search.download_btn') }}</el-button></div>
      </div>

      <el-collapse v-model="activeNames" class="collapse-1" @change="changeHandle()">
        <template v-for="(searchResultItem, searchResultIndex) in searchResultList">
          <el-collapse-item :class="searchResultItem.hasChild == '0' ? '' : 'hide-arrow-right'" :name="searchResultItem.versionId" :key="searchResultIndex">
            <template slot="title">
              <div class="tit tit-1">
                 <el-checkbox v-if="searchResultItem.hasPerms || searchResultItem.isBorrow" v-model="checkedVersionIds" :label="searchResultItem.versionId" >
                  <span class="txt" v-html="searchResultItem.docName"></span>
                 </el-checkbox>
                <span v-else class="txt" v-html="searchResultItem.docName"></span>
                <el-link v-if="searchResultItem.hasPerms || searchResultItem.isBorrow" :underline="false" type="primary" @click.stop="handlePreview(searchResultItem, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('search.view_file') }}</el-link>

                <el-link v-if="searchResultItem.hasPerms || searchResultItem.isBorrow" :underline="false" type="primary" @click.stop="handleDetails(searchResultItem)"><i class="el-icon-view el-icon--right"></i> {{ $t('search.view_details') }}</el-link>
              </div>
              <div class="txt-1">
                <span><span>{{ $t('home.search_item_label_1') }}</span><span v-html="searchResultItem.docId"></span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_2') }}</span><span v-html="searchResultItem.versionValue"></span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_3') }}</span><span v-html="searchResultItem.dataTypeName"></span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_4') }}</span><span v-html="searchResultItem.pushDate"></span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('search.compiler') }}</span><span v-html="searchResultItem.userName"></span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('search.file_status') }}</span><el-tag type="success">{{searchResultItem.statusName}}</el-tag></span>
              </div>
              <div class="txt-2">
                <span><span>{{ $t('search.file_content') }}</span><span v-html="searchResultItem.textContent"></span></span>
              </div>
            </template><!--title-->
            <div class="col-list">
              <template v-for="(searchDetailItem, searchDetailIndex) in searchDetailList">
                <div v-if="searchDetailItem.versionId == searchResultItem.versionId" :key="searchDetailIndex">
                  <div class="list" v-if="searchDetailItem.noteDocList.length > 0">
                    <div class="tit tit-2"> <!--<el-checkbox></el-checkbox>--> <span class="txt">{{ $t('doc.this_dept_master_file') }}</span></div>
                    <div class="txt-2">
                      <template v-for="(item, noteDocIndex) in searchDetailItem.noteDocList">
                        <p :key="noteDocIndex"> {{item.docName}}
                          <el-link v-if="searchResultItem.hasPerms" :underline="false" type="primary" @click.stop="handlePreview(item, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
                        </p>
                      </template>
                    </div>
                  </div><!--list-->
                  <div class="list" v-if="searchDetailItem.recordList.length > 0">
                    <div class="tit tit-2"> <!--<el-checkbox></el-checkbox>--> <span class="txt">{{ $t('home.search_item_label_10') }}</span></div>
                    <div class="txt-2">
                      <template v-for="(item, recordIndex) in searchDetailItem.recordList">
                        <p :key="recordIndex"> {{item.docName}}
                          <el-link v-if="searchResultItem.hasPerms" :underline="false" type="primary" @click.stop="handlePreview(item, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
                        </p>
                      </template>
                    </div>
                  </div><!--list-->
                  <div class="list" v-if="searchDetailItem.refDocList.length > 0">
                    <div class="tit tit-2"> <!--<el-checkbox></el-checkbox>--> <span class="txt">{{ $t('home.search_item_label_11') }}</span></div>
                    <div class="txt-2">
                      <template v-for="(item, refDocIndex) in searchDetailItem.refDocList">
                        <p :key="refDocIndex">
                          {{item.docName}}
                          <el-link v-if="searchResultItem.hasPerms" :underline="false" type="primary" @click.stop="handlePreview(item, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
                        </p>
                      </template>
                    </div>
                  </div><!--list-->
                </div>
              </template>
            </div><!--col-list-->
          </el-collapse-item><!--el-collapse-item-->
        </template>
      </el-collapse><!--el-collapse-->

    </el-card><!--------el-card--------->

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getSearchLinkLogList"
    />

    <as-pre-view
      :visible="viewShow"
      :id="viewId"
      ref="viewRef"
      @close="close"
    >
    </as-pre-view>

    <el-drawer
      :visible.sync="drawerShow"
      direction="rtl"
      size="90%"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <main-component ref="mainComponent" :code="path+code"  :data="data"  :dataType="dataType" @close="handleCloseChange"></main-component>
    </el-drawer>
  </div>
  <!--app-->
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import { saveAs } from 'file-saver'
import {
  searchLinkLogList,
  searchLinkLogDetail, searchLinkLogDownload
} from "@/api/system/standard";
import { workflowToDoList } from "@/api/my_business/workflow";
import DealDrawer from "@/components/DealDrawer";
import {searchList} from "@/api/es";
import mainComponent from "@/components/mainComponent/index.vue";
export default {
  name: "Index",
  components: {
    mainComponent,
    DealDrawer
  },
  mixins: [mixin],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: "",
        searchType: "business"
      },
      total: 0,
      searchResultList: [],
      searchDetailList: [],
      viewShow: false,
      showSearchDetail: true,
      dealDrawerShow: false,
      loading: false,
      checkedVersionIds: [],
      viewId: "",
      activeNames: "",
      searchTypeOptions: [
        {
          value: "business",
          label: this.$t('search.business_fields')
        },
        {
          value: "content",
          label: this.$t('search.file_content_keyword')
        }
      ],
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      dataType: undefined,
    };
  },
  watch: {

  },
  created() {
    this.getSearchLinkLogList()
  },
  methods: {
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      //外来文件直接跳转
      if(row.dataType == 'external'){
        //document_account/external/index
        this.$router.push({path:'/document_account/external/index',query: {content: this.queryParams.nameOrCode, type: row.docClass}})
        return
      }
      _this.handleDetail({type:'detail/index',docId:row.docId,versionId:row.versionId,flag:'0',status:row.status,dataType:row.dataType})
    },
    handleDetail(row){
      let _this = this
      _this.code = row.type
      _this.data = row
      _this.dataType = row.dataType
      _this.drawerShow = true
    },
    handleCloseChange(){
      this.drawerShow = false
      this.getSearchLinkLogList();
    },
    changeHandle(){
      this.getSearchLinkLogDetail()
    },
    downloadHandle(){
      let loading = this.$loading({
        lock: true,
        text: "下载中",
        background: "rgba(0, 0, 0, 0.7)",
      });
      searchLinkLogDownload(this.checkedVersionIds).then((res) => {
        const blob = new Blob([res], { type: 'application/zip' })
        saveAs(blob, this.parseTime(Date.now(), "{y}{m}{d}{h}{i}{s}") + ".zip")
        // try {
        //   const blobUrl = window.URL.createObjectURL(res);
        //   const a = document.createElement("a");
        //   a.style.display = "none";
        //   a.download = this.parseTime(Date.now(), "{y}{m}{d}{h}{i}{s}") + ".zip";
        //   a.href = blobUrl;
        //   a.click();
        // } catch (e) {
        // }
      }).finally(()=>{
        loading.close();
      });
    },
    getSearchLinkLogList() {
      this.loading = true;
      this.checkedVersionIds = [];
      this.searchResultList = [];
      searchList(this.queryParams).then((response) => {
        this.searchResultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getSearchLinkLogDetail(){
      let versionIds = [];
      this.searchResultList.forEach((item) => {
        this.activeNames.forEach(str => {
          if(str !== "" && str == item["versionId"] && item["hasChild"] == "0"){
            versionIds.push(str);
          }
        })
      })
      if(versionIds.length > 0){
        searchLinkLogDetail(versionIds).then((res) => {
          this.searchDetailList = res;
          this.relead();
        });
      }
    },
    relead(){
      this.showSearchDetail = false;
      this.$nextTick(() => {
        this.showSearchDetail = true;
      })
    },
    handlePreview(row, source) {
      if (row.encryptFileId != null) {
        this.viewId = row.encryptFileId;
      } else if (row.mergeFileId != null) {
        this.viewId = row.mergeFileId;
      } else {
        this.viewId = row.fileId;
      }
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    getPlaceholder() {
      return this.queryParams.searchType === 'business'
        ? this.$t('search.placeholder_business')
        : this.$t('search.placeholder_content')
    }
  },
};
</script>
<style lang="scss">
.hide-arrow-right>div>.el-collapse-item__header>.el-collapse-item__arrow.el-icon-arrow-right{
  display: none !important;
}
.vue-treeselect__control {
  height: 32px;
}
.text-name {
  width: 200px; /* 设置元素宽度 */
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 文本溢出显示省略号 */
  cursor: pointer; /* 设置鼠标样式 */
}
.text-name:hover {
  white-space: normal; /* 鼠标悬浮时换行显示全部内容 */
  overflow: visible; /* 不再隐藏文本 */
  text-overflow: clip; /* 移除省略号 */
}
em {
  color: coral;
  font-weight: bolder;
  font-size: 17px;
}
.search-box {
  display: flex;
  align-items: center;
  margin: 8px 0;

  .el-input {
    margin: 0 10px;
    width: 400px;
  }

  .el-button {
    margin-left: 10px;
  }
}
.ssjg {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  padding: 6px 15px;
  background: #f5f7fa;
  border-radius: 4px;

  .fl {
    display: flex;
    align-items: center;

    .el-icon-info {
      color: #409EFF;
      margin-right: 8px;
      font-size: 16px;
    }

    .tit {
      color: #606266;
      font-size: 14px;
    }
  }
}
.collapse-1 {
  border: none;

  .el-collapse-item {
    margin-bottom: 6px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-collapse-item__header {
      padding: 8px 15px;
      background: #fafafa;
      border-bottom: none;

      &:hover {
        background: #f5f7fa;
      }
    }

    .el-collapse-item__content {
      padding: 8px 15px;
    }
  }

  // 标题样式
  .tit {
    &.tit-1 {
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      .txt {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin-right: 15px;
      }

      .el-link {
        font-size: 13px;
      }
    }

    &.tit-2 {
      font-size: 14px;
      color: #606266;
      margin: 8px 0 6px;
      padding-left: 10px;
      border-left: 3px solid #409EFF;
    }
  }

  // 文本内容样式
  .txt-1 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 6px 0;
    color: #606266;
    font-size: 13px;

    span {
      display: flex;
      align-items: center;
      margin: 2px 0;

      &:first-child {
        color: #909399;
        margin-right: 5px;
      }
    }

    .el-divider--vertical {
      margin: 0 12px;
      height: 14px;
    }

    .el-tag {
      margin-left: 5px;
    }
  }

  .txt-2 {
    color: #606266;
    font-size: 13px;
    line-height: 1.6;

    p {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      margin: 0;
      border-bottom: 1px dashed #EBEEF5;

      &:last-child {
        border-bottom: none;
      }

      .el-link {
        font-size: 13px;
      }
    }
  }

  // 子列表样式
  .col-list {
    background: #fafafa;
    border-radius: 4px;
    margin-top: 6px;

    .list {
      padding: 8px;
      border-bottom: 1px solid #EBEEF5;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
.pagination-container {
  margin-top: 12px;
  padding: 8px 0;
  text-align: right;
}
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}
@import "../../public/css/poctstyle.css";

/* 重置下拉箭头样式 */
/*.el-select > .el-input {
  .el-input__suffix {
    .el-input__suffix-inner {
      .el-select__caret {
        display: inline-block !important;
        width: 0 !important;
        height: 0 !important;
        border: 5px solid transparent !important;
        border-top-color: #C0C4CC !important;
        margin-top: 15px !important;
        transform: none !important;

        &.is-reverse {
          transform: rotate(180deg) !important;
          margin-top: 7px !important;
        }
      }
    }
  }

  &:hover .el-input__suffix {
    .el-select__caret {
      border-top-color: #409EFF !important;
    }
  }
}*/

/* 移除之前的所有箭头相关样式 */
/*.el-select .el-input__suffix,
.el-select .el-input__suffix-inner,
.el-input__icon,
.el-input__icon:after {
  all: initial;
}*/

.custom-select {
  position: relative;
  display: inline-block;

  .custom-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #C0C4CC;
    font-size: 12px;
    pointer-events: none;
  }

  &:hover .custom-arrow {
    color: #409EFF;
  }
}

/* 隐藏原有的箭头 */
/*.el-select .el-input__suffix {
  display: none;
}*/
</style>
