<template>
  <div class="sso-page" v-loading="loading"></div>
</template>

<script>
import { setToken, removeToken } from "@/utils/auth";
export default {
  name: "ssoCheck",
  data() {
    return {
    };
  },
  mounted() {
    // 开始执行SSO检查
    this.ssoCheck()
  },
  methods: {
    ssoCheck() {
      let self = this
      // 删除令牌和清理会话信息
      removeToken()
      // 爱数用户令牌
      let token = self.$route.query.token;
      // 指定展示页面URL
      let redirect = self.$route.query.redirect;
      if( token&&redirect) {
        console.log("oa单点登录到>>>>>>>>>>",decodeURIComponent(redirect))
        // SSO验证通过
        setToken(token)
        // document.location.href =  redirect
        this.$router.push(decodeURIComponent(redirect))
      }else{
        alert("非法访问")
      }

    },
  },
};
</script>
