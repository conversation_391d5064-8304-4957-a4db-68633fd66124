<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t('sys_mgr.role') }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item :label="$t('sys_mgr.user_role_name')" prop="roleName">
          <el-input
            v-model.trim="queryParams.roleName"
            :placeholder="$t('sys_mgr.role_input_name')"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr.user_auth_char')" prop="roleKey">
          <el-input
            v-model.trim="queryParams.roleKey"
            :placeholder="$t('sys_mgr.role_input_auth_char')"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('doc.this_dept_status')" prop="status">
          <el-select
            v-model.trim="queryParams.status"
            :placeholder="$t('sys_mgr.role_status')"
            clearable
            size="small"
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('file_set.version_create_time')">
          <el-date-picker
            v-model.trim="dateRange"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('doc.this_dept_start_date')"
            :end-placeholder="$t('doc.this_dept_end_date')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >{{ $t('doc.this_dept_search') }}</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:role:add']"
          >{{ $t('doc.this_dept_new_add') }}</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:role:edit']"
          >{{ $t('doc.this_dept_edit') }}</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:role:remove']"
          >{{ $t('doc.this_dept_delete') }}</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:role:export']"
          >{{ $t('doc.exterior_dept_export') }}</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="roleList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!--
          <el-table-column label="角色编号" prop="roleId" width="120" />-->
          <el-table-column
            :label="$t('sys_mgr.user_role_name')"
            prop="roleName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('sys_mgr.user_auth_char')"
            prop="roleKey"
            :show-overflow-tooltip="true"
          />
          <el-table-column :label="$t('sys_mgr.role_display_order')" prop="roleSort" />
          <el-table-column :label="$t('doc.this_dept_status')" align="center" >
            <template slot-scope="scope">
              <el-switch
                v-model.trim="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('file_set.version_create_time')"
            align="center"
            prop="createTime"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.roleId !== 1">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:role:edit']"
              >{{ $t('doc.this_dept_edit') }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:role:remove']"
              >{{ $t('doc.this_dept_delete') }}</el-button>

              <el-button
                size="mini"
                type="text"
                icon="el-icon-menu"
                @click="handleDataScope(scope.row)"
                v-hasPermi="['system:role:dataFilter']"
              >{{ $t('sys_mgr.role_auth_data') }}</el-button>

              <el-button
                size="mini"
                type="text"
                icon="el-icon-s-custom"
                @click="handleAuthUser(scope.row)"
                v-hasPermi="['system:role:distributeUser']"
              >{{ $t('sys_mgr.role_assign_user') }}</el-button>
              <!--
              <el-dropdown
                size="mini"
                @command="(command) => handleCommand(command, scope.row)"
                v-hasPermi="['system:role:dataFilter','system:role:distributeUser']"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="handleDataScope"
                    icon="el-icon-circle-check"
                    v-hasPermi="['system:role:dataFilter']"
                    >数据权限</el-dropdown-item
                  >
                  <el-dropdown-item
                    command="handleAuthUser"
                    icon="el-icon-user"
                    v-hasPermi="['system:role:distributeUser']"
                    >分配用户</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>-->
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改角色配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="500px"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item :label="$t('sys_mgr.user_role_name')" prop="roleName">
            <el-input
              v-model.trim="form.roleName"
              :placeholder="$t('sys_mgr.role_input_name')"
            />
          </el-form-item>
          <el-form-item prop="roleKey">
            <span slot="label">
              <el-tooltip
                :content="$t('sys_mgr.role_text')"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              {{ $t('sys_mgr.user_auth_char') }}
            </span>
            <el-input
              v-model.trim="form.roleKey"
              :placeholder="$t('sys_mgr.role_input_auth_char')"
            />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.role_order')" prop="roleSort">
            <el-input-number
              v-model.trim="form.roleSort"
              controls-position="right"
              :min="0"
            />
          </el-form-item>
          <el-form-item :label="$t('doc.this_dept_status')">
            <el-radio-group v-model.trim="form.status">
              <el-radio
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
              >{{ dictLanguage(dict) }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.role_auth_menu')">
            <el-checkbox
              v-model.trim="menuExpand"
              @change="handleCheckedTreeExpand($event, 'menu')"
            >{{ $t('sys_mgr.role_expand_fold') }}</el-checkbox
            >
            <el-checkbox
              v-model.trim="menuNodeAll"
              @change="handleCheckedTreeNodeAll($event, 'menu')"
            >{{ $t('sys_mgr.role_whether_select_all') }}</el-checkbox
            >
            <el-checkbox
              v-model.trim="form.menuCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'menu')"
            >{{ $t('sys_mgr.role_father_son_link') }}</el-checkbox
            >
            <el-tree
              class="tree-border"
              :data="menuOptions"
              show-checkbox
              ref="menu"
              node-key="id"
              :check-strictly="!form.menuCheckStrictly"
              :default-checked-keys="defaultcheckedkeys"
              :empty-text="$t('sys_mgr.role_loading')"
              :props="defaultProps"
            ></el-tree>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.user_remark')">
            <el-input
              v-model.trim="form.remark"
              type="textarea"
              :placeholder="$t('file_set.version_fill_content')"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>

      <!-- 分配角色数据权限对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="openDataScope"
        width="500px"
        append-to-body
      >
        <el-form :model="form" label-width="80px">
          <el-form-item :label="$t('sys_mgr.user_role_name')">
            <el-input v-model.trim="form.roleName" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.user_auth_char')">
            <el-input v-model.trim="form.roleKey" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.role_auth_range')">
            <el-select
              v-model.trim="form.dataScope"
              @change="dataScopeSelectChange"
            >
              <el-option
                v-for="item in dataScopeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.role_auth_data')" v-show="form.dataScope == 2">
            <el-checkbox
              v-model.trim="deptExpand"
              @change="handleCheckedTreeExpand($event, 'dept')"
            >{{ $t('sys_mgr.role_expand_fold') }}</el-checkbox
            >
            <el-checkbox
              v-model.trim="deptNodeAll"
              @change="handleCheckedTreeNodeAll($event, 'dept')"
            >{{ $t('sys_mgr.role_whether_select_all') }}</el-checkbox
            >
            <el-checkbox
              v-model.trim="form.deptCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'dept')"
            >{{ $t('sys_mgr.role_father_son_link') }}</el-checkbox
            >
            <el-tree
              class="tree-border"
              :data="deptOptions"
              show-checkbox
              default-expand-all
              ref="dept"
              node-key="id"
              :check-strictly="!form.deptCheckStrictly"
              :empty-text="$t('sys_mgr.role_loading')"
              :props="defaultProps"
            ></el-tree>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitDataScope">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancelDataScope">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listRole,
  getRole,
  delRole,
  addRole,
  updateRole,
  dataScope,
  changeRoleStatus,
} from "@/api/system/role";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";
import {
  treeselect as deptTreeselect,
  roleDeptTreeselect,
} from "@/api/system/dept";

export default {
  name: "Role",
  dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: this.$t('sys_mgr.role_all_data_auth'),
        },
        {
          value: "2",
          label: this.$t('sys_mgr.role_custom_data_auth'),
        },
        {
          value: "3",
          label: this.$t('sys_mgr.role_dept_data_auth'),
        },
        // {
        //   value: "4",
        //   label: "本部门及以下数据权限"
        // },
        // {
        //   value: "5",
        //   label: "仅本人数据权限"
        // }
      ],
      defaultcheckedkeys: [
        // 2202, 2201, 2237, 2238, 2239, 2240, 2162, 2173, 2174, 2184, 2185, 2244,
        // 2232, 2235, 2236, 2245, 2246, 2247, 2248, 2249, 2221, 2251, 2222, 2253,
        // 2224, 2182, 2241, 2242, 2227,
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: this.$t('sys_mgr.role_name_not_null'), trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: this.$t('sys_mgr.role_auth_char_not_null'), trigger: "blur" },
        ],
        roleSort: [
          { required: true, message: this.$t('sys_mgr.role_order_not_null'), trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.roleList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
      });
    },
    /** 查询部门树结构 */
    getDeptTreeselect() {
      deptTreeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.menu.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      // 目前被选中的部门节点
      let checkedKeys = this.$refs.dept.getCheckedKeys();
      // 半选中的部门节点
      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      return roleMenuTreeselect(roleId).then((response) => {
        this.menuOptions = response.data.menus;
        return response;
      });
    },
    /** 根据角色ID查询部门树结构 */
    getRoleDeptTreeselect(roleId) {
      return roleDeptTreeselect(roleId).then((response) => {
        this.deptOptions = response.data.depts;
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? this.$t('file_set.number_enable') : this.$t('sys_mgr.user_stop');
      this.$modal
        .confirm(this.$t('sys_mgr.user_text2') + text + '""' + row.roleName + this.$t('sys_mgr.role_text1'))
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + this.$t('file_handle.change_succ'));
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          roleId: undefined,
          roleName: undefined,
          roleKey: undefined,
          roleSort: 0,
          status: "0",
          menuIds: [],
          deptIds: [],
          menuCheckStrictly: true,
          deptCheckStrictly: true,
          remark: undefined,
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type == "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == "menu") {
        this.form.menuCheckStrictly = value ? true : false;
      } else if (type == "dept") {
        this.form.deptCheckStrictly = value ? true : false;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.title = this.$t('sys_mgr.role_add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getRole(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.data.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        });
        this.title = this.$t('sys_mgr.role_edit');
      });
    },
    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },
    /** 分配数据权限操作 */
    handleDataScope(row) {
      this.reset();
      const roleDeptTreeselect = this.getRoleDeptTreeselect(row.roleId);
      getRole(row.roleId).then((response) => {
        this.form = response.data;
        this.openDataScope = true;
        this.$nextTick(() => {
          roleDeptTreeselect.then((res) => {
            this.$refs.dept.setCheckedKeys(res.data.checkedKeys);
          });
        });
        this.title = this.$t('sys_mgr.role_assign_data_auth');
      });
    },
    /** 分配用户操作 */
    handleAuthUser: function (row) {
      const roleId = row.roleId;
      this.$router.push("/system/role-auth/user/" + roleId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            addRole(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮（数据权限） */
    submitDataScope: function () {
      if (this.form.roleId != undefined) {
        this.form.deptIds = this.getDeptAllCheckedKeys();
        dataScope(this.form).then((response) => {
          this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
          this.openDataScope = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal
        .confirm(this.$t('sys_mgr.role_text2') + roleIds + this.$t('file_set.signature_text1'))
        .then(function () {
          return delRole(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/role/export",
        {
          ...this.queryParams,
        },
        `role_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
