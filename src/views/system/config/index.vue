<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
          {{ $t('sys_mgr.config_setting') }}
        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :label-width="columnLangSizeFlag ? '128px' : '68px'" :inline="true">
        <el-form-item :label="$t('sys_mgr.config_setting')" prop="configName">
          <el-input
            v-model.trim="queryParams.configName"
            :placeholder="$t('sys_mgr.config_input_name')"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr.config_key_name')" prop="configKey" :label-width="columnLangSizeFlag ? '158px' : '68px'">
          <el-input
            v-model.trim="queryParams.configKey"
            :placeholder="$t('sys_mgr.config_input_key_name')"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr.config_built_in')" prop="configType">
          <el-select v-model.trim="queryParams.configType" :placeholder="$t('sys_mgr.config_built_in')" clearable size="small">
            <el-option
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('file_set.version_create_time')">
          <el-date-picker
            v-model.trim="dateRange"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('doc.this_dept_start_date')"
            :end-placeholder="$t('doc.this_dept_end_date')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:config:add']"
          >{{ $t('doc.this_dept_new_add') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:config:edit']"
          >{{ $t('doc.this_dept_edit') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:config:remove']"
          >{{ $t('doc.this_dept_delete') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:config:export']"
          >{{ $t('doc.exterior_dept_export') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="handleRefreshCache"
            v-hasPermi="['system:config:remove']"
          >{{ $t('sys_mgr.dict_refresh_cache') }}</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column :label="$t('sys_mgr.config_key')" align="center" prop="configId" />
          <el-table-column :label="$t('sys_mgr.config_setting')" align="center" prop="configName" :show-overflow-tooltip="true" />
          <el-table-column :label="$t('sys_mgr.config_key_name')" align="center" prop="configKey" :show-overflow-tooltip="true" />
          <el-table-column :label="$t('sys_mgr.config_key_val')" align="center" prop="configValue" :show-overflow-tooltip="true" />
          <el-table-column :label="$t('sys_mgr.config_built_in')" align="center" prop="configType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.configType"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('sys_mgr.user_remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
          <el-table-column :label="$t('file_set.version_create_time')" align="center" prop="createTime" width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:config:edit']"
              >{{ $t('doc.this_dept_edit') }}</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:config:remove']"
              >{{ $t('doc.this_dept_delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item :label="$t('sys_mgr.config_setting')" prop="configName">
            <el-input v-model.trim="form.configName" :placeholder="$t('sys_mgr.config_input_name')" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.config_key_name')" prop="configKey">
            <el-input v-model.trim="form.configKey" :placeholder="$t('sys_mgr.config_input_key_name')" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.config_key_val')" prop="configValue">
            <el-input v-model.trim="form.configValue" :placeholder="$t('sys_mgr.config_input_key_val')" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.config_built_in')" prop="configType">
            <el-radio-group v-model.trim="form.configType">
              <el-radio
                v-for="dict in dict.type.sys_yes_no"
                :key="dict.value"
                :label="dict.value"
              >{{dictLanguage(dict)}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.user_remark')" prop="remark">
            <el-input v-model.trim="form.remark" type="textarea" :placeholder="$t('file_set.version_fill_content')" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listConfig, getConfig, delConfig, addConfig, updateConfig, refreshCache} from "@/api/system/config";

export default {
  name: "Config",
  dicts: ['sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined,
        configKey: undefined,
        configType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configName: [
          { required: true, message: this.$t('sys_mgr.config_name_not_null'), trigger: "blur" }
        ],
        configKey: [
          { required: true, message: this.$t('sys_mgr.config_key_name_not_null'), trigger: "blur" }
        ],
        configValue: [
          { required: true, message: this.$t('sys_mgr.config_key_val_not_null'), trigger: "blur" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      listConfig(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.configList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: undefined,
        configName: undefined,
        configKey: undefined,
        configValue: undefined,
        configType: "Y",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('sys_mgr.config_add');
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids
      getConfig(configId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr.config_edit');
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != undefined) {
            updateConfig(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addConfig(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$modal.confirm(this.$t('sys_mgr.config_text') + configIds + this.$t('file_set.signature_text1')).then(function() {
        return delConfig(configIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/config/export', {
        ...this.queryParams
      }, `config_${new Date().getTime()}.xlsx`)
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshCache().then(() => {
        this.$modal.msgSuccess(this.$t('sys_mgr.dict_clear_succ'));
      });
    },
  }
};
</script>
