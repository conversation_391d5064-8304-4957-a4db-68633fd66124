<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
          {{ $t('sys_mgr.menu_manage') }}
        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :inline="true">
        <el-form-item :label="$t('sys_mgr.menu_name')" prop="menuName">
          <el-input
            v-model.trim="queryParams.menuName"
            :placeholder="$t('sys_mgr.menu_input_name')"
            clearable

            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('doc.this_dept_status')" prop="status">
          <el-select v-model.trim="queryParams.status" :placeholder="$t('sys_mgr.menu_status')" clearable size="small">
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:menu:add']"
          >{{ $t('doc.this_dept_new_add') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-sort"
            size="mini"
            @click="toggleExpandAll"
          >{{ $t('sys_mgr.role_expand_fold') }}</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="menuList"
          row-key="menuId"
          :default-expand-all="isExpandAll"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        >
          <el-table-column prop="menuName" :label="$t('sys_mgr.menu_name')" :show-overflow-tooltip="true" width="160"></el-table-column>
          <el-table-column prop="icon" :label="$t('sys_mgr.menu_icon')" align="center" width="100">
            <template slot-scope="scope">
              <svg-icon :icon-class="scope.row.icon" />
            </template>
          </el-table-column>
          <el-table-column prop="orderNum" :label="$t('file_set.number_order')" width="60"></el-table-column>
          <el-table-column prop="perms" :label="$t('sys_mgr.menu_auth_mark')" width="300" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="component" :label="$t('sys_mgr.menu_component_path')" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="status" :label="$t('doc.this_dept_status')" width="80">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('file_set.version_create_time')" align="center" prop="createTime"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini"
                         type="text"
                         icon="el-icon-edit"
                         @click="handleUpdate(scope.row)"
                         v-hasPermi="['system:menu:edit']"
              >{{ $t('doc.this_dept_edit') }}</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleAdd(scope.row)"
                v-hasPermi="['system:menu:add']"
              >{{ $t('doc.this_dept_new_add') }}</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:menu:remove']"
              >{{ $t('doc.this_dept_delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <!-- 添加或修改菜单对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body :close-on-click-modal="false">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="24">
              <el-form-item :label="$t('sys_mgr.menu_sup')">
                <treeselect
                  v-model.trim="form.parentId"
                  :options="menuOptions"
                  :normalizer="normalizer"
                  :show-count="true"
                  :searchable="false"
                  :placeholder="$t('sys_mgr.menu_select_sup')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('sys_mgr.menu_type')" prop="menuType">
                <el-radio-group v-model.trim="form.menuType">
                  <el-radio label="M">{{ $t('sys_mgr.menu_catalog') }}</el-radio>
                  <el-radio label="C">{{ $t('sys_mgr.menu') }}</el-radio>
                  <el-radio label="F">{{ $t('sys_mgr.menu_button') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item v-if="form.menuType != 'F'" :label="$t('sys_mgr.menu_ico')">
                <el-popover
                  placement="bottom-start"
                  width="460"
                  trigger="click"
                  @show="$refs['iconSelect'].reset()"
                >
                  <IconSelect ref="iconSelect" @selected="selected" />
                  <el-input slot="reference" v-model.trim="form.icon" :placeholder="$t('sys_mgr.menu_select_icon')" readonly>
                    <svg-icon
                      v-if="form.icon"
                      slot="prefix"
                      :icon-class="form.icon"
                      class="el-input__icon"
                      style="height: 32px;width: 16px;"
                    />
                    <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                  </el-input>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.menu_name')" prop="menuName">
                <el-input v-model.trim="form.menuName" :placeholder="$t('sys_mgr.menu_input_name')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.menu_display_order')" prop="orderNum">
                <el-input-number v-model.trim="form.orderNum" controls-position="right" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType != 'F'">
              <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_whether_out_chain') }}
              </span>
                <el-radio-group v-model.trim="form.isFrame">
                  <el-radio label="0">{{ $t('doc.this_dept_yes') }}</el-radio>
                  <el-radio label="1">{{ $t('doc.this_dept_no') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType != 'F'" prop="path">
              <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text1')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_routing_address') }}
              </span>
                <el-input v-model.trim="form.path" :placeholder="$t('sys_mgr.menu_input_routing_address')" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType == 'C'">
              <el-form-item prop="component">
              <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text2')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_component_path') }}
              </span>
                <el-input v-model.trim="form.component" :placeholder="$t('sys_mgr.menu_input_component_path')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType != 'M'">
                <el-input v-model.trim="form.perms" :placeholder="$t('sys_mgr.menu_input_auth_mark')" maxlength="100" />
                <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text3')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.user_auth_char') }}
              </span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType == 'C'">
                <el-input v-model.trim="form.query" :placeholder="$t('sys_mgr.menu_input_routing_param')" maxlength="255" />
                <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text4')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_routing_param') }}
              </span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType == 'C'">
              <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text5')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_whether_cache') }}
              </span>
                <el-radio-group v-model.trim="form.isCache">
                  <el-radio label="0">{{ $t('sys_mgr.menu_cache') }}</el-radio>
                  <el-radio label="1">{{ $t('sys_mgr.menu_not_cache') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType != 'F'">
              <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text6')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_display_status') }}
              </span>
                <el-radio-group v-model.trim="form.visible">
                  <el-radio
                    v-for="dict in dict.type.sys_show_hide"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dictLanguage(dict)}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType != 'F'">
              <span slot="label">
                <el-tooltip :content="$t('sys_mgr.menu_text7')" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('sys_mgr.menu_status') }}
              </span>
                <el-radio-group v-model.trim="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dictLanguage(dict)}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";

export default {
  name: "Menu",
  dicts: ['sys_show_hide', 'sys_normal_disable'],
  components: { Treeselect, IconSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        menuName: undefined,
        visible: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        menuName: [
          { required: true, message: this.$t('sys_mgr.menu_name_not_null'), trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: this.$t('sys_mgr.menu_order_not_null'), trigger: "blur" }
        ],
        path: [
          { required: true, message: this.$t('sys_mgr.menu_routing_address_not_null'), trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 选择图标
    selected(name) {
      this.form.icon = name;
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      listMenu(this.queryParams).then(response => {
        this.menuList = this.handleTree(response.data, "menuId");
        this.loading = false;
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listMenu().then(response => {
        this.menuOptions = [];
        const menu = { menuId: 0, menuName: this.$t('file_set.type_main_category'), children: [] };
        menu.children = this.handleTree(response.data, "menuId");
        this.menuOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuId: undefined,
        parentId: 0,
        menuName: undefined,
        icon: undefined,
        menuType: "M",
        orderNum: undefined,
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.menuId) {
        this.form.parentId = row.menuId;
      } else {
        this.form.parentId = 0;
      }
      this.open = true;
      this.title = this.$t('sys_mgr.menu_add');
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getMenu(row.menuId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr.menu_edit');
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.menuId != undefined) {
            updateMenu(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addMenu(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm(this.$t('file_set.signature_text') + row.menuName + this.$t('file_set.signature_text1')).then(function() {
        return delMenu(row.menuId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).catch(() => {});
    }
  }
};
</script>
