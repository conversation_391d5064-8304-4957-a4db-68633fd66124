<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
          {{ $t('sys_mgr.dept_manage') }}

        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('sys_mgr.dept_name')" prop="deptName">
          <el-input
            v-model.trim="queryParams.deptName"
            :placeholder="$t('sys_mgr.user_input_dept_name')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('doc.this_dept_status')" prop="status">
          <el-select
            v-model.trim="queryParams.status"
            :placeholder="$t('sys_mgr.dept_status')"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >{{ $t('doc.this_dept_search') }}</el-button
          >
          <el-button icon="el-icon-refresh"  @click="resetQuery"
          >{{ $t('myItem.handle_reset') }}</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:dept:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-sort"
            size="mini"
            @click="toggleExpandAll"
          >{{ $t('sys_mgr.role_expand_fold') }}</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="deptList"
          row-key="deptId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          @selection-change="handleSelectionChange"
          width="100%"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            prop="deptName"
            :label="$t('sys_mgr.dept_name')"
          ></el-table-column>
          <el-table-column
            prop="deptCode"
            :label="$t('sys_mgr.dept_code')"
            width="150"
          >
            <template v-slot="scope">
              <el-input v-model="scope.row.deptCode" @focus="curDeptCode=scope.row.deptCode" @blur="updateDeptCode(scope.row)"></el-input>

            </template>
          </el-table-column>
          <el-table-column
            prop="orderNum"
            :label="$t('file_set.number_order')"
            width="80"
          ></el-table-column>

          <el-table-column
            prop="leaderName"
            :label="'部门分管领导'"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="principalName"
            :label="'部门负责人'"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="docManagerName"
            :label="'文控'"
          ></el-table-column>
          <el-table-column prop="status" :label="$t('doc.this_dept_status')" width="100">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template v-slot="scope">
              <el-button
                size="mini"
                type="text"
                @click="selectLeader(scope.row)"
                v-hasPermi="['system:dept:edit']"
                >{{'部门分管领导'}}</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="selectPrincipal(scope.row)"
                v-hasPermi="['system:dept:edit']"
                >{{'部门负责人'}}</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="selectDocManager(scope.row)"
                v-hasPermi="['system:dept:edit']"
                >{{'文控'}}</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:dept:edit']"
              >{{'修改'}}</el-button
              >

            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <!-- 添加或修改部门对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="60%"
        append-to-body
        :close-on-click-modal="false"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="24" v-if="form.parentId !== 0">
              <el-form-item :label="$t('sys_mgr.dept_sup')" prop="parentId">
                <treeselect
                  v-model.trim="form.parentId"
                  :options="deptOptions"
                  :normalizer="normalizer"
                  @input="changeMenu"
                  :searchable="false"
                  :placeholder="$t('sys_mgr.dept_select_sup')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.dept_name')" prop="deptName">
                <el-input v-model.trim="form.deptName" :placeholder="$t('sys_mgr.user_input_dept_name')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.dept_code')" prop="deptCode">
                <el-input
                  v-model.trim="form.deptCode"
                  :placeholder="$t('sys_mgr.dept_input_code')"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.menu_display_order')" prop="orderNum">
                <el-input-number
                  v-model.trim="form.orderNum"
                  controls-position="right"
                  :min="0"
                />
              </el-form-item>
            </el-col>
<!--            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.dept_handler')" prop="leader">
                <el-input
                  v-model.trim="form.leader"
                  :placeholder="$t('sys_mgr.dept_input_handler')"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>-->
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.dept_status')">
                <el-radio-group v-model.trim="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dictLanguage(dict) }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
<!--            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.dept_connect_phone')" prop="phone">
                <el-input
                  v-model.trim="form.phone"
                  :placeholder="$t('sys_mgr.dept_input_connect_phone')"
                  maxlength="11"
                />
              </el-form-item>
            </el-col>-->
<!--            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_email')" prop="email">
                <el-input
                  v-model.trim="form.email"
                  :placeholder="$t('sys_mgr.user_input_emal')"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>-->

<!--            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.dept_whether_external_org')" label-width="200">
                <el-radio-group v-model.trim="form.deptType">
                  <el-radio
                    v-for="dict in dict.type.sys_dept_type"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dictLanguage(dict) }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>-->

            <el-col :span="24">
              <el-form-item :label="$t('sys_mgr.dept_main_business')" prop="majorBusinesses">
                <el-input
                  v-model.trim="form.majorBusinesses"
                  :placeholder="$t('sys_mgr.dept_input_main_business')"
                  type="textarea"
                  maxlength="500"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item :label="$t('sys_mgr.dept_address')" prop="address">
                <el-input
                  v-model.trim="form.address"
                  :placeholder="$t('sys_mgr.dept_input_address')"
                  type="textarea"
                  maxlength="500"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
    <assign-users ref="assignUsers" @selectHandle="userSelectHandle"></assign-users>
  </div>
</template>

<script>
import {
  listDept,
  getDept,
  delDept,
  addDept,
  updateDept,
  listDeptExcludeChild, listDeptDetail, updateDeptCode
} from '@/api/system/dept'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AssignUsers from '@views/workflowList/addWorkflow/add_import/assignUsers.vue'
import {getCompanyList, getUser} from '@/api/system/user'

export default {
  name: "Dept",
  dicts: ["sys_normal_disable", "sys_dept_type"],
  components: { AssignUsers, Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否展开
      expand: false,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined,
      },
      curDeptCode:'',
      // 表单参数
      form: {
        tenantId:""
      },
      tenantOptions: [],
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: this.$t('sys_mgr.dept_sup_not_null'), trigger: ["blur", "change","input"] },
        ],
        deptName: [
          { required: true, message: this.$t('sys_mgr.dept_name_not_null'), trigger: "blur" },
        ],
        orderNum: [
          { required: true, message: this.$t('sys_mgr.dept_display_order_not_null'), trigger: "change" },
        ],
        email: [
          {
            type: "email",
            message: this.$t('sys_mgr.user_input_right_email'),
            trigger: ["blur", "change"],
          },
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t('sys_mgr.user_input_right_phone'),
            trigger: "blur",
          },
        ],
        deptCode: [
          { required: true, message: this.$t('sys_mgr.dept_code_not_null'), trigger: "blur" },
        ],
        multipleSelection: [],
        isMultiSelectUser: false
      },
    };
  },
  created() {
    this.getList();
    this.getTenants();
  },
  methods: {
    //获取租户
    getTenants(){
      getCompanyList({}).then((response) => {
        this.tenantOptions = response.data;
      });
    },
    // 排序节点
    sortDataById(data) {
      // 按id排序函数
      function sortById(a, b) {
        return parseInt(a.orderNum) - parseInt(b.orderNum)
      }
      // 递归排序子级
      function sortChildren(node) {
        if (node.children && node.children.length > 0) {
          node.children.sort(sortById);
          node.children.forEach(child => {
            sortChildren(child);
          });
        }
      }
      // 初始排序
      data.sort(sortById);
      // 对每个节点递归排序子级
      data.forEach(node => {
        sortChildren(node);
      });
      return data;
    },
    changeMenu() {
      this.$refs.form.validateField('parentId')
    },
    /** 查询部门列表 */
    getList() {
      this.loading = true;
      listDeptDetail(this.queryParams).then((response) => {
        let data = this.sortDataById(response.data);
        this.deptList = this.handleTree(data, "deptId");
        this.loading = false;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: "0",
        deptType: undefined,
        majorBusinesses: undefined,
        jointBusiness: undefined,
        address: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row != undefined) {
        this.form.parentId = row.deptId;
      }
      this.open = true
      this.$set(this.form,"tenantId","")
      this.form.tenantId = this.tenantOptions[0].id
      this.title = this.$t('sys_mgr.dept_add');
      listDept({status: 0}).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getDept(row.deptId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr.dept_edit');
      });
      listDeptExcludeChild(row.deptId).then((response) => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.deptId != undefined) {
            updateDept(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addDept(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm(this.$t('file_set.signature_text') + row.deptName + this.$t('file_set.signature_text1'))
        .then(function () {
          return delDept(row.deptId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log('分发',this.multipleSelection);
    },
    userSelectHandle(source,index,data){
      if (data&&data.length>0) {
        this.currentRow[`${index}Code`] = data.map(item=>item.userName).join(',')
        this.currentRow[`${index}Name`] = data.map(item=>item.nickName).join(',')
      }else{
        this.currentRow[`${index}Code`] = ''
        this.currentRow[`${index}Name`] = ''
      }
      this.currentRow.children=null
      updateDept(this.currentRow).then((response) => {
        this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
        this.open = false;
        this.getList();
      });
    },
    getUsers(row, prefix) {
      let u = row[`${prefix}Code`]
      let un = row[`${prefix}Name`]
      let users = []
      if (u && un) {
        u = u.split(',')
        un = un.split(',')
        u.forEach((item, index) => {
          users.push({
            userName: u[index],
            nickName: un[index]
          })
        })
      }
      return users;
    },
    selectLeader(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      let users = this.getUsers(row, "leader")
      this.$refs.assignUsers.init('选择人员', row.type, "leader", users,false)
    },
    selectPrincipal(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      let users = this.getUsers(row, "principal")
      this.$refs.assignUsers.init('选择人员', row.type, "principal", users,false)
    },
    selectDocManager(row) {
      this.currentRow = JSON.parse(JSON.stringify(row))
      let users = this.getUsers(row, "docManager")
      this.$refs.assignUsers.init('选择人员', row.type, "docManager", users,true)
    },
    updateDeptCode(row){
      if(this.curDeptCode!=row.deptCode){
        updateDeptCode(row).then((response) => {
          this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
          this.open = false;
        });
      }
    }
  },
};
</script>
<style lang="scss">
</style>
