<template>
  <div id="app">
    <el-row :gutter="20">
		<el-col :span="24">
    <div class="ss-card">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目代码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入项目代码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    </div>
    </el-col>
	</el-row>

   	<el-row :gutter="20">
		<el-col :span="16">
			<el-card>
        <div slot="header" class="clearfix"> 
					<span>项目</span>
          <div class="header-button">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:project:add']" >新增项目</el-button>
          </div>   	
        </div>   			
      <!--项目列表-->
        <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange"
          @row-click="rowClick"
          highlight-current-row="true">
          <el-table-column label="项目代码" align="center" prop="code" />
          <el-table-column label="项目名称" align="center" prop="name" />
          <el-table-column label="备注" align="center" prop="remark" />
          <el-table-column label="创建时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:project:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                v-if = "scope.row.groups!=undefined && scope.row.groups!=null && scope.row.groups.length===0"
                @click="handleLinkGroup(scope.row)"
                v-hasPermi="['system:infoGroup:add']"
              >关联项目组</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:project:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
     </el-card><!--------el-card--------->
		</el-col>
      <!--关联项目组列表-->
      <el-col :span="8">
			<el-card>
        <div slot="header" class="clearfix">
            <span> 关联项目组 </span>
        </div>
        <el-table v-loading="loading" :data="projectRelGroupList" >
          <el-table-column label="项目组名称" align="center" prop="group.name" />
          <el-table-column label="关联时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDeleteDetail(scope.row)"
              v-hasPermi="['system:infoGroup:remove']"
            >移除</el-button>
          </template>
        </el-table-column>
        </el-table>
        </el-card><!--------el-card--------->
      </el-col>
    </el-row>

    <!-- 添加或修改项目信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="项目代码" prop="code">
          <el-input v-model="form.code" placeholder="请输入项目代码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" maxlength="200" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 关联项目组对话框 begin-->
    <el-dialog title="关联项目组" :visible.sync="linkGroupOpen" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="linkGroupForm" :model="linkGroupForm" :rules="linkGroupRules" label-width="100px">
      <el-form-item label="关联项目组" prop="groupId">
        <el-select v-model="linkGroupForm.groupId" placeholder="请选择" clearable size="small">
          <el-option
            v-for="dict in groupList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" maxlength="200" v-model="linkGroupForm.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="linkGroupSubmitForm">确 定</el-button>
        <el-button @click="linkGroupCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 关联项目组对话框 end-->
  </div>
</template>

<script>
import { listGroup } from "@/api/system/group";
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from "@/api/system/project";
import { listInfoGroup,delInfoGroup,addInfoGroup } from "@/api/system/projectInfoGroup";
export default {
  name: "Info",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目信息表格数据
      infoList: [],
      // 所有有效项目组集合
      groupList: [],
      // 项目相关的群组表格数据
      projectRelGroupList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层-关联项目组
      linkGroupOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: undefined,
        name: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      // 表单参数-关联项目组
      linkGroupForm: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "主键不能为空", trigger: "blur" }
        ],
        tenantId: [
          { required: true, message: "租户id不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "项目代码不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态;Y有效，N失效不能为空", trigger: "blur" }
        ]
      },
      // 表单校验-关联项目组
      linkGroupRules: {
        groupId: [
          { required: true, message: "关联项目组不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮-关联项目组
    linkGroupCancel() {
      this.linkGroupOpen = false;
      this.linkGroupForm = {
        groupId: undefined,
        remark: undefined
      };
      this.resetForm("linkGroupForm");
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        tenantId: undefined,
        code: undefined,
        name: undefined,
        status: "Y",
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 单击行记录
    rowClick(row,column,event) {
      this.getProjectRelGroupList(row.id)
    },
    /** 查询项目相关群组列表 */
    getProjectRelGroupList(projectId) {
      let self = this
      this.loading = true;
      let query = {}
      query.projectId = projectId
      listInfoGroup(query).then(response => {
        self.projectRelGroupList = response.rows;
        this.loading = false;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改项目信息";
      });
    },
    /** 关联项目组按钮操作 */
    handleLinkGroup(row) {
      this.loading = true;
      let param = {
        pageSize:10000,
        status:'Y'
      }
      listGroup(param).then(response => {
        this.linkGroupForm.projectId = row.id;
        this.groupList = response.rows;
        this.loading = false;
        this.linkGroupOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            // 当前登录人所属租户
            this.form.tenantId = userInfo.tenantId
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 提交按钮-关联项目组 */
    linkGroupSubmitForm() {
      const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
      this.$refs["linkGroupForm"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          // 当前登录人所属租户
          this.linkGroupForm.tenantId = userInfo.tenantId
            addInfoGroup(this.linkGroupForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.linkGroupOpen = false;
              this.getList()
              this.getProjectRelGroupList(this.linkGroupForm.projectId);
            }).finally(() => {
              this.buttonLoading = false;
            });
          
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if(this.projectRelGroupList.length > 0) {
         this.$modal.alert('您好，此项目已关联了' + this.projectRelGroupList.length + '个项目组，请先移除相关项目组再删除项目。')
         return false;
      }
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除项目代码为"' + row.code + '"的数据项？').then(() => {
        this.loading = true;
        return delInfo(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.getProjectRelGroupList(row.id);
        this.$modal.msgSuccess("删除成功");
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 删除相关项目组-按钮操作 */
    handleDeleteDetail(row) {
      let self = this
      self.$modal.confirm('是否确认移除项目组为"' + row.group.name + '"的数据项？').then(() => {
        self.loading = true;
        return delInfoGroup(row.id);
      }).then(() => {
        self.$message({
              message: '移除成功',//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                self.getList()
                self.getProjectRelGroupList(row.projectId);
                self.loading = false;
              }
          });
      }).finally(() => {
        self.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
