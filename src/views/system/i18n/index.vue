<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
         国际化管理
        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :label-width="columnLangSizeFlag ? '128px' : '68px'" :inline="true">
        <el-form-item label="编码" prop="code">
          <el-input
            v-model.trim="queryParams.code"
            placeholder="请输入编码"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="queryParams.name"
            placeholder="请输入名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="语言" prop="lang">
          <el-select
            v-model.trim="queryParams.lang"
            placeholder="请选择语言"
            clearable
            size="small"
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.i18n_mgr_language_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select
            v-model.trim="queryParams.type"
            placeholder="请选择类型"
            clearable
            size="small"
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.i18n_mgr_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="租户" prop="type">
          <el-select
            v-model.trim="queryParams.tenantId"
            placeholder="请选择租户"
            clearable
            size="small"
            style="width: 300px"
          >
            <el-option
              v-for="dict in dict.type.i18n_tenantId_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="24" >
        <el-col :span="24"><span style="color: red">操作注意事项：一件翻译调用腾讯AI的翻译接口，默认会把已翻译的覆盖掉，我们系统手动调整过翻译的版本，因此有新增的数据先保存到临时租户里面去，在通过sql更改租户id</span></el-col>
      </el-row>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:i18n:add']"
          >新建</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            size="mini"
            @click="handleImport()"
            v-hasPermi="['system:i18n:import']"
          >导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            size="mini"
            @click="clickTranslate()"
            v-hasPermi="['system:i18n:translate']"
            v-if="this.queryParams.tenantId == 'HAI'"
          >一键翻译</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="handleInitI18n"
            v-hasPermi="['system:i18n:initRedis']"
          >初始化redis国际化语言</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="编码" align="center" prop="code" />
          <el-table-column label="语言" align="center" prop="lang">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.i18n_mgr_language_type" :value="scope.row.lang"/>
            </template>
          </el-table-column>
          <el-table-column label="名称" align="name" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="类型" align="center" prop="type">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.i18n_mgr_type" :value="scope.row.type"/>
            </template>
          </el-table-column>
          <el-table-column label="更新人" align="center" prop="updateBy" :show-overflow-tooltip="true" />
          <el-table-column label="更新时间" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:i18n:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:i18n:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-drawer
        title="新增编码"
        :before-close="cancel"
        :visible.sync="open"
        direction="rtl"
        size="40%"
        ref="drawer"
      >
        <div class="demo-drawer__content">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="code" prop="code">
              <el-input v-model.trim="form.code" placeholder="请输入code" style="width: 300px" :disabled="editStatus"/>
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model.trim="form.name" placeholder="请输入名称" style="width: 300px"/>
            </el-form-item>
            <el-form-item label="语言" prop="lang">
              <el-select
                v-model.trim="form.lang"
                placeholder="请选择语言"
                clearable
                size="small"
                style="width: 300px"
              >
                <el-option
                  v-for="dict in dict.type.i18n_mgr_language_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select
                v-model.trim="form.type"
                placeholder="请选择语言"
                clearable
                size="small"
                style="width: 300px"
              >
                <el-option
                  v-for="dict in dict.type.i18n_mgr_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="租户" prop="tenantId">
              <el-select
                v-model.trim="form.tenantId"
                placeholder="请选择租户"
                clearable
                size="small"
                style="width: 300px"
              >
                <el-option
                  v-for="dict in dict.type.i18n_tenantId_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="cancel">关闭</el-button>
          </div>
        </div>
      </el-drawer>

      <!-- 导入编码 -->
      <el-drawer
        title="导入编码"
        :before-close="cancel"
        :visible.sync="importDisable"
        direction="rtl"
        size="40%"
        ref="drawer"
      >
        <div class="demo-drawer__content">
            <el-form ref="elForm" :model="formData" :rules="formDataRules" size="medium" label-width="100px">
              <el-row :gutter="18">
                <el-col :span="2">
                  <el-button type="text" @click="downTemplate">下载表格模版</el-button>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="租户" prop="tenantId">
                    <el-select
                      v-model.trim="fileTenantId"
                      placeholder="请选择租户"
                      clearable
                      size="small"
                      style="width: 120px"
                    >
                      <el-option
                        v-for="dict in dict.type.i18n_tenantId_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="上传" prop="fileList">
                    <el-upload
                      :http-request="customHttpRequest"
                      ref="fileRef"
                      list-type="text"
                      :on-success="handleUploadSuccess"
                      :on-error="handleUploadError"
                      :limit="1"
                      :on-exceed="handleExceed"
                      :auto-upload="false"
                    >
                      <el-button type="primary">点击上传</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="2">

                </el-col>
              </el-row>
            </el-form>


          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitUpload">提交</el-button>
            <el-button @click="cancel">关闭</el-button>
          </div>
        </div>
      </el-drawer>


      <!-- 一键翻译 -->
      <el-drawer
        title="一键翻译"
        :before-close="cancel"
        :visible.sync="translateDisable"
        direction="rtl"
        size="40%"
        ref="drawer"
      >
        <div class="demo-drawer__content">
          <el-form ref="translateForm" :model="translateForm" label-width="80px">
            <el-form-item label="目标语言" prop="sourceLang">
              <el-input v-model.trim="translateForm.sourceLang" placeholder="请输入源语言" style="width: 300px" />
            </el-form-item>
            <el-form-item label="源语言" prop="targetLang">
              <el-input v-model.trim="translateForm.targetLang" placeholder="请输入目标语言" style="width: 300px"/>
            </el-form-item>
            <el-form-item label="租户" prop="tenantId">
              <el-select
                v-model.trim="translateForm.tenantId"
                placeholder="请选择租户"
                clearable
                size="small"
                style="width: 300px"
              >
                <el-option
                  v-for="dict in dict.type.i18n_tenantId_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="translateSubmitForm">提交</el-button>
            <el-button @click="cancel">关闭</el-button>
          </div>
        </div>
      </el-drawer>



    </div>
  </div>
</template>

<script>
import { listInternationals,saveI18nData,removeI18nData, importI18nData, translateI18nData, initI18nBack, updateI18nData} from "@/api/system/internationals";
import axios from "axios";
import { getToken } from '@/utils/auth'

export default {
  name: "International",
  dicts: ['i18n_mgr_type','i18n_mgr_language_type','i18n_tenantId_type'],
  data() {
    let validateFileUrl = (rule, value, callback) => {
      if (this.standardDocfileList.length < 1) {
        //我控制了FileList 长度代表文件个数
        callback(new Error("请上传文件"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      importDisable: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: undefined,
        name: undefined,
        lang: undefined,
        type: undefined,
        tenantId: 'CAM'
      },
      // 表单参数
      form: {
        code: undefined,
        name: undefined,
        lang: undefined,
        type: undefined,
        tenantId: 'HAI'
      },
      // 表单参数
      translateForm: {
        sourceLang: 'zh',
        targetLang: 'en',
        tenantId: 'HAI'
      },
      // 表单校验
      rules: {
      },
      formDataRules: {
        fileList: [
          {
            required: true,
            validator: validateFileUrl,
            trigger: ["blur", "change", "input"],
          },
        ],
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
      editStatus: false,
      //表单数据
      formData:[],
      fileList:[],
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/system/i18n/import", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      translateDisable: false,
      fileTenantId: 'HAI',
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listInternationals(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.dataList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.importDisable = false;
      this.translateDisable = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        code: undefined,
        name: undefined,
        lang: undefined,
        type: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.editStatus = false
      this.open = true;
      this.title = this.$t('sys_mgr.dict_add_type');
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dictId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.editStatus = true
      this.form = JSON.parse(JSON.stringify(row))
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$modal.confirm("您确定要提交吗？").then(() => {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if(this.editStatus){
              updateI18nData(this.form).then(res => {
                this.$modal.msgSuccess(this.$t('file_set.number_field'));
                this.open = false;
                this.getList();
              });
            }else{
              saveI18nData(this.form).then(res => {
                this.$modal.msgSuccess(this.$t('file_set.number_field'));
                this.open = false;
                this.getList();
              });
            }

          }
        });
      }).catch((err) => { console.info("err=====submitForm==========", err)});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm("您确定要删除吗？").then(function() {
        return removeI18nData({id: row.id});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).catch((err) => { console.info("err===============", err)});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/dict/type/export', {
        ...this.queryParams
      }, `type_${new Date().getTime()}.xlsx`)
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshCache().then(() => {
        this.$modal.msgSuccess(this.$t('sys_mgr.dict_clear_succ'));
      });
    },
    /** 处理导入 **/
    handleImport(){
      this.importDisable = true;
    },
    /** 点击翻译 **/
    clickTranslate(){
      this.translateDisable = true;
    },
    downTemplate(){
      axios({
        method: 'get',
        url: process.env.VUE_APP_BASE_API + "/system/i18n/downloadTemplate",
        responseType: 'blob',
        headers: { 'Authorization': 'Bearer ' + getToken() }
      }).then(res => {
        const aLink = document.createElement('a')
        aLink.style.display = 'none'
        aLink.href = URL.createObjectURL(res.data)
        aLink.setAttribute('download', decodeURI(`国际化编码模板文件.xlsx`)) // 设置下载文件名称
        document.body.appendChild(aLink)
        aLink.click()
        URL.revokeObjectURL(aLink.href);//清除引用
        document.body.removeChild(aLink);
      }).catch(error => {
        console.error('下载失败', error)
      })
    },
    beforeUpload(file) {
      this.fileList = []
      // 阻止默认的上传行为
      this.fileList.push(file);
      return false;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    /** 提交上传 **/
    submitUpload(){
      this.$refs.fileRef.submit()
    },
    // 上传成功回调
    handleUploadSuccess(res,uploadFile,uploadFiles) {
      console.info('Upload success:', res);
    },
    handleUploadError(err, file, fileList) {
      console.error('Upload Error:', err);
    },
    customHttpRequest(options) {
      let self = this
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append("tenantId", this.fileTenantId);

      importI18nData(formData)
        .then(res => {
          self.$message.success("操作成功");
          self.cancel();
          self.getList();
        })
        .catch(error => {
          console.info("error========", error)
        });
    },
    /** 翻译提交按钮 */
    translateSubmitForm: function() {
      this.$modal.confirm("您确定要翻译吗？").then(() => {
        this.$refs["translateForm"].validate(valid => {
          if (valid) {
            translateI18nData(this.translateForm).then(res => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
              this.cancel();
            });
          }
        });
      }).catch((err) => { console.info("err===============", err)});

    },
    /** 刷新缓存按钮操作 */
    handleInitI18n() {
      initI18nBack().then(() => {
        this.$modal.msgSuccess(this.$t('sys_mgr.dict_clear_succ'));
      });
    }

  }
};
</script>
