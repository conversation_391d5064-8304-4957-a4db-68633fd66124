<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
          {{ $t('sys_mgr.notice') }}

        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :label-width="columnLangSizeFlag ? '88px' : '68px'" :inline="true">
        <el-form-item :label="$t('sys_mgr.notice_title')" prop="noticeTitle" :label-width="columnLangSizeFlag ? '144px' : '68px'" >
          <el-input
            v-model.trim="queryParams.noticeTitle"
            :placeholder="$t('sys_mgr.notice_input_title')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr.notice_operator')" prop="createBy">
          <el-input
            v-model.trim="queryParams.createBy"
            :placeholder="$t('sys_mgr.notice_input_operator')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('file_set.type_type')" prop="noticeType">
          <el-select v-model.trim="queryParams.noticeType" :placeholder="$t('sys_mgr.notice_type')" clearable size="small">
            <el-option
              v-for="dict in dict.type.sys_notice_type"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:notice:add']"
          >{{ $t('doc.this_dept_new_add') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:notice:edit']"
          >{{ $t('doc.this_dept_edit') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:notice:remove']"
          >{{ $t('doc.this_dept_delete') }}</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column :label="$t('file_handle.recovery_num')" align="center" prop="noticeId" width="100" />
          <el-table-column
            :label="$t('sys_mgr.notice_title')"
            align="center"
            prop="noticeTitle"
            :show-overflow-tooltip="true"
          />
          <el-table-column :label="$t('sys_mgr.notice_type')" align="center" prop="noticeType" width="100">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_notice_type" :value="scope.row.noticeType"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('doc.this_dept_status')" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_notice_status" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('sys_mgr.notice_creator')" align="center" prop="createBy" width="100" />
          <el-table-column :label="$t('file_set.version_create_time')" align="center" prop="createTime" width="100">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:notice:edit']"
              >{{ $t('doc.this_dept_edit') }}</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:notice:remove']"
              >{{ $t('doc.this_dept_delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改公告对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.notice_title')" prop="noticeTitle">
                <el-input v-model.trim="form.noticeTitle" :placeholder="$t('sys_mgr.notice_input_title')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.notice_type')" prop="noticeType">
                <el-select v-model.trim="form.noticeType" :placeholder="$t('doc.this_dept_pls_select')">
                  <el-option
                    v-for="dict in dict.type.sys_notice_type"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('doc.this_dept_status')">
                <el-radio-group v-model.trim="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_notice_status"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dictLanguage(dict)}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('sys_mgr.notice_content')">
                <editor v-model.trim="form.noticeContent" :min-height="192"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listNotice, getNotice, delNotice, addNotice, updateNotice } from "@/api/system/notice";

export default {
  name: "Notice",
  dicts: ['sys_notice_status', 'sys_notice_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: this.$t('sys_mgr.notice_title_not_null'), trigger: "blur" }
        ],
        noticeType: [
          { required: true, message: this.$t('sys_mgr.notice_type_not_null'), trigger: "change" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listNotice(this.queryParams).then(response => {
        this.noticeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('sys_mgr.notice_add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.noticeId || this.ids
      getNotice(noticeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr.notice_edit');
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.noticeId != undefined) {
            updateNotice(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addNotice(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids
      this.$modal.confirm(this.$t('sys_mgr.notice_text') + noticeIds + this.$t('file_set.signature_text1')).then(function() {
        return delNotice(noticeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).catch(() => {});
    }
  }
};
</script>
