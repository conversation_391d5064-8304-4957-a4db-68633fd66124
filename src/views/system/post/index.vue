<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
          {{ $t('sys_mgr.post_manage') }}

        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :label-width="columnLangSizeFlag ? '88px' : '68px'" :inline="true">
        <el-form-item :label="$t('sys_mgr.post_code')" prop="postCode">
          <el-input
            v-model.trim="queryParams.postCode"
            :placeholder="$t('sys_mgr.post_input_code')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr.post_name')" prop="postName">
          <el-input
            v-model.trim="queryParams.postName"
            :placeholder="$t('sys_mgr.post_input_name')"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('doc.this_dept_status')" prop="status">
          <el-select v-model.trim="queryParams.status" :placeholder="$t('sys_mgr.post_status')" clearable size="small">
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:post:add']"
          >{{ $t('doc.this_dept_new_add') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:post:edit']"
          >{{ $t('doc.this_dept_edit') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:post:remove']"
          >{{ $t('doc.this_dept_delete') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:post:export']"
          >{{ $t('doc.exterior_dept_export') }}</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column :label="$t('sys_mgr.post_num')" align="center" prop="postId" />
          <el-table-column :label="$t('sys_mgr.post_code')" align="center" prop="postCode" />
          <el-table-column :label="$t('sys_mgr.post_name')" align="center" prop="postName" />
          <el-table-column :label="$t('sys_mgr.post_sort')" align="center" prop="postSort" />
          <el-table-column :label="$t('doc.this_dept_status')" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('file_set.version_create_time')" align="center" prop="createTime" width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:post:edit']"
              >{{ $t('doc.this_dept_edit') }}</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:post:remove']"
              >{{ $t('doc.this_dept_delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改岗位对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item :label="$t('sys_mgr.post_name')" prop="postName">
            <el-input v-model.trim="form.postName" :placeholder="$t('sys_mgr.post_input_name')" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.post_code')" prop="postCode">
            <el-input v-model.trim="form.postCode" :placeholder="$t('sys_mgr.post_input_code_name')" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.post_order')" prop="postSort">
            <el-input-number v-model.trim="form.postSort" controls-position="right" :min="0" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.post_status')" prop="status">
            <el-radio-group v-model.trim="form.status">
              <el-radio
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
              >{{dictLanguage(dict)}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.user_remark')" prop="remark">
            <el-input v-model.trim="form.remark" type="textarea" :placeholder="$t('file_set.version_fill_content')" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listPost, getPost, delPost, addPost, updatePost } from "@/api/system/post";

export default {
  name: "Post",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [
          { required: true, message: this.$t('sys_mgr.post_name_not_null'), trigger: "blur" }
        ],
        postCode: [
          { required: true, message: this.$t('sys_mgr.post_code_not_null'), trigger: "blur" }
        ],
        postSort: [
          { required: true, message: this.$t('sys_mgr.post_order_not_null'), trigger: "blur" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      listPost(this.queryParams).then(response => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.postId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('sys_mgr.post_add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const postId = row.postId || this.ids
      getPost(postId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr.post_edit');
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal.confirm(this.$t('sys_mgr.post_text') + postIds + this.$t('file_set.signature_text1')).then(function() {
        return delPost(postIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/post/export', {
        ...this.queryParams
      }, `post_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
