<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" :label-width="columnLangSizeFlag ? '88px' : '68px'" >
      <el-form-item :label="$t('sys_mgr_log.template_msg_code')" prop="sendType" :label-width="columnLangSizeFlag ? '121px' : '68px'">
        <el-input
          v-model="queryParams.sendType"
          :placeholder="$t('sys_mgr_log.template_input_msg_code')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('doc.this_dept_title')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="$t('sys_mgr_log.template_input_title')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:notifyTemplate:add']"
        >{{ $t('doc.this_dept_new_add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:notifyTemplate:edit']"
        >{{ $t('doc.this_dept_edit') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:notifyTemplate:export']"
        >{{ $t('doc.exterior_dept_export') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="notifyTemplateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!--      <el-table-column label="消息模板" align="center" prop="templateId" v-if="true"/>-->
      <el-table-column :label="$t('sys_mgr_log.template_msg_code')" align="center" prop="sendType" />
      <el-table-column :label="$t('doc.this_dept_title')" align="center" prop="title" />
      <!--      <el-table-column label="通知内容" align="center" prop="message">
              <template slot-scope="scope">
                <span v-html="scope.message"></span>
              </template>
            </el-table-column>-->
      <el-table-column :label="$t('sys_mgr_log.template_noti_method')" align="center" prop="templateTypes" />
      <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:notifyTemplate:edit']"
          >{{ $t('doc.this_dept_edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【邮件模板】对话框 -->
    <el-dialog
      class="custom-dialog-class"
      :title="title"
      :visible.sync="open"
      :modal="false"
      append-to-body
      :close-on-click-modal="false"
      :fullscreen="false"
      width="60%"
      :modal-append-to-body="true"
      :custom-class="'notify-template-dialog'"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('sys_mgr_log.template_msg_code')" prop="sendType">
          <el-input v-model="form.sendType" :placeholder="this.$t('sys_mgr_log.template_input_msg_code')" />
        </el-form-item>
        <el-form-item :label="$t('doc.this_dept_title')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('doc.this_dept_insert_title')" />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr_log.template_noti_method')" prop="templateTypes">
          <el-radio-group v-model="form.templateTypes">
            <el-radio key="email" label="email">{{ $t('sys_mgr_log.template_email') }}</el-radio>
            <el-radio key="note" label="note">{{ $t('sys_mgr_log.template_text_msg') }}</el-radio>
            <el-radio key="wechat" label="wechat">{{ $t('sys_mgr_log.template_vx_public') }}</el-radio>
            <el-radio key="message" label="message">{{ $t('sys_mgr_log.template_mail') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('sys_mgr_log.template_noti_context')" prop="message">
          <UEditorPlus v-model="form.message" />
        </el-form-item>
        <!--    <el-form-item label="短信模板code" prop="templateCode">
                    <el-input v-model="form.templateCode" placeholder="请输入短信模板code" />
                  </el-form-item>
                  <el-form-item label="公众号消息模板code" prop="mpCode">
                    <el-input v-model="form.mpCode" placeholder="请输入公众号消息模板code" />
                  </el-form-item>-->
        <!--        <el-form-item label="1启用 0禁用">
                <el-radio-group v-model="form.status">
                  <el-radio label="1">请选择字典生成</el-radio>
                </el-radio-group>
                </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
        <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNotifyTemplate, getNotifyTemplate, delNotifyTemplate, addNotifyTemplate, updateNotifyTemplate } from "@/api/system/notifyTemplate";
import UEditorPlus from "@/components/UEditorPlus";

export default {
  name: "NotifyTemplate",
  components: { UEditorPlus },
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【邮件模板】表格数据
      notifyTemplateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sendType: undefined,
        message: undefined,
        templateTypes: undefined,
        templateCode: undefined,
        mpCode: undefined,
        status: undefined,
        title: undefined,
        orderByColumn: 'create_time',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        /*templateId: [
          { required: true, message: "消息模板表不能为空", trigger: "blur" }
        ],*/
        sendType: [
          { required: true, message: this.$t('sys_mgr_log.template_msg_code'), trigger: "change" }
        ],
        message: [
          { required: true, message: this.$t('sys_mgr_log.template_noti_context_not_null'), trigger: "blur" }
        ],
        templateTypes: [
          { required: true, message: this.$t('sys_mgr_log.template_noti_method_not_null'), trigger: "blur" }
        ],
        /*templateCode: [
          { required: true, message: "短信模板code不能为空", trigger: "blur" }
        ],
        mpCode: [
          { required: true, message: "公众号消息模板code不能为空", trigger: "blur" }
        ],*/
        /*status: [
          { required: true, message: "1启用 0禁用不能为空", trigger: "blur" }
        ],*/
        title: [
          { required: true, message: this.$t('doc.this_dept_title_not_null'), trigger: "blur" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【邮件模板】列表 */
    getList() {
      this.loading = true;
      listNotifyTemplate(this.queryParams).then(response => {
        this.notifyTemplateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        templateId: null,
        sendType: '',
        message: '',
        templateTypes: '',
        templateCode: '',
        mpCode: '',
        status: 1,
        createTime: '',
        title: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.templateId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('sys_mgr_log.template_add_email');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const templateId = row.templateId || this.ids
      getNotifyTemplate(templateId).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr_log.template_edit_email');
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.templateId) {
            updateNotifyTemplate(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addNotifyTemplate(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const templateIds = row.templateId || this.ids;
      this.$modal.confirm(this.$t('sys_mgr_log.template_text') + templateIds + this.$t('file_set.signature_text1')).then(() => {
        this.loading = true;
        return delNotifyTemplate(templateIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/notifyTemplate/export', {
        ...this.queryParams
      }, `notifyTemplate_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
