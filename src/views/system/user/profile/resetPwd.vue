<template>
  <el-form ref="form" :model="user" :rules="rules" :label-width="columnLangSizeFlag ? '168px' : '80px'">
    <el-form-item :label="$t('login.old_password')" prop="oldPassword">
      <el-input v-model.trim="user.oldPassword" :placeholder="$t('login.input_old_password')" type="password" show-password/>
    </el-form-item>
    <el-form-item :label="$t('login.new_password')" prop="newPassword">
      <el-input v-model.trim="user.newPassword" :placeholder="$t('login.input_new_password')" type="password" show-password/>
    </el-form-item>
    <el-form-item :label="$t('login.confrim_password')" prop="confirmPassword">
      <el-input v-model.trim="user.confirmPassword" :placeholder="$t('login.please_confrim_password')" type="password" show-password/>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{$t('doc.this_dept_save')}}</el-button>
      <el-button type="danger" size="mini" @click="close">{{$t('doc.this_dept_close')}}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwd } from "@/api/system/user";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error(this.$t('login.password_inconsistent')));
      } else {
        callback();
      }
    };
    return {
      test: "1test",
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: this.$t('login.old_password_not_null'), trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: this.$t('login.new_password_not_null'), trigger: "blur" },
          { min: 6, max: 20, message: this.$t('login.confrim_password_length_validate'), trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: this.$t('login.please_confrim_password_not_null'), trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(
            response => {
              this.$modal.msgSuccess(this.$t('doc.this_dept_update_success'));
            }
          );
        }
      });
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/index" });
    }
  }
};
</script>
