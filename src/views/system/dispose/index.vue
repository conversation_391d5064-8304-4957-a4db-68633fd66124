<template>
  <div class="app-container news el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs">
        <el-tab-pane name="0">
          <span slot="label">{{ $t('sys_mgr.dispose_apply') }}</span>
        </el-tab-pane>
        <el-tab-pane name="1">
          <span slot="label">{{ $t('sys_mgr.dispose_history_file_intial') }}</span>
        </el-tab-pane>
        <el-tab-pane name="2">
          <span slot="label">{{ $t('sys_mgr.dispose_project_history_file_intial') }}</span>
        </el-tab-pane>
      </el-tabs>
      <el-row :gutter="15" v-show="activeName == 0">
        <config></config>
      </el-row>
      <div v-show="activeName == 1">
        <initHistory dataType="stdd"></initHistory>
      </div>
      <div v-show="activeName == 2">
        <initHistory dataType="project"></initHistory>
      </div>
    </div>
  </div>
</template>
<script>
import config from "./config";
import initHistory from "./initHistory";
export default {
  components: {
    config,initHistory
  },
  data() {
    return {
      activeName: "1",
    };
  },
  computed: {},
  created() {
  },
  mounted() {},
  methods: {
  },
};
</script>
<style lang="scss">
</style>
