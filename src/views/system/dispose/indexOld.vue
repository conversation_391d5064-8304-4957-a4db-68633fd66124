<template>
  <div class="app-container news el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane name="0">
          <span slot="label">应用功能配置</span></el-tab-pane
        >
        <el-tab-pane name="1">
          <span slot="label">历史文件初始化</span>
        </el-tab-pane>
      </el-tabs>
      <el-row :gutter="15" v-show="activeName == 0">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="100px"
        >
          <el-col :span="24">
            <el-row gutter="15">
              <el-col :span="8">
                <el-form-item label="当前系统租户" prop="">
                  <el-select
                    v-model="formData.field104"
                    placeholder="请选择下拉选择"
                    clearable
                    :style="{ width: '100%' }"
                  >
                    <el-option
                      v-for="(item, index) in field104Options"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>

          <el-col :span="24">
            <el-row gutter="15">
              <el-col :span="8">
                <el-form-item label="当前系统版本" prop="">
                  <el-select
                    v-model="formData.field104"
                    placeholder="请选择下拉选择"
                    clearable
                    :style="{ width: '100%' }"
                  >
                    <el-option
                      v-for="(item, index) in field104Options"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-button size="small"> 备份+恢复初始化配置 </el-button>
                <span style="margin-left: 10px"
                  >温馨提示-备份文件路径：192.168.1.168/C:/20220314dms_bak</span
                >
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="4">
            <el-row gutter="15"> </el-row>
          </el-col>
          <el-col :span="20">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item label="变更申请功能" prop="">
                  <el-radio-group v-model="formData.field108" size="medium">
                    <el-radio
                      v-for="(item, index) in field108Options"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item label="复审流程功能" prop="">
                  <el-radio-group v-model="formData.field109" size="medium">
                    <el-radio
                      v-for="(item, index) in field109Options"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item label="关于签收规则" prop="">
                  <el-radio-group v-model="formData.field1090" size="medium">
                    <el-radio
                      v-for="(item, index) in field109Options0"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item label="关于打印规则" prop="">
                  <el-radio-group v-model="formData.field10901" size="medium">
                    <el-radio
                      v-for="(item, index) in field109Options01"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item label="打印回收规则" prop="">
                  <el-radio-group v-model="formData.field10902" size="medium">
                    <el-radio
                      v-for="(item, index) in field109Options02"
                      :key="index"
                      :label="item.value"
                      :disabled="item.disabled"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item size="large">
              <el-button type="primary" @click="submitForm">提交</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <div v-show="activeName == 1">
        <el-form :model="queryParams" ref="queryForm" label-width="68px">
          <div class="global-ser" id="add">
            <div class="ser-top">
              <treeselect
                v-model="queryParams.docClass"
                :options="classLevelOptions"
                :normalizer="normalizer"
                :show-count="true"
                :searchable="false"
                placeholder="选择文件类型"
                :disabled="disabled"
                style="width: 350px; float: left; margin-right: 10px"
              />
              <el-input
                v-model.trim="queryParams.searchValue"
                placeholder="输入文件名称/文件编号/文件版本关键字搜索"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
                style="width: 350px"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>

              <el-button slot="append" @click="handleQuery"> 查询 </el-button>

              <el-button slot="append" @click="handleuploadFile">
                上传文件夹
              </el-button>
              <el-button slot="append" @click="handleExport">
                导出模板
              </el-button>
              <el-button slot="append" @click="handlestandardimport">
                批量修改上传
              </el-button>
              <el-button
                slot="append"
                @click="handlestandardUpdateStatus"
                :disabled="single && multiple"
              >
                批量生效
              </el-button>
              <!--    上传-->
              <input
                v-show="false"
                ref="fileRef"
                id="fileFolder"
                type="file"
                @change="fileChange"
                webkitdirectory
                multiple="multiple"
              />
              <!--批量修改上传-->
              <input
                v-show="false"
                ref="filetandardimport"
                type="file"
                @change="handlestandardimportfileChange"
              />

              <!--替换文件-->
              <input
                v-show="false"
                ref="replaceFile"
                type="file"
                @change="handlestandardReplaceFileChange"
              />
            </div>
          </div>
        </el-form>
        <el-card class="gray-card">
          <el-table
            v-loading="loading"
            ref="singleTable"
            :data="postList"
            @selection-change="handleSelectionChange"
            highlight-current-row
            @current-change="handleCurrentChange"
            header-align="left"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="ID"
              align="left"
              prop="id"
              :show-overflow-tooltip="true"
            />

            <el-table-column
              label="文件名称"
              align="left"
              prop="docName"
              width="200"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span class="wenjcolor">{{ scope.row.docName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="文件扩展名" align="left" prop="fileType">
            </el-table-column>
            <el-table-column label="文件编号" align="left" prop="docId">
            </el-table-column>

            <el-table-column
              label="文件版本"
              align="left"
              prop="versionValue"
            />

            <el-table-column label="文件类型" align="left" prop="docClass">
              <template slot-scope="scope">
                {{ handleFileType(scope.row.docClass) }}
              </template>
            </el-table-column>

            <el-table-column
              label="录入时间"
              :show-overflow-tooltip="true"
              align="left"
              prop="startDate"
            >
              <template slot-scope="scope">
                <span>{{
                  parseTime(scope.row.startDate, "{y}-{m}-{d} {h}:{i}:{s}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="生效日期" align="left" prop="startDate">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="复审日期" align="left" prop="reviewTime">
              <template slot-scope="scope">
                <span>{{
                  parseTime(scope.row.reviewTime, "{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="文件状态" align="left" prop="status">
              <template slot-scope="scope">
                <span v-show="scope.row.status == 0">草稿</span>
                <span v-show="scope.row.status == 1">有效</span>
                <span v-show="scope.row.status == 2">失效</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="left"
              width="200"
              class-name="small-padding fixed-width"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDetails(scope.row, '详情')"
                  >详情
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDetails(scope.row, '修改')"
                  >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row)"
                  >删除
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  @click="handlestandardReplaceFile(scope.row)"
                  >替换文件
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          :page-sizes="[5, 10, 30, 40, 50, 100]"
          @pagination="getList"
        />

        <div style="display: flex">
          <div style="width: 50%; padding-right: 5px">
            <el-tabs
              v-model.trim="activeName1"
              class="out-tabs"
              @tab-click="linkstate"
            >
              <el-tab-pane name="first">
                <span slot="label">关联文件</span>
                <el-card class="gray-card">
                  <el-table
                    v-loading="linkloading"
                    :data="linkpostList"
                    header-align="left"
                  >
                    <el-table-column
                      label="文件名称"
                      align="left"
                      width="200"
                      :show-overflow-tooltip="true"
                    >
                      <template slot-scope="scope">
                        <span class="wenjcolor">{{ scope.row.fileName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="文件编号"
                      align="left"
                      prop="linkCode"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>

                    <el-table-column
                      label="文件版本"
                      align="left"
                      prop="versionValue"
                    />

                    <el-table-column
                      label="文件类型"
                      align="left"
                      prop="docClass"
                    >
                      <template slot-scope="scope">
                        {{ handleFileType(scope.row.docClass) }}
                      </template>
                    </el-table-column>

                    <el-table-column
                      label="操作"
                      align="left"
                      class-name="small-padding fixed-width"
                      fixed="right"
                    >
                      <template slot-scope="scope">
                        <el-button
                          size="mini"
                          type="text"
                          @click="handlelinkLogDelete(scope.row)"
                          >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <pagination
                  v-show="linktotal > 0"
                  :total="linktotal"
                  :page.sync="linkqueryParams.pageNum"
                  :limit.sync="linkqueryParams.pageSize"
                  :page-sizes="[5, 10, 30, 40, 50, 100]"
                  @pagination="getlinkLogList"
                />
              </el-tab-pane>
              <el-tab-pane name="second">
                <span slot="label">关联记录</span>
                <el-card class="gray-card">
                  <el-table
                    v-loading="linkloading"
                    :data="linkpostList"
                    header-align="left"
                  >
                    <el-table-column label="文件名称" align="left" width="200">
                      <template slot-scope="scope">
                        <span class="wenjcolor">{{ scope.row.fileName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="文件编号"
                      align="left"
                      prop="docId"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>

                    <el-table-column
                      label="文件版本"
                      align="left"
                      prop="versionValue"
                    />

                    <el-table-column
                      label="文件类型"
                      align="left"
                      prop="docClass"
                    >
                      <template slot-scope="scope">
                        {{ handleFileType(scope.row.docClass) }}
                      </template>
                    </el-table-column>

                    <el-table-column
                      label="操作"
                      align="left"
                      class-name="small-padding fixed-width"
                      fixed="right"
                    >
                      <template slot-scope="scope">
                        <el-button
                          size="mini"
                          type="text"
                          @click="handlelinkLogDelete(scope.row)"
                          >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <pagination
                  v-show="linktotal > 0"
                  :total="linktotal"
                  :page.sync="linkqueryParams.pageNum"
                  :limit.sync="linkqueryParams.pageSize"
                  :page-sizes="[5, 10, 30, 40, 50, 100]"
                  @pagination="getlinkLogList"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
          <div style="width: 50%; padding-left: 5px">
            <el-tabs
              v-model.trim="activeName2"
              class="out-tabs"
              @tab-click="state"
            >
              <el-tab-pane name="0">
                <span slot="label">双击选择关联文件/关联记录</span>
                <el-form :model="sjiParams" ref="queryForm" label-width="68px">
                  <div class="global-ser" id="add">
                    <div class="ser-top">
                      <treeselect
                        v-model="sjiParams.docClass"
                        :options="classLevelOptions"
                        :normalizer="normalizer"
                        :show-count="true"
                        :searchable="false"
                        placeholder="选择文件类型"
                        :disabled="disabled"
                        style="width: 200px; float: left; margin-right: 10px"
                      />
                      <el-input
                        v-model.trim="sjiParams.searchValue"
                        placeholder="输入文件名称/文件编号/文件版本关键字搜索"
                        @keyup.enter.native="sjihandleQuery"
                        class="input-with-select"
                        style="width: 350px"
                      >
                        <el-button
                          slot="append"
                          icon="el-icon-search"
                          @click="sjihandleQuery"
                        ></el-button>
                      </el-input>
                    </div>
                  </div>
                </el-form>
                <el-card class="gray-card">
                  <el-table
                    v-loading="sjiloading"
                    :data="sjipostList"
                    header-align="left"
                    @row-dblclick="rowDblclick"
                  >
                    <el-table-column
                      label="文件名称"
                      align="left"
                      width="200"
                      :show-overflow-tooltip="true"
                    >
                      <template slot-scope="scope">
                        <span class="wenjcolor">{{ scope.row.docName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="文件编号"
                      align="left"
                      prop="docId"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>

                    <el-table-column
                      label="文件版本"
                      align="left"
                      prop="versionValue"
                    />

                    <el-table-column
                      label="文件类型"
                      align="left"
                      prop="deptName"
                    >
                      <template slot-scope="scope">
                        {{ handleFileType(scope.row.docClass) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <pagination
                  v-show="sjitotal > 0"
                  :total="sjitotal"
                  :page.sync="sjiParams.pageNum"
                  :limit.sync="sjiParams.pageSize"
                  :page-sizes="[5, 10, 30, 40, 50, 100]"
                  @pagination="getsjiLogList"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <div v-if="progressShow" class="mask"></div>
    <div v-if="progressShow" class="maskprogress">
      <div
        style="
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
        "
      >
        <el-progress
          type="dashboard"
          :percentage="percentage"
          :color="colors"
        ></el-progress>
        <br />
        <div
          style="
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
          "
        >
          <div style="color: #ffff">正在上传中...</div>
          <br />
          <div style="color: #ffff">
            （已上传{{ filesnumber }}个/共{{ fileslength }}个）
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      append-to-body
      width="80%"
    >
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-position="right"
          label-width="150px"
          :disabled="title == '详情'"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="文件类型:" prop="docClass">
                <treeselect
                  v-model.trim="formData.docClass"
                  :options="classLevelOptions"
                  :normalizer="normalizer"
                  :searchable="false"
                  :show-count="true"
                  :disabled="title == '详情'"
                  placeholder="选择文件类型"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="变更类型:" prop="changeType">
                <el-select v-model="formData.changeType" placeholder="请选择">
                  <el-option
                    v-for="item in changeTypeoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="文件名称:" prop="docName">
                <div>
                  <el-input
                    v-model.trim="formData.docName"
                    placeholder="请输入文件名称"
                    clearable
                  >
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="变更原因:" prop="changeReason">
                <el-input
                  v-model.trim="formData.changeReason"
                  placeholder="请输入变更原因"
                  :style="{ width: '100%' }"
                  maxlength="500"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="当前版本文件:">
                <div style="display: flex; justify-content: space-between">
                  <span
                    style="color: #385bb4; margin-left: 10px; cursor: pointer"
                    :key="i"
                    v-if="formData.docName != ''"
                    @click="handlePreview(formData.fileId)"
                    >{{ formData.docName }}
                  </span>

                  <fileUpload
                    v-model.trim="docNamefileList"
                    limit="1"
                    :fileType="['docx', 'doc', 'xls', 'xlsx','pdf','ppt','pptx']"
                    :isfileType="true"
                    :fileSize="100"
                    :showTransition="false"
                    title="替换文件"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件编号:" prop="docId">
                <el-input
                  v-model.trim="formData.docId"
                  placeholder="请输入文件编号"
                  @blur="docIdblur"
                  maxlength="20"
                  show-word-limit
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="文件版本:" prop="currentVersion">
                <el-input
                  v-model.trim="formData.currentVersion"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="变更要素:" prop="changeFactor">
                <template>
                  <el-checkbox-group v-model.trim="changeFactor">
                    <el-checkbox
                      v-for="item in dict.type.change_factor"
                      :label="item.label"
                      :key="item.value"
                    >
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </template>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="生效日期:" prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期"
                  :picker-options="pickerOptionsstartDate"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="编制部门:" prop="deptName">
                <span>{{ formData.deptName }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="文件有效期:">
                <el-date-picker
                  v-model="formData.endDate"
                  align="right"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期"
                  :picker-options="pickerOptions0"
                >
                </el-date-picker>
                <span style="margin: 0px 5px">是否永久</span>
                <el-radio-group
                  v-model="formData.forever"
                  @change="handleforeverchange"
                >
                  <el-radio :label="'1'">是</el-radio>
                  <el-radio :label="'0'">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="编制人员:" prop="nickName">
                <div>
                  <el-input
                    style="width: 80%; margin-right: 10px"
                    v-model.trim="formData.nickName"
                    placeholder="请选择编制人员"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="handleSelect"
                    ></el-button>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="文件复审期:" prop="reviewTime">
                <el-date-picker
                  v-model="formData.reviewTime"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期"
                  :picker-options="pickerOptions0"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="编制时间:" prop="applyTime">
                <el-date-picker
                  v-model="formData.applyTime"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期"
                >
                </el-date-picker>
                <!-- <span>{{ parseTime(nowDate, "{y}-{m}-{d} {h}:{i}") }}</span> -->
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="文件状态:" prop="status">
                <el-select v-model="formData.status" placeholder="请选择">
                  <el-option
                    v-for="item in statusoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <!-- <el-upload
                  v-if="formData.changeType != 'ADD'"
                  :file-list="fileList"
                  :action="action"
                  :before-upload="beforeUpload"
                  :http-request="standardDocBeforeUpload"
                  :on-remove="handleRemoveAttachment1"
                >
                  <el-button size="small" type="primary" icon="el-icon-upload"
                    >点击上传
                  </el-button>
                </el-upload> -->
                <span v-for="(itme, i) in appendixsList" :key="i">
                  {{ itme.docName }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="ID:" prop="id">
                {{ formData.id }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="变更内容:" prop="content">
                <el-input
                  v-model.trim="formData.content"
                  type="textarea"
                  placeholder="请输入变更内容"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                  maxlength="500"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <div slot="footer" class="dialog-footer" v-if="title == '修改'">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="`编制人`"
      :visible.sync="userOpen"
      width="50%"
      append-to-body
    >
      <el-table
        v-loading="userListloading"
        :data="userList"
        ref="multipleTable"
      >
        <el-table-column fixed="left" label="选择" align="center">
          <!-- 添加单选按钮 -->
          <template slot-scope="scope">
            <el-radio
              v-model="selectRadio"
              :label="scope.$index"
              @change.native="getTemplateRow(scope.row)"
            >
              {{ "" }}
            </el-radio>
          </template>
        </el-table-column>

        <el-table-column
          label="用户名称"
          align="left"
          key="userName"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户昵称"
          align="left"
          key="nickName"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="部门"
          align="left"
          key="deptName"
          prop="dept.deptName"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <pagination
        v-show="userListtotal > 0"
        :total="userListtotal"
        :page.sync="userListqueryParams.pageNum"
        :limit.sync="userListqueryParams.pageSize"
        @pagination="getuserList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleuserOpen">取 消</el-button>
        <el-button type="primary" @click="handleSubmitUser">确 定</el-button>
      </div>
    </el-dialog>

    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close">
    </as-pre-view>
  </div>
</template>
<script>
import {
  standardList,
  standardUpload,
  linkLogList,
  processstandard,
  standardUpdate,
  standarddelete,
  linkLogDelete,
  standardimport,
  standardUpdateStatus,
  standardReplaceFile,
  standardJoin,
  checkNoIsExist,
} from "@/api/system/standard";
import { listUser } from "@/api/system/user";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  components: {
    Treeselect,
  },
  props: [],
  dicts: ["change_factor"],
  mixins: [mixin],
  data() {
    return {
      userListqueryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: "",
      },
      statusoptions: [
        {
          value: "0",
          label: "草稿",
        },
        {
          value: "1",
          label: "有效",
        },
        {
          value: "2",
          label: "失效",
        },
      ],
      changeTypeoptions: [
        {
          value: "ADD",
          label: "新增",
        },
        {
          value: "UPDATE",
          label: "修订",
        },
      ],

      dialogFormVisible: false,
      userListloading: false,
      userOpen: false,

      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      percentage: 0,
      fileslength: 0,
      filesnumber: 0,
      changeFactor: [],
      docNamefileList: [],
      colors: [
        { color: "#007dff", percentage: 20 },
        { color: "#007dff", percentage: 40 },
        { color: "#007dff", percentage: 60 },
        { color: "#007dff", percentage: 80 },
        { color: "#007dff", percentage: 100 },
      ],
      nowDate: new Date(),
      // 遮罩层
      loading: true,
      progressShow: false,
      postList: [],
      classLevelOptions: [],
      activeName: "1",
      activeName1: "first",
      activeName2: 0,
      userListtotal: 0,
      formData: {
        forever: undefined,
      },
      rules: {
        docName: [{ required: true, message: "请填写", trigger: "blur" }],
        currentVersion: [
          { required: true, message: "请填写", trigger: "blur" },
        ],
        changeReason: [{ required: true, message: "请填写", trigger: "blur" }],
        docId: [{ required: true, message: "请填写", trigger: "blur" }],
        content: [{ required: true, message: "请填写", trigger: "blur" }],
        versionValue: [{ required: true, message: "请填写", trigger: "blur" }],
        changeFactor: [{ required: true, message: "请选择", trigger: "blur" }],
        changeType: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        startDate: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],

        reviewTime: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        field108: [
          {
            required: true,
            message: "单选框组不能为空",
            trigger: "blur",
          },
        ],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        initFile: 1,
        searchValue: null,
        searchValue: undefined,
      },
      // 查询参数
      linkqueryParams: {
        pageNum: 1,
        pageSize: 5,
        linkType: "REF_DOC",
        standardId: "",
      },
      // 双击选择列表参数
      sjiParams: {
        pageSize: 5,
        pageNum: 1,
        searchValue: "",
        docClass: undefined,
        searchValue: "",
        status: "1",
      },
      linkloading: false,
      linktotal: 0,
      linkpostList: [],
      sjiloading: false,
      sjitotal: 0,
      sjipostList: [],
      field104Options: [
        {
          label: "选项一",
          value: 1,
        },
        {
          label: "选项二",
          value: 2,
        },
      ],
      field108Options: [
        {
          label: "变更申请按需选择",
          value: 1,
        },
        {
          label: "必须变更申请通过后-新增修订",
          value: 2,
        },
        {
          label: "不启用变更申请功能",
          value: 3,
        },
      ],
      title: "",
      field109: "",
      field109Options: [
        {
          label: "自动发起复审流程（待办提醒）",
          value: 1,
        },
        {
          label: "手动复审流程（消息提醒）",
          value: 2,
        },
        {
          label: "不启用复审流程",
          value: 3,
        },
      ],
      field1090: "",
      field109Options0: [
        {
          label: "未签收-直接修订/作废",
          value: 1,
        },
        {
          label: "必须签收-才可修订/作废",
          value: 2,
        },
      ],
      field10901: "",
      field109Options01: [
        {
          label: "未打印-直接修订/作废",
          value: 1,
        },
        {
          label: "必须打印-才可修订/作废",
          value: 2,
        },
      ],
      field10902: "",
      selectRadio: "",
      field109Options02: [
        {
          label: "必须先回收-再打印新版本",
          value: 1,
        },
        {
          label: "未回收-先打印新版本",
          value: 2,
        },
      ],
      selectionUser: {},
      selection: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      replaceFileId: undefined,
      currentRow: null,
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
        },
      },
      pickerOptionsstartDate: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6; //如果没有后面的-8.64e6就是不可以选择今天的
        },
      },
      cgnum: 0,
      sbnum: 0,
      sbnummsg: "",
    };
  },
  computed: {},
  watch: {
    activeName1(val) {
      console.log(val);
    },
    docNamefileList(val) {
      console.log("docNamefileList", val);
      if (val != "") {
        this.formData.fileId = val[0].url;
        this.formData.docName = val[0].name;
        this.docNamefileList = [];
      }
    },
    "formData.forever"(val) {
      console.log(val);
      if (val == 1) {
        this.formData.endDate = "";
      }
    },
    changeFactor(val) {
      this.formData.changeFactor = val.join(",");
    },
  },
  created() {
    this.getList();
    //this.getlinkLogList();
    this.getsjiLogList();
    this.getclassLevelOptions();
  },
  mounted() {},
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      standardList(this.queryParams).then((response) => {
        this.postList = response.rows;
        if (this.currentRow == null) {
          this.setCurrent(this.postList[0]);
        }
        this.total = response.total;
        this.loading = false;
      });
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },
    handleCurrentChange(val) {
      console.log(val, "2222");
      if (val != null) {
        console.log("单选主文件", val);
        this.currentRow = val;
        this.linkqueryParams.standardId = val.id;
        this.getlinkLogList();
      }
    },
    /** 查询关联列表 */
    getlinkLogList() {
      this.linkloading = true;
      linkLogList(this.linkqueryParams).then((response) => {
        this.linkpostList = response.rows;
        this.linktotal = response.total;
        this.linkloading = false;
        this.getsjiLogList();
      });
    },
    /** 查询列表 */
    getsjiLogList() {
      this.sjiloading = true;
      this.sjiParams.linkType = this.linkqueryParams.linkType;
      this.sjiParams.standardId = this.linkqueryParams.standardId;

      standardList(this.sjiParams).then((response) => {
        this.sjipostList = this.arrayRepeat(this.linkpostList, response.rows);

        this.sjitotal = response.total;
        this.sjiloading = false;
      });
    },

    /**
     * 两个数组对象去复
     * @param {*} array1
     * @param {*} array2
     */
    arrayRepeat(array1, array2) {
      var result = [];
      for (var i = 0; i < array2.length; i++) {
        var obj = array2[i];
        var num = obj.id;
        var isExist = false;
        for (var j = 0; j < array1.length; j++) {
          var aj = array1[j];
          var n = aj.linkCode;
          if (n === num) {
            isExist = true;
            break;
          }
        }
        if (!isExist) {
          result.push(obj);
        }
      }
      return result;
    },

    handleuploadFile() {
      if (this.queryParams.docClass == undefined) {
        this.$modal.msg("选择文件类型");
      } else {
        document.getElementById("fileFolder").value = null;
        this.$refs.fileRef.dispatchEvent(new MouseEvent("click"));
      }
    },
    fileChange(e) {
      console.log("fileChange", e);
      let _this = this;
      let files = e.target.files;
      this.fileslength = files.length;

      this.filesnumber = 0;
      let filesper = 100 / (files.length + 1);

      this.progressShow = true;
      if (this.fileslength == 0) {
        this.progressShow = false;
        this.$modal.msg("上传完成0个，未完成0个");
      }
      for (var i = 0; i < files.length; i++) {
        let file = e.target.files[i];
        let index = i;
        let formDatafile = new FormData();
        formDatafile.append("file", file);
        formDatafile.append("docClass", this.queryParams.docClass);
        standardUpload(formDatafile)
          .then((res) => {
            if (res.data == true) {
              this.cgnum = this.cgnum + 1;
            } else {
              this.sbnum = this.sbnum + 1;
              this.sbnummsg = this.sbnummsg + res.msg + "<br/>";
            }
            this.filesnumber = this.filesnumber + 1;
            if (this.filesnumber == this.fileslength) {
              this.progressShow = false;
              this.$message({
                showClose: true,
                dangerouslyUseHTMLString: true,
                duration: 0,
                message:
                  "上传完成" +
                  this.cgnum +
                  "个，未完成" +
                  this.sbnum +
                  "个<br/>" +
                  this.sbnummsg,
                type: "warning",
              });
              document.getElementById("fileFolder").value = null;
              this.cgnum = 0;
              this.sbnum = 0;
              this.sbnummsg = "";
            }
            if (res.code == 200) {
              this.getList();
            } else {
            }
            this.percentage = this.percentage + filesper * (index + 1);
            if (this.percentage > 100) {
              this.percentage = 100;
            }
          })
          .catch((error) => {
            console.log("error", error);
            this.progressShow = false;
          });
      }
      this.$refs.fileRef.value=null;
    },
    handlestandardimport() {
      this.$refs.filetandardimport.dispatchEvent(new MouseEvent("click"));
    },
    handlestandardReplaceFile(e) {
      console.log(e);
      this.replaceFileId = e;
      this.$modal
        .confirm("一经替换无法恢复，确定替换当前文件，上传新文件？")

        .then(() => {
          this.$refs.replaceFile.dispatchEvent(new MouseEvent("click"));
        })
        .catch(() => {});
    },
    handlestandardimportfileChange(e) {
      let file = e.target.files[0];
      console.log(e.target);
      this.formimportfile = new FormData();
      this.formimportfile.append("file", file);
      this.formimportfile.append("tenantId", cache.session.get('tenantId'));
      if (file != undefined) {
        standardimport(this.formimportfile).then((res) => {
          if(res.data.initFileMsg.length>0||res.data.initRecordMsg.length>0||res.data.linkFileMsg.length>0 ){
            this.$message({
              showClose: true,
              dangerouslyUseHTMLString: true,
              duration: 0,
              message:
                res.data.initFileMsg +
                "<br/>" +
                res.data.initRecordMsg +
                "<br/>" +
                res.data.linkFileMsg,
              type: "warning",
            });
          }else{
            this.$message.success("操作成功");
          }
          this.getList();
          this.$refs.filetandardimport.value=null;
        });
      }
    },
    handlestandardReplaceFileChange(e) {
      let file = e.target.files[0];
      //校验文件类型
      let fileType = ["doc", "docx", "xls", "xlsx", "pdf"];
      let fileExtension = "";

      if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => {
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      if (!isTypeOk) {
        this.$modal.msg(`文件格式不支持!`);
        return false;
      }

      // 校检文件大小

      const isLt = file.size / 1024 / 1024 < 100;
      if (!isLt) {
        this.$modal.msgError(`上传文件大小不能超过 100 MB!`);
        return false;
      }

      console.log(e);
      this.formimportfile = new FormData();
      this.formimportfile.append("file", file);
      this.formimportfile.append("id", this.replaceFileId.id);
      this.formimportfile.append("docId", this.replaceFileId.docId);
      this.formimportfile.append("versionId", this.replaceFileId.versionId);
      standardReplaceFile(this.formimportfile).then((res) => {
        this.$message.success("操作成功");
        this.getList();
        this.$refs.replaceFile.value=null;
      });
    },
    state(tab) {
      console.log(tab);
    },
    linkstate(tab) {
      console.log(tab.index);
      if (tab.index == 0) {
        this.linkqueryParams.linkType = "REF_DOC";
      }
      if (tab.index == 1) {
        this.linkqueryParams.linkType = "RECORD";
      }
      this.getlinkLogList();
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单

        this.formData.applyTime = this.parseTime(
          this.nowDate,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
        standardUpdate(this.formData).then((res) => {
          this.$message.success("操作成功");
          this.getList();
          this.dialogFormVisible = false;
        });
      });
    },
    increase() {
      this.percentage += 10;
      if (this.percentage > 100) {
        this.percentage = 100;
      }
    },
    decrease() {
      this.percentage -= 10;
      if (this.percentage < 0) {
        this.percentage = 0;
      }
    },
    getclassLevelOptions() {
      settingDocClassList({  classStatus: "1", dataType: "stdd"  }).then(
        (response) => {
          this.classLevelOptions = [];
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          this.classLevelOptions = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 搜索按钮操作 */
    sjihandleQuery() {
      this.sjiParams.pageNum = 1;
      this.getsjiLogList();
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    handleDetails(e, title) {
      this.title = title;
      console.log(e);
      processstandard(e.id).then((res) => {
        console.log("详情", res);

        res.data.forever = String(res.data.forever);

        this.formData = res.data;
        if (res.data.changeFactor == null) {
          this.changeFactor = [];
        }
        this.formData.editUserName = res.data.userName;
        if (res.data.nickName != null) {
          this.formData.nickName = res.data.nickName;
        } else {
          this.formData.nickName = this.userInfo.nickName;
        }
        //  this.formData.userName = this.selectionUser.userName;
        if (res.data.userName != null) {
          this.formData.userName = res.data.userName;
        } else {
          this.formData.userName = this.userInfo.userName;
        }
        if (res.data.deptId != null) {
          this.formData.deptId = res.data.deptId;
        } else {
          this.formData.deptId = this.userInfo.deptId;
        }
        if (res.data.deptName != null) {
          this.formData.deptName = res.data.deptName;
        } else {
          this.formData.deptName = this.userInfo.dept.deptName;
        }

        if (res.data.applyTime != null) {
          this.formData.applyTime = res.data.applyTime;
        } else {
          this.formData.applyTime = this.nowDate;
        }

        this.changeFactor = res.data.changeFactor.split(",");
      });
      if (this.title == "修改" && e.status != 0) {
        this.$modal
          .confirm(
            "确定修改吗？确定，文件状态将变成草稿状态，需要重新生效操作！"
          )
          .then(() => {
            this.dialogFormVisible = true;
          })
          .catch(() => {});
      } else {
        this.dialogFormVisible = true;
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/process/standard/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection);
      this.selection = selection;
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleSelect() {
      // this.formData.userName = "";
      // this.formData.deptId = "";
      // this.formData.deptName = "";
      this.userOpen = true;
      this.getuserList();
    },
    getuserList() {
      this.userListloading = true;
      this.userList = [];
      //this.userListqueryParams.deptId = this.userInfo.deptId;
      listUser(this.userListqueryParams).then((res) => {
        this.userList = res.rows;
        this.userListtotal = res.total;
        this.userListloading = false;
      });
    },

    getTemplateRow(item) {
      console.log(item);
      this.selectionUser = item;
      // ... 拿到选中的表格数据
    },
    handleSubmitUser() {
      this.userOpen = false;
      this.formData.editUserName = this.selectionUser.userName;
      this.formData.nickName = this.selectionUser.nickName;
      this.formData.userName = this.selectionUser.userName;
      this.formData.deptId = this.selectionUser.deptId;
      this.formData.deptName = this.selectionUser.dept.deptName;
    },
    handleuserOpen() {
      console.log("11");
      this.userOpen = false;
      console.log("2222");
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm(
          "一经删除不可恢复！您确定要删除当前生效主文件、解除关联文件及关联记录关系吗？"
        )
        .then(function () {
          return standarddelete({
            id: row.id,
            docId: row.docId,
            versionId: row.versionId,
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 删除按钮操作 */
    handlelinkLogDelete(row) {
      this.$modal
        .confirm("一经删除无法恢复，您确定要取消关联关系吗？")
        .then(function () {
          return linkLogDelete(row.id);
        })
        .then(() => {
          this.getlinkLogList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handlestandardUpdateStatus() {
      let ids = this.selection.map((item) => item.id);
      standardUpdateStatus(null,ids).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      });
    },
    rowDblclick(e) {
      console.log("双击", e);
      console.log("this.currentRow", this.currentRow);
      if (this.currentRow == null) {
        this.$modal.msg("请选择主文件");
        return;
      }
      if (this.currentRow.status != 1) {
        this.$modal.msg("请选择文件状态为'有效'的文件");
        return;
      }
      e.id = this.currentRow.id;
      e.linkType = this.linkqueryParams.linkType;
      standardJoin(e).then((res) => {
        this.$modal.msgSuccess("操作成功");
        this.getlinkLogList();
        this.getsjiLogList();
      });
    },
    handleforeverchange(e) {
      console.log(e);
    },
    docIdblur(e) {
      console.log(e.target.value);
      checkNoIsExist({ docId: e.target.value }).then((res) => {
        //true:存在，false：不存在
        if (res.data == true) {
          this.formData.docId = "";
          this.$modal.msg("文件编号已存在");
        }
      });
    },
  },
};
</script>
<style lang="scss">
.vue-treeselect {
  height: 34px;
}
.vue-treeselect .vue-treeselect__control {
  height: 34px !important;
}
.vue-treeselect__placeholder {
  line-height: 34px;
  font-size: 14px;
}
.vue-treeselect input {
  font-size: 14px;
}
.mask {
  background-color: rgb(0, 0, 0);
  opacity: 0.6;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
}
.maskprogress {
  position: fixed;
  top: 50%; /*距顶部50%*/
  left: 50%;
  margin: -100px 0 0 -100px; /*设定这个div的margin-top的负值为自身的高度的一半,margin-left的值也是自身的宽度的一半的负值.
  // width: 300px; /*宽为400,那么margin-top为-200px*/
  // height: 200px; /*高为200那么margin-left为-100px;*/
  z-index: 9999999; /*浮动在最上层 */
  .el-progress__text {
    color: #ffff;
  }
}
</style>
