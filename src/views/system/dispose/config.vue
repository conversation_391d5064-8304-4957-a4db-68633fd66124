<template>
  <div>
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      :label-width="columnLangSizeFlag ? '188px' : '100px'"
    >
      <el-col :span="24">
        <el-row gutter="15">
          <el-col :span="8">
            <el-form-item :label="$t('sys_mgr.dispose_current_tenant')" prop="">
              <el-select
                v-model="formData.field104"
                :placeholder="$t('sys_mgr.dispose_select_drop_down')"
                clearable
                :style="{ width: '100%' }"
              >
                <!--                  <el-option-->
                <!--                    v-for="(item, index) in field104Options"-->
                <!--                    :key="index"-->
                <!--                    :label="item.label"-->
                <!--                    :value="item.value"-->
                <!--                    :disabled="item.disabled"-->
                <!--                  ></el-option>-->
                <el-option
                  v-for="(item,index) in dict.type.sys_ver_for_now"
                  :key="index"
                  :label="dictLanguage(item)"
                  :value="item.value"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="24">
        <el-row gutter="15">
          <el-col :span="8">
            <el-form-item :label="$t('sys_mgr.dispose_current_ver')" prop="">
              <el-select
                v-model="formData.field104"
                :placeholder="$t('sys_mgr.dispose_select_drop_down')"
                clearable
                :style="{ width: '100%' }"
              >
                <!--                  <el-option-->
                <!--                    v-for="(item, index) in field104Options"-->
                <!--                    :key="index"-->
                <!--                    :label="item.label"-->
                <!--                    :value="item.value"-->
                <!--                    :disabled="item.disabled"-->
                <!--                  ></el-option>-->
                <el-option
                  v-for="(item,index) in dict.type.sys_ver_for_now"
                  :key="index"
                  :label="dictLanguage(item)"
                  :value="item.value"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-button size="small"> {{ $t('sys_mgr.dispose_button_text') }} </el-button>
            <span style="margin-left: 10px"
            >{{ $t('sys_mgr.dispose_apply_text') }}192.168.1.168/C:/20220314dms_bak</span
            >
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="4">
        <el-row gutter="15"> </el-row>
      </el-col>
      <el-col :span="20">
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item :label="$t('sys_mgr.dispose_subtitle1')" prop="">
              <el-radio-group v-model="formData.field108" size="medium">
                <el-radio
                  v-for="(item, index) in dict.type.sys_change_req"
                  :key="index"
                  :label="item.value"
                  :disabled="item.disabled"
                >{{ dictLanguage(item) }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="24">
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item :label="$t('sys_mgr.dispose_subtitle2')" prop="">
              <el-radio-group v-model="formData.field109" size="medium">
                <el-radio
                  v-for="(item, index) in dict.type.sys_review_process"
                  :key="index"
                  :label="item.value"
                  :disabled="item.disabled"
                >{{ dictLanguage(item) }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="24">
        <el-row gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('sys_mgr.dispose_subtitle3')" prop="">
              <el-radio-group v-model="formData.field1090" size="medium">
                <!--                  <el-radio-->
                <!--                    v-for="(item, index) in field109Options0"-->
                <!--                    :key="index"-->
                <!--                    :label="item.value"-->
                <!--                    :disabled="item.disabled"-->
                <!--                    >{{ item.label }}</el-radio-->
                <!--                  >-->
                <el-radio
                  v-for="(item, index) in dict.type.regard_sign_rule"
                  :key="index"
                  :label="item.value"
                  :disabled="item.disabled"
                >{{ dictLanguage(item) }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="24">
        <el-row gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('sys_mgr.dispose_subtitle4')" prop="">
              <el-radio-group v-model="formData.field10901" size="medium">
                <el-radio
                  v-for="(item, index) in dict.type.about_print_rule"
                  :key="index"
                  :label="item.value"
                  :disabled="item.disabled"
                >{{ dictLanguage(item) }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="24">
        <el-row gutter="15">
          <el-col :span="12">
            <el-form-item :label="$t('sys_mgr.dispose_subtitle5')" prop="">
              <el-radio-group v-model="formData.field10902" size="medium">
                <!--                  <el-radio-->
                <!--                    v-for="(item, index) in field109Options02"-->
                <!--                    :key="index"-->
                <!--                    :label="item.value"-->
                <!--                    :disabled="item.disabled"-->
                <!--                    >{{ item.label }}</el-radio-->
                <!--                  >-->
                <el-radio
                  v-for="(item, index) in dict.type.print_recycle_rule"
                  :key="index"
                  :label="item.value"
                  :disabled="item.disabled"
                >{{ dictLanguage(item) }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="24">
        <el-form-item size="large">
          <el-button type="primary" @click="submitForm">{{ $t('doc.this_dept_annex') }}</el-button>
          <el-button @click="resetForm">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>
<script>
import {
  standardUpdate,
} from "@/api/system/standard";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  components: {
    Treeselect,
  },
  props: [],
  dicts: ["change_factor","sys_ver_for_now","sys_change_req","sys_review_process","regard_sign_rule","about_print_rule","print_recycle_rule"],
  mixins: [mixin],
  data() {
    return {
      nowDate: new Date(),
      rules: {
        docName: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        currentVersion: [
          { required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" },
        ],
        changeReason: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        docId: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        content: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        versionValue: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        changeFactor: [{ required: true, message: this.$t('doc.this_dept_pls_select'), trigger: "blur" }],
        changeType: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],
        startDate: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],

        reviewTime: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],
        field108: [
          {
            required: true,
            message: this.$t('sys_mgr.dispose_radio_not_null'),
            trigger: "blur",
          },
        ],
      },
      formData: {
        forever: undefined,
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  computed: {},
  watch: {
    activeName1(val) {
      console.log(val);
    },
    docNamefileList(val) {
      console.log("docNamefileList", val);
      if (val != "") {
        this.formData.fileId = val[0].url;
        this.formData.docName = val[0].name;
        this.docNamefileList = [];
      }
    },
    "formData.forever"(val) {
      console.log(val);
      if (val == 1) {
        this.formData.endDate = "";
      }
    },
    changeFactor(val) {
      this.formData.changeFactor = val.join(",");
    },
  },
  created() {
  },
  mounted() {},
  methods: {

    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单

        this.formData.applyTime = this.parseTime(
          this.nowDate,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
        standardUpdate(this.formData).then((res) => {
          this.$message.success(this.$t('doc.this_dept_operation_succ'));
        });
      });
    },
  },
};
</script>
<style lang="scss">
</style>
