<template>
  <div>
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="130px"
    >
      <el-form-item :label="getLabelWithColon($t('sys_mgr_log.user_signature_user'))" prop="userName">
        <el-input style="width: 80%; margin-right: 10px" readonly v-model.trim="formData.userName" :placeholder="$t('sys_mgr_log.user_signature_select_user')">
          <el-button slot="append" icon="el-icon-search" @click="handleSelect"></el-button>
        </el-input>
      </el-form-item>

      <el-form-item :label="getLabelWithColon($t('file_set.signature_temp'))" prop="fileIdfileList">
        <fileUpload v-model="fileIdfileList" limit="1" :fileType="['png', 'jpg', 'gif', 'svg', 'bmp']" />
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t('doc.this_dept_status'))" prop="status">
        <el-radio-group v-model.trim="formData.status" size="medium">
          <el-radio
            v-for="(item, index) in dict.type.class_status"
            :key="index"
            :label="item.value"
            :disabled="item.disabled"
          >{{ dictLanguage(item) }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item size="large">
        <el-button type="primary" @click="submitForm" v-dbClick>{{ $t('doc.this_dept_annex') }}</el-button>
        <el-button @click="resetForm">{{ $t('myItem.handle_reset') }}</el-button>
      </el-form-item>
    </el-form>
    <user-list ref="userList" pageType="userSignature" @selectHandle="handleSubmitUser"></user-list>
  </div>
</template>
<script>
import {
  listSignature,
  getUserSignature,
  delSignature,
  addUserSignature,
  updateUserSignature,
} from "../../../api/system/userSignature";
import { processFileLocalUpload } from "@/api/commmon/file";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'

export default {
  dicts: ["class_status"],
  components: {
    Treeselect,UserList
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    let validateFileUrl = (rule, value, callback) => {
      if (this.fileIdfileList.length < 1) {
        //我控制了FileList 长度代表文件个数
        callback(new Error(this.$t('sys_mgr_log.user_signature_upload_temp')));
      } else {
        callback();
      }
    };
    return {
      classLevelOptions: [],
      formData: {},
      classLevelHandle: [],
      rules: {
        userName: [
          {
            required: true,
            message: this.$t('sys_mgr_log.user_signature_select_user'),
            trigger: "change",
          }
        ],
        fileIdfileList: [{ required: true, validator: validateFileUrl, trigger: ["blur", "change", "input"] }],
        status: [
          {
            required: true,
            message: this.$t('sys_mgr_log.user_signature_select_status'),
            trigger: "change",
          }
        ]
      },
      fileIdfileList: []
    };
  },
  computed: {},
  watch: {},
  created() {
    if (this.id != "") {
      getUserSignature(this.id).then((response) => {
        if (response.data.fileId != "") {
          this.fileIdfileList.push({
            name: response.data.templateName,
            url: response.data.fileId,
          });
        }
        this.formData = response.data;
      });
    }
  },
  mounted() {
  },
  methods: {
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          if (this.fileIdfileList != "") {
            this.formData.fileId = this.fileIdfileList[0].url;
            this.formData.templateName = this.fileIdfileList[0].name;
          }
          if (this.id != "") {
            updateUserSignature(this.formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.$emit("chinldClose");
            });
          } else {
            addUserSignature(this.formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.$emit("chinldClose");
            });
          }
        }
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    appendixesUpload(params) {
      this.fileIdfileList = [];
      let fd = new FormData();
      fd.append("file", params.file); //传文件
      processFileLocalUpload(fd).then((res) => {
        this.fileIdfileList.push({
          name: res.data.fileName,
          url: res.data.fileId,
        });
      });
    },
    // 删除附件
    handleRemoveAttachment(file, fileList) {
      this.fileIdfileList = this.fileIdfileList.filter(
        (item) => item.url !== file.url
      );
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    handleSelect() {
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(null,null,null)
      })
    },
    handleSubmitUser(source,index,user) {
      this.$set(this.formData,'userName',user.nickName)
      this.$set(this.formData,'userCode',user.userName)
    },
  },
};
</script>
<style>
</style>
