<template>
  <div id="app">

    <el-row :gutter="20">
      <el-col :span="24">
        <div class="ss-card">
          <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
            <el-form-item label="人员分组编码" prop="code">
              <el-input
                v-model="queryParams.code"
                placeholder="请输入人员分组编码"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="人员分组名称" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入人员分组名称"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="是否有效" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
                <el-option
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div><!-----sc-card 搜索卡片---->
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>

          <div slot="header" class="clearfix">
            <span>人员分组</span>
            <div class="header-button">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['system:group:add']"
              >新增人员分组
              </el-button>
            </div>
          </div>
          <el-table v-loading="loading" :data="groupList" @selection-change="handleSelectionChange"
                    @row-click="rowClick"
                    highlight-current-row="true" style="width: 100%"
          >
            <el-table-column label="主键" align="center" prop="id" v-if="false"/>
            <el-table-column label="人员分组编码" align="center" prop="code"/>
            <el-table-column label="人员分组名称" align="center" prop="name"/>
            <el-table-column label="是否有效" align="center" prop="status">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.status"/>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-share"
                  @click="handleLinkUser(scope.row)"
                >关联成员
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-document-copy"
                  @click="handleCopy(scope.row)"
                >复制
                </el-button>

                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:group:edit']"
                >修改
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:group:remove']"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card><!--------el-card--------->
      </el-col>

      <!--项目相关的人员列表-->
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span> 关联成员 </span>
          </div>
          <el-table v-loading="loading" :data="userGroupList">
            <el-table-column label="用户昵称" align="center" prop="user.nickName"/>
            <el-table-column label="用户名称" align="center" prop="user.userName"/>
            <el-table-column label="部门" align="center" prop="user.dept.deptName"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteDetail(scope.row)"
                  v-hasPermi="['system:group:remove']"
                >移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card><!--------el-card--------->
      </el-col>
    </el-row>


    <!-- 添加或修改人员分组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="人员分组编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入人员分组编码"/>
        </el-form-item>
        <el-form-item label="人员分组名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入人员分组名称"/>
        </el-form-item>
        <el-form-item label="是否有效" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" maxlength="200" v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <userSelectCom @callback="handelUserSelectBack" ref="userSelectCom" title="title" width="800px" :bizId="currRow.id"
                   :to_data="form.selectedUserArray"
    />
  </div>
</template>

<script>
import { copyGroup, listGroup, getGroup, delGroup, addGroup, updateGroup } from '@/api/system/group'
import { listUserGroup, delUserGroup, batchAddUserGroup } from '@/api/system/userGroup'
import { listInfoGroup, delInfoGroup, addInfoGroup } from '@/api/system/projectInfoGroup'
// 引入用户选择组件
import userSelectCom from '@/components/UserSelect'

export default {
  name: 'Group',
  components: { userSelectCom },
  dicts: ['sys_yes_no'],
  data() {
    return {
      // 当前行记录
      currRow: {},
      // 用户选择组件展示标识
      linkUserShow: false,
      boxClass: false,
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人员分组表格数据
      groupList: [],
      // 人员分组成员表格数据
      userGroupList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: undefined,
        code: undefined,
        name: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        status: 'Y',
        // 已经选择用户清单（传递给用户组件）
        selectedUserArray: []
      },
      // 表单校验
      rules: {
        id: [
          { required: true, message: '主键不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '人员分组编码不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '人员分组名称不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '是否有效', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 打开人员选择组件
    handleLinkUser(row) {
      this.currRow = row
      this.$refs.userSelectCom.handleShow(true)
    },
    // 接收人员选择组件返回数据
    handelUserSelectBack(bizId, jsonArrayStr) {
      // 返回结构
      // [{"id":"123","parentId":"208","label":"设备部领导","weight":"user","deptName":"设备部"}]
      let self = this
      let itemArr = JSON.parse(jsonArrayStr)
      let userGroupList = []
      itemArr.forEach(function(item, index) {
        let newObj = {}
        newObj.userId = item.id
        newObj.groupId = self.currRow.id
        newObj.tenantId = self.currRow.tenantId
        userGroupList.push(newObj)
      })
      self.handelBatchAdd(bizId, userGroupList)
    },
    /** 批量关联成员列表 */
    handelBatchAdd(bizId, userGroupList) {
      let self = this
      self.loading = true
      batchAddUserGroup(bizId, userGroupList).then(response => {
        self.loading = false
        if (response.code == 200) {
          self.$modal.msgSuccess('更新成员清单成功')
          self.getUserGroupList(bizId)
        } else {
          self.$modal.msgError('更新成员清单失败：' + response.msg)
        }
      })
    },
    /** 查询人员分组列表 */
    getList() {
      this.loading = true
      listGroup(this.queryParams).then(response => {
        this.groupList = response.rows
        this.total = response.total
      }).finally(()=>{
        this.loading = false
      })
    },
    /** 查询人员分组成员列表 */
    getUserGroupList(groupId) {
      let self = this
      this.loading = true
      let query = {}
      query.groupId = groupId
      listUserGroup(query).then(response => {
        self.userGroupList = response.rows
        // 转换为选人组件已有名单集合
        self.form.selectedUserArray = []
        self.userGroupList.forEach(function(item, index) {
          if (item.user != null) {
            let newObj = {}
            newObj.id = item.user.userId
            newObj.detpName = item.user.dept.deptName
            newObj.label = item.user.nickName
            self.form.selectedUserArray.push(newObj)
          }
        })
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        tenantId: undefined,
        code: undefined,
        name: undefined,
        status: 'Y',
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        remark: undefined

      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 单击行记录
    rowClick(row, column, event) {
      this.currRow = row
      this.getUserGroupList(row.id)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加人员分组'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getGroup(id).then(response => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '修改人员分组'
      })
    },
    handleCopy(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getGroup(id).then(response => {
        this.loading = false
        this.form = response.data
        this.form.code = this.form.code + '_copy'
        this.form.name = this.form.name + '_副本'
        this.open = true
        this.title = '复制人员分组'
      })
    },
    /** 提交按钮 */
    submitForm() {
      const userInfo = JSON.parse(sessionStorage.getItem('USER_INFO'))
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.buttonLoading = true
          if (this.title == '复制人员分组') {
            copyGroup(this.form).then(response => {
              this.$modal.msgSuccess('复制成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.buttonLoading = false
            })
          } else {
            if (this.form.id != null) {
              updateGroup(this.form).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              }).finally(() => {
                this.buttonLoading = false
              })
            } else {
              // 当前登录人所属租户
              this.form.tenantId = userInfo.tenantId
              addGroup(this.form).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              }).finally(() => {
                this.buttonLoading = false
              })
            }
          }

        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let self = this
      self.loading = true
      let query = {}
      if (this.userGroupList.length > 0) {
        this.$modal.alert('您好，此人员分组已关联了' + this.userGroupList.length + '个成员，请先移除相关成员再删除人员分组。')
        return false
      }
      self.$modal.confirm('是否确认删除人员分组名称为"' + row.name + '"的数据项？').then(() => {
        self.loading = true
        return delGroup(row.id)
      }).then(() => {
        self.loading = false
        self.getList()
        self.$modal.msgSuccess('删除成功')
      }).then(()=>{
        if(this.groupList&&this.groupList.length>0) {
          this.rowClick(this.groupList[0])
        }
      }).finally(() => {
        self.loading = false
      })

    },
    /** 移除人员分组成员-按钮操作 */
    handleDeleteDetail(row) {
      this.loading=true
      delUserGroup(row.id).then(() => {
        this.loading = false
        this.getUserGroupList(row.groupId)
        this.$modal.msgSuccess('移除成功')
      }).finally(() => {
        this.loading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/group/export', {
        ...this.queryParams
      }, `group_${new Date().getTime()}.xlsx`)
    }
  }
}

</script>
