<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>
          {{ $t('sys_mgr.dict_manage') }}

        </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :label-width="columnLangSizeFlag ? '128px' : '68px'" :inline="true">
        <el-form-item :label="$t('sys_mgr.dict_name')" prop="dictName">
          <el-input
            v-model.trim="queryParams.dictName"
            :placeholder="$t('sys_mgr.dict_input_name')"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('sys_mgr.dict_type')" prop="dictType">
          <el-input
            v-model.trim="queryParams.dictType"
            :placeholder="$t('sys_mgr.dict_input_type')"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="$t('doc.this_dept_status')" prop="status">
          <el-select
            v-model.trim="queryParams.status"
            :placeholder="$t('sys_mgr.dict_status')"
            clearable
            size="small"
            style="width: 240px"
          >
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('file_set.version_create_time')">
          <el-date-picker
            v-model.trim="dateRange"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('doc.this_dept_start_date')"
            :end-placeholder="$t('doc.this_dept_end_date')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
          <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"

            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:dict:add']"
          >{{ $t('doc.this_dept_new_add') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:dict:edit']"
          >{{ $t('doc.this_dept_edit') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:dict:remove']"
          >{{ $t('doc.this_dept_delete') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:dict:export']"
          >{{ $t('doc.exterior_dept_export') }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="handleRefreshCache"
            v-hasPermi="['system:dict:remove']"
          >{{ $t('sys_mgr.dict_refresh_cache') }}</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column :label="$t('sys_mgr.dict_num')" align="center" prop="dictId" />
          <el-table-column :label="$t('sys_mgr.dict_name')" align="center" prop="dictName" :show-overflow-tooltip="true" />
          <el-table-column :label="$t('sys_mgr.dict_type')" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <router-link :to="'/system/dict-data/index/' + scope.row.dictId" class="link-type">
                <span>{{ scope.row.dictType }}</span>
              </router-link>
            </template>
          </el-table-column>
          <el-table-column :label="$t('doc.this_dept_status')" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('sys_mgr.user_remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
          <el-table-column :label="$t('file_set.version_create_time')" align="center" prop="createTime" width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:dict:edit']"
              >{{ $t('doc.this_dept_edit') }}</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:dict:remove']"
              >{{ $t('doc.this_dept_delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item :label="$t('sys_mgr.dict_name')" prop="dictName">
            <el-input v-model.trim="form.dictName" :placeholder="$t('sys_mgr.dict_input_name')" />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.dict_type')" prop="dictType">
            <el-input v-model.trim="form.dictType" :placeholder="$t('sys_mgr.dict_input_type')" />
          </el-form-item>
          <el-form-item :label="$t('doc.this_dept_status')" prop="status">
            <el-radio-group v-model.trim="form.status">
              <el-radio
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
              >{{dictLanguage(dict)}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.user_remark')" prop="remark">
            <el-input v-model.trim="form.remark" type="textarea" :placeholder="$t('file_set.version_fill_content')"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listType, getType, delType, addType, updateType, refreshCache } from "@/api/system/dict/type";

export default {
  name: "Dict",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dictName: undefined,
        dictType: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dictName: [
          { required: true, message: this.$t('sys_mgr.dict_name_not_null'), trigger: "blur" }
        ],
        dictType: [
          { required: true, message: this.$t('sys_mgr.dict_type_not_null'), trigger: "blur" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listType(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.typeList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dictId: undefined,
        dictName: undefined,
        dictType: undefined,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('sys_mgr.dict_add_type');
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dictId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dictId = row.dictId || this.ids
      getType(dictId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('sys_mgr.dict_edit_type');
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dictId != undefined) {
            updateType(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addType(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dictIds = row.dictId || this.ids;
      this.$modal.confirm(this.$t('sys_mgr.dict_text') + dictIds + this.$t('file_set.signature_text1')).then(function() {
        return delType(dictIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/dict/type/export', {
        ...this.queryParams
      }, `type_${new Date().getTime()}.xlsx`)
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshCache().then(() => {
        this.$modal.msgSuccess(this.$t('sys_mgr.dict_clear_succ'));
      });
    }
  }
};
</script>
