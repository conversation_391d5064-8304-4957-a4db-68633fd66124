<template>
  <div>
    <!-- 台账查询 -->
    <el-card>
      <div>
        <el-form inline>
          <el-form-item :label="$t('sys_mgr.account_data_type')">
            <el-select :placeholder="$t('sys_mgr.account_data_type')" v-model="filter.dataType" style="width: 150px;">
              <!--              <el-option value="stdd" :label="$t('file_handle.change_sys')" />-->
              <!--              <el-option value="project" :label="$t('file_handle.change_project')" />-->
              <el-option
                v-for="(item,index) in dict.type.ledger_data_type"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>


            </el-select>
          </el-form-item>
          <el-form-item :label="$t('myItem.borrow_file_type')">
            <treeselect
              v-model="filter.docClassType"
              :options="docClassTypeTree"
              :normalizer="normalizer"
              :show-count="true"
              :searchable="false"
              :placeholder="$t('doc.this_dept_select_type')"
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item :label="$t('doc.this_dept_select_type')">
            <treeselect
              v-model="filter.docClass"
              :options="docClassTree"
              :normalizer="normalizer"
              :show-count="true"
              :searchable="false"
              :placeholder="$t('sys_mgr.account_select_file_classify')"
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.account_fuzzy_matching')">
            <el-input
              v-model="filter.searchValue"
              :placeholder="$t('sys_mgr.account_input_text')"
              @keyup.enter.native="handleQuery"
              class="input-with-select"
              style="width: 200px;"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('doc.this_dept_status')">
            <el-select v-model="filter.status" :placeholder="$t('doc.this_dept_pls_select')" style="width: 100px;" @change="handleQuery()">
              <!--              <el-option label="全部" value=""></el-option>-->
              <!--              <el-option label="草稿" value="0"></el-option>-->
              <!--              <el-option label="有效" value="1"></el-option>-->
              <!--              <el-option label="作废" value="2"></el-option>-->
              <el-option
                v-for="(item,index) in dict.type.ledger_status"
                :key="index"
                :label="dictLanguage(item)"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.account_whether_origin')">
            <el-select v-model="filter.initFile" :placeholder="$t('doc.this_dept_pls_select')" style="width: 100px;" @change="handleQuery()">
              <!--              <el-option label="全部" value=""></el-option>-->
              <!--              <el-option label="是" value="1"></el-option>-->
              <!--              <el-option label="否" value="0"></el-option>-->
              <el-option
                v-for="(item,index) in dict.type.ledger_whether_origin"
                :key="index"
                :label="dictLanguage(item)"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-row :gutter="32">
        <!-- 文件台账 -->
        <el-col :xs="24" :sm="24" :lg="12">
          <el-table
            ref="singleTable"
            :data="postList"
            v-loading="loading"
            highlight-current-row
            header-align="left"
            @row-click="rowClick"
          >
            <!--
            <el-table-column type="selection" align="center" />
            <el-table-column prop="id" label="ID" show-overflow-tooltip align="left" />
            -->
            <el-table-column prop="id" :label="$t('sys_mgr.account_key')" align="left"/>
            <el-table-column prop="dataType" :label="$t('sys_mgr.account_data_type')" align="left" >
              <template slot-scope="scope">
                <span v-if="scope.row.dataType == 'stdd'">{{ $t('file_handle.change_sys') }}</span>
                <span v-else-if="scope.row.dataType == 'project'">{{ $t('file_handle.change_project') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="docName"
              :label="$t('sys_mgr.account_ledger')"
              :show-overflow-tooltip="true"
              align="left"
            >
              <template slot-scope="scope">
                <span class="wenjcolor">{{ scope.row.docName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="docClassType" :label="$t('sys_mgr.account_whether_origin')" align="left" >
              <template slot-scope="scope">{{ handleFileType(scope.row.docClassType) }}</template>
            </el-table-column>
            <el-table-column prop="docClass" :label="$t('sys_mgr.account_classify')" align="left" >
              <template slot-scope="scope">{{ handleFileType(scope.row.docClass) }}</template>
            </el-table-column>
            <el-table-column prop="docCode" :label="$t('sys_mgr.account_num')" align="left"></el-table-column>
            <el-table-column prop="currentVersion" :label="$t('doc.this_dept_ver')" align="left" />
            <el-table-column prop="status" :label="$t('doc.this_dept_status')" align="left" >
              <template slot-scope="scope">
                <span v-show="scope.row.status == 0">{{ $t('doc.this_dept_draft') }}</span>
                <span v-show="scope.row.status == 1"><span style="color:green">{{ $t('doc.this_dept_validity') }}</span></span>
                <span v-show="scope.row.status == 2"><span style="color:red">{{ $t('doc.this_dept_cancel') }}</span></span>
              </template>
            </el-table-column>
            <el-table-column prop="initFile" :label="$t('sys_mgr.account_origin')" align="left">
              <template slot-scope="scope">
                <span v-show="scope.row.initFile == 0">{{ $t('doc.this_dept_no') }}</span>
                <span v-show="scope.row.initFile == 1">{{ $t('doc.this_dept_yes') }}</span>
              </template>
            </el-table-column>
            <!--
            <el-table-column
              v-show="false"
              label="操作"
              align="left"
              class-name="small-padding fixed-width"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDetails(scope.row, '详情')"
                >详情</el-button>
              </template>
            </el-table-column>-->
          </el-table>
          <pagination
            v-show="pagination.total > 0"
            :total="pagination.total"
            :page.sync="pagination.pageNum"
            :limit.sync="pagination.pageSize"
            :page-sizes="[5, 10, 30, 40, 50, 100]"
            @pagination="getList"
          />
        </el-col>
        <!-- 台账对应的文件版本列表 -->
        <el-col :xs="24" :sm="24" :lg="12">
          <el-table
            ref="singleTable"
            :data="versionList"
            v-loading="loading"
            highlight-current-row
            header-align="left"
            @current-change="handleCurrentChange"
          >
            <el-table-column prop="id" :label="$t('sys_mgr.account_key')" align="left" />
            <el-table-column
              prop="docName"
              :label="$t('sys_mgr.account_ver_name')"
              :show-overflow-tooltip="true"
              align="left"
            >
              <template slot-scope="scope">
                <span class="wenjcolor">{{ scope.row.docName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="versionValue" :label="$t('doc.this_dept_ver')" align="left" />
            <el-table-column prop="startDate" :label="$t('doc.this_dept_effective_date')" align="left" >
              <template slot-scope="scope">
                <span class="wenjcolor">{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="releaseTime" :label="$t('doc.this_dept_release_date')" align="left" >
              <template slot-scope="scope">
                <span class="wenjcolor">{{ parseTime(scope.row.releaseTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="userName" :label="$t('myItem.borrow_preparer')" align="left" />
            <el-table-column prop="distributeType" :label="$t('sys_mgr.account_distri_type')" align="left" />
            <el-table-column prop="status" :label="$t('doc.this_dept_status')" align="left" >
              <template slot-scope="scope">
                <span v-show="scope.row.status == 0">{{ $t('doc.this_dept_draft') }}</span>
                <span v-show="scope.row.status == 1">{{ $t('doc.this_dept_validity') }}</span>
                <span v-show="scope.row.status == 2">{{ $t('doc.this_dept_Invalid') }}</span>
              </template>
            </el-table-column>
            <!--
            <el-table-column
              v-show="false"
              label="操作"
              align="left"
              class-name="small-padding fixed-width"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDetails(scope.row, '详情')"
                >详情</el-button>
              </template>
            </el-table-column>-->
          </el-table>
        </el-col>
      </el-row>
    </el-card>
    <Detail
      v-if="!!detailId"
      :id="detailId"
      :title="detailTitle"
      @close="closeDetail"
    />
  </div>
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import Treeselect from "@riophae/vue-treeselect";
import Detail from './Detail.vue'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { accountList } from '@/api/system/standard'
import { listVersion } from '@/api/file_processing/version'
import { settingDocClassList } from "@/api/file_settings/type_settings";

export default {
  dicts: ["ledger_status","ledger_whether_origin","ledger_data_type"],
  name: 'SystemDocumentPage',
  components: {
    Treeselect,
    Detail,
  },
  mixins: [mixin],
  data: () => ({
    /**
     * 文件台账列表
     */
    postList: [],

    /**
     * 文件版本列表
     */
    versionList: [],

    /**
     * 分页查询条件
     */
    pagination: {
      pageNum: 1,
      pageSize: 10,
      total: 0,
    },
    /**
     * 筛选查询条件
     */
    filter: {
      // 默认为体系文件范围
      dataType: 'stdd',
      docClass: null,
      docClassType: null,
      searchValue: null,
      status: null,
      initFile: null
    },
    /**
     * 加载状态
     */
    loading: false,
    /**
     * 文件类型（树形结构）
     */
    docClassTypeTree: [],
    /**
     * 文件分类（树形结构）
     */
    docClassTree: [],
    /**
     * 详情框标题
     */
    detailTitle: '',
    /**
     * 当前文件ID
     */
    detailId: undefined,
  }),
  computed: {
    queryParams() {
      return {
        ...this.filter,
        ...this.pagination,
      };
    },
  },
  watch: {
    "filter.dataType"(val) {
      // 数据类型选择，触发文件类型下拉框填充和页面查询
      this.getclassTypeOptions()
      this.handleQuery()
    },
    "filter.docClassType"(val) {
      // 文件类型选择，触发文件分类下拉框填充和页面查询
      this.getclassLevelOptions()
      this.handleQuery()
    },
    "filter.docClass"(val) {
      // 文件分类选择，触发页面查询
      this.handleQuery()
    },
  },
  mounted() {
    // 加载文件类型树结构
    this.getclassTypeOptions()
    // 加载文件分类树结构
    this.getclassLevelOptions()
    // 执行查询
    this.handleQuery()
  },
  methods: {
    treeSelectChange() {
      // 执行查询
      this.handleQuery()
    },
    /**
     * 获取文档列表
     */
    getList() {
      this.loading = true;
      accountList(this.queryParams)
        .then(({ rows, total }) => {
          this.postList = rows;
          this.pagination.total = total;
          if(rows.length > 0){
            // 默认查询第一个文件台账的文件版本清单
            this.getVersionList(rows[0].id)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 获取文件版本列表
     */
    getVersionList(standardId) {
      let _this = this
      _this.loading = true
      // 默认为空
      _this.versionList = []
      listVersion({"standardId":standardId})
        .then((res) => {
          _this.versionList = res
        })
        .finally(() => {
          _this.loading = false
        })
    },
    // 单击行记录
    rowClick(row,column,event) {
      this.getVersionList(row.id)
    },
    /**
     * 查询按钮事件
     */
    handleQuery() {
      this.pagination.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.filter = {dataType:'stdd'}
      this.handleQuery();
    },
    /**
     * 查询文件类型
     */
    getclassTypeOptions() {
      let self = this
      self.loading = true
      self.docClassTypeTree = [];
      settingDocClassList({  classStatus: "1", parentClassId: "0",dataType: self.filter.dataType }).then(
        (response) => {
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          self.docClassTypeTree = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
          self.loading = false
        }
      );
    },
    /**
     * 查询文件分类
     */
    getclassLevelOptions() {
      let self = this
      self.loading = true
      self.docClassTree = [];
      settingDocClassList({  classStatus: "1", parentClassId: this.filter.docClassType,dataType: self.filter.dataType }).then(
        (response) => {
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          self.docClassTree = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
          self.loading = false
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    /**
     * 打开详情窗
     */
    handleDetails({ id }, title) {
      this.detailTitle = title
      this.detailId = id
    },
    /**
     * 关闭详情窗
     */
    closeDetail(refresh = false) {
      this.detailId = undefined
      if (refresh) {
        this.handleDetails()
      }
    },
    // 检验行记录必要信息
    checkForm(row) {
      if(row.docClass.trim().length = 0) {
        this.$modal.alert('类型不能为空')
        return false
      } else if(row.docId.trim().length = 0) {
        this.$modal.alert('编号不能为空')
        return false
      } else if(row.versionValue.trim().length = 0) {
        this.$modal.alert('版本不能为空')
        return false
      } else if(row.startDate.trim().length = 0) {
        this.$modal.alert('生效日期不能为空')
        return false
      } else if(row.releaseTime.trim().length = 0) {
        this.$modal.alert('发布日期不能为空')
        return false
      }
      return true
    }
  },

};
</script>
