<template>
  <el-dialog
    :title="title"
    :visible="true"
    append-to-body
    width="80%"
    :close-on-click-modal="false"
    @close="$emit('close')"
  >
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-position="right"
        label-width="150px"
        :disabled="title == $t('doc.this_dept_detail')"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_type'))" prop="docClass">
              <treeselect
                v-model.trim="formData.docClass"
                :options="classLevelOptions"
                :normalizer="normalizer"
                :searchable="false"
                :show-count="true"
                :disabled="title == $t('doc.this_dept_detail')"
                :placeholder="$t('doc.this_dept_select_type')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_change_type'))" prop="changeType">
              <el-select v-model="formData.changeType" :placeholder="$t('doc.this_dept_pls_select')">
                <el-option
                  v-for="item in changeTypeoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_name'))" prop="docName">
              <div>
                <el-input
                  v-model.trim="formData.docName"
                  :placeholder="$t('doc.this_dept_insert_name')"
                  clearable
                >
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_change_reason'))" prop="changeReason">
              <el-input
                v-model.trim="formData.changeReason"
                :placeholder="$t('doc.this_dept_insert_change_reason')"
                :style="{ width: '100%' }"
                maxlength="500"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('sys_mgr.doc_current_file'))">
              <div style="display: flex; justify-content: space-between">
                <span
                  style="color: #385bb4; margin-left: 10px; cursor: pointer"
                  :key="i"
                  v-if="formData.fileName != ''"
                  @click="handlePreview(formData.fileId)"
                >{{ formData.fileName }}
                </span>
                <!--
                <fileUpload
                  v-if="title == '修改'"
                  v-model.trim="docNamefileList"
                  limit="1"
                  :fileType="['docx', 'doc', 'xls', 'xlsx', 'pdf']"
                  :isfileType="true"
                  :fileSize="100"
                  :showTransition="false"
                  title="替换文件"
                />-->
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_id'))" prop="docId">
              <el-input
                v-model.trim="formData.docId"
                :placeholder="$t('sys_mgr.doc_input_num')"
                maxlength="20"
                show-word-limit
                clearable
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_ver'))" prop="currentVersion">
              <el-input v-model.trim="formData.currentVersion" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('myItem.borrow_preparation_dept'))" prop="deptName">
              <span>{{ formData.deptName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_effective_date'))" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                :placeholder="$t('doc.this_dept_select_date')"
                :picker-options="pickerOptionsstartDate"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_staffing'))" prop="nickName">
              <div>
                <el-input
                  style="width: 80%; margin-right: 10px"
                  readonly
                  v-model.trim="formData.nickName"
                  :placeholder="$t('sys_mgr.doc_select_staffing')"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="handleSelect"
                  ></el-button>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_file_lifespan'))">
              <el-date-picker
                v-model="formData.endDate"
                align="right"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                :placeholder="$t('doc.this_dept_select_date')"
                :picker-options="pickerOptions0"
              >
              </el-date-picker>
              <span style="margin: 0px 5px">{{ $t('doc.this_dept_whether_permanent') }}</span>
              <el-radio-group v-model="formData.forever">
                <el-radio :label="'1'">{{ $t('doc.this_dept_yes') }}</el-radio>
                <el-radio :label="'0'">{{ $t('doc.this_dept_no') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_preparation_time'))" prop="applyTime">
              <el-date-picker
                v-model="formData.applyTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                :placeholder="$t('doc.this_dept_select_date')"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_status'))" prop="status">
              <el-select v-model="formData.status" :placeholder="$t('doc.this_dept_pls_select')">
                <el-option
                  v-for="item in statusoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <span v-for="(itme, i) in appendixsList" :key="i">
                {{ itme.docName }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="ID:" prop="id">
              {{ formData.id }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_release_date'))" prop="startDate">
              <el-date-picker
                v-model="formData.releaseTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                :placeholder="$t('doc.this_dept_select_date')"
                :picker-options="pickerOptionsstartDate"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_change_ver_annex'))">
              <!-- </div> -->
              <div v-if="title == $t('doc.this_dept_detail')">
                <div
                  v-for="(item, i) in formData.preAppendixes"
                  class="link-box bzlink-box"
                  :key="i"
                >
                  <span
                    style="color: #385bb4; margin-left: 10px; cursor: pointer"
                    :key="i"
                    @click="handlePreview(item.fileId)"
                  >{{ item.fileName }}
                  </span>
                  <span
                    :key="i"
                    style="color: #385bb4; cursor: pointer; margin-left: 10px"
                    @click="
                      handelefileLocalDownload(item.fileId, item.fileName)
                    "
                  >{{ $t('doc.this_dept_download') }}
                  </span>
                </div>
              </div>
              <fileUpload
                v-else
                v-model.trim="appendixesfileList"
                :isShowTip="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24"> </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="getLabelWithColon($t('doc.this_dept_changes'))" prop="content">
              <el-input
                v-model.trim="formData.content"
                type="textarea"
                :placeholder="$t('doc.this_dept_insert_change_content')"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :style="{ width: '100%' }"
                maxlength="500"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div slot="footer" class="dialog-footer" v-if="title == $t('doc.this_dept_edit')">
      <el-button @click="$emit('close')">{{ $t('file_set.type_cancel') }}</el-button>
      <el-button
        type="primary"
        v-loading="updateFileLoading"
        @click="submitForm()"
      >{{ $t('file_set.type_confim') }}</el-button
      >
    </div>
    <as-pre-view
      :visible="!!viewId"
      :id="viewId"
      ref="viewRef"
      @close="viewId = undefined"
    />
  </el-dialog>
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import { processstandard } from '@/api/system/standard'

export default {
  mixins: [mixin],
  props: {
    id: {
      type: String,
    },
    title: {
      type: String,
    },
  },
  data: () => ({
    formData: {},
    viewId: undefined,
  }),
  methods: {
    getDetail() {
      // 获取当前登录用户
      const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
      processstandard(this.id)
        .then(({ data }) => data)
        .then(({ changeFactor, nickName, userName, deptId, deptName, applyTime, preAppendixes, ...rest }) => {
          this.formData = {
            ...rest,
            nickName: nickName || userInfo.nickName,
            userName: userName || userInfo.userName,
            deptId: deptId || userInfo.deptId,
            deptName: deptName || userInfo.dept.deptName,
            applyTime: applyTime || new Date(),
          }
          this.changeFactor = changeFactor ? changeFactor.split(',') : []
          this.appendixesfileList = preAppendixes.map(({ fileName, fileId }) => ({ name: fileName, url: fileId }))
        })
    },
    submitForm() {
      this.$emit('close', true)
    },
  },
  mounted() {
    this.getDetail()
  },
};
</script>
