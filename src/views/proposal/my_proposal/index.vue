<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t(`doc.this_dept_proposal_summary`) }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-date-picker
                v-model="value2"
                type="datetimerange"
                :picker-options="pickerOptions"
                :range-separator="$t(`doc.this_dept_to`)"
                :start-placeholder="$t(`doc.this_dept_start_date`)"
                :end-placeholder="$t(`doc.this_dept_end_date`)"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                align="right"
                size="small"
                style="float: left; height: 34px; margin-right: 5px"
              >
              </el-date-picker>
              <el-input
                v-model.trim="queryParams.docName"
                :placeholder="$t(`doc.this_dept_name_select`)"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >{{ $t(`myItem.handle_reset`) }}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button
                type="primary"
                @click="handleAdd()"
                v-hasPermi="['proposal:myProposal:advice']"
                >{{ $t(`doc.this_dept_put_summary`) }}</el-button
              >
              <el-button plain @click="handleExport">{{ $t(`doc.exterior_dept_export`) }}</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item :label="$t(`myItem.borrow_file_type`)" >
                <treeselect
                  v-model.trim="queryParams.docClass"
                  :options="classLevelOptions"
                  :normalizer="normalizerFile"
                  :searchable="false"
                  :show-count="true"
                  :placeholder="$t(`doc.this_dept_select_type`)"
                />
              </el-form-item>
              <el-form-item :label="$t(`myItem.borrow_preparation_dept`)" style="width: 400px"  :label-width="columnLangSizeFlag ? '188px' : '68px'" >
                <treeselect
                  v-model.trim="queryParams.docDeptId"
                  :options="deptOptions"
                  :normalizer="normalizer"
                  :placeholder="$t(`doc.this_dept_select_dept`)"
                  style="width: 200px"
                  :searchable="false"
                  size="mini"
                  class="pop-width400"
                />
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_put_dept`)" :label-width="columnLangSizeFlag ? '168px' : '68px'">
                <treeselect
                  v-model.trim="queryParams.deptId"
                  :options="deptOptions"
                  :normalizer="normalizer"
                  :placeholder="$t(`doc.this_dept_select_dept`)"
                  style="width: 200px"
                  :searchable="false"
                  size="mini"
                  class="pop-width400"
                />
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_put_proposal`)">
                <el-input
                  :placeholder="$t(`doc.this_dept_put_proposal`)"
                  v-model.trim="queryParams.createName"
                ></el-input>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
              <el-button @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              <el-button @click="boxClass = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @selection-change="handleSelectionChange"
          header-align="left"
        >
          <el-table-column
            :label="$t(`doc.this_dept_file_type`)"
            align="left"
            prop="docTypeName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_file_name`)"
            align="left"
            width="300"
            prop="docName"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <a @click="handleDetails(scope.row)" class="wenjcolor">{{
                scope.row.docName
              }}</a>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_comments`)"
            align="left"
            width="300"
            prop="summary"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`myItem.borrow_file_id`)"
            align="left"
            prop="docId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`myItem.borrow_file_ver`)"
            align="left"
            prop="versionValue"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`myItem.borrow_preparation_dept`)"
            align="left"
            prop="docDeptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_put_dept`)"
            align="left"
            prop="deptName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_put_proposal`)"
            align="left"
            prop="createName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_put_time`)"
            align="left"
            prop="createTime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
              }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
            label="操作"
            align="left"
            width="105"
            fixed="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
                >详情</el-button
              >
            </template>
          </el-table-column> -->
          <el-table-column :label="$t(`myItem.msg_operation`)" align="left" width="105">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
                >{{ $t(`doc.this_dept_detail`) }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改-->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="60%"
        append-to-body
        :close-on-click-modal="false"
      >
        <add @close="handleCloseAdd"></add>
      </el-dialog>

      <!-- 详情-->
      <rz-drawer :title="title" @closeDrawer="changeDrawer" :drawer="drawer">
        <rzdetails :detailInfo="detailInfo"></rzdetails>
      </rz-drawer>
    </div>
  </div>
</template>

<script>
import { listFileAdvise } from "@/api/file_processing/fileAdvise";
import add from "./add";
import rzdetails from "./rzdetails.vue";
import rzDrawer from "../../components/rz-drawer/index2.vue";
import { parseTime } from "../../../utils/ruoyi";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from "@/api/system/dept";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  name: "Post",
  components: {
    add,
    Treeselect,
    rzdetails,
    rzDrawer,
  },
  mixins: [mixin],
  data() {
    return {
      deptOptions: [],
      fileTypeList: [],
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchType: "1",
        docTypeCode: "",
        docClass: null,
        docName: null,
        deptId: null,
        docDeptId: null,
        createName: null,
        params: {
          startTime: "",
          endTime: "",
        },
      },
      taskData: [], // 任务数据
      drawer: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      classLevelOptions: [],
      detailInfo: {},
      value2: "",
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
    listDept({ status: 0 }).then((response) => {
      this.deptOptions = this.handleTree(response.data, "deptId");
    });
    this.getSettingDocClassTreeseList();
  },
  watch: {
    value2(val) {
      console.log(val);
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
  },
  methods: {
    getSettingDocClassTreeseList() {
      settingDocClassList({  classStatus: "1" }).then(
        (response) => {
          this.classLevelOptions = [];
          //console.log(response.rows);
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          //console.log(response.rows);
          this.classLevelOptions = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
        }
      );
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      listFileAdvise(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleCloseAdd() {
      this.open = false;
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.docTypeCode = "";
      this.queryParams.docClass = null;
      this.queryParams.docName = null;
      this.queryParams.deptId = null;
      this.queryParams.docDeptId = null;
      this.queryParams.createName = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.title = this.$t(`doc.this_dept_new_add`);
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      this.detailInfo = row;
      this.title = this.$t(`doc.this_dept_detail`);
      this.drawer = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm( this.$t(`doc.this_dept_is_remove_code`) + '"' + postIds + this.$t(`file_set.signature_text1`))
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t(`file_set.signature_delete_succ`));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "process/fileAdvise/export",
        {
          ...this.queryParams,
        },
        `文件建议_${parseTime(new Date(), "{y}{m}{d}")}.xlsx`
      );
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    state(p) {
      if (p == 1) {
        this.varChangeColor1 = true;
        this.varChangeColor2 = false;
        this.getList();
      }
      if (p == 2) {
        this.varChangeColor1 = false;
        this.varChangeColor2 = true;
        this.postList = [];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../../public/css/poctstyle.css";
</style>
