<template>
  <div class="news-card">
      <div class="card-head">
      <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
    </div>
      <el-card class="gray-card table-card no-padding">
        <!---->
          <el-form
            ref="elForm"
            size="medium"
            label-position="right"
            label-width="150px"
          >

            <div class="el-row">
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_file_name`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.docName }}
                  </div>
                </div>
              </div>
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_file_type`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.docTypeName }}
                  </div>
                </div>
              </div>
            </div>
            <div class="el-row">
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_file_code`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.docId }}
                  </div>
                </div>
              </div>
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_file_versions2`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.versionValue }}
                  </div>
                </div>
              </div>
            </div>
            <div class="el-row">
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`myItem.borrow_preparation_dept`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.docDeptName }}
                  </div>
                </div>
              </div>
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_put_proposal`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.createName }}
                  </div>
                </div>
              </div>
            </div>
            <div class="el-row">
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_put_time`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.createTime ?detailInfo.createTime.substring(0,16):'' }}
                  </div>
                </div>
              </div>
              <div class="el-col el-col-24">
                <div class="el-form-item">
                  <label class="el-form-item__label" style="width: 150px"
                  >{{ $t(`doc.this_dept_summary_remark`) }}：</label
                  >
                  <div class="el-form-item__content" style="margin-left: 150px">
                    {{ detailInfo.summary }}
                  </div>
                </div>
              </div>
            </div>

          </el-form>
      </el-card>
  </div>
</template>
<script>
export default {
  name: "Post",
  props: ["detailInfo"],
  data() {
    return {};
  },
  created() {
    this.$nextTick(() => {
      console.log('detailInfo====>', this.detailInfo)
    })
  },
  methods: {}
};
</script>

<style lang="scss">
@import '../../../../public/css/poctstyle.css'
</style>
