<template>
  <div class="sso-page" v-loading="loading"></div>
</template>

<script>
// 20230601
// 爱数7集成体系文件系统入口
import { ssoCheck, ssoLogin } from '@/api/login'
import {
  setToken,
  removeToken,
  getToken,
  setAsToken,
  getLanguageData,
  setLanguageData,
  setLanguage,
  getLanguage
} from '@/utils/auth'
import cache from '@/plugins/cache'
import { nextTick } from '@vue/runtime-dom'
import { get3Token } from '@/api/system/user'

export default {
  name: 'ssoCheck',
  data() {
    return {}
  },
  mounted() {
    // 开始执行SSO检查
    this.ssoCheck()
  },
  methods: {
    ssoCheck() {
      console.log(getToken())
      let self = this
      // 用于跳转的token
      let token3 = self.$route.query.token3
      // 业务地址
      let fullpath = self.$route.query.fullpath||"/"
      // 租户id
      let language = self.$route.query.language
      // 跳转地址
      let href = self.$route.query.href;
      // 先存储token 和 租户，然后再跳转到业务页面
      console.log('token3:'+ token3)
      if (token3) {
        console.log(`根据token3：${token3}去登录，并跳转到业务页面`)
        // setLanguage(language)
        // this.$i18n.locale = language
        // 删除令牌和清理会话信息
        removeToken()
        ssoLogin({username:token3}).then(res=>{
          let token = res.data.token;
          setToken(token)
          nextTick(()=>{
            this.$router.push({ path: fullpath||'/' })
          })
        })
      } else if(fullpath) {
        get3Token().then(res=>{
          let token3 = res.msg
          language = getLanguage()
          this.$nextTick(()=>{
            // 删除令牌和清理会话信息
            removeToken()
            console.log("href 重定向>>>>",href+`/ssoredirect?token3=${token3}&fullpath=${encodeURIComponent(fullpath)}&language=${language}`)
            window.location.href = href+`/ssoredirect?token3=${token3}&language=${language}&fullpath=${encodeURIComponent(fullpath)}`
          }
          )
        })
      }else{
        this.$router.push({ path: '/sso_error?data=非法请求' })
      }
    }
  }}

</script>
