<template>
  <div class="number_manageadd">
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="120px"
    >
      <el-form-item :label="getLabelWithColon($t('file_set.version_rule_name'))" prop="ruleName">
        <el-input
          v-model.trim="formData.ruleName"
          :placeholder="getLabelWithColon($t('file_set.version_fill_rule_name'))"
          clearable
          :style="{ width: '100%' }"
        >
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="编号类型:" prop="field114">
        <el-select
          v-model.trim="formData.field114"
          placeholder="请选择编号类型"
          clearable
          :style="{ width: '100%' }"
        >
          <el-option
            v-for="(item, index) in field114Options"
            :key="index"
            :label="item.label"
            :value="item.value"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item :label="getLabelWithColon($t('file_set.number_reset_type'))" prop="resetCycle">
        <el-select
          v-model.trim="formData.resetCycle"
          :placeholder="$t('file_set.number_select_reset_type')"
          multiple
          clearable
          :style="{ width: '100%' }"
        >
          <el-option
            v-for="(arr, index) in resetCycleList"
            :key="index"
            :label="arr.label"
            :value="arr.value"
            :disabled="arr.disabled"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t('file_set.number_start_val'))" prop="numberInitValue">
        <el-input-number
          v-model.trim="formData.numberInitValue"
          :min="1"
          :style="{ width: '100%' }"
          :label="$t('file_set.number_select_start_val')"
        ></el-input-number>
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t('file_set.number_serial_num_digits'))" prop="numberDigit">
        <el-input-number
          v-model.trim="formData.numberDigit"
          :min="1"
          :label="$t('file_set.number_select_start_val')"
        ></el-input-number>
      </el-form-item>

      <el-form-item :label="getLabelWithColon($t('file_set.version_num_rule'))" prop="">
        <div>
          <span style="margin: 0 40px">{{ $t('file_handle.recovery_num') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.version_rule_type') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.number_rule_val') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.number_delimiter') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.number_order') }}</span>
        </div>
        <draggable
          :options="{
            group: 'people',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'chosenClass',
            scroll: true,
            scrollSensitivity: 200,
          }"
          v-model.trim="form.dynamicItem"
          @change="change"
          @start="start"
          @end="end"
          :move="move"
          style="
            display: inline-block;
            width: 800px;
            height: 150px;
            background: #eee;
            overflow: auto;
          "
        >
          <div
            v-for="(val, key) in form.dynamicItem"
            :key="val.orderBy"
            class="fromdynamicItem"
          >
            <span style="margin: 0 40px">{{ val.orderBy }}</span>
            <el-form-item>
              <el-select
                v-model.trim="val.ruleType"
                :placeholder="$t('file_set.number_select_rule_type')"
                clearable
                :style="{ width: '100%' }"
                @change="handleChange(key)"
              >
                <el-option
                  v-for="(rule_code_value, i) in dict.type.rule_code_value"
                  :key="i"
                  :label="rule_code_value.label"
                  :value="rule_code_value.value"
                  :disabled="rule_code_value.disabled"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-show="changetype[key] == 'DATE'">
              <el-select
                v-model.trim="val.ruleValue"
                :placeholder="$t('file_set.number_select_rule_val')"
                clearable
                :style="{ width: '100%' }"
              >
                <el-option
                  v-for="(changeval, changekey) in changeOptions[key]"
                  :key="changekey"
                  :label="changeval.label"
                  :value="changeval.value"
                  :disabled="changeval.disabled"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              style="width: 180.33px"
              v-show="changetype[key] == 'FORM'||changetype[key] == 'DICT'"
            >
              <el-input
                v-model.trim="val.ruleValue"
                :placeholder="$t('file_set.version_fill_content')"
              ></el-input>
            </el-form-item>

            <el-form-item
              style="width: 180.33px"
              v-show="changetype[key] == 'STR'"
            >
              <el-input
                v-model.trim="val.ruleValue"
                :placeholder="$t('file_set.version_fill_content')"
              ></el-input>
            </el-form-item>

            <el-form-item
              style="width: 180.33px"
              v-show="changetype[key] == 'SNUM'"
            >
              <el-input
                v-model.trim="val.ruleValue"
                placeholder=""
                :disabled="true"
              ></el-input>
            </el-form-item>

            <el-form-item>
              <el-select
                v-model.trim="val.slicerValue"
                :placeholder="$t('file_set.number_select_delimiter')"
                clearable
                :style="{ width: '100%' }"
              >
                <el-option
                  v-for="(separatorval, separatorkey) in dict.type
                    .separator_value"
                  :key="separatorkey"
                  :label="separatorval.label"
                  :value="separatorval.value"
                  :disabled="separatorval.disabled"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <i class="el-icon-rank" style="margin: 0 5px"></i>
              <!-- <i
              class="el-icon-circle-plus-outline"
              @click="deleteItem2(val, key)"
              style="margin: 0 5px"
            ></i> -->
              <i
                class="el-icon-delete"
                @click="deleteItem(val, key)"
                style="margin: 0 5px"
              ></i>
              <i class="el-icon-plus" @click="addItem"></i>
            </el-form-item>
          </div>
        </draggable>
        <div><el-button @click="addItem" type="primary">{{ $t('file_set.version_add') }}</el-button></div>
      </el-form-item>

      <el-form-item size="large">
        <el-button type="primary" @click="submitForm">{{ $t('doc.this_dept_annex') }}</el-button>
        <el-button @click="handleClose">{{ $t('doc.this_dept_close') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import {
  addCodeRule,
  updateCodeRule,
  getCodeRule,
} from "@/api/setting/codeRule";
//https://blog.csdn.net/weixin_44770377/article/details/103471776
import draggable from "vuedraggable";
export default {
  dicts: ["reset_cycle", "rule_code_value", "rule_code", "separator_value"],
  components: { draggable },
  props: ["id"],
  data() {
    return {
      formData: {
        field101: [1, 2],
        ruleName: undefined,
        resetCycle: undefined,
        numberInitValue: undefined,
        numberDigit: 1,
        field106: undefined,
        ruleDetailList: [],
      },
      form: {
        ruleType: "",
        ruleValue: "",
        dynamicItem: [],
      },
      formRules: {
        name: [{ required: true, message: this.$t('file_set.number_fill_name'), trigger: "blur" }],
        phone: [
          { required: true, message: this.$t('file_set.number_fill_phone_num'), trigger: "blur" },
          {
            pattern: /^1[34578]\d{9}$/,
            message: this.$t('file_set.number_support'),
          },
        ],
      },
      rules: {
        resetCycle: [
          {
            required: true,
            message: this.$t('file_set.number_least_one'),
            trigger: "change",
          },
        ],
        ruleName: [
          {
            required: true,
            message: this.$t('file_set.version_fill_rule_name')+":",
            trigger: "blur",
          },
          {
            min: 1,
            max: 200,
            message: this.$t('file_set.number_long_rule'),
            trigger: "blur",
          },
        ],
        numberInitValue: [
          {
            required: true,
            message: this.$t('file_set.number_select_start_val'),
            trigger: "blur",
          },
        ],
      },
      field104Action: "https://jsonplaceholder.typicode.com/posts/",
      field104fileList: [],
      field101Options: [],
      changeOptions: [],
      changetype: [],
      field105Options: [
        {
          label: this.$t('file_set.number_enable'),
          value: 1,
        },
        {
          label: this.$t('file_set.number_banned'),
          value: 2,
        },
      ],
      field106Options: [
        {
          label: this.$t('file_set.number_option_one'),
          value: 1,
        },
        {
          label: this.$t('file_set.number_option_two'),
          value: 2,
        },
      ],
      field114Options: [
        {
          label: this.$t('file_set.number_null'),
          value: 1,
        },
      ],
      field101Props: {
        multiple: false,
      },
    };
  },
  computed: {
    resetCycleList(){
      let _this = this
      let list = JSON.parse(JSON.stringify(_this.dict.type.reset_cycle))
      let itemList = _this.form.dynamicItem.filter(item=>item.ruleType==='FORM')
      if (itemList) {
        itemList.forEach(item=>{
          if (!!item.ruleValue) {
            list.push({
              label:'('+this.$t('file_set.number_form_')+')'+item.ruleValue,
              value:item.ruleValue
            })
          }
        })
      }
      if (list.findIndex(item=>item.value==='docClass')===-1) {
        list.push({
          label:'('+this.$t('file_set.number_form_')+')'+'docClass',
          value:'docClass'
        })
      }
      return list;
    }
  },
  watch: {},
  created() {},
  mounted() {
    if (this.id) {
      getCodeRule(this.id).then((response) => {
        // console.log(response.data);
        this.formData = response.data;
        this.form.dynamicItem = response.data.ruleDetailList;
        this.formData.resetCycle =  response.data.resetCycle.split(",")
        response.data.ruleDetailList.forEach((element) => {
          if (element.ruleType == "STR") {
            this.changetype.push("STR");
          }
          if (element.ruleType == "DATE") {
            this.changetype.push("DATE");
          }
          if (element.ruleType == "SNUM") {
            this.changetype.push("SNUM");
          }
          if (element.ruleType == "FORM") {
            this.changetype.push("FORM");
          }
          if (element.ruleType == "DICT") {
            this.changetype.push("DICT");
          }
        });
        console.log("ces", this.changetype);
      });
    }
  },
  methods: {
    handleClose() {
      this.$emit("chinldClose", false);
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          this.formData.ruleDetailList = this.form.dynamicItem;
          let formData = JSON.parse(JSON.stringify(this.formData))
          formData.resetCycle = formData.resetCycle.join(',')
          if (this.formData.id != undefined) {
            updateCodeRule(formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.$emit("chinldClose");
            });
          } else {
            addCodeRule(formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.$emit("chinldClose");
            });
          }
        }
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    getField101Options() {
      // TODO 发起请求获取数据
      this.field101Options;
    },
    field104BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2;
      if (!isRightSize) {
        this.$message.error(this.$t('file_set.number_file_over_2mb'));
      }
      return isRightSize;
    },

    addItem() {
      console.log(this.form.dynamicItem);
      this.changetype.push("SNUM");
      if (this.form.dynamicItem == "") {
        console.log(this.changetype);
        this.form.dynamicItem.push({
          orderBy: 1,
          ruleType: "",
          ruleValue: "",
          slicerValue: "",
        });
      } else {
        var maximum = 1;
        this.form.dynamicItem.forEach((element) => {
          if (element.orderBy > maximum) {
            maximum = element.orderBy;
          }
        });
        this.form.dynamicItem.push({
          orderBy: maximum + 1,
          ruleType: "",
          ruleValue: "",
          slicerValue: "",
        });
      }

      // console.log(this.form.dynamicItem.slice(-1)[0].orderBy);
    },
    deleteItem(item, index) {
      this.form.dynamicItem.splice(index, 1);
    },
    handleChange(e) {
      // console.log(e);
      // console.log(this.form.dynamicItem[e].ruleType);
      if (this.form.dynamicItem[e].ruleType == "DATE") {
        this.changeOptions[e] = this.dict.type.rule_code;
        this.changetype[e] = "DATE";
        this.form.dynamicItem[e].ruleValue = "";
      }
      if (this.form.dynamicItem[e].ruleType == "STR") {
        this.changeOptions[e] = [];
        this.changetype[e] = "STR";
        this.form.dynamicItem[e].ruleValue = "";
      }
      if (this.form.dynamicItem[e].ruleType == "FORM") {
        this.changeOptions[e] = [];
        this.changetype[e] = "FORM";
        this.form.dynamicItem[e].ruleValue = "";
      }
      if (this.form.dynamicItem[e].ruleType == "SNUM") {
        this.changeOptions[e] = [];
        this.changetype[e] = "SNUM";
        this.form.dynamicItem[e].ruleValue = "";
      }
      if (this.form.dynamicItem[e].ruleType == "DICT") {
        this.changeOptions[e] = [];
        this.changetype[e] = "DICT";
        this.form.dynamicItem[e].ruleValue = "";
      }
      // console.log("dynamicItem", this.form.dynamicItem);
      // console.log("this.changeOptions", this.form.dynamicItem);
      // console.log("this.changetype", this.changetype);
    },

    //evt里面有两个值，一个evt.added 和evt.removed  可以分别知道移动元素的ID和删除元素的ID
    change: function (evt) {
      this.form.dynamicItem.forEach((element, index) => {
        this.form.dynamicItem[index].orderBy = index + 1;
      });
      //console.log(this.form.dynamicItem);
    },
    //start ,end ,add,update, sort, remove 得到的都差不多
    start: function (evt) {
      //console.log(evt);
    },
    end: function (evt) {
      //console.log(evt);
      evt.item; //可以知道拖动的本身
      evt.to; // 可以知道拖动的目标列表
      evt.from; // 可以知道之前的列表
      evt.oldIndex; // 可以知道拖动前的位置
      evt.newIndex; // 可以知道拖动后的位置
    },
    move: function (evt, originalEvent) {
      // console.log(originalEvent); //鼠标位置
    },
  },
};
</script>
<style lang="scss" scoped>
.number_manageadd {
  .el-upload__tip {
    line-height: 1.2;
  }
  .fromdynamicItem {
    display: flex;
    margin: 5px 0px;
  }
}
</style>
