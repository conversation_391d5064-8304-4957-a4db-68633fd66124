<template>
	<div class="right-wrap">
	    <!-- 路线展示区 -->
	    <div class="all-item-wrap">
	        <div class="item-wrap" 
	            v-for="(item, index) in lineItems" 
	            :key="`${item.name}-${index}`"
	            @click="choosedItem(index)"
	            draggable="true" 
	            ondragstart="event.dataTransfer.setData('text/plain',null)"
	            :class="{'active-item': currentIndex === index}"
	        >
	            <div class="item-info">
	                {{item.name}}-{{item.interval}}
	            </div>
	            <Button class="item-opera" type="primary" size="small">跳转</Button>
	        </div>
	    </div>
        <!-- 添加路线区 -->
        <div class="add-item-wrap" v-if="isAddItem">
            <Input class="name" v-model.trim="item.name" placeholder="名称" />
            <Input class="interval" v-model.trim="item.interval" placeholder="停留时间（s）" />
            <Icon 
                class="confirm-add" 
                :size="22" 
                color="#61beff" 
                type="md-checkmark" 
                @click="confirmAddItem()"
            />
        </div>
	   	<!-- 添加按钮与删除按钮区 -->
		<div class="option-wrap">
	       <div class="add-icon" @click="isAddItem = true">
	           <img :src="addIcon">
	       </div>
	      <div class="delete-icon" @click="deleteItem()">
	           <img :src="deleteIcon">
	       </div>
		</div>
	</div>
</template>
