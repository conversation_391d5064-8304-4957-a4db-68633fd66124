<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t('file_set.number') }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.ruleName"
                :placeholder="$t('doc.this_dept_insert_keyword')"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">{{ $t('doc.this_dept_new_add') }}</el-button>
            </div>
            <div class="ser-bottom">
              <div class="cell-left">
                <!-- <el-form-item label="分类层级">
                <el-input
                  placeholder="分类层级"
                  v-model.trim="queryParams.classLevel"
                ></el-input>
              </el-form-item>
              <el-form-item label="分类代码">
                <el-input
                  placeholder="分类代码"
                  v-model.trim="queryParams.id"
                ></el-input>
              </el-form-item> -->
                <el-select
                  :placeholder="$t('doc.this_dept_status')"
                  v-model.trim="queryParams.classStatus"
                >
                  <el-option
                    v-for="dict in dict.type.class_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </div>
              <div class="cell-right">
                <el-button type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
                <el-button @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
                <el-button @click="boxClass = false">{{ $t('doc.this_dept_abolish') }}</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="left" />
          <el-table-column :label="$t('file_set.version_num_rule_name')" align="left" prop="ruleName" />
          <!-- <el-table-column label="编号类型" align="center" prop="p2" /> -->
          <el-table-column
            :label="$t('file_set.number_start_val')"
            align="left"
            prop="numberInitValue"
          />

          <el-table-column :label="$t('file_set.number_figure')" align="left" prop="numberDigit" />
          <el-table-column :label="$t('file_set.version_create_time')" align="left" prop="createTime">
            <template slot-scope="scope">
              {{
                scope.row.createTime
                  ? scope.row.createTime.substring(0, 16)
                  : ""
              }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('file_set.version_update_time')" align="left" prop="updateTime">
            <template slot-scope="scope">
              {{
                scope.row.updateTime
                  ? scope.row.updateTime.substring(0, 16)
                  : ""
              }}
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
              >{{ $t('file_set.version_edit') }}</el-button
              >
              <!-- <el-button size="mini" type="text" @click="handleDetails2(scope.row)"
            >设置</el-button
          > -->
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >{{ $t('doc.this_dept_delete') }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改-->
      <div v-if="open">
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="1050px"
          append-to-body
          :close-on-click-modal="false"
        >
          <add :id="id" @chinldClose="chinldClose"></add>
        </el-dialog>
      </div>

      <!-- 新增文件-->
      <el-drawer
        :title="title"
        :visible.sync="drawer"
        direction="rtl"
        size="80%"
        modal-append-to-body
      >
        <add :pButton="pButton"></add>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listCodeRule,
  getPost,
  delCodeRule,
  addPost,
  updatePost,
} from "@/api/setting/codeRule";
import add from "./add";
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    add,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawer: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        ruleName: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listCodeRule(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.id = "";
      this.title = this.$t('doc.this_dept_new_add');
    },
    handleUpdata(e) {
      this.id = e.id;
      this.open = true;
      this.title = this.$t('file_set.version_edit');
    },
    handleZuofei() {
      this.reset();
      this.drawer = true;
      this.title = this.$t('file_set.signature_invalid_file');
      this.pButton = "zuofei";
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      const postId = row.postId || this.ids;
      this.title = this.$t('doc.this_dept_detail');
      this.drawer = true;

    },


    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id || this.ids;
      this.$modal
        .confirm(this.$t('file_set.version_delete_or_not'))
        .then(function () {
          return delCodeRule(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    chinldClose() {
      this.id = "";
      this.open = false;
      this.getList();
    },
  },
};
</script>
<style lang="scss" >
@import "../../../../public/css/poctstyle.css";
</style>
