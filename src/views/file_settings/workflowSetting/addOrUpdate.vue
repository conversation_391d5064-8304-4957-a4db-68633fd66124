<template>
  <div class="document_change_add" v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">流程设置</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" @click="saveDocClassFlow">提交</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </div>
      <el-card class="gray-card">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          label-position="left"
          label-width="160px"
        >
          <el-form-item
            label="选择业务类型:"
            prop="flowKey">
            <el-select
              placeholder="业务类型"
              filterable
              v-model.trim="formData.bizType"
            >
              <el-option
                v-for="dict in dict.type.sys_biz_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="选择流程模板:"
            prop="flowKey">
            <el-select
              placeholder="流程模板"
              filterable
              v-model.trim="formData.flowKey"
              @change="onChangeFlowKey"
            >
              <el-option
                v-for="dict in template"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              >
              </el-option>
            </el-select>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleRefresh()"
            >同步最新节点</el-button>
          </el-form-item>
          <el-alert
            v-if="alertTitle"
            :title="alertTitle"
            style="margin-bottom: 5px"
            type="error">
          </el-alert>
          <span>待办按钮控制:</span>
          <el-table :data="formData.nodeList" border>
            <el-table-column label="序号" type="index" width="50"></el-table-column>
            <el-table-column label="环节节点" align="left" prop="nodeName" width="200"/>
            <el-table-column label="按钮权限" align="left" prop="nodeDetailList">
              <template slot-scope="scope">
                <el-tag
                  v-for="tag in scope.row.nodeDetailList"
                  closable
                  @click="handleClick(tag,scope.row)"
                  @close="()=>handleClose(tag,scope.row)"
                  :key="tag.code"
                  :type="tag.code">
                  {{(tag.funCondition?'*':'')+tag.name}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="left" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleSelect(scope.row,scope.$index)"
                >重新选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>
      <selection-box ref="selectionBox" @selectHandle="selectBoxHandle"></selection-box>
    <el-dialog title="权限附加条件" append-to-body :visible.sync="visible1" width="800px"  :close-on-click-modal="false">
      <el-card class="gray-card">
        <el-form
          ref="elForm"
          :model="funCondition"
          label-position="left"
          label-width="160px"
        >
          <el-form-item
            label="绑定目标环节节点:"
            prop="nodeCode">
            <el-select
              placeholder="环节节点"
              v-model.trim="funCondition.nodeCode"
              multiple
              clearable
            >
              <el-option
                v-for="dict in formData.nodeList"
                :key="dict.nodeCode"
                :label="dict.nodeName"
                :value="dict.nodeCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否需要验证:" prop="validate">
            <el-switch
              v-model.trim="funCondition.validate"
              active-color="#13ce66"
              :active-value="true"
              :inactive-value="false"
              size="medium"
              inactive-color="#ff4949"
            >
            </el-switch>
          </el-form-item>
          <el-form-item
            label="反向绑定目标环节节点:"
            prop="nodeCode">
            <el-select
              placeholder="环节节点"
              v-model.trim="funCondition.neNodeCode"
              multiple
              clearable
            >
              <el-option
                v-for="dict in formData.nodeList"
                :key="dict.nodeCode"
                :label="dict.nodeName"
                :value="dict.nodeCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="限定值:"
            prop="limitValue">
            <el-input v-model.trim="funCondition.limitValue" placeholder="限定值"></el-input>
          </el-form-item>
          <el-form-item
            label="设置人员:"
            prop="groupId">
            <el-select
              placeholder="预设人员分组"
              v-model.trim="funCondition.groupId"
              clearable
            >
              <el-option
                v-for="dict in groupOptions"
                :key="dict.id"
                :label="dict.groupName"
                :value="dict.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="设置人员范围:"
            prop="groupId">
            <el-select
              placeholder="选择角色"
              v-model.trim="funCondition.roleId"
              clearable
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible1 = false">取 消</el-button>
        <el-button type="primary" @click="tagSubmitForm">确 定</el-button>
      </div>
    </el-dialog>
    </div>
</template>

<script>
    import {getByFlowKey,compareFlowPlatNodeList,syncFlowPlatNodeList} from "@/api/setting/docClassFlowNode";
    import {
      queryFlowList,
      getByDocClass,
      saveDocClassFlow,
      addDocClassFlow,
      getDocClassFlow, updateDocClassFlow
    } from '@/api/setting/docClassFlow'
    import Treeselect from '@riophae/vue-treeselect'
    import SelectionBox from '@viewscomponents/selectionBox/index.vue'
    import { listDistributeGroup } from '@/api/setting/distributeGroup'
    import {listRole} from "@/api/system/role";
    export default {
        name: "AddOrUpdate",
      components: { SelectionBox, Treeselect },
        props: ['id'],
        dicts: ['sys_yes_no','flow_node_fun_list','sys_biz_type'],
        data() {
            return {
                roleOptions:[],
                groupOptions: [],
                loading: false,
                alertTitle: '',
                data: [],
                funCondition: {
                  nodeCode: [],
                  validate: true,
                  neNodeCode: [],
                  limitValue: undefined,
                  groupId: undefined,
                  roleId: ''
                },
                visible1: false,
                row: {},
                value: [],
                template: [],
                activeIndex: 'ADD',
                visible: false,
                formData: {
                    openFlag: undefined,
                    applyFlag: undefined,
                    flowKey: undefined,
                    flowName: undefined,
                    bizType: undefined,
                    nodeList: []
                },
                rules:{

                }
            }
        },
        mounted(){
            this.getByDocClass()
            this.getGroupList();
            this.getRoleList();
        },
        methods: {
            getRoleList(){
              let parames = {
                pageNum: 1,
                pageSize: 1000,
                roleName: undefined,
                roleKey: undefined,
                status: undefined
              }
              listRole(this.addDateRange(parames, [])).then(
                (response) => {
                  this.roleOptions = response.rows;
                }
              );
            },
            reset(){
              this.formData= {
                openFlag:undefined,
                applyFlag: undefined,
                flowKey: undefined,
                flowName: undefined,
                bizType: undefined,
                nodeList: []
              }
            },
            getGroupList(){
              listDistributeGroup({type:'countersign'}).then(res=>{
                this.groupOptions=res.rows
              })
            },
            getByDocClass(){
              let _this = this
              _this.changeTemplate()
              _this.reset()
              if (this.id) {
                _this.loading = true
                getDocClassFlow(this.id).then(res=>{
                  this.formData = res.data
                  _this.loading = false
                })
              }
            },
            handleClick(tag,row){
              this.visible1=true
              this.tag = tag
              if (tag.funCondition) {
                this.funCondition = JSON.parse(tag.funCondition)
              }else {
                this.funCondition = {
                  nodeCode: [],
                  validate: true,
                  neNodeCode: [],
                  limitValue: undefined,
                  groupId: undefined,
                }
              }
            },
            tagSubmitForm(){
              this.visible1=false
              this.tag.funCondition = JSON.stringify(this.funCondition)
            },
            handleClose(tag,row){
                row.nodeDetailList.splice(row.nodeDetailList.findIndex(node=>node.code===tag.code), 1);
            },
            onChangeFlowKey(flowKey) {
              let _this = this
              getByFlowKey(_this.id,_this.formData.bizType,flowKey).then(res=>{
                _this.formData.nodeList=res.data
              })
            },
            changeTemplate(){
                let _this = this
                _this.loading = true
                _this.formData = {}
                // 获取流程模板
                queryFlowList({flowKey:undefined}).then(res=>{
                  _this.template = res.data
                  _this.loading = false
                })
            },
            handleSelect(row,index){
                let _this = this
                _this.selectionBoxInit(row,index, row.nodeDetailList,_this.dict.type.flow_node_fun_list,{
                  title: '选择按钮权限',
                  id: 'value',
                  label: 'label',
                  valueId: 'code',
                  valueLabel: 'name',
                  valueModel: {code: null, name: null, remark: null, type: null, sort: null, funCondition: null}
                })
            },
            selectionBoxInit(source,index,selectedList,dataList, settings,treeList) {
              this.$nextTick(()=> {
                this.$refs.selectionBox.init(source, index, selectedList, dataList, settings, treeList)
              })
            },
            selectBoxHandle(source,index,selectedList,valueList){
              valueList.forEach(value=>{
                let item = selectedList.find(selected=>selected.value===value.code)
                if (item) {
                    value.remark= source.nodeName
                    if (item.raw) {
                      value.type= item.raw.remark
                      value.sort= item.raw.dictSort
                    }
                }
              })
              this.$set(this.formData.nodeList[index],'nodeDetailList',valueList)
            },
            saveDocClassFlow(){
              let _this = this
              if (!_this.formData.id) {
                addDocClassFlow(_this.formData).then((res)=>{
                  this.$modal.msgSuccess("设置成功");
                  this.close()
                })
              }else {
                updateDocClassFlow(_this.formData).then(res=>{
                  this.$modal.msgSuccess("设置成功");
                })
              }
            },
            handleRefresh() { // 对接流程平台，同步最新的节点
              let _this = this
              _this.formData.flowName = _this.template.find(item=>item.dictValue===_this.formData.flowKey).dictLabel
              syncFlowPlatNodeList(_this.id,_this.formData.bizType,_this.formData.flowKey,_this.formData.id === undefined ? "null" : _this.formData.id).then(res=>{
                this.$modal.msgSuccess("同步成功");
                _this.formData.nodeList=res.data
                _this.alertTitle = ''
              })
            },
          close(){
              this.$emit("close")
          }
        }
    }
</script>

<style scoped>

</style>
