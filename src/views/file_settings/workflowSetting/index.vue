<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>流程设置</span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.searchValue"
                placeholder="输入流程Key/名称搜索"
                clearable
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >重置
              </el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">新增</el-button>
              <el-button type="primary" @click="handleCopyAdd()">拷贝新增</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item label="业务类型:" prop="classType">
                <el-select
                  v-model.trim="queryParams.bizType"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.sys_biz_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button @click="boxClass = false">取消</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          ref="dragTable"
          :data="dataList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="id"
            align="left"
            width="200"
            prop="id"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="业务类型"
            align="left"
            prop="bizType"
            width="150"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_biz_type" :value="scope.row.bizType"/>
            </template>
          </el-table-column>
          <el-table-column
            label="流程Key"
            align="left"
            prop="flowKey"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="流程名称"
            align="left"
            prop="flowName"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            align="left"
            width="150"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
                >编辑
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <el-drawer
        :visible.sync="drawer"
        direction="rtl"
        size="80%"
        modal-append-to-body
        :with-header="false"
        :show-close="false"
        :destroy-on-close="true"
      >
        <add-or-update :id="id" @close="closeDrawer"></add-or-update>
      </el-drawer>

    </div>
  </div>
</template>

<script>
import AddOrUpdate from '@views/file_settings/workflowSetting/addOrUpdate.vue'
import {
  addDocClassFlow,
  delDocClassFlow,
  getDocClassFlow,
  listDocClassFlow,
} from '@/api/setting/docClassFlow'

export default {
  name: "WorkflowManage",
  dicts: ["sys_normal_disable", "class_status","sys_biz_type"],
  components: {
    AddOrUpdate
    // rzDrawer,
  },
  data() {
    return {
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        noClass: true,
        bizType:undefined,
        flowKey:undefined,
        searchValue:undefined
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        postName: [{ required: true, message: "不能为空", trigger: "blur" }],
        postCode: [{ required: true, message: "不能为空", trigger: "blur" }],
        postSort: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      drawerSettings: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listDocClassFlow(this.queryParams).then(res=>{
        this.dataList = res.rows
        this.total = res.total
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.flowKey = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.drawer = true;
      this.id = undefined;
    },
    handleCopyAdd(){
      let _this = this
      if (_this.ids.length===1) {
        _this.loading = true
        for (let id of _this.ids){
          getDocClassFlow(id).then(res=>{
            addDocClassFlow(res.data).then((res)=>{
              _this.$modal.msgSuccess("拷贝成功");
              _this.loading = false
              _this.getList();
            })
          })
        }
      }else{
        _this.$modal.msgWarning("请选择一条数据进行拷贝");
      }
    },
    handleUpdata(e) {
      //console.log(e);
      this.id = e.id;
      this.drawer = true;
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除id为"' + ids + '"的数据项？')
        .then(function () {
          return delDocClassFlow(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    closeDrawer(v) {
      this.drawer = false;
      this.getList();
    },
  },
};
</script>
