<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t('file_set.signature') }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.signatureName"
                :placeholder="$t('file_set.signature_fill_name_to_search')"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">{{ $t('doc.this_dept_new_add') }}</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <!-- <el-form-item label="分类层级">
                <el-input
                  placeholder="分类层级"
                  v-model.trim="queryParams.classLevel"
                ></el-input>
              </el-form-item>
              <el-form-item label="分类代码">
                <el-input
                  placeholder="分类代码"
                  v-model.trim="queryParams.id"
                ></el-input>
              </el-form-item> -->
              <el-select
                :placeholder="$t('doc.this_dept_status')"
                v-model.trim="queryParams.signatureStatus"
              >
                <el-option
                  v-for="dict in dict.type.class_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
              <el-button @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
              <el-button @click="boxClass = false">{{ $t('doc.this_dept_abolish') }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @selection-change="handleSelectionChange"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          row-key="id"
        >
          >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.signature_name')"
            align="left"
            prop="signatureName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.signature_temp')"
            align="left"
            prop="templateName"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            :label="$t('doc.this_dept_status')"
            align="left"
            prop="signatureStatus"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{
                  scope.row.signatureStatus == 1 ? $t('file_set.number_enable') : $t('file_set.number_banned')
                }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
              >{{ $t('file_set.version_edit') }}</el-button
              >
              <el-upload
                class="upload-rz"
                :http-request="standardDocBeforeUpload"
                :show-file-list="false"
              >
                <el-button
                  size="mini"
                  type="text"
                  @click="hangleshangchuan(scope.row)"
                >{{ $t('doc.this_dept_upload') }}
                </el-button>
              </el-upload>
              <el-button
                size="mini"
                type="text"
                @click="
                  handelefileLocalDownload(
                    scope.row.fileId,
                    scope.row.templateName
                  )
                "
              >{{ $t('doc.this_dept_download') }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >{{ $t('doc.this_dept_delete') }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改-->
      <div v-if="open">
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="1000px"
          append-to-body
          destroy-on-close
        >
          <add :id="id" @chinldClose="chinldClose"></add>
        </el-dialog>
      </div>

      <el-drawer
        :title="title"
        :visible.sync="drawer"
        direction="rtl"
        size="80%"
        modal-append-to-body
      >
        <add :pButton="pButton"></add>
      </el-drawer>

      <el-drawer
        :title="title"
        :visible.sync="drawerSettings"
        direction="rtl"
        size="80%"
        modal-append-to-body
      >
        <settings :id="id"></settings>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listSignature,
  getSignature,
  delSignature,
  addSignature,
  updateSignature,
} from "@/api/setting/signature";
import { processFileLocalUpload } from "@/api/commmon/file";
import add from "./add";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  name: "Post",
  dicts: ["sys_normal_disable", "class_status"],
  components: {
    add,
    // rzDrawer,
  },
  mixins: [mixin],
  data() {
    return {
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        signatureName: undefined,
        classLevel: undefined,
        signatureStatus: undefined,
        id: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      drawerSettings: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
      shangchuanid: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listSignature(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        signatureName: undefined,

        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.signatureStatus = "";
      this.queryParams.signatureName = "";
      this.queryParams.classLevel = "";
      this.queryParams.id = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    standardDocBeforeUpload(params) {
      console.log(params);
      let standardDoc = new FormData();
      standardDoc.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      processFileLocalUpload(standardDoc).then((res) => {
        updateSignature({
          id: this.shangchuanid,
          fileId: res.data.fileId,
          templateName: res.data.fileName,
        }).then((response) => {
          this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
          this.getList();
        });
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.id = "";
      this.title = this.$t('doc.this_dept_new_add');
    },
    handleUpdata(e) {
      //console.log(e);
      this.id = e.id;
      this.open = true;
      this.title = this.$t('doc.this_dept_edit');
    },
    handleZuofei() {
      this.reset();
      this.drawer = true;
      this.title = this.$t('file_set.signature_invalid_file');
      this.pButton = "zuofei";
    },
    handleSettings(e) {
      this.reset();
      this.id = e.id;
      this.drawerSettings = true;
      this.title = e.className;
    },

    chinldClose() {
      this.open = false;
      this.getList();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    hangleshangchuan(e) {
      this.shangchuanid = e.id;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id || this.ids;
      console.log(postIds);
      this.$modal
        .confirm(this.$t('file_set.signature_text') + row.signatureName + this.$t('file_set.signature_text1'))
        .then(function () {
          return delSignature(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    changeDrawer(v) {
      this.drawer = v;
    },
  },
};
</script>
<style lang="scss">
.upload-rz {
  display: inline-block;
  margin: 0px 5px;
  .el-upload {
    border: 0px solid #013288;
    background: #fff0;
  }
  .el-upload:hover {
    background-color: #fff0 !important;
  }
}
@import "../../../../public/css/poctstyle.css";
</style>
