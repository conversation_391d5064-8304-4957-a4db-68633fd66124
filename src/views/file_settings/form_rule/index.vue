<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header"
           class="clearfix">
        <span> 表单规则 </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams"
               ref="queryForm"
               :inline="true"
               v-show="showSearch"
               label-width="68px">
        <el-form-item label="规则名称"
                      prop="ruleName">
          <el-input v-model="queryParams.ruleName"
                    placeholder="请输入规则名称"
                    clearable
                    size="small"
                    @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     icon="el-icon-search"
                     size="mini"
                     @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh"
                     size="mini"
                     @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10"
              class="mb8">
        <el-col :span="1.5">
          <el-button type="primary"
                     plain
                     icon="el-icon-plus"
                     size="mini"
                     @click="handleAdd"
                     v-hasPermi="['setting:formRule:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success"
                     plain
                     icon="el-icon-edit"
                     size="mini"
                     :disabled="single"
                     @click="handleUpdate"
                     v-hasPermi="['setting:formRule:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger"
                     plain
                     icon="el-icon-delete"
                     size="mini"
                     :disabled="multiple"
                     @click="handleDelete"
                     v-hasPermi="['setting:formRule:remove']">删除</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch"
                       @queryTable="getList"></right-toolbar>
      </el-row>
      <el-card class="gray-card">
        <el-table v-loading="loading"
                  :data="formRuleList"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           width="55"
                           align="center" />
          <el-table-column label="规则名称"
                           align="center"
                           prop="ruleName" />
          <el-table-column label="操作"
                           align="center"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini"
                         type="text"
                         icon="el-icon-edit"
                         @click="handleUpdate(scope.row)">修改</el-button>
              <el-button size="mini"
                         type="text"
                         icon="el-icon-delete"
                         @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination v-show="total>0"
                  :total="total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="getList" />
    </div>
    <!-- 添加或修改单规则对话框 -->
    <el-dialog :title="title"
               :visible.sync="open"
               width="80%"
               append-to-body>
      <el-form ref="form"
               :model="form"
               :rules="rules"
               label-width="100px">
        <el-form-item label="规则名称"
                      prop="ruleName">
          <el-input v-model="form.ruleName"
                    placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则明细">
          <div class="gray-card table-card no-padding">
            <el-table :data="formItems"
                      height="500px">
              <el-table-column label="字段名称"
                               align="center"
                               prop="label"></el-table-column>
              <el-table-column label="显示"
                               align="center"
                               prop="show">
                <template slot-scope="scope">
                  <el-checkbox true-label="Y"
                               false-label="N"
                               v-model.trim="scope.row.show"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="必填"
                               align="center"
                               prop="show">
                <template slot-scope="scope">
                  <el-checkbox true-label="Y"
                               false-label="N"
                               v-model.trim="scope.row.required"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="默认值"
                               align="center"
                               prop="defaultValue">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.defaultValue"
                            style="width:100%"
                            max-length="100"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="显示类型"
                               align="center"
                               prop="htmlType">
                <template slot-scope="scope">
                  <el-select v-model.trim="scope.row.htmlType"
                             @change="(val) => handleHtmlTypeChange(val, scope.row)">
                    <el-option label="文本框"
                               value="input" />
                    <el-option label="文本域"
                               value="textarea" />
                    <el-option label="下拉框"
                               value="select" />
                    <el-option label="单选框"
                               value="radio" />
                    <el-option label="复选框"
                               value="checkbox" />
                    <el-option label="选择部门树形控件"
                               value="tree" />
                    <el-option label="日期控件"
                               value="datetime" />
                    <!-- 单选选人控件、多选选人控件 -->
                    <el-option label="单选选人控件"
                               value="selectUser" />
                    <el-option label="多选选人控件"
                               value="selectUserMultiple" />
                    <!-- 输入可多选数据 -->
                    <el-option label="输入可多选数据"
                               value="inputMultiple" />
                    <!-- <el-option label="图片上传"
                               value="imageUpload" />
                    <el-option label="文件上传"
                               value="fileUpload" />
                    <el-option label="富文本控件"
                               value="editor" /> -->
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="字典类型"
                               prop="dictType">
                <template slot-scope="scope">
                  <el-select v-model.trim="scope.row.dictType"
                             clearable
                             filterable
                             :disabled="!['select', 'radio', 'checkbox'].includes(scope.row.htmlType)"
                             placeholder="请选择">
                    <el-option v-for="dict in dictOptions"
                               :key="dict.dictType"
                               :label="dict.dictName"
                               :value="dict.dictType">
                      <span style="float: left">{{ dict.dictName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.dictType }}</span>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="字符长度"
                               align="center"
                               prop="maxLength">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.maxLength"
                                   :min="0"
                                   :max="9999"
                                   placeholder="请输入字符长度"></el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button :loading="buttonLoading"
                   type="primary"
                   @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFormRule, getFormRule, delFormRule, addFormRule, updateFormRule } from "@/api/setting/formRule";
import { optionselect as getDictOptionselect } from "@/api/system/dict/type";
export default {
  name: "FormRule",
  dicts: ["form_control"],
  data () {
    return {
      dictOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 单规则表格数据
      formRuleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      formItems: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  computed: {

  },
  created () {
    this.getDictOption()
    this.getList();
  },
  methods: {
    /** 查询字典下拉列表 */
    getDictOption () {
      getDictOptionselect().then(response => {
        this.dictOptions = response.data;
      });
    },

    /** 查询单规则列表 */
    getList () {
      this.loading = true;
      listFormRule(this.queryParams).then(response => {
        this.formRuleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel () {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset () {
      this.form = {
        id: undefined,
        ruleDetails: undefined,
        ruleName: undefined,
        tenantId: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.formItems = [...this.dict.type.form_control.map(v => {
        return { label: v.label, value: v.value, show: 'N', required: 'N', defaultValue: undefined, query: 'N', list: 'N', htmlType: undefined, dictType: undefined, maxLength: undefined }
      })]
      this.reset();
      this.open = true;
      this.title = "添加表单规则";
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.formItems = [...this.dict.type.form_control.map(v => {
        return { label: v.label, value: v.value, show: 'N', required: 'N', defaultValue: undefined, query: 'N', list: 'N', htmlType: undefined, dictType: undefined, maxLength: undefined }
      })]
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getFormRule(id).then(response => {
        this.loading = false;
        this.form = response.data;
        if (this.form.ruleDetails) {
          let target = JSON.parse(this.form.ruleDetails)
          this.formItems.forEach(v => {
            target.forEach(v1 => {
              if (v.value === v1.value) {
                v.show = v1.show
                v.required = v1.required
                v.defaultValue = v1.defaultValue
                v.query = v1.query
                v.list = v1.list
                v.htmlType = v1.htmlType
                v.dictType = v1.dictType
                v.maxLength = v1.maxLength
              }
            })
          })
        }
        console.log(this.formItems)
        this.open = true;
        this.title = "修改表单规则";
      });
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.ruleDetails = JSON.stringify(this.formItems)
          // 增加校验规则：1、如果显示项勾选，则对应显示类型不能为空；2、显示类型为下拉选，则字典类型不能为空
          let hasError = false
          this.formItems.forEach(item => {
            if (item.show === 'Y' && !item.htmlType) {
              this.$message.error(`${item.label}的显示类型不能为空`)
              hasError = true
            }
            if (item.htmlType === 'select' && !item.dictType) {
              this.$message.error(`${item.label}的字典类型不能为空`)
              hasError = true
            }
          })
          if (hasError) {
            this.buttonLoading = false
            return
          }
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateFormRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addFormRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除表单规则编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delFormRule(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 显示类型改变时的处理 */
    handleHtmlTypeChange (val, row) {
      if (!['select', 'radio', 'checkbox'].includes(val)) {
        row.dictType = undefined
      }
    }
  }
};
</script>
