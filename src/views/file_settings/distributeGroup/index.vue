<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{$t(`menus.2423`)}} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.groupName"
                :placeholder="$t('doc.this_dept_insert_keyword')"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
                clearable
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">{{ $t('doc.this_dept_new_add') }}</el-button>
            </div>
            <div class="ser-bottom">
              <div class="cell-left">
                <el-select
                  :placeholder="$t('doc.this_dept_status')"
                  v-model.trim="queryParams.classStatus"
                >
                  <el-option
                    v-for="dict in dict.type.class_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </div>
              <div class="cell-right">
                <el-button type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
                <el-button @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
                <el-button @click="boxClass = false">{{ $t('doc.this_dept_abolish') }}</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="dataList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="left" />
          <el-table-column :label="$t('doc.group_name')" align="left" prop="groupName" />
          <el-table-column :label="$t(`file_set.type_type`)" align="left" prop="type" >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.sys_group_type" :value="scope.row.type"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t('file_set.version_create_time')" align="left" prop="createTime">
          </el-table-column>
          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
              >{{ $t('file_set.version_edit') }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >{{ $t('doc.this_dept_delete') }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改-->
      <div v-if="open">
          <el-drawer
            :with-header="false"
            :visible.sync="open"
            :show-close="false"
            direction="rtl"
            size="85%"
            modal-append-to-body
            append-to-body
          >
            <add :id="id" :title="title" @chinldClose="chinldClose"></add>
          </el-drawer>
      </div>
    </div>
  </div>
</template>

<script>
import add from "./add";
import { listDistributeGroup,delDistributeGroup } from '@/api/setting/distributeGroup'
export default {
  name: "DistributeGroup",
  dicts: ['sys_group_type'],
  components: {
    add,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: undefined,
        orderByColumn: 'create_time',
        isAsc:'desc'
      },
      id: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listDistributeGroup(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.groupName = undefined
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.id = "";
      this.title = this.$t('doc.this_dept_new_add');
    },
    handleUpdata(e) {
      this.id = e.id;
      this.open = true;
      this.title = this.$t('file_set.version_edit');
    },


    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id || this.ids;
      this.$modal
        .confirm(this.$t('file_set.version_delete_or_not'))
        .then(function () {
          return delDistributeGroup(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    chinldClose() {
      this.id = "";
      this.open = false;
      this.getList();
    },
  },
};
</script>
