<template>
  <div class="document_change_add">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{title}}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" @click="submitForm" v-dbClick>{{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button @click="handleClose">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="120px"
    >
      <el-form-item :label="getLabelWithColon($t('doc.group_name'))" prop="groupName">
        <el-input
          v-model.trim="formData.groupName"
          :placeholder="getLabelWithColon($t('file_set.version_fill_rule_name'))"
          clearable
          :style="{ width: '100%' }"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t(`file_set.type_type`))" prop="type">
        <el-select v-model.trim="formData.type">
          <el-option
            v-for="dict in dict.type.sys_group_type"
            :key="dict.value"
            :label="dictLanguage(dict)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <distribute-box
        :editStatus="true"
        :workflowStatus="true"
        :deptList="deptList"
        :deptOptions="deptOptions"
        :distributeList="distributeList"
        :displayOne="formData.type"
        :hideIsDistribute="true"
        ref="distributeBox"
      >
      </distribute-box>
      <div class="news-card" v-if="formData.type==='countersign'">
        <div class="card-head">
          <div class="cell-title">{{$t(`doc.countersign`)}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-button  type="primary" @click="selectHandle()">{{ $t(`doc.this_dept_select_user`) }}</el-button>
          <el-table :data="userList" border max-height="500">
            <el-table-column
              :label="$t(`doc.this_dept_dept`)"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_people`)"
              align="center"
              prop="receiveNickName"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`myItem.msg_operation`)"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <el-button
                  circle
                  type="danger"
                  @click="handleDel('userList',scope.$index)"
                >{{ $t(`doc.this_dept_delete`) }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-form>
    <assign-users ref="assignUsers" @selectHandle="userSelectHandle"></assign-users>
  </div>
</template>
<script>
import { listDept } from '@/api/system/dept'
import { listVersionRuleDetail } from '@/api/setting/versionRuleDetail'
import DistributeBox from '../../workflowList/addWorkflow/add_import/distributeBox.vue'
import { addDistributeGroup, getDistributeGroup, updateDistributeGroup } from '../../../api/setting/distributeGroup'
import { listDistributeGroupDetail } from '../../../api/setting/distributeGroupDetail'
import AssignUsers from '../../workflowList/addWorkflow/add_import/assignUsers.vue'
export default {
  dicts: ['sys_group_type'],
  components: { AssignUsers, DistributeBox },
  props: ["id","title"],
  data() {
    return {
      loading:false,
      deptList:[],
      deptOptions: [],
      distributeList: [],
      userList: [],
      formData: {
        id: undefined,
        groupName: undefined,
        type:'print',
        distributeList: [],
      },
      rules: {
        groupName: [
          {
            required: true,
            message: this.$t(`doc.this_dept_insert`)+this.$t('doc.group_name'),
            trigger: "blur",
          },
          {
            min: 1,
            max: 200,
            message: this.$t('file_set.number_long_rule'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {},
  created() {
    this.getDeptList()
  },
  mounted() {
    let _this = this
    if (this.id) {
      _this.loading = true
      getDistributeGroup(_this.id).then(async(response) => {
        _this.formData = response.data
        let res = await listDistributeGroupDetail({ groupId: _this.id })
        if (_this.formData.type==='countersign') {
          _this.userList = res.rows
        }else {
          _this.distributeList = res.rows
          _this.$refs.distributeBox.init(_this.formData,{trainType:'scopeType',distributeType:'scopeType'})
        }
      }).finally(()=>{
        _this.loading = false
      });
    }
  },
  methods: {
    getDeptList(){
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    handleClose() {
      this.$emit("chinldClose", false);
    },
    selectHandle(){
      let _this = this
      let userList = []
      _this.userList.forEach(item=>{
        userList.push({
          userName: item.receiveUserName,
          nickName: item.receiveNickName,
          deptId: item.receiveUserDeptId,
          deptName: item.receiveUserDept,
        })
      })
      _this.$nextTick(()=>{
        _this.$refs.assignUsers.init(_this.$t(`doc.select_personnel_multiple`),null,null,userList,true)
      })
    },
    handleDel(source,index){
      this[source].splice(index,1)
    },
    userSelectHandle(source,index,data){
      let _this = this
      let userList = []
      data.forEach(item=>{
        userList.push({
          receiveUserName: item.userName,
          receiveNickName: item.nickName,
          receiveUserDeptId: item.deptId,
          receiveUserDept: item.deptName
        })
      })
      _this.userList=userList
    },
    submitForm() {
      let _this = this
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(_this.formData))
          if (formData.type==='countersign') {
            formData.itemList = _this.userList
          }else {
            formData.itemList = _this.$refs.distributeBox.getDistributeList()
            let boxFormData = _this.$refs.distributeBox.formData
            formData.scopeType =  formData.type==='print'?boxFormData.distributeType:boxFormData.trainType
          }
          if (!formData.itemList||formData.itemList.length<1) {
            this.$modal.msgWarning(this.$t('file_handle.print_least_one_data'));
            return
          }
          if (formData.id != undefined) {
            updateDistributeGroup(formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.$emit("chinldClose");
            });
          } else {
            addDistributeGroup(formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.$emit("chinldClose");
            });
          }
        }
      });
    },
  },
};
</script>
