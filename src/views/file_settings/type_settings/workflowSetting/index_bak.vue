<template>
  <div class="card-head" v-loading="loading">
    <el-row>
      <el-col :span="6">
        <treeselect
          v-model.trim="docClass"
          :options="classList"
          :normalizer="normalizer"
          :searchable="false"
          :show-count="true"
          :clearable="false"
          noOptionsText="暂无数据"
          placeholder="选择文件类型"
        />
      </el-col>
      <el-col :span="16">
        <el-button
          type="text"
          @click="handleCopy()"
        >复制流程设置</el-button>
      </el-col>
    </el-row>
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal"  @select="handleMenu">
      <el-menu-item v-for="menu in menuList" :key="menu.index" :index="menu.index">{{menu.label}}</el-menu-item>
    </el-menu>
      <el-card class="gray-card">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          label-position="left"
          label-width="160px"
        >
          <el-form-item label="是否启用:" prop="openFlag">
            <el-switch
              v-model.trim="formData.openFlag"
              active-color="#13ce66"
              active-value="Y"
              inactive-value="N"
              size="medium"
              inactive-color="#ff4949"
            >
            </el-switch>
          </el-form-item>
          <el-form-item
            label="是否应用到子文件类型:"
            prop="applyFlag"
          >
            <el-radio-group
              v-model.trim="formData.applyFlag"
              size="medium"
            >
              <el-radio
                v-for="(item, index) in dict.type.sys_yes_no"
                :key="index"
                :label="item.value"
                :disabled="item.disabled"
              >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="选择流程模板:"
            prop="flowKey">
            <el-select
              placeholder="流程模板"
              v-model.trim="formData.flowKey"
              @change="onChangeFlowKey"
            >
              <el-option
                v-for="dict in template"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              >
              </el-option>
            </el-select>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleRefresh()"
            >同步最新节点</el-button>
          </el-form-item>
          <el-alert
            v-if="alertTitle"
            :title="alertTitle"
            style="margin-bottom: 5px"
            type="error">
          </el-alert>
          <span>待办按钮控制:</span>
          <el-table :data="formData.nodeList" border>
            <el-table-column label="序号" type="index" width="50"></el-table-column>
            <el-table-column label="环节节点" align="left" prop="nodeName" width="200"/>
            <el-table-column label="按钮权限" align="left" prop="nodeDetailList">
              <template slot-scope="scope">
                <el-tag
                  v-for="tag in scope.row.nodeDetailList"
                  closable
                  @click="handleClick(tag,scope.row)"
                  @close="()=>handleClose(tag,scope.row)"
                  :key="tag.code"
                  :type="tag.code">
                  {{(tag.funCondition?'*':'')+tag.name}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="left" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleSelect(scope.row)"
                >重新选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>
      <el-dialog title="选择按钮权限" append-to-body :visible.sync="visible" width="800px"  :close-on-click-modal="false">
        <el-transfer
          v-model="value"
          :titles="['待选按钮（多选）', '已选按钮']"
          :data="dict.type.flow_node_fun_list"
          :props="{key: 'value',label:'label'}"
        >
        </el-transfer>
        <div slot="footer" class="dialog-footer">
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="权限附加条件" append-to-body :visible.sync="visible1" width="800px"  :close-on-click-modal="false">
        <el-card class="gray-card">
          <el-form
            ref="elForm"
            :model="funCondition"
            label-position="left"
            label-width="160px"
          >
            <el-form-item
              label="绑定目标环节节点:"
              prop="nodeCode">
              <el-select
                placeholder="环节节点"
                v-model.trim="funCondition.nodeCode"
                multiple
                clearable
              >
                <el-option
                  v-for="dict in formData.nodeList"
                  :key="dict.nodeCode"
                  :label="dict.nodeName"
                  :value="dict.nodeCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否需要验证:" prop="validate">
              <el-switch
                v-model.trim="funCondition.validate"
                active-color="#13ce66"
                :active-value="true"
                :inactive-value="false"
                size="medium"
                inactive-color="#ff4949"
              >
              </el-switch>
            </el-form-item>
            <el-form-item
              label="反向绑定目标环节节点:"
              prop="nodeCode">
              <el-select
                placeholder="环节节点"
                v-model.trim="funCondition.neNodeCode"
                multiple
                clearable
              >
                <el-option
                  v-for="dict in formData.nodeList"
                  :key="dict.nodeCode"
                  :label="dict.nodeName"
                  :value="dict.nodeCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="限定值:"
              prop="limitValue">
              <el-input v-model.trim="funCondition.limitValue" placeholder="限定值"></el-input>
            </el-form-item>
          </el-form>
        </el-card>
        <div slot="footer" class="dialog-footer">
          <el-button @click="visible1 = false">取 消</el-button>
          <el-button type="primary" @click="tagSubmitForm">确 定</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
    import {getByFlowKey,compareFlowPlatNodeList,syncFlowPlatNodeList} from "@/api/setting/docClassFlowNode";
    import {queryFlowList,getByDocClass,saveDocClassFlow} from "@/api/setting/docClassFlow";
    import { settingDocClassList } from '@/api/file_settings/type_settings'
    import Treeselect from '@riophae/vue-treeselect'
    import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    export default {
        name: "WorkflowSetting",
      components: { Treeselect },
        props: ['id'],
        dicts: ['sys_yes_no','flow_node_fun_list'],
        data() {
            return {
                docClass: undefined,
                funCondition: {
                  nodeCode: [],
                  validate: true,
                  neNodeCode: [],
                  limitValue: undefined,
                },
                visible1: false,
                tag: {},
                loading: false,
                alertTitle: '',
                menuList:[{
                    index: 'ADD',
                    label: '文件新增',
                },{
                    index: 'UPDATE',
                    label: '文件修订',
                },{
                    index: 'DISUSE',
                    label: '文件作废',
                }],
                data: [],
                row: {},
                value: [],
                template: [],
                activeIndex: 'ADD',
                visible: false,
                formData: {
                    openFlag: 'N',
                    applyFlag: 'N',
                    flowKey: undefined,
                    bizType: 'ADD',
                    nodeList: []
                },
                rules:{

                },
                classList:[],
            }
        },
        mounted(){
            this.getByDocClass(this.id)
            this.getDocClassList()
        },
        methods: {
            async getDocClassList() {
              const { rows } = await settingDocClassList({});
              this.$set(this, "classList", this.handleTree(rows, "id", "parentClassId"));
            },
            reset(){
                this.formData= {
                    openFlag:undefined,
                    applyFlag: undefined,
                    flowKey: undefined,
                    bizType: 'ADD',
                    nodeList: []
                }
            },
            getByDocClass(docClass){
                let _this = this
                _this.loading = true
                getByDocClass(docClass).then(res=>{
                    if (res.data&&res.data.length>0) {
                        res.data.forEach(item=>{
                          item.docClass = _this.id
                        })
                        _this.data = res.data
                    }else {
                        let data= []
                        _this.menuList.forEach(menu=>{
                            data.push({
                                docClass: _this.id,
                                openFlag: 'N',
                                applyFlag: 'N',
                                flowKey: undefined,
                                bizType: menu.index,
                                nodeList: []})
                        })
                        _this.data = data
                    }
                    _this.changeTemplate(this.activeIndex)
                })
            },
            handleClick(tag,row){
                this.visible1=true
                this.tag = tag
                if (tag.funCondition) {
                  this.funCondition = JSON.parse(tag.funCondition)
                }else {
                  this.funCondition = {
                    nodeCode: [],
                    validate: true,
                    neNodeCode: [],
                    limitValue: undefined,
                  }
                }
            },
            tagSubmitForm(){
              this.visible1=false
              this.tag.funCondition = JSON.stringify(this.funCondition)
            },
            handleClose(tag,row){
                row.nodeDetailList.splice(row.nodeDetailList.findIndex(node=>node.code===tag.code), 1);
            },
            onChangeFlowKey(flowKey) {
                let _this = this
                getByFlowKey(_this.id,_this.formData.bizType,flowKey).then(res=>{
                    _this.formData.nodeList=res.data
                    // _this.flowKeyUpdateMsg(flowKey,_this.formData.id)
                })
            },
            flowKeyUpdateMsg(flowKey,id){
                let _this = this
                if (flowKey&&id) {
                    _this.alertTitle = ''
                    compareFlowPlatNodeList(flowKey,id).then(res=>{
                        let msg = ''
                        if (res.data) {
                            if (res.data.addNodes) {
                                msg = msg + '新增节点有：' + res.data.addNodes + '，'
                            }
                            if (res.data.delNodes) {
                                msg = msg + '删除节点有：' + res.data.delNodes + '，'
                            }
                            if (msg) {
                                msg = ' 流程更新提示：' + msg + '请点击上方按钮“同步最新节点”！';
                            }
                        }
                        _this.alertTitle = msg
                    })
                }
            },
            handleMenu(key){
                let _this = this
                _this.changeTemplate(key)
            },
            changeTemplate(key){
                let _this = this
                _this.loading = true
                let bizType = key
                _this.formData = {}
                // 流程模板在流程平台的 前缀关键字
                let flowKeyWord = "disuse_doc";
                if (bizType==='ADD'){
                  flowKeyWord = "add_doc"
                } else if (bizType==='UPDATE') {
                  flowKeyWord = "update_doc"
                }
                // 获取流程模板
                queryFlowList({flowKey:flowKeyWord}).then(res=>{
                  _this.template = res.data
                  let formData = _this.data.find(item=>item.bizType===bizType)
                  if (formData) {
                      formData.flowKey = formData.flowKey?formData.flowKey:_this.template?_this.template[0].dictValue:''
                      formData.bizType =bizType
                      _this.formData = formData
                  }else {
                      _this.formData.flowKey = _this.template?_this.template[0].dictValue:'',
                      _this.formData.bizType = bizType
                      _this.formData.nodeList = []
                  }
                    // _this.flowKeyUpdateMsg(_this.formData.flowKey,_this.formData.id)
                  _this.loading = false
                })
            },
            handleSelect(row){
                let _this = this
                _this.visible=true
                _this.row = row
                if(row.nodeDetailList){
                    _this.value = row.nodeDetailList.map(node=>node.code)
                }else {
                    _this.value = []
                }

            },
            submitForm(){
                let _this = this
                _this.visible=false
                let nodeDetailList = []
                _this.value.forEach(code=>{
                    let node =_this.dict.type.flow_node_fun_list.find(node=>node.value===code)
                    let funCondition = undefined
                    if (_this.row.nodeDetailList) {
                      let nodeDetail = _this.row.nodeDetailList.find(item=>item.code === code)
                      funCondition = nodeDetail&&nodeDetail.funCondition?nodeDetail.funCondition:undefined
                    }
                    nodeDetailList.push({
                        code: node.value,
                        name: node.label,
                        remark: _this.row.nodeName,
                        type: node.raw.remark,
                        sort: node.raw.dictSort,
                        funCondition: funCondition,
                    })
                })
                _this.row.nodeDetailList = nodeDetailList
            },
            saveDocClassFlow(){
                saveDocClassFlow(this.data).then((res)=>{
                    this.$modal.msgSuccess("设置成功");
                })
            },
            handleRefresh() { // 对接流程平台，同步最新的节点
              let _this = this
              syncFlowPlatNodeList(_this.id,_this.formData.bizType,_this.formData.flowKey,_this.formData.id === undefined ? "null" : _this.formData.id).then(res=>{
                this.$modal.msgSuccess("同步成功");
                _this.formData.nodeList=res.data
                _this.alertTitle = ''
              })
            },
          /** 转换菜单数据结构 */
          normalizer(node) {
            if (node.children && !node.children.length) {
              delete node.children;
            }
            return {
              id: node.id,
              label: node.className,
              children: node.children,
            };
          },
          handleCopy(){
            if (this.docClass) {
              this.getByDocClass(this.docClass)
            }else {
              this.$message.warning("请选择文件类型")
            }
          },
        },
    }
</script>

<style scoped>

</style>
