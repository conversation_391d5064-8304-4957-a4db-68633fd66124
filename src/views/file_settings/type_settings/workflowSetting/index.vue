<template>
  <div class="news-card" v-loading="loading">
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal"  @select="handleMenu">
      <el-menu-item v-for="menu in dict.type.sys_biz_type" :key="menu.value" :index="menu.value">{{menu.label}}</el-menu-item>
    </el-menu>
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="200px"
      >
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item label="是否启用:" prop="openFlag">
              <el-switch
                v-model.trim="formData.openFlag"
                active-color="#13ce66"
                active-value="Y"
                inactive-value="N"
                size="medium"
                inactive-color="#ff4949"
              >
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item label="流程配置:" prop="ruleId">
              <el-select
                v-model.trim="formData.ruleId"
                placeholder="请选择流程配置"
                clearable
                :style="{ width: '100%' }"
              >
                <el-option
                  v-for="(item, index) in typeDataList"
                  :key="index"
                  :label="item.flowName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item
              label="是否应用到子文件类型:"
              prop="applyFlag"
            >
              <el-radio-group
                v-model.trim="formData.applyFlag"
                size="medium"
              >
                <el-radio
                  v-for="(item, index) in dict.type.sys_yes_no"
                  :key="index"
                  :label="item.value"
                >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'
import { listDocClassFlow } from '@/api/setting/docClassFlow'

export default {
  name: 'DocClassSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no','sys_biz_type'],
  data() {
    return {
      loading: false,
      dataList: [],
      typeDataList: [],
      listCodeRuleOptions: [],
      activeIndex: 'add',
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      bizType: 'add',
      rules: {},
    }
  },
  created() {
    this.getDataList()
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    handleMenu(key){
      this.bizType = key
      this.filterDataList()
      this.getByDocClass()
    },
    getDataList(){
      listDocClassFlow({noClass:true}).then(res=>{
        this.dataList = res.rows
        this.filterDataList()
      });
    },
    filterDataList(){
      this.typeDataList = this.dataList.filter(item=>item.bizType===this.bizType)
    },
    reset() {
      this.formData = {
        id: undefined,
        type: this.bizType,
        docClass: this.id,
        ruleId: undefined,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:_this.bizType,docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
    },
    saveDocClassFlow(){
      let _this  = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      updateDocClassSetting(formData).then(res=>{
        this.$modal.msgSuccess("设置成功");
      })
    }
  }
}
</script>

<style scoped>

</style>
