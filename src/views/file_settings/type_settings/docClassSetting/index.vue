<template>
    <div class="news-card" v-loading="loading">
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否启用:" prop="openFlag">
                <el-switch
                  v-model.trim="formData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="版本号规则:" prop="ruleId">
                <el-select
                  v-model.trim="formData.ruleId"
                  placeholder="请选择文件编号规则"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in listCodeRuleOptions"
                    :key="index"
                    :label="item.ruleName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item
                label="是否应用到子文件类型:"
                prop="applyFlag"
              >
                <el-radio-group
                  v-model.trim="formData.applyFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in dict.type.sys_yes_no"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
</template>

<script>
import { getDocClassPurview, updateDocClassPurview } from '@/api/setting/docClassPurview'
import { listVersionRule } from '@/api/setting/versionRule'
import { getDocClassSetting, getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'

export default {
  name: 'DocClassSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      listCodeRuleOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      rules: {},
    }
  },
  created() {
    this.getRuleList()
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    getRuleList(){
      listVersionRule({}).then(res=>{
        this.listCodeRuleOptions=res.rows
      })
    },
    reset() {
      this.formData = {
        id: undefined,
        type: 'version',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:'version',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
    },
    savaDocClassSetting(){
       let _this  = this
       let formData = JSON.parse(JSON.stringify(_this.formData))
        updateDocClassSetting(formData).then(res=>{
          this.$modal.msgSuccess("设置成功");
        })
    }
  }
}
</script>

<style scoped>

</style>
