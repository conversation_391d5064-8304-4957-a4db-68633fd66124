<template>
  <div class="gray-card table-card no-padding">
    <el-row gutter="15">
      <el-col :span="24">
        <el-form-item
          label="合稿内容:"
          prop="applyFlag"
        >
          <el-button v-if="isShowAdd" type="primary" @click="handleAdd()">新增</el-button>
          <el-table :data="formData.detailList" border>
            <el-table-column label="合稿内容" align="left" prop="codraftName"/>
            <el-table-column label="附件" align="left" prop="fileId">
              <template slot-scope="scope">
                <el-link type="primary" @click="
                  handelefileLocalDownload(
                    scope.row.fileId,
                    scope.row.templateName
                  )
                ">{{ scope.row.templateName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column label="合稿位置" align="left" prop="position" width="200">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.setting_position" :value="scope.row.position"/>
              </template>
            </el-table-column>
            <el-table-column label="排序" align="left" prop="sort" width="200"/>
            <el-table-column label="操作" align="left" width="100" v-if="isShowAdd">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row,scope.$index)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-col>
    </el-row>

    <el-drawer
      title="新增"
      :append-to-body="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogVisible">
        <el-form
          ref="elForm"
          :model="newData"
          :rules="rules"
          size="medium"
          label-width="100px"
        >
          <el-form-item label="合稿内容" prop="codraftName">
            <el-input v-model="newData.codraftName" style="width: 400px;" maxlength="200" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="合稿附件" prop="fileId">
            <el-upload
              ref="field1041"
              :file-list="fileIdfileList"
              :http-request="appendixesUpload"
              :on-remove="handleRemoveAttachment"
            >
              <el-button 
                size="small" 
                type="primary" 
                icon="el-icon-upload" 
                v-loading="uploading" 
                :disabled="uploading"
              >
                点击上传
              </el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="合稿位置" prop="position">
            <el-select
              v-model.trim="newData.position"
              @change="positionChange"
              style="width: 400px;"
            >
              <el-option
                v-for="dict in dict.type.setting_position"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="newData.sort" type="number" style="width: 400px;" maxlength="7" show-word-limit></el-input>
          </el-form-item>
        </el-form>
       <div style="text-align: right;">
         <el-button type="primary" @click="saveAdd()">确 定</el-button>
         <el-button @click="dialogVisible = false">取 消</el-button>
       </div>
    </el-drawer>
  </div>
</template>

<script>
import { listVersionRule } from '@/api/setting/versionRule'
import { getDocClassSetting, getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'
import {fileLocalDownload, processFileLocalUpload} from "@/api/commmon/file";
import {addCodraft, delCodraft, listCodraft} from "@/api/setting/codraft";
import mixin from "@/layout/mixin/Commmon.js";

export default {
  name: 'DocClassSetting',
  components: {},
  props: {
    id: {
      type: String,
      required: false
    },
    bizId: {
      type: String,
      required: false
    },
    isShowAdd: {
      type: Boolean,
      default: true
    }
  },
  dicts: ['sys_yes_no', 'setting_position'],
  mixins: [mixin],
  data() {
    return {
      dialogVisible: false,
      loading: false,
      uploading: false,
      listCodeRuleOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openMerge: undefined,
        applyMerg: undefined,
        detailList: []
      },
      newData: {
        codraftName: '',
        position: '',
        sort: 1,
        fileId: '',
        templateName: '',
        classId: '',
        codraftStatus: '1'
      },
      rules: {
        codraftName: [
          { required: true, message: '合稿内容不能为空', trigger: 'blur' }
        ],
        fileId: [
          { required: true, message: '合稿附件不能为空', trigger: 'change' }
        ],
        position: [
          { required: true, message: '合稿位置不能为空', trigger: 'change' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (value && (value < 1 || value > 9999999)) {
                callback(new Error('排序必须在1到9999999之间'));
              } else {
                callback();
              }
            }, trigger: 'blur' }
        ]
      },
      fileIdfileList: [],
      jointQueryParams: {
        pageNum: 1,
        pageSize: 10,
        classId: '',
        deleteFlag: 0
      }
    }
  },
  mounted() {
    this.getRuleList()
    this.getByDocClass()
    this.getList()
  },
  methods: {
    //下载文件
    handelefileLocalDownload(id, name) {
      fileLocalDownload(id).then((res) => {
        this.saveFile(res, name);
      });
    },
    positionChange(val){
      this.newData.position = val
    },
    getList(){
      let _this = this
      _this.loading = true
      if(_this.isNull(_this.id) && _this.isNull(_this.bizId)){
        return;
      }
      _this.jointQueryParams.classId = _this.id
      _this.jointQueryParams.bizId = _this.bizId
      listCodraft(_this.jointQueryParams).then(res => {
        if (res.code == 200) {
          _this.$set(_this.formData, 'detailList', res.rows)
          _this.$emit('changeFormatData', _this.formData.detailList);
          this.dialogVisible = false
        }
        _this.loading = false
      })
    },
    resetForm() {
      this.newData =  {
        codraftName: '',
        position: '',
        sort: 1,
        fileId: '',
        templateName: '',
        classId: '',
        codraftStatus: '1'
      }
      this.fileIdfileList = [];
    },
    handleDelete(row, index) {
      let _this = this;
      this.$confirm('删除后不可恢复，您确认要删除吗？').then(item => {
        _this.loading = true;
        _this.formData.detailList.splice(index, 1);
        _this.$emit('changeFormatData', _this.formData.detailList);
        _this.loading = false;
      }).catch(err => {console.error(err)});
    },
    saveAdd(){
      let _this = this
      _this.loading = true
      _this.newData.classId = _this.id
      if (!_this.formData.detailList) {
        _this.formData.detailList = []
      }
      /*const isPositionExists = this.formData.detailList.some(dataItem => dataItem.position === this.newData.position)
      if (isPositionExists) {
        //使用dict.type.setting_position找到this.newData.position对应的label,用于提示
        let positionLabel = this.dict.type.setting_position.find(item => item.value === this.newData.position).label;
        this.$message.error(`合稿位置：${positionLabel}已存在，请选择其他位置`) // 提示错误信息
        _this.loading = false; // 停止 loading
        return; // 结束函数执行
      }*/
      // 校验表单
      this.$refs.elForm.validate(valid => {
        if (valid) {
          _this.formData.detailList.push({..._this.newData});
          _this.$emit('changeFormatData', _this.formData.detailList);
          this.dialogVisible = false
          this.resetForm()
        } else {
          _this.loading = false; // 校验失败时停止 loading
        }
      });
    },
    handleAdd(){
      this.dialogVisible = true
      this.resetForm()
    },
    appendixesUpload(params) {
      this.uploading = true;
      this.fileIdfileList = [];
      let fd = new FormData();
      fd.append("file", params.file); //传文件
      processFileLocalUpload(fd).then((res) => {
        this.fileIdfileList.push({
          name: res.data.fileName,
          url: res.data.fileId,
        });
        this.newData.fileId = res.data.fileId;
        this.newData.templateName = res.data.fileName;
        this.uploading = false;
      }).catch(() => {
        this.uploading = false;
      });
    },
    // 删除附件
    handleRemoveAttachment(file, fileList) {
      this.fileIdfileList = this.fileIdfileList.filter(
        (item) => item.url !== file.url
      );
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    getRuleList(){
      listVersionRule({}).then(res=>{
        this.listCodeRuleOptions=res.rows
      })
    },
    reset() {
      this.formData = {
        id: undefined,
        type: 'version',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      if(_this.isNull(_this.id)){
        return;
      }
      getInfoBy({type:'version',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
    },
    /** 返回 **/
    isNull(value){
      if(value == null || value == undefined || value == ''){
        return true
      }
      return false
    },
    /** 获取合稿数据 **/
    getCombinedData(bizId){
      this.bizId = bizId
      this.getList()
    }
  }
}
</script>

<style scoped>

</style>
