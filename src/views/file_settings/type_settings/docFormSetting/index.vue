<template>
    <div class="news-card" v-loading="loading">
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="工厂、物料编码、物料描述展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>


        <el-form
          ref="elForm"
          :model="formCustomerData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="客户编码展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formCustomerData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>


        <el-form
          ref="elForm"
          :model="formDeviceData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="设备编码、设备名称展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formDeviceData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="formProductVersionData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="产品版本展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formProductVersionData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="shelfLifeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="保存期限展示:" prop="openFlag">
                <el-switch
                  v-model.trim="shelfLifeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="codeAndTypeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="名称拼接（物料编码+文件类型）:" prop="openFlag">
                <el-switch
                  v-model.trim="codeAndTypeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
</template>

<script>
import { listVersionRule } from '@/api/setting/versionRule'
import { getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'

export default {
  name: 'DocFormSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      listCodeRuleOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      formCustomerData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      formDeviceData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      formProductVersionData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      shelfLifeData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      codeAndTypeData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      rules: {},
    }
  },
  created() {
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    reset() {
      this.formData = {
        id: undefined,
        type: 'formShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formCustomerData = {
        id: undefined,
        type: 'formCustomerShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formDeviceData = {
        id: undefined,
        type: 'formDeviceShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formProductVersionData = {
        id: undefined,
        type: 'formProductVersionShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.shelfLifeData = {
        id: undefined,
        type: 'shelfLifeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.codeAndTypeData = {
        id: undefined,
        type: 'codeAndTypeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:'formShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'formCustomerShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formCustomerData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'formDeviceShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formDeviceData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'formProductVersionShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formProductVersionData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'shelfLifeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.shelfLifeData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'codeAndTypeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.codeAndTypeData = res.data
        }
        _this.loading = false
      })
    },
    savaDocFormSetting(){
       let _this  = this
      _this.loading = true
       let formData = JSON.parse(JSON.stringify(_this.formData))
        updateDocClassSetting(formData).then(res=>{
          this.$modal.msgSuccess("设置成功");
          this.getByDocClass()
        })
        let formCustomerData = JSON.parse(JSON.stringify(_this.formCustomerData))
        updateDocClassSetting(formCustomerData).then(res=>{
        })
        let formDeviceData = JSON.parse(JSON.stringify(_this.formDeviceData))
        updateDocClassSetting(formDeviceData).then(res=>{
        })
        let formProductVersionData = JSON.parse(JSON.stringify(_this.formProductVersionData))
        updateDocClassSetting(formProductVersionData).then(res=>{
        })
        let shelfLifeData = JSON.parse(JSON.stringify(_this.shelfLifeData))
        updateDocClassSetting(shelfLifeData).then(res=>{
        })
        let codeAndTypeData = JSON.parse(JSON.stringify(_this.codeAndTypeData))
        updateDocClassSetting(codeAndTypeData).then(res=>{
        })
       setTimeout(() => {
        _this.loading = false;
       }, 1000);
    }
  }
}
</script>

<style scoped>

</style>
