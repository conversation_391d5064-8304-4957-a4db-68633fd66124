<template>
  <div class="news-card" v-loading="loading">
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="200px"
      >
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item label="是否启用:" prop="openFlag">
              <el-switch
                v-model.trim="formData.openFlag"
                active-color="#13ce66"
                active-value="Y"
                inactive-value="N"
                size="medium"
                inactive-color="#ff4949"
              >
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item label="表单规则:" prop="ruleId">
              <el-select
                v-model.trim="formData.ruleId"
                placeholder="请选择表单规则"
                clearable
                :style="{ width: '100%' }"
              >
                <el-option
                  v-for="(item, index) in listFormRuleOptions"
                  :key="index"
                  :label="item.ruleName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="15">
          <el-col :span="24">
            <el-form-item
              label="是否应用到子文件类型:"
              prop="applyFlag"
            >
              <el-radio-group
                v-model.trim="formData.applyFlag"
                size="medium"
              >
                <el-radio
                  v-for="(item, index) in dict.type.sys_yes_no"
                  :key="index"
                  :label="item.value"
                >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row gutter="15">
          <el-form-item label="规则明细:">

            <div class="gray-card table-card no-padding">
              <el-row :key="item.value" v-for="item in formItems">
                <el-col :span="12">
                  <el-form-item :key="item.value+'_form'" :label="item.label">
                    <el-switch disabled v-model="item.show" :key="item.value+'_show'" active-text="是否显示" style="margin-right:5px; line-height: 20px;" active-value="Y" inactive-value="N"></el-switch>
                    <el-switch disabled v-model="item.required" :key="item.value+'_require'" active-text="是否必填" style="margin-right:5px; line-height: 20px;" active-value="Y" inactive-value="N"></el-switch>
                    <span>默认值：</span>
                    <el-input disabled v-model="item.defaultValue" style="width: 100px; margin-right:5px;"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { listFormRule} from '@/api/setting/formRule'
import { getDocClassSetting, getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'

export default {
  name: 'DocClassSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      listFormRuleOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      rules: {},
    }
  },
  created() {
    this.getRuleList()
  },
  mounted() {
    this.getByDocClass()
  },
  computed: {
    formItems() {
      let item = this.listFormRuleOptions.find(item =>item.id == this.formData.ruleId)
      if(item&&item.ruleDetails){
        return JSON.parse(item.ruleDetails)
      }else{
        return []
      }
    }
  },
  methods: {
    getRuleList(){
      listFormRule({}).then(res=>{
        this.listFormRuleOptions=res.rows
      })
    },
    reset() {
      this.formData = {
        id: undefined,
        type: 'formShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:'formShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
    },
    savaDocFormSetting(){
      let _this  = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      updateDocClassSetting(formData).then(res=>{
        this.$modal.msgSuccess("设置成功");
      })
    }
  }
}
</script>

<style scoped>

</style>
