<template>
  <div class="gray-card table-card no-padding">
    <el-row gutter="15">
      <el-col :span="24">
        <el-form-item
          label="合稿内容:"
          prop="applyFlag"
        >
          <el-button type="primary" @click="handleAdd()">新增</el-button>
          <el-table :data="formData.detailList" border>
            <el-table-column label="合稿内容" align="left" prop="codraftName"/>
            <el-table-column label="附件" align="left" prop="fileId">
              <template slot-scope="scope">
                <el-link type="primary" @click="
                  handelefileLocalDownload(
                    scope.row.fileId,
                    scope.row.templateName
                  )
                ">{{ scope.row.templateName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column label="合稿位置" align="left" prop="position" width="200">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.setting_position" :value="scope.row.position"/>
              </template>
            </el-table-column>
            <el-table-column label="排序" align="left" prop="sort" width="200"/>
            <el-table-column label="操作" align="left" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row,scope.$index)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-col>
    </el-row>

    <el-drawer
      title="新增"
      :append-to-body="true"
      :before-close="handleClose"
      :visible.sync="dialogVisible">
        <el-form
          ref="elForm"
          :model="newData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-form-item label="合稿内容">
            <el-input v-model="newData.codraftName"></el-input>
          </el-form-item>
          <el-form-item label="合稿附件">
            <el-upload
              ref="field1041"
              :file-list="fileIdfileList"
              :http-request="appendixesUpload"
              :on-remove="handleRemoveAttachment"
            >
              <el-button size="small" type="primary" icon="el-icon-upload"
              >点击上传
              </el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="合稿位置">
            <el-select
              v-model.trim="formData.position"
              @change="positionChange"
            >
              <el-option
                v-for="dict in dict.type.setting_position"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序">
            <el-input v-model="newData.sort" type="number"></el-input>
          </el-form-item>
        </el-form>
       <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveAdd()">确 定</el-button>
       </span>
    </el-drawer>
  </div>
</template>

<script>
import { getDocClassPurview, updateDocClassPurview } from '@/api/setting/docClassPurview'
import { listVersionRule } from '@/api/setting/versionRule'
import { getDocClassSetting, getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'
import {fileLocalDownload, processFileLocalUpload} from "@/api/commmon/file";
import {addCodraft, delCodraft, listCodraft} from "@/api/setting/codraft";
import mixin from "@/layout/mixin/Commmon.js";

export default {
  name: 'DocClassSetting',
  components: {},
  props: ['id', 'applyMerge', 'openMerge'],
  dicts: ['sys_yes_no', 'setting_position'],
  mixins: [mixin],
  data() {
    return {
      dialogVisible: false,
      loading: false,
      listCodeRuleOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openMerge: undefined,
        applyMerg: undefined,
        detailList: []
      },
      detailList: [],
      newData: {
        codraftName: '',
        position: '',
        sort: 1,
        fileId: '',
        templateName: '',
        classId: '',
        codraftStatus: '1'
      },
      rules: {},
      fileIdfileList: [],
      jointQueryParams: {
        pageNum: 1,
        pageSize: 10,
        classId: '',
        deleteFlag: 0
      }
    }
  },
  mounted() {
    this.getRuleList()
    this.formData.applyMerge = this.applyMerge
    this.formData.openMerge = this.openMerge
    this.getByDocClass()
    this.getList()
  },
  methods: {
    //下载文件
    handelefileLocalDownload(id, name) {
      fileLocalDownload(id).then((res) => {
        //console.log("file", res);
        this.saveFile(res, name);
      });
    },
    openChage(val){
      this.$emit('openChageParent',val)
    },
    appChage(val){
      this.$emit('applyChageParent',val)
    },
    positionChange(val){
      this.newData.position = val
    },
    getList(){
      let _this = this
      _this.loading = true
      _this.jointQueryParams.classId = _this.id
      listCodraft(_this.jointQueryParams).then(res => {
        if (res.code == 200) {
          _this.$set(_this.formData, 'detailList', res.rows)
          this.dialogVisible = false
        }
        _this.loading = false
      })
    },
    resetForm() {
      this.newData =  {
        codraftName: '',
        position: '',
        sort: 1,
        fileId: '',
        templateName: '',
        classId: '',
        codraftStatus: '1'
      }
    },
    handleDelete (row, index){
      let _this = this
      this.$confirm('确认关闭？').then(_ => {
        _this.loading = true
        delCodraft(row.id).then(async res => {
          if (res.code == 200) {
            this.getList()
          }
          _this.loading = false
        })
      })
      .catch(_ => {});
    },
    saveAdd(){
      let _this = this
      _this.loading = true
      _this.newData.classId = _this.id
      addCodraft(_this.newData).then(async res => {
        if (res.code == 200) {
          this.getList()
          this.dialogVisible = false
          this.resetForm()
        }
        _this.loading = false
      })
    },
    handleAdd(){
      this.dialogVisible = true
    },
    appendixesUpload(params) {
      this.fileIdfileList = [];
      let fd = new FormData();
      fd.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      processFileLocalUpload(fd).then((res) => {
        this.fileIdfileList.push({
          name: res.data.fileName,
          url: res.data.fileId,
        });
        this.newData.fileId = res.data.fileId;
        this.newData.templateName = res.data.fileName;
      });
    },
    // 删除附件
    handleRemoveAttachment(file, fileList) {
      this.fileIdfileList = this.fileIdfileList.filter(
        (item) => item.url !== file.url
      );
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    getRuleList(){
      listVersionRule({}).then(res=>{
        this.listCodeRuleOptions=res.rows
      })
    },
    reset() {
      this.formData = {
        id: undefined,
        type: 'version',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:'version',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
