<template>
  <div class="card-head" v-loading="loading">
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal"  @select="handleMenu">
      <el-menu-item v-for="menu in menuList" :key="menu.index" :index="menu.index">{{menu.label}}</el-menu-item>
    </el-menu>
      <el-card class="gray-card table-card no-padding">
        <el-form ref="elForm" :model="formData" :rules="rules" label-position="left" label-width="200px">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否启用:" prop="openFlag">
                <el-switch v-model.trim="formData.openFlag" active-color="#13ce66" active-value="Y" inactive-value="N" size="medium" inactive-color="#ff4949">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否应用到子文件类型:" prop="applyFlag">
                <el-radio-group v-model.trim="formData.applyFlag" size="medium">
                  <el-radio v-for="(item, index) in dict.type.sys_yes_no" :key="index" :label="item.value" :disabled="item.disabled">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="水印规则:" prop="ruleId">
                <el-select 
                  v-model="formData.ruleId" 
                  placeholder="请选择水印规则" 
                  style="width: 100%"
                  clearable 
                  @change="handleRuleChange">
                  <el-option 
                    v-for="item in watermarkRules" 
                    :key="item.id" 
                    :label="item.ruleName"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="文件水印设置:" prop="signatureType">
                <div style="width: 100%; overflow: auto;">
                  <el-button class="el-icon-plus" type="primary" @click="addItem()" style="position: absolute;left: 1250px;"></el-button>
                  <div>
                    <span style="margin: 0 40px">序号</span>
                    <span style="margin: 0 60px">名称</span>
                    <span style="margin: 0 60px">是否选择</span>
                    <span style="margin: 0 90px">样式</span>
                    <span style="margin: 0 150px">位置</span>
                    <span style="margin: 0 75px">范围</span>
                  </div>
                  <draggable
                    :options="{
                            group: 'people',
                            animation: 150,
                            ghostClass: 'sortable-ghost',
                            chosenClass: 'chosenClass',
                            scroll: true,
                            scrollSensitivity: 200,
                          }"
                    v-model.trim="formData.nodeList" @start="start" @end="end" :move="move"
                    style="display: inline-block;width: 1200px;height: 350px; background: #eee;overflow: auto;">

                    <div v-for="(val, key) in formData.nodeList" :key="key" class="fromdynamicItem">
                      <el-form-item>
                        <span style="margin: 0 40px">{{ key + 1 }}</span>
                      </el-form-item>
                      <el-form-item>
                        <el-select v-model.trim="val.watermarkSettingCode" placeholder="请选择名称" clearable :style="{ width: '100%' }"  @change="changeWatermarkSettingName($event, val)">
                          <el-option v-for="(changeval, changekey) in dict.type.watermark_type" :key="changekey" :label="changeval.label"
                                     :value="changeval.value" :disabled="changeval.disabled">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item>
                        <el-select v-model.trim="val.watermarkSettingFactorUse" placeholder="请选择规则值" clearable :style="{ width: '100%' }">
                          <el-option v-for="(changeval, changekey) in dict.type.yes_no" :key="changekey" :label="changeval.label"
                                     :value="parseInt(changeval.value)" :disabled="changeval.disabled">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item>
                        <el-input v-if="val.watermarkSettingType === 'text'" style="width: 200px"
                          v-model.trim="val.typeSize"
                          placeholder="请输入字号输入"
                          clearable
                          class="input-with-select"></el-input>
                        <template v-else>
                          <el-input style="width: 200px"
                                    v-model.trim="val.scale"
                                    @input="handleInput($event, val)"
                                    placeholder="请输入规模"
                                    clearable
                                    class="input-with-select"></el-input>
                        </template>
                      </el-form-item>

                      <el-form-item>
                          <el-input style="width: 50%"
                            v-model.number.trim="val.xposition"
                            placeholder="请输入X"
                            clearable
                            class="input-with-select"></el-input>
                          <el-input style="width: 50%"
                            v-model.number.trim="val.yposition"
                            placeholder="请输入Y"
                            clearable
                            class="input-with-select"></el-input>
                      </el-form-item>

                      <el-form-item>
                        <el-select v-model.trim="val.appliedRange" placeholder="请选择应用范围" clearable :style="{ width: '50%' }">
                          <el-option v-for="(changeval, changekey) in dict.type.applied_range" :key="changekey" :label="changeval.label"
                                     :value="changeval.value" :disabled="changeval.disabled">
                          </el-option>
                        </el-select>
                        <el-select v-model.trim="val.pageOption" placeholder="请选择页面范围" clearable :style="{ width: '50%' }">
                          <el-option v-for="(changeval, changekey) in dict.type.watermark_page_option" :key="changekey" :label="changeval.label"
                                     :value="changeval.value" :disabled="changeval.disabled">
                          </el-option>
                        </el-select>
                      </el-form-item>

                      <el-form-item>
                        <i class="el-icon-rank" style="margin: 0 5px"></i>
                        <i class="el-icon-delete" v-show="val.defaultFlag != 1" @click="deleteQianzhan(val, key)" style="margin: 0 5px"></i>
                        <i class="el-icon-plus" @click="addItem"></i>
                      </el-form-item>
                    </div>
                  </draggable>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </el-card>
    </div>
</template>

<script>
import {getWatermarkSettingList, savaWatermarkSetting} from "../../../../api/setting/docWatermarkSetting";
import {listAllWatermarkRule} from "@/api/setting/watermarkRule";
import draggable from "vuedraggable";
export default {
    name: "watermarkSetting",
    props: ['id'],
    components: { draggable },
    dicts: ['sys_yes_no','watermark_type','yes_no','applied_range','watermark_page_option'],
    data() {
        return {
          loading: false,
          // 变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废  PRINT 打印
          menuList:[{
              index: 'ADD',
              label: '文件新增',
          },{
              index: 'UPDATE',
              label: '文件修订',
          },{
              index: 'DISUSE',
              label: '文件作废',
          },{
            index: 'PRINT',
            label: '文件打印',
          },{
            index: 'RETAIN',
            label: '文件留用',
          }],
          formData: {
            openFlag: 'N',
            applyFlag: 'N',
            nodeList: []
          },
          activeIndex: 'ADD',
          rules:{

          },
          watermarkRules: [], // 水印规则列表
        }
    },
    mounted(){
      this.getWatermarkSettingList()
      this.getWatermarkRules()
    },
    methods: {
      handleInput(value,val) {
        // 使用正则表达式过滤输入内容
        const regex = /^\d+\.?\d*$/;
        if (!regex.test(value)) {
          val.scale = ''
        }
      },
      handleMenu(key){
        this.activeIndex = key
        this.changeTemplate(key)
      },
      getWatermarkSettingList(){
        let _this = this
        _this.loading = true
        getWatermarkSettingList(this.id).then(res=>{
          if (res.data&&res.data.length>0) {
            _this.data = res.data
          }else {
            let data= []
            _this.menuList.forEach(menu=>{
              data.push({
                docClass: _this.id,
                openFlag: 'N',
                applyFlag: 'N',
                flowKey: undefined,
                bizType: menu.index,
                nodeList: []})
            })
            _this.data = data
          }
          _this.changeTemplate(this.activeIndex)
        })
      },
      changeTemplate(key){
        let _this = this
        _this.loading = true
        _this.formData = {}
        // 获取流程模板
        let formData = _this.data.find(item=>item.bizType===key)
        if (formData) {
          _this.formData = formData
        }else {
          _this.formData = {
            docClass: _this.id,
            openFlag: 'N',
            applyFlag: 'N',
            flowKey: undefined,
            bizType: key,
            nodeList: []
          }
          _this.data.push(_this.formData)
        }
        _this.loading = false
      },
      deleteQianzhan(item, index) {
        this.formData.nodeList.splice(index, 1);
        // if (item.id != undefined) {
        //   settingdocClassSignaturedelete(item.id);
        // }
      },
      addItem() {
        if (this.formData.nodeList == null) {
          this.formData.nodeList = []
        }
        this.formData.nodeList.push({
          watermarkSettingType: "",
          watermarkSettingName: "",
          watermarkSettingCode: "",
          watermarkSettingFactorUse: 2,
          xposition: undefined,
          yposition: undefined,
          appliedRange: undefined,
          pageOption: undefined,
          bizId: this.formData.id
        })
      },
      savaWatermarkSetting(){
        // 校验名称是否存在重复数据
        for (const item of this.data) {
          let menuData = this.menuList.find(item1=>item1.index===item.bizType)
          let watermarkSettingCodeArr = item.nodeList.map(item => item['watermarkSettingCode'])
          let watermarkSettingCodeArrSet = new Set(watermarkSettingCodeArr)
          if(watermarkSettingCodeArrSet.size !== watermarkSettingCodeArr.length){
            this.$message.error(menuData.label +'页签存在名称重复项，请核对！')
            return
          }
          // 校验名称是否存在空数据
          for (const item1 of item.nodeList) {
            if(item1.watermarkSettingCode===''){
              this.$message.error(menuData.label +'页签存在名称项为空，请核对！')
              return
            }
            // 检查是否选择字段是否为空
            if(!item1.watermarkSettingFactorUse || item1.watermarkSettingFactorUse===''){
              this.$message.error(menuData.label +'页签存在'+item1.watermarkSettingName+'行是否选择项为空，请核对！')
              return
            }
            // 检查样式字段是否为空
            let isYangshi = false
            if(item1.watermarkSettingType ==='text'){
              if(!item1.typeSize || item1.typeSize ===''){
                isYangshi = true
              }
            }else{
              if(!item1.scale || item1.scale === 0){
                isYangshi = true
              }
            }
            if(isYangshi){
              this.$message.error(menuData.label +'页签存在'+item1.watermarkSettingName+'行样式项为空，请核对！')
              return
            }
            // 检查是否位置是否为空
            if(!item1.xposition || !item1.yposition || (item1.xposition === '' || item1.yposition === '')){
              this.$message.error(menuData.label +'页签存在'+item1.watermarkSettingName+'行位置项为空，请核对！')
              return
            }
            // 检查应用范围字段是否为空
            if(!item1.appliedRange || item1.appliedRange===''){
              this.$message.error(menuData.label +'页签存在'+item1.watermarkSettingName+'行应用范围项为空，请核对！')
              return
            }
            // 检查页面范围字段是否为空
            if(!item1.pageOption || item1.pageOption===''){
              this.$message.error(menuData.label +'页签存在'+item1.watermarkSettingName+'行页面范围项为空，请核对！')
              return
            }
          }
        }
        savaWatermarkSetting(this.data).then((res)=>{
          this.$modal.msgSuccess("设置成功");
        })
      },
      changeWatermarkSettingName(val,item){
        let selectWatermarkType = this.dict.type.watermark_type.find(item=>item.value===val)
        // 选中水印名称赋值
        item.watermarkSettingCode = val
        item.watermarkSettingName = selectWatermarkType.label
        // 设置watermarkSettingType值（水印类型，字典中带image是图片，否则是文本）
        if(val.indexOf('image')>= 0){
          item.watermarkSettingType = 'image'
          item.typeSize = ''
        }else{
          item.watermarkSettingType = 'text'
          item.scale = ''
        }
      },
      // 获取水印规则列表
      async getWatermarkRules() {
        try {
          const res = await listAllWatermarkRule() // 需要添加相应的 API 方法
          this.watermarkRules = res.data || []
        } catch (error) {
          console.error('获取水印规则失败:', error)
        }
      },

      // 水印规则变更处理
      handleRuleChange(ruleId) {
        if (!ruleId) {
          this.formData.nodeList = []
          return
        }
        console.log("ruleId",ruleId)
        const selectedRule = this.watermarkRules.find(rule => rule.id === ruleId)
        console.log("selectedRule",selectedRule)
        if (selectedRule && selectedRule.ruleDetails) {
          const ruleDetails = JSON.parse(selectedRule.ruleDetails)
          // 将规则详情转换为nodeList格式
          this.formData.nodeList = ruleDetails.map(detail => ({
            watermarkSettingType: detail.watermarkSettingType,
            watermarkSettingName: detail.watermarkSettingName,
            watermarkSettingCode: detail.watermarkSettingCode,
            watermarkSettingFactorUse: detail.watermarkSettingFactorUse,
            xposition: detail.xposition,
            yposition: detail.yposition,
            appliedRange: detail.appliedRange,
            pageOption: detail.pageOption,
            scale: detail.scale,
            typeSize: detail.typeSize
          }))
        }
      }
    }
}
</script>

<style scoped>

</style>
