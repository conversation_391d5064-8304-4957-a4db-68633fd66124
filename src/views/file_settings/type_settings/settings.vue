<template>
  <div class="document_change_add settings">
    <div class="rz-button-handel-90">
      <el-button type="primary" @click="submitForm(activeIndex)" v-loading="submitLoading" :disabled="submitLoading"
        >提交</el-button
      >
      <el-button @click="topclose">关闭</el-button>
    </div>
    <el-card class="news-card no-padding">
      <el-tabs
        v-model.trim="activeIndex"
        class="news-tabs"
        @tab-click="handleSelect"
      >
        <el-tab-pane name="2">
          <span slot="label">文件编号设置</span>
          <div class="news-card">
            <el-card class="gray-card table-card no-padding">
              <el-form
                ref="elForm"
                :model="formData"
                :rules="rules"
                size="medium"
                label-width="200px"
                v-show="activeIndex == '2'"
              >
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="是否启用:" prop="ynopenCode">
                      <el-switch
                        v-model.trim="formData.ynopenCode"
                        active-color="#13ce66"
                        active-value="'true'"
                        inactive-value="'false'"
                        size="medium"
                        inactive-color="#ff4949"
                      >
                      </el-switch>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="文件编号规则:" prop="codeId">
                      <el-select
                        v-model.trim="formData.codeId"
                        placeholder="请选择文件编号规则"
                        clearable
                        :style="{ width: '100%' }"
                      >
                        <el-option
                          v-for="(item, index) in listCodeRuleOptions"
                          :key="index"
                          :label="item.ruleName"
                          :value="item.id"
                          :disabled="item.disabled"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item
                      label="是否应用到子文件类型:"
                      prop="ynapplyCode"
                    >
                      <el-radio-group
                        v-model.trim="formData.ynapplyCode"
                        size="medium"
                      >
                        <el-radio
                          v-for="(item, index) in dict.type.yes_no"
                          :key="index"
                          :label="item.value"
                          :disabled="item.disabled"
                          >{{ item.label }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>

<!--        <el-tab-pane name="3">-->
<!--          <span slot="label">文件分发号设置</span>-->
<!--          <div class="news-card">-->
<!--            <el-card class="gray-card table-card no-padding">-->
<!--              <el-form-->
<!--                ref="elForm"-->
<!--                :model="formData"-->
<!--                :rules="rules"-->
<!--                size="medium"-->
<!--                label-width="200px"-->
<!--                v-show="activeIndex == '3'"-->
<!--              >-->
<!--                <el-row gutter="15">-->
<!--                  <el-col :span="24">-->
<!--                    <el-form-item label="是否启用:" prop="yNOpenDistribute">-->
<!--                      <el-switch-->
<!--                        v-model.trim="formData.openDistribute"-->
<!--                        active-color="#13ce66"-->
<!--                        active-value="'true'"-->
<!--                        inactive-value="'false'"-->
<!--                        size="medium"-->
<!--                        inactive-color="#ff4949"-->
<!--                      >-->
<!--                      </el-switch>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row gutter="15">-->
<!--                  <el-col :span="24">-->
<!--                    <el-form-item label="文件分发号规则:" prop="distributeId">-->
<!--                      <el-select-->
<!--                        v-model.trim="formData.distributeId"-->
<!--                        placeholder="请选择文件分发号规则"-->
<!--                        clearable-->
<!--                        :style="{ width: '100%' }"-->
<!--                      >-->
<!--                        <el-option-->
<!--                          v-for="(item, index) in listCodeRuleOptions"-->
<!--                          :key="index"-->
<!--                          :label="item.ruleName"-->
<!--                          :value="item.id"-->
<!--                          :disabled="item.disabled"-->
<!--                        ></el-option>-->
<!--                      </el-select>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row gutter="15">-->
<!--                  <el-col :span="24">-->
<!--                    <el-form-item-->
<!--                      label="是否应用到子文件类型:"-->
<!--                      prop="reviewCycle"-->
<!--                    >-->
<!--                      <el-radio-group-->
<!--                        v-model.trim="formData.applyDistribute"-->
<!--                        size="medium"-->
<!--                      >-->
<!--                        <el-radio-->
<!--                          v-for="(item, index) in dict.type.yes_no"-->
<!--                          :key="index"-->
<!--                          :label="item.value"-->
<!--                          :disabled="item.disabled"-->
<!--                          >{{ item.label }}</el-radio-->
<!--                        >-->
<!--                      </el-radio-group>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--              </el-form>-->
<!--            </el-card>-->
<!--          </div>-->
<!--        </el-tab-pane>-->

        <el-tab-pane name="1">
          <span slot="label">文件时效设置</span>
          <div class="news-card">
            <el-card class="gray-card table-card no-padding">
              <el-form
                ref="elForm"
                :model="formData"
                :rules="rules"
                size="medium"
                label-width="200px"
                v-show="activeIndex == '1'"
              >
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="是否启用:" prop="openPrescription">
                      <el-switch
                        v-model.trim="formData.openPrescription"
                        active-color="#13ce66"
                        active-value="'true'"
                        inactive-value="'false'"
                        size="medium"
                        inactive-color="#ff4949"
                      >
                      </el-switch>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">


                    <el-form-item label="文件有效期（月）:" prop="expiration">
                      <el-input v-model.number.trim="formData.expiration" size="medium" style="width: 20%" placeholder="请输入文件有效期"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item
                      label="是否应用到子文件类型:"
                      prop="applyPrescription"
                    >
                      <el-radio-group
                        v-model.trim="formData.applyPrescription"
                        size="medium"
                      >
                        <el-radio
                          v-for="(item, index) in dict.type.yes_no"
                          :key="index"
                          :label="item.value"
                          :disabled="item.disabled"
                          >{{ item.label }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="是否启用:" prop="openReview">
                      <el-switch
                        v-model.trim="formData.openReview"
                        active-color="#13ce66"
                        active-value="'true'"
                        inactive-value="'false'"
                        size="medium"
                        inactive-color="#ff4949"
                      >
                      </el-switch>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="文件复审周期（月）:" prop="reviewCycle">
                      <el-input v-model.number.trim="formData.reviewCycle" size="medium" style="width: 20%" placeholder="请输入文件复审周期"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item
                      label="是否应用到子文件类型:"
                      prop="applyReview"
                    >
                      <el-radio-group
                        v-model.trim="formData.applyReview"
                        size="medium"
                      >
                        <el-radio
                          v-for="(item, index) in dict.type.yes_no"
                          :key="index"
                          :label="item.value"
                          :disabled="item.disabled"
                          >{{ item.label }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane name="5">
          <span slot="label">文件合稿设置</span>
          <div class="news-card">
            <el-card class="gray-card table-card no-padding">
              <el-form
                ref="elForm"
                :model="formData"
                :rules="rules"
                size="medium"
                label-width="200px"
              >
                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="是否启用:" prop="openFlag">
                      <el-switch
                        v-model.trim="formData.openMerge"
                        active-color="#13ce66"
                        active-value="Y"
                        inactive-value="N"
                        size="medium"
                        inactive-color="#ff4949"
                      >
                      </el-switch>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item
                      label="是否应用到子文件类型:"
                      prop="applyFlag"
                    >
                      <el-radio-group
                        v-model.trim="formData.applyMerge"
                        size="medium"
                      >
                        <el-radio
                          v-for="(item, index) in dict.type.yes_no"
                          :key="index"
                          :label="item.value"
                        >{{ item.label }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row gutter="15">
                  <el-col :span="24">
                    <el-form-item label="合稿:" prop="combinedMgrId">
                      <el-select
                        v-model="formData.combinedMgrId"
                        placeholder="请选择合稿"
                        style="width: 100%"
                        clearable
                        @change="handleCombinedChange">
                        <el-option
                          v-for="item in combinedMgrData"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

<!--                @changeFormatData="combinedChangeFormatData"
:id="id"-->

                <combined-setting ref="combinedSetting"
                                  :isShowAdd="false"
                                  :bizId="formData.combinedMgrId"
                                  @changeFormatData="combinedChangeFormatData"
                ></combined-setting>
<!--                <joint-setting ref="jointSetting"-->
<!--                               :id="id"-->
<!--                               :applyMerge="formData.applyMerge"-->
<!--                               :openMerge="formData.openMerge"-->
<!--                               @openChageParent="openChageParent"-->
<!--                               @applyChageParent="applyChageParent"-->
<!--                ></joint-setting>-->
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>

<!--        <el-tab-pane name="6">-->
<!--          <span slot="label">文件签章设置</span>-->
<!--          <div class="card-head">-->
<!--            <el-card class="gray-card table-card no-padding">-->
<!--              <el-form-->
<!--                ref="elForm"-->
<!--                :model="formData"-->
<!--                :rules="rules"-->
<!--                size="medium"-->
<!--                label-width="200px"-->
<!--                v-show="activeIndex == '6'"-->
<!--              >-->
<!--                <el-row gutter="15">-->
<!--                  <el-col :span="24">-->
<!--                    <el-form-item label="是否启用:" prop="openSignature">-->
<!--                      <el-switch-->
<!--                        v-model.trim="formData.openSignature"-->
<!--                        active-color="#13ce66"-->
<!--                        active-value="'true'"-->
<!--                        inactive-value="'false'"-->
<!--                        size="medium"-->
<!--                        inactive-color="#ff4949"-->
<!--                      >-->
<!--                      </el-switch>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row gutter="15">-->
<!--                  <el-col :span="24">-->
<!--                    &lt;!&ndash; <el-form-item label="签章类型:">-->
<!--                      <el-radio-group-->
<!--                        v-model.trim="formData.encryptType"-->
<!--                        size="medium"-->
<!--                      >-->
<!--                        <el-radio-->
<!--                          v-for="(item, index) in dict.type.encrypt_type"-->
<!--                          :key="index"-->
<!--                          :label="item.value"-->
<!--                          :disabled="item.disabled"-->
<!--                          >{{ item.label }}</el-radio-->
<!--                        >-->
<!--                      </el-radio-group>-->
<!--                    </el-form-item> &ndash;&gt;-->
<!--                    <el-form-item-->
<!--                      label="是否应用到子文件类型:"-->
<!--                      prop="applySignature"-->
<!--                    >-->
<!--                      <el-radio-group-->
<!--                        v-model.trim="formData.applySignature"-->
<!--                        size="medium"-->
<!--                      >-->
<!--                        <el-radio-->
<!--                          v-for="(item, index) in dict.type.yes_no"-->
<!--                          :key="index"-->
<!--                          :label="item.value"-->
<!--                          :disabled="item.disabled"-->
<!--                          >{{ item.label }}</el-radio-->
<!--                        >-->
<!--                      </el-radio-group>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--                <el-row gutter="15">-->
<!--                  <el-col :span="24">-->
<!--                    <el-form-item label="文件签章设置:" prop="signatureType">-->
<!--                      <div>-->
<!--                        <span style="margin: 0 40px">序号</span>-->
<!--                        <span style="margin: 0 60px">名称</span>-->
<!--                        <span style="margin: 0 60px">是否选择</span>-->
<!--                        <span style="margin: 0 60px">样式</span>-->
<!--                        <span style="margin: 0 60px">应用范围</span>-->
<!--                        <span style="margin: 0 60px">排序</span>-->
<!--                      </div>-->
<!--                      <draggable-->
<!--                        :options="{-->
<!--                          group: 'people',-->
<!--                          animation: 150,-->
<!--                          ghostClass: 'sortable-ghost',-->
<!--                          chosenClass: 'chosenClass',-->
<!--                          scroll: true,-->
<!--                          scrollSensitivity: 200,-->
<!--                        }"-->
<!--                        v-model.trim="formData.signatureType"-->
<!--                        @change="change"-->
<!--                        @start="start"-->
<!--                        @end="end"-->
<!--                        :move="move"-->
<!--                        style="-->
<!--                          display: inline-block;-->
<!--                          width: 900px;-->
<!--                          height: 350px;-->
<!--                          background: #eee;-->
<!--                          overflow: auto;-->
<!--                        "-->
<!--                      >-->
<!--                        <div-->
<!--                          v-for="(val, key) in formData.signatureType"-->
<!--                          :key="key"-->
<!--                          class="fromdynamicItem"-->
<!--                        >-->
<!--                          <el-form-item style="width: 90.33px">-->
<!--                            <span style="margin: 0 40px">{{ key + 1 }}</span>-->
<!--                          </el-form-item>-->
<!--                          <el-form-item style="width: 180.33px">-->
<!--                            <el-input-->
<!--                              v-model.trim="val.signatureFactorName"-->
<!--                              :disabled="val.defaultFlag == 1"-->
<!--                              placeholder="请输入内容"-->
<!--                            ></el-input>-->
<!--                          </el-form-item>-->
<!--                          <el-form-item style="width: 180.33px">-->
<!--                            <el-select-->
<!--                              v-model.trim="val.signatureFactorUse"-->
<!--                              placeholder="请选择规则值"-->
<!--                              clearable-->
<!--                              :style="{ width: '100%' }"-->
<!--                            >-->
<!--                              <el-option-->
<!--                                v-for="(changeval, changekey) in dict.type-->
<!--                                  .yes_no"-->
<!--                                :key="changekey"-->
<!--                                :label="changeval.label"-->
<!--                                :value="changeval.value"-->
<!--                                :disabled="changeval.disabled"-->
<!--                              ></el-option>-->
<!--                            </el-select>-->
<!--                          </el-form-item>-->

<!--                          <el-form-item style="width: 180.33px">-->
<!--                            <el-select-->
<!--                              v-model.trim="val.signatureId"-->
<!--                              placeholder="请选择样式"-->
<!--                              clearable-->
<!--                              :style="{ width: '100%' }"-->
<!--                            >-->
<!--                              <el-option-->
<!--                                v-for="(-->
<!--                                  changeval, changekey-->
<!--                                ) in listSignatureOptions"-->
<!--                                :key="changekey"-->
<!--                                :label="changeval.signatureName"-->
<!--                                :value="changeval.id"-->
<!--                                :disabled="changeval.disabled"-->
<!--                              ></el-option>-->
<!--                            </el-select>-->
<!--                          </el-form-item>-->
<!--                          <el-form-item style="width: 180.33px">-->
<!--                            <el-select-->
<!--                              v-model.trim="val.rangeValue"-->
<!--                              placeholder="请选择应用范围"-->
<!--                              clearable-->
<!--                              :style="{ width: '100%' }"-->
<!--                            >-->
<!--                              <el-option-->
<!--                                v-for="(changeval, changekey) in dict.type-->
<!--                                  .scope_of_application"-->
<!--                                :key="changekey"-->
<!--                                :label="changeval.label"-->
<!--                                :value="changeval.value"-->
<!--                                :disabled="changeval.disabled"-->
<!--                              ></el-option>-->
<!--                            </el-select>-->
<!--                          </el-form-item>-->

<!--                          <el-form-item>-->
<!--                            <i class="el-icon-rank" style="margin: 0 5px"></i>-->
<!--                            <i-->
<!--                              class="el-icon-delete"-->
<!--                              v-show="val.defaultFlag != 1"-->
<!--                              @click="deleteQianzhan(val, key)"-->
<!--                              style="margin: 0 5px"-->
<!--                            ></i>-->
<!--                            <i class="el-icon-plus" @click="addItem"></i>-->
<!--                          </el-form-item>-->
<!--                        </div>-->
<!--                      </draggable>-->
<!--                      <div>-->
<!--                        &lt;!&ndash; <el-button @click="addItem" type="primary"-->
<!--                          >增加</el-button-->
<!--                        > &ndash;&gt;-->
<!--                      </div>-->
<!--                    </el-form-item>-->
<!--                  </el-col></el-row-->
<!--                >-->
<!--              </el-form>-->
<!--            </el-card>-->
<!--          </div>-->
<!--        </el-tab-pane>-->
        <el-tab-pane name="7">
          <span slot="label">文件流程设置</span>
          <workflow-setting ref="workflowSetting" :id="id" ></workflow-setting>
        </el-tab-pane>
        <el-tab-pane name="8">
          <span slot="label">文件权限设置</span>
          <purview-setting ref="purviewSetting" :id="id"></purview-setting>
        </el-tab-pane>
        <el-tab-pane name="9">
          <span slot="label">文件版本号设置</span>
          <doc-class-setting ref="docClassSetting" :id="id"></doc-class-setting>
        </el-tab-pane>
        <el-tab-pane name="10">
          <span slot="label">文件水印设置</span>
          <watermark-setting ref="watermarkSetting" :id="id"></watermark-setting>
        </el-tab-pane>
        <el-tab-pane name="11">
          <span slot="label">表单字段设置</span>
          <form-rule-setting ref="docFormSetting" :id="id"></form-rule-setting>
        </el-tab-pane>
        <el-tab-pane name="12">
          <span slot="label">预设人员</span>
          <group-setting ref="groupSetting" :id="id"></group-setting>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>
<script>
import {
  settingDocClassUpdata,
  settingDocClassId,
  settingdocClassMergedelete,
  settingdocClassSignaturedelete,
} from "@/api/file_settings/type_settings";
import { listSignature } from "@/api/setting/signature";
import { listCodraft } from "@/api/setting/codraft";
import draggable from "vuedraggable";
import {
  listCodeRule,
  getPost,
  delCodeRule,
  addPost,
  updatePost,
} from "@/api/setting/codeRule";
import workflowSetting from "./workflowSetting/index"
import watermarkSetting from "./watermarkSetting/index"
import PurviewSetting from '@views/file_settings/type_settings/purviewSetting/index.vue'
import DocClassSetting from '@views/file_settings/type_settings/docClassSetting/index.vue'
import JointSetting from '@views/file_settings/type_settings/jointSetting/index.vue'
import FormRuleSetting  from '@views/file_settings/type_settings/formRuleSetting/index.vue'
import GroupSetting from './groupSetting/index.vue'
import {listAllWatermarkRule} from "@/api/setting/watermarkRule";
import { listMgr} from "@/api/setting/mgr";
import combinedSetting from '@views/file_settings/type_settings/combinedSetting/index.vue'

export default {
  dicts: [
    "expiration",
    "reviewCycle",
    "encrypt_type",
    "yes_no",
    "scope_of_application",
  ],
  components: { DocClassSetting, PurviewSetting, draggable,
    workflowSetting, watermarkSetting,JointSetting,
    FormRuleSetting, GroupSetting, combinedSetting},
  props: ["id"],
  data() {
    return {
      id: this.id,
      form: {
        type1: [],
        type2: [],
        type3: [],
      },
      activeName: "first",
      formData: {
        expiration: undefined,
        reviewCycle: undefined,
        field103: [],
        encryptType: undefined,
        codeId: undefined,
        ynopenCode: undefined,
        ynapplyCode: undefined,
        signatureType: [],
        applyMerge: undefined,
        openMerge: undefined,
        combinedMgrId: undefined,
        codraftBoList: [],
      },
      typeform: {
        ruleType: "",
        ruleValue: "",
        dynamicItem: [],
      },
      listCodeRuleOptions: [],
      rules: {
        field101: [
          {
            required: true,
            message: "单选框组不能为空",
            trigger: "change",
          },
        ],
        field102: [
          {
            required: true,
            message: "请选择下拉选择",
            trigger: "change",
          },
        ],
        field103: [
          {
            required: true,
            type: "array",
            message: "请至少选择一个field103",
            trigger: "change",
          },
        ],
      },
      field101Options: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 2,
        },
      ],

      field102Options2: [
        {
          label: "起草",
          value: 1,
        },
        {
          label: "部门领导审核",
          value: 2,
        },
        {
          label: "部门会签",
          value: 3,
        },
        {
          label: "公司领导审核",
          value: 4,
        },
        {
          label: "审批",
          value: 5,
        },
        {
          label: "培训",
          value: 6,
        },
        {
          label: "分发",
          value: 7,
        },
      ],
      field102Options1: [
        {
          label: "批准",
          value: 1,
        },
        {
          label: "生成文件编号",
          value: 2,
        },
        {
          label: "生成文件编号",
          value: 3,
        },
        {
          label: "合稿",
          value: 4,
        },
        {
          label: "签章",
          value: 5,
        },
        {
          label: "生效发布",
          value: 6,
        },
      ],
      activeIndex: "2",
      field103Options: [
        {
          label: "选项一",
          value: 1,
        },
        {
          label: "选项二",
          value: 2,
        },
      ],
      field103Options12: [
        {
          label: "个人签收",
          value: 1,
        },
        {
          label: "部门签收",
          value: 2,
        },
      ],
      field103Options123: [],
      listSignatureOptions: [],
      listCodraftOptions: [],
      shenxiao1: [],
      shenxiao2: [],
      sixiao1: [],
      sixiao2: [],
      queryParams: {
        pageNum: 1,
        pageSize: 100,
      },
      submitLoading: false,
      combinedMgrData: []
    };
  },
  computed: {},
  watch: {
    "formData.codeId"(val) {
      console.log(val);
    },
    "formData.signatureType"(val) {
      console.log(val);
    },
    "formData.mergeDocConfig"(val) {
      console.log(val);
    },
    shenxiao1(val) {
      console.log(val);
    },
    shenxiao2(val) {
      console.log(val);
    },
    sixiao1(val) {
      console.log(val);
    },
  },
  created() {
    listCodeRule(this.queryParams).then((response) => {
      this.listCodeRuleOptions = response.rows;
    });
    listSignature(this.queryParams).then((response) => {
      this.listSignatureOptions = response.rows;
    });
    listCodraft(this.queryParams).then((response) => {
      this.listCodraftOptions = response.rows;
    });
    if (this.id) {
      settingDocClassId(this.id).then((response) => {
        if (response.data.expiration != null) {
          response.data.expiration = response.data.expiration.toString();
        }
        if (response.data.reviewCycle != null) {
          response.data.reviewCycle = response.data.reviewCycle.toString();
        }
        //this.formData = { expiration: "1", reviewCycle: "2" };
        this.formData = response.data;
        if (response.data.signatureType != "") {
          this.formData.signatureType = response.data.docClassSignatureVoList;
          console.log(JSON.stringify(this.formData.signatureType))
          this.formData.signatureType.forEach((element, index) => {
            this.formData.signatureType[index].signatureFactorUse = String(
              element.signatureFactorUse
            );
            this.formData.signatureType[index].rangeValue = String(
              element.rangeValue
            );
          });
        }
        if(this.formData.combinedMgrId){
          this.handleCombinedChange(this.formData.combinedMgrId)
        }

        //this.formData.mergeDocConfig = JSON.parse(response.data.mergeDocConfig);
        //this.formData.mergeDocConfig = { sx1: [], sx2: [] };
        /*if (response.data.docClassMergeVoList != "") {
          this.shenxiao1 = [];
          this.shenxiao2 = [];
          this.sixiao1 = [];
          this.sixiao2 = [];
        }*/
        /*response.data.docClassMergeVoList.forEach((item) => {
          item.anotherPage = String(item.anotherPage);
          item.mergeFactorUse = String(item.mergeFactorUse);
          if (item.type == "VALID" && item.mergeFactor == 1) {
            this.shenxiao1.push(item);
          }
          if (item.type == "VALID" && item.mergeFactor == 2) {
            this.shenxiao2.push(item);
          }
          if (item.type == "INVALID" && item.mergeFactor == 1) {
            this.sixiao1.push(item);
          }
          if (item.type == "INVALID" && item.mergeFactor == 2) {
            this.sixiao2.push(item);
          }
        });*/
      });

    }
    this.getCombinedMgr()
  },
  mounted() {},
  methods: {
    openChageParent(val){
      this.formData.openMerge = val
    },
    applyChageParent(val){
      this.formData.applyMerge = val
    },
    submitForm(e) {
      var merge = JSON.stringify(this.form);
      this.submitLoading = true;
      if (this.id != "" && e == 1) {
        var arr = {
          id: this.id,
          expiration: this.formData.expiration,
          reviewCycle: this.formData.reviewCycle,
          openReview: this.formData.openReview,
          applyReview: this.formData.applyReview,
          applyPrescription: this.formData.applyPrescription,
          openPrescription: this.formData.openPrescription,
        };

        settingDocClassUpdata(arr).then((response) => {
          this.$modal.msgSuccess("设置成功");
        });
      }
      if (this.id != "" && e == 2) {
        var arr = {
          id: this.id,
          codeId: this.formData.codeId,
          ynopenCode: this.formData.ynopenCode,
          ynapplyCode: this.formData.ynapplyCode,
        };
        settingDocClassUpdata(arr).then((response) => {
          this.$modal.msgSuccess("设置成功");
        });
      }
      if (this.id != "" && e == 3) {
        var arr = {
          id: this.id,
          distributeId: this.formData.distributeId,
          openDistribute: this.formData.openDistribute,
          applyDistribute: this.formData.applyDistribute,
        };
        settingDocClassUpdata(arr).then((response) => {
          this.$modal.msgSuccess("设置成功");
        });
      }
      //合稿设置
      if (this.id != "" && e == 5) {
        /*var docClassMergeBoList = [];
        docClassMergeBoList = [
          ...this.shenxiao1,
          ...this.shenxiao2,
          ...this.sixiao1,
          ...this.sixiao2,
        ];*/

        var arr = {
          id: this.id,
          // docClassMergeBoList: docClassMergeBoList,
          openMerge: this.formData.openMerge,
          applyMerge: this.formData.applyMerge,
          combinedMgrId: this.formData.combinedMgrId,
          codraftBoList: this.formData.codraftBoList,
        };
        settingDocClassUpdata(arr).then((response) => {
          this.$modal.msgSuccess("设置成功");
        });
      }

      if (this.id != "" && e == 6) {
        var arr = {
          id: this.id,
          docClassSignatureBoList: this.formData.signatureType,
          openSignature: this.formData.openSignature,
          applySignature: this.formData.applySignature,
        };
        settingDocClassUpdata(arr).then((response) => {
          this.$modal.msgSuccess("设置成功");
        });
      }

      if (this.id != "" && e == 7) {
          this.$refs.workflowSetting.saveDocClassFlow()
      }

      if (this.id != "" && e == 8) {
        this.$refs.purviewSetting.savaDocClassPurview()
      }

      if (this.id != "" && e == 9) {
        this.$refs.docClassSetting.savaDocClassSetting()
      }

      if (this.id != "" && e == 10) {
        this.$refs.watermarkSetting.savaWatermarkSetting()
      }

      if (this.id != "" && e == 11) {
        this.$refs.docFormSetting.savaDocFormSetting()
      }

      if (this.id != "" && e == 12) {
        this.$refs.groupSetting.savaDocClassSetting()
      }
      setTimeout(() => {
        this.submitLoading = false;
      }, 1000);
    },
    addItem() {
      if (this.formData.signatureType == null) {
        this.formData.signatureType = [];
      }
      this.formData.signatureType.push({
        rangeValue: "",
        signatureFactorUse: "",
        signature: "",
        signatureId: "",
        docClass: this.id,
        defaultFlag: 0,
      });
    },
    addItemHegao(type) {

      var arr = {
        mergeFactorName: "",
        mergeFactorUse: "",
        codraftId: "",
        anotherPage: "",
        docClass: this.id,
      };
      if (type == "shenxiao1") {
        arr.mergeFactor = 1;
        arr.type = "VALID";
        this.shenxiao1.push(arr);
      }
      if (type == "shenxiao2") {
        arr.mergeFactor = 2;
        arr.type = "VALID";
        this.shenxiao2.push(arr);
      }
      if (type == "sixiao1") {
        arr.mergeFactor = 1;
        arr.type = "INVALID";
        this.sixiao1.push(arr);
      }
      if (type == "sixiao2") {
        arr.mergeFactor = 2;
        arr.type = "INVALID";
        this.sixiao2.push(arr);
      }
    },
    deleteItem(item, index) {
      if (item.type == "VALID" && item.mergeFactor == 1) {
        this.shenxiao1.splice(index, 1);
      }
      if (item.type == "VALID" && item.mergeFactor == 2) {
        this.shenxiao2.splice(index, 1);
      }
      if (item.type == "INVALID" && item.mergeFactor == 1) {
        this.sixiao1.splice(index, 1);
      }
      if (item.type == "INVALID" && item.mergeFactor == 2) {
        this.sixiao2.splice(index, 1);
      }

      if (item.id != undefined) {
        settingdocClassMergedelete(item.id);
      }

      // this.typeform.dynamicItem.splice(index, 1);
    },
    deleteQianzhan(item, index) {
      this.formData.signatureType.splice(index, 1);
      if (item.id != undefined) {
        settingdocClassSignaturedelete(item.id);
      }
    },
    handleSelect(key, keyPath) {},
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    topclose() {
      this.$emit("changeDrawer", false);
    },
    // 获取合稿列表
    async getCombinedMgr() {
      try {
        const res = await listMgr() // 需要添加相应的 API 方法
        this.combinedMgrData = res.rows || []
      } catch (error) {
        console.error('获取水印规则失败:', error)
      }
    },
    // 获取合稿变更处理
    handleCombinedChange(id) {
      this.$refs.combinedSetting.getCombinedData(id);
    },
    /** 获取子组件数据 **/
    combinedChangeFormatData(val){
      this.$set(this.formData,'codraftBoList',val)
    },
  },
};
</script>
<style lang="scss">
.settings {
  .fromdynamicItem {
    display: flex;
    margin: 5px 0px;
  }
  .rz-button-handel-90 {
    position: absolute;
    top: 10px;
    right: 90px;
  }
}
</style>
