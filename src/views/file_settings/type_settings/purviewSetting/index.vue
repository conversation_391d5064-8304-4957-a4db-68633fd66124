<template>
  <div class="card-head" v-loading="loading">
    <el-card class="gray-card">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="160px"
      >
        <el-form-item label="是否启用:" prop="openFlag">
          <el-switch
            v-model.trim="formData.openFlag"
            active-color="#13ce66"
            active-value="Y"
            inactive-value="N"
            size="medium"
            inactive-color="#ff4949"
          >
          </el-switch>
        </el-form-item>
        <el-form-item
          label="是否应用到子文件类型:"
          prop="applyFlag"
        >
          <el-radio-group
            v-model.trim="formData.applyFlag"
            size="medium"
          >
            <el-radio
              v-for="(item, index) in dict.type.sys_yes_no"
              :key="index"
              :label="item.value"
              :disabled="item.disabled"
            >{{ item.label }}
            </el-radio
            >
          </el-radio-group>
        </el-form-item>
        <span>权限设置:</span>
        <el-table :data="formData.detailList" border>
          <el-table-column label="类型" align="left" prop="type" width="200">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.doc_purview_type" :value="scope.row.type"/>
            </template>
          </el-table-column>
          <el-table-column label="范围" align="left" prop="valueList">
            <template slot-scope="scope">
              <el-tag
                v-for="tag in scope.row.valueList"
                closable
                @close="()=>handleClose(tag,scope.row)"
                :key="tag.value"
                :type="tag.value"
              >
                {{ tag.label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="100">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleSelect(scope.row,scope.$index)"
              >选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-card>
    <selection-box ref="selectionBox" @selectHandle="selectBoxHandle"></selection-box>
    <assign-users ref="assignUsers" @selectHandle="userSelectHandle"></assign-users>
  </div>
</template>

<script>
import { getDocClassPurview, updateDocClassPurview } from '@/api/setting/docClassPurview'
import SelectionBox from '@viewscomponents/selectionBox/index.vue'
import { listDept } from '@/api/system/dept'
import { getCompanyList } from '@/api/system/user'
import { listRole } from '@/api/system/role'
import AssignUsers from '@views/workflowList/addWorkflow/add_import/assignUsers.vue'
import { listDocClassPurviewDetail } from '@/api/setting/docClassPurviewDetail'

export default {
  name: 'PurviewSetting',
  components: { AssignUsers, SelectionBox },
  props: ['id'],
  dicts: ['sys_yes_no', 'doc_purview_type'],
  data() {
    return {
      loading: false,
      alertTitle: '',
      data: [],
      row: {},
      value: [],
      template: [],
      activeIndex: 'ADD',
      visible: false,
      formData: {
        openFlag: 'N',
        applyFlag: 'Y'
      },
      rules: {},
      deptList:[],
      deptOptions: [],
      companyDataList:[],
      roleList: [],
    }
  },
  created() {
    this.getCompanyDataList()
    this.getDeptList()
    this.getRoleList()
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    reset() {
      this.formData = {
        id: this.id,
        openFlag: 'N',
        applyFlag: 'N',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getDocClassPurview(this.id).then(async res => {
        let detailList = []
        if (res.data) {
          _this.formData = res.data
          let res1 = await listDocClassPurviewDetail({ purviewId: this.id })
          detailList = res1.rows
        }
        _this.detailListLoad(detailList)
        _this.loading = false
      })
    },
    detailListLoad(data){
      let _this = this
      let detailList = []
      this.dict.type.doc_purview_type.forEach(dict => {
        let valueList = []
        data.forEach(item=>{
          if (item.type === dict.value) {
            valueList.push({
              label: item.label,
              value: item.value
            })
          }
        })
        detailList.push({ type: dict.value, valueList: valueList })
      })
      _this.formData.detailList = detailList
    },
    restData() {
      return {
        label: undefined,
        value: undefined
      }
    },
    getDeptList() {
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, 'deptId')
      })
    },
    getCompanyDataList() {
      let _this = this
      getCompanyList({}).then(res=>{
        _this.companyDataList = res.data
      })
    },
    getRoleList(){
      let _this = this
      listRole({status:'0'}).then(res=>{
        _this.roleList = res.rows
      })
    },
    handleSelect(row,index) {
      let _this = this
      if(row.type==='company') {
        console.log(_this.companyDataList)
        _this.selectionBoxInit(row.type,index, row.valueList,_this.companyDataList,{
          title: '选择公司',
          id: 'id',
          label: 'tenantName',
          valueId: 'value',
          valueLabel: 'label',
          valueModel: _this.restData()
        })
      } else if (row.type==='dept') {
        _this.selectionBoxInit(row.type,index, row.valueList,_this.deptOptions,{
          title: '选择部门',
          id: 'deptId',
          label: 'deptName',
          valueId: 'value',
          valueLabel: 'label',
          valueModel: _this.restData(),
          anySelect:true,
          FSLink: true,
        }, _this.deptList)
      } else if (row.type==='role') {
        _this.selectionBoxInit(row.type,index, row.valueList,_this.roleList,{
          title: '选择角色',
          id: 'roleId',
          label: 'roleName',
          valueId: 'value',
          valueLabel: 'label',
          valueModel: _this.restData()
        })
      }else if (row.type==='person'){
        _this.$nextTick(()=>{
          let user = []
          if (row.valueList&&row.valueList.length>0) {
            row.valueList.forEach(item=>{
              user.push({
                userName: item.value,
                nickName: item.label,
              })
            })
          }
          _this.$refs.assignUsers.init("选择人员",row.type,index,user,true)
        })
      }
    },
    selectionBoxInit(source,index,selectedList,dataList, settings,treeList) {
      this.$nextTick(()=> {
        this.$refs.selectionBox.init(source, index, selectedList, dataList, settings, treeList)
      })
    },
    selectBoxHandle(label,index,selectedList,valueList){
      this.$set(this.formData.detailList[index],'valueList',valueList)
    },
    userSelectHandle(source,index,data){
      let _this = this
      let valueList = []
      if (data&&data.length>0) {
        data.forEach(user=>{
          valueList.push({
            value: user.userName,
            label: user.nickName,
          })
        })
      }
      _this.$set(_this.formData.detailList[index],"valueList",valueList)
    },
    handleClose(tag, row) {
      row.valueList.splice(row.valueList.findIndex(node => node.value === tag.value), 1)
    },
    savaDocClassPurview(){
       let _this  = this
       let formData = JSON.parse(JSON.stringify(_this.formData))
       let detailList = []
       formData.detailList.forEach(detail=>{
         detail.valueList.forEach(item=>{
           detailList.push({
             purviewId: formData.id,
             type: detail.type,
             value: item.value,
             label: item.label,
           })
         })
       })
      formData.detailList = detailList
      updateDocClassPurview(formData).then(res=>{
        this.$modal.msgSuccess("设置成功");
      })
    }
  }
}
</script>

<style scoped>

</style>
