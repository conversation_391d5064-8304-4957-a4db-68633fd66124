<template>
    <div class="news-card" v-loading="loading">
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否启用:" prop="openFlag">
                <el-switch
                  v-model.trim="formData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item
                label="是否应用到子文件类型:"
                prop="applyFlag"
              >
                <el-radio-group
                  v-model.trim="formData.applyFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in dict.type.sys_yes_no"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="分发人员:" prop="ruleId">
                <el-select
                  v-model.trim="formData.ruleId"
                  placeholder="请选择分组设置"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in printGroupOptions"
                    :key="index"
                    :label="item.groupName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="预览人员:" prop="settingId">
                <el-select
                  v-model.trim="formData.settingId"
                  placeholder="请选择分组设置"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in purviewGroupOptions"
                    :key="index"
                    :label="item.groupName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
</template>

<script>
import { getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'
import { listDistributeGroup } from '@/api/setting/distributeGroup'

export default {
  name: 'GroupSetting',
  components: {},
  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      printGroupOptions: [],
      purviewGroupOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      rules: {
        ruleId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`)+this.$t(`menus.2423`),
            trigger: "blur",
          },
        ],
        settingId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`)+this.$t(`menus.2423`),
            trigger: "blur",
          },
        ],
      },
    }
  },
  created() {
    this.getRuleList()
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    async getRuleList() {
      try {
        const [printRes, purviewRes] = await Promise.all([
          listDistributeGroup({type:'print'}),
          listDistributeGroup({type:'purview'})
        ])
        this.printGroupOptions = printRes.rows
        this.purviewGroupOptions = purviewRes.rows
      } catch(err) {
        console.error('获取规则列表失败:', err)
      }
    },
    reset() {
      this.formData = {
        id: undefined,
        type: 'distribute',
        docClass: this.id,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    async getByDocClass() {
      try {
        this.loading = true
        this.reset()
        const res = await getInfoBy({type:'distribute',docClass:this.id})
        if (res.data) {
          this.formData = res.data
        }
      } catch(err) {
        console.error('获取文档类型失败:', err)
      } finally {
        this.loading = false
      }
    },
    async savaDocClassSetting() {
      try {
        const valid = await this.$refs.elForm.validate()
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          const res = await updateDocClassSetting(formData)
          this.formData.id = res.data
          this.$modal.msgSuccess("设置成功")
        }
      } catch(err) {
        console.error('保存设置失败:', err)
      }
    }
  }
}
</script>
