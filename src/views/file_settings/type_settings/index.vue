<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>{{queryParams.dataType == 'stdd' ? $t('file_handle.change_sys') : $t('doc.this_dept_project')}} {{ $t('file_set.type_file_setting') }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :label-width="columnLangSizeFlag ? '128px' : '68px'"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.className"
                :placeholder="$t('file_set.type_input_name_search')"
                clearable
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{ $t('myItem.handle_reset') }}
              </el-button>
              <el-button icon="el-icon-refresh" @click="handleInitAncestors">{{ '初始化祖级列表' }}</el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">{{ $t('doc.this_dept_new_add') }}</el-button>
              <el-button type="primary" @click="handleImport()">{{ $t('doc.this_dept_import') }}</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item :label="$t('file_set.type_category_level')">
                <el-input
                  :placeholder="$t('file_set.type_category_level')"
                  clearable
                  v-model.trim="queryParams.classLevel"
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t('file_set.type_category_code')" :label-width="columnLangSizeFlag ? '158px' : '68px'">
                <el-input
                  :placeholder="$t('file_set.type_category_code')"
                  clearable
                  v-model.trim="queryParams.classCode"
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t('file_set.type_category_status')" prop="classStatus" :label-width="columnLangSizeFlag ? '158px' : '68px'">
                <el-select
                  :placeholder="$t('file_set.type_category_status')"
                  clearable
                  v-model.trim="queryParams.classStatus"
                >
                  <el-option
                    v-for="dict in dict.type.class_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('file_set.type_belong_type')" prop="classType" :label-width="columnLangSizeFlag ? '158px' : '68px'">
                <el-select
                  v-model.trim="queryParams.classType"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.class_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
              <el-button @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
              <el-button @click="boxClass = false">{{ $t('doc.this_dept_abolish') }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          border
          v-loading="loading"
          :row-class-name="tableRowClassName"
          ref="dragTable"
          :key="tableKey"
          :data="postList"
          @selection-change="handleSelectionChange"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          row-key="id"
        >
          >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_name')"
            align="left"
            prop="className"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_level')"
            align="left"
            prop="classLevel"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_num')"
            align="left"
            prop="classCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.number_order')"
            align="left"
            prop="sort"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('file_set.type_category_type')"
            align="left"
            prop="classType"
          >
            <template slot-scope="scope">
              <dict-tag :options="dict.type.class_type" :value="scope.row.classType"/>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('file_set.type_category_status')"
            align="left"
            prop="classStatus"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.classStatus == 1 ? $t('file_set.number_enable') : $t('file_set.number_banned') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('sys_mgr.user_auth_char')"
            align="left"
            prop="permission"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
              >{{ $t('file_set.version_edit') }}
              </el-button>
              <el-button
                size="mini"
                type="text"
                v-hasPermi="[`file_settings:${queryParams.dataType}:settings`]"
                @click="handleSettings(scope.row)"
              >{{ $t('file_set.type_setting') }}
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >{{ $t('doc.this_dept_delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <!--      <pagination-->
      <!--        v-show="total > 0"-->
      <!--        :total="total"-->
      <!--        :page.sync="queryParams.pageNum"-->
      <!--        :limit.sync="queryParams.pageSize"-->
      <!--        @pagination="getList"-->
      <!--      />-->

      <!-- 添加或修改-->
      <div v-if="open">
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="600px"
          append-to-body
          destroy-on-close
        >
          <add :id="id" @chinldClose="chinldClose" :dataType="queryParams.dataType"></add>
        </el-dialog>
      </div>

      <el-drawer
        :title="title"
        :visible.sync="drawer"
        direction="rtl"
        size="80%"
        modal-append-to-body
      >
        <add :pButton="pButton"></add>
      </el-drawer>

      <el-drawer
        :title="title"
        :visible.sync="drawerSettings"
        :show-close="false"
        direction="rtl"
        size="85%"
        modal-append-to-body
      >
        <settings
          @changeDrawer="changeDrawer"
          v-if="drawerSettings"
          :id="id"
        ></settings>
      </el-drawer>
      <el-dialog
        :title="upload.title"
        :visible.sync="upload.open"
        width="420px"
        append-to-body
      >
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport+'&dataType=' + queryParams.dataType"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">{{$t("sys_mgr.upload_tip")}}</div>
          <div class="el-upload__tip text-center" slot="tip">
            <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model.trim="upload.updateSupport" :label="$t('sys_mgr.upload_checkbox_label')"/>

            </div>
            <span>{{$t("sys_mgr.user_only_excel")}}</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="handleExport"
            >{{$t("doc.this_dept_down_temp")}}</el-link
            >
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">{{ $t("file_handle.change_confirm") }}</el-button>
          <el-button @click="upload.open = false">{{ $t("doc.this_dept_abolish") }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  settingDocClassList,
  settingDocClassIds,
  settingDocClassSort,
} from "@/api/file_settings/type_settings";
import add from "./add";
import settings from "./settings.vue";
import Sortable from "sortablejs";
import { initAncestors } from '../../../api/setting/docClass'
import { getToken } from '@/utils/auth'

export default {
  name: "Post",
  dicts: ["sys_normal_disable", "class_status","class_type"],
  components: {
    add,
    settings,
    // rzDrawer,
  },
  data() {
    return {
      tableKey: undefined,
      activeRows: [],
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 入参数
      upload: {
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/setting/docClass/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        // pageSize: 10,
        classCode: undefined,
        className: undefined,
        classLevel: undefined,
        classStatus: undefined,
        classType: undefined,
        id: undefined,
        dataType: 'stdd'
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t('file_set.signature_not_null'), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      drawerSettings: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    /*
    by xfc 20230626
    系统分别建立 体系文件类型设置和项目文件类型设置菜单，路由参数不一样
    dataType参数区分：体系文件=stdd、项目文件=project
    */
    // 获取数据类型参数来决定显示 体系文件类型设置列表还是 项目文件类型设置列表
    this.queryParams.dataType = this.$route.query.dataType
    this.getList();
  },
  methods: {
    setSort() {
      const el = document.querySelectorAll("table.el-table__body > tbody")[0];
      if (!el) {
        return;
      }
      let that = this;
      this.sortable = Sortable.create(el, {
        animation: 150, //动画
        filter: ".disabled", //指定不可拖动的类名（el-table中可通过row-class-name设置行的class）
        dragClass: "dragClass", //设置拖拽样式类名
        ghostClass: "ghostClass", //设置拖拽停靠样式类名
        chosenClass: "chosenClass", //设置选中样式类名
        setData: function (dataTransfer) {
          dataTransfer.setData("Text", "");
        },
        // 拖拽移动的时候
        onMove: function ({ dragged, related }) {
          // 对了，树数据中不管是哪一层，都一定要有level字段表示当前是第几层哦
          /*
                          evt.dragged; // 被拖拽的对象
                          evt.related; // 被替换的对象
                         */
          const oldRow = that.activeRows[dragged.rowIndex];
          const newRow = that.activeRows[related.rowIndex];

          if (
            oldRow.classLevel !== newRow.classLevel ||
            oldRow.parentClassId !== newRow.parentClassId
          ) {
            return false;
          }
        },
        onEnd: async ({ oldIndex, newIndex }) => {
          const oldRow = that.activeRows[oldIndex];
          const newRow = that.activeRows[newIndex];
          if (oldRow.classLevel != "1") {
            that.$modal.msg(this.$t('file_set.type_text'));
            //that.getList()
          }
          if (
            oldIndex !== newIndex &&
            oldRow.classLevel === newRow.classLevel &&
            oldRow.parentClassId === newRow.parentClassId
          ) {
            const oldRow = that.activeRows[oldIndex];
            const newRow = that.activeRows[newIndex];
            let oldRowSuffixData = that.activeRows.slice(oldIndex);
            let newRowSuffixData = that.activeRows.slice(newIndex);

            oldRowSuffixData = oldRowSuffixData.filter(
              (d, i) =>
                i <
                that.getLeastIndex(
                  oldRowSuffixData.findIndex(
                    (_d, _i) => _d.classLevel === oldRow.classLevel && _i !== 0
                  )
                )
            );
            newRowSuffixData = newRowSuffixData.filter(
              (d, i) =>
                i <
                that.getLeastIndex(
                  newRowSuffixData.findIndex(
                    (_d, _i) => _d.classLevel === newRow.classLevel && _i !== 0
                  )
                )
            );
            const targetRows = that.activeRows.splice(
              oldIndex,
              oldRowSuffixData.length
            );

            if (oldIndex > newIndex) {
              that.activeRows.splice(newIndex, 0, ...targetRows);
            } else if (oldIndex < newIndex) {
              that.activeRows.splice(
                newIndex + newRowSuffixData.length - oldRowSuffixData.length,
                0,
                ...targetRows
              );
            }
            let ids = [];
            that.activeRows.forEach((item) => {
              if (item.classLevel === "1") {
                ids.push(item.id);
              }
            });
            await settingDocClassSort(ids);
            that.getList();
          }
        },
      });
    },
    getLeastIndex(index) {
      return index >= 1 ? index : 1;
    },
    treeToTile(treeData, childKey = "children") {
      let arr = [];
      const expanded = (data) => {
        if (data && data.length > 0) {
          data
            .filter((d) => d)
            .forEach((e) => {
              arr.push(e);
              expanded(e[childKey] || []);
            });
        }
      };
      expanded(treeData);
      return arr;
    },
    // 设置表格row的class
    tableRowClassName({ row }) {
      // if (row.classLevel !== '1') {
      //     return "disabled";
      // }
      // return "";
    },
    /** 查询列表 */
    async getList() {
      if (this.sortable && this.sortable.el) {
        this.sortable.destroy();
      }
      this.loading = true;
      const { rows } = await settingDocClassList(this.queryParams);
      let showed_rows = rows.filter(v=>v.purview)
      this.$set(this, "postList", this.handleTree(showed_rows, "id", "parentClassId"));
      this.$set(this, "activeRows", this.treeToTile(this.postList));
      this.tableKey = new Date().getTime();
      this.$nextTick(() => {
        this.setSort();
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,

        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.classStatus = "";
      this.queryParams.className = "";
      this.queryParams.classLevel = "";
      this.queryParams.id = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.id = "";
      this.title = this.$t('doc.this_dept_new_add');
    },
    handleUpdata(e) {
      //console.log(e);
      this.id = e.id;
      this.open = true;
      this.title = this.$t('doc.this_dept_edit');
    },
    handleZuofei() {
      this.reset();
      this.drawer = true;
      this.title = this.$t('file_set.signature_invalid_file');
      this.pButton = "zuofei";
    },
    handleSettings(e) {
      this.drawerSettings = false;
      this.reset();
      this.id = e.id;
      this.drawerSettings = true;
      this.title = e.className;
    },

    chinldClose() {
      this.open = false;
      //this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id || this.ids;
      console.log(postIds);
      this.$modal
        .confirm(this.$t('file_set.type_text1') + row.className + this.$t('file_set.signature_text1'))
        .then(function () {
          return settingDocClassIds(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "setting/docClass/export",
        {
          ...this.queryParams,
        },
        `type_${new Date().getTime()}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, this.$t("sys_mgr.user_import_result"), { dangerouslyUseHTMLString: true,customClass: "prompt-box" });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.open=true;
    },
    changeDrawer(v) {
      // this.drawer = v;
      this.drawerSettings = false;
    },
    handleInitAncestors(){
      initAncestors().then(res=>{
        this.$modal.msgSuccess(res.msg);
      })
    }
  },

};
</script>
<style lang="scss">
@import "../../../../public/css/poctstyle.css";
// 拖拽
.dragClass {
  background: rgba($color: #6cacf5, $alpha: 0.5) !important;
}
// 停靠
.ghostClass {
  background: rgba($color: #6cacf5, $alpha: 0.5) !important;
}
// 选择
.chosenClass:hover > td {
  background: rgba($color: #6cacf5, $alpha: 0.5) !important;
}
.prompt-box .el-message-box__content{
  max-height: 500px;
  overflow: auto;
}
</style>
