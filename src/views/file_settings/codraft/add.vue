<template>
  <div>
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="130px"
    >
      <el-form-item label="合稿要素名称:" prop="codraftName">
        <el-input
          v-model.trim="formData.codraftName"
          placeholder="请输入合稿要素名称:"
          clearable
          :style="{ width: '100%' }"
          @blur="handeNameBlur"
        >
        </el-input>
      </el-form-item>

      <el-form-item label="合稿模板:">
        <fileUpload v-model="fileIdfileList" limit="1" />
      </el-form-item>
      <el-form-item label="状态:" prop="codraftStatus">
        <el-radio-group v-model.trim="formData.codraftStatus" size="medium">
          <el-radio
            v-for="(item, index) in dict.type.class_status"
            :key="index"
            :label="item.value"
            :disabled="item.disabled"
            >{{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item size="large">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import {
  listCodraft,
  getCodraft,
  delCodraft,
  addCodraft,
  updateCodraft,
} from "@/api/setting/codraft";
import { listCodeRule } from "@/api/setting/codeRule";
import { processFileLocalUpload } from "@/api/commmon/file";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { parseTime } from "../../../utils/ruoyi";

export default {
  dicts: ["class_status"],
  components: {
    Treeselect,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      classLevelOptions: [],

      formData: {},
      classLevelHandle: [],
      rules: {
        id: [
          { required: true, message: "合稿管理ID不能为空", trigger: "blur" },
        ],
        codraftName: [
          { required: true, message: "合稿要素名称不能为空", trigger: "blur" },
        ],
        fileId: [
          { required: true, message: "合稿模板不能为空", trigger: "blur" },
        ],
      },

      fileIdfileList: [],
      field101Options: [],
      codeIdptions: [],

      field101Props: {
        multiple: false,
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    console.log(this.id);
    if (this.id != "") {
      getCodraft(this.id).then((response) => {
        // console.log(response.data);
        if (response.data.fileId != null) {
          this.fileIdfileList.push({
            name: response.data.templateName,
            url: response.data.fileId,
          });
        }
        this.formData = response.data;
      });
    }
  },
  mounted() {
    //  listCodeRule ({ pageNum: 1, pageSize: 100 }).then((response) => {
    //     this.codeIdptions = response.rows;
    //   });
  },
  methods: {
    handleSelectNode(node, instanceId) {
      if (node.id === 0) {
        this.formData.classLevel = 1;
      } else {
        this.formData.classLevel = parseInt(node.classLevel) + 1;
      }
    },

    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          if (this.fileIdfileList != "") {
            this.formData.fileId = this.fileIdfileList[0].url;
            this.formData.templateName = this.fileIdfileList[0].name;
          }
          if (this.id != "") {
            // this.formData.createTime = parseTime(this.formData.createTime);
            // this.formData.updateTime = parseTime(this.formData.updateTime);
            updateCodraft(this.formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$emit("chinldClose");
            });
          } else {
            addCodraft(this.formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.$emit("chinldClose");
            });
          }
        }
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    getField101Options() {
      // TODO 发起请求获取数据
      this.field101Options;
    },
    appendixesUpload(params) {
      this.fileIdfileList = [];
      let fd = new FormData();
      fd.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      processFileLocalUpload(fd).then((res) => {
        this.fileIdfileList.push({
          name: res.data.fileName,
          url: res.data.fileId,
        });
      });
    },
    // 删除附件
    handleRemoveAttachment(file, fileList) {
      this.fileIdfileList = this.fileIdfileList.filter(
        (item) => item.url !== file.url
      );
    },
    handleChange(value) {
      console.log(value);

      this.formData.classLevel = value.slice(-1).toString();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    handeNameBlur() {
      settingDocClassIsExistByName(this.formData.className).then((res) => {
        if (res.data >= 1) {
          this.$modal.msgWarning("当前输入的分类名称已经存在");
          this.formData.className = "";
        }
      });
    },
    handeBlur() {
      settingDocClassIsExistByCode(this.formData.id).then((response) => {
        console.log(response);
        if (response.data == 1) {
          this.$modal.msgWarning("当前输入的分类代码已经存在");
          this.formData.id = "";
        }
      });
    },
  },
};
</script>
<style>
</style>
