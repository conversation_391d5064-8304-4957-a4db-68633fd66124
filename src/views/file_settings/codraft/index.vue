<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> 合稿管理 </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.codraftName"
                placeholder="输入合稿要素名称搜索"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >重置
              </el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">新增</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <!-- <el-form-item label="分类层级">
                <el-input
                  placeholder="分类层级"
                  v-model.trim="queryParams.classLevel"
                ></el-input>
              </el-form-item>
              <el-form-item label="分类代码">
                <el-input
                  placeholder="分类代码"
                  v-model.trim="queryParams.id"
                ></el-input>
              </el-form-item> -->
              <el-select
                placeholder="状态"
                v-model.trim="queryParams.codraftStatus"
              >
                <el-option
                  v-for="dict in dict.type.class_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button @click="boxClass = false">取消</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @selection-change="handleSelectionChange"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          row-key="id"
        >
          >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="合稿要素名称"
            align="left"
            prop="codraftName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="合稿模板"
            align="left"
            prop="templateName"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="状态"
            align="left"
            prop="codraftStatus"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.codraftStatus == 1 ? "启用" : "禁用" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
                >编辑</el-button
              >
              <el-upload
                class="upload-rz"
                :http-request="standardDocBeforeUpload"
                :show-file-list="false"
              >
                <el-button
                  size="mini"
                  type="text"
                  @click="hangleshangchuan(scope.row)"
                  >上传
                </el-button>
              </el-upload>
              <el-button
                size="mini"
                type="text"
                @click="
                  handelefileLocalDownload(
                    scope.row.fileId,
                    scope.row.templateName
                  )
                "
                >下载</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改-->
      <div v-if="open">
        <el-dialog
          :title="title"
          :visible.sync="open"
          width="800px"
          append-to-body
          destroy-on-close
        >
          <add :id="id" @chinldClose="chinldClose"></add>
        </el-dialog>
      </div>

      <el-drawer
        :title="title"
        :visible.sync="drawer"
        direction="rtl"
        size="80%"
        modal-append-to-body
      >
        <add :pButton="pButton"></add>
      </el-drawer>

      <el-drawer
        :title="title"
        :visible.sync="drawerSettings"
        direction="rtl"
        size="80%"
        modal-append-to-body
      >
        <settings :id="id"></settings>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listCodraft,
  getCodraft,
  delCodraft,
  addCodraft,
  updateCodraft,
} from "@/api/setting/codraft";
import add from "./add";
import { processFileLocalUpload } from "@/api/commmon/file";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  name: "Post",
  dicts: ["sys_normal_disable", "class_status"],
  components: {
    add,

    // rzDrawer,
  },
  mixins: [mixin],
  data() {
    return {
      remarkfileList: [],
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        codraftName: undefined,
        classLevel: undefined,
        codraftStatus: undefined,
        id: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: "不能为空", trigger: "blur" }],
        postCode: [{ required: true, message: "不能为空", trigger: "blur" }],
        postSort: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      drawerSettings: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
      shangchuanid: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listCodraft(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,

        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.codraftStatus = "";
      this.queryParams.codraftName = "";
      this.queryParams.classLevel = "";
      this.queryParams.id = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    standardDocBeforeUpload(params) {
      console.log(params);
      let standardDoc = new FormData();
      standardDoc.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      processFileLocalUpload(standardDoc).then((res) => {
        updateCodraft({
          id: this.shangchuanid,
          fileId: res.data.fileId,
          templateName: res.data.fileName,
        }).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.getList();
        });
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.id = "";
      this.title = "新增";
    },
    handleUpdata(e) {
      //console.log(e);
      this.id = e.id;
      this.open = true;
      this.title = "修改";
    },
    hangleshangchuan(e) {
      this.shangchuanid = e.id;
    },
    handleZuofei() {
      this.reset();
      this.drawer = true;
      this.title = "作废文件";
      this.pButton = "zuofei";
    },
    handleSettings(e) {
      this.reset();
      this.id = e.id;
      this.drawerSettings = true;
      this.title = e.className;
    },

    chinldClose() {
      this.open = false;
      this.getList();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id || this.ids;
      console.log(row);
      this.$modal
        .confirm('是否确认删除名称为"' + row.codraftName + '"的数据项？')
        .then(function () {
          return delCodraft(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    changeDrawer(v) {
      this.drawer = v;
    },
  },
};
</script>
<style lang="scss">
.upload-rz {
  display: inline-block;
  margin: 0px 5px;
  .el-upload {
    border: 0px solid #013288;
    background: #fff0;
  }
  .el-upload:hover {
    background-color: #fff0 !important;
  }
}
@import "../../../../public/css/poctstyle.css";
</style>
