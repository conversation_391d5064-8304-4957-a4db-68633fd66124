<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['combined:mgr:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mgrList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="合稿名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime">
      <template slot-scope="scope">
        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
      </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">.
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleDetail(scope.row)"
            v-hasPermi="['combined:mgr:edit']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['combined:mgr:edit']"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleApplication(scope.row)"
            v-hasPermi="['combined:mgr:edit']"
          >应用</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['combined:mgr:remove']"
          >删除</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改合稿管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="60%" append-to-body :close-on-press-escape="false" :close-on-click-modal="false"
               :show-close="false" :wrapperClosable="false">
      <el-form ref="form" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="合稿名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" maxlength="200" show-word-limit :disabled="isDetail"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入内容" maxlength="1000" show-word-limit :disabled="isDetail"/>
        </el-form-item>
        <combined-setting v-if="open" ref="combinedSetting"
                          :isShowAdd="isDetail==true?false:true"
                       :bizId="formData.id"
                       @changeFormatData="combinedChangeFormatData"
        ></combined-setting>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isDetail" :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 应用 -->
<!--    :show-close="false"-->
    <el-dialog :title="title" :visible.sync="applicationOpen" width="60%" append-to-body :close-on-press-escape="false" :close-on-click-modal="false"
                :wrapperClosable="false">
      <FileClassTree v-if="applicationOpen" :bizId="bizId" @treeClose="treeClose"/>
    </el-dialog>

  </div>
</template>

<script>
import { listMgr, getMgr, delMgr, addMgr, updateMgr } from "@/api/setting/mgr";
import combinedSetting from '@views/file_settings/type_settings/combinedSetting/index.vue'
import FileClassTree from '@/components/fileClassTree/index.vue';

export default {
  name: "Mgr",
  components: { combinedSetting, FileClassTree},
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 合稿管理表格数据
      mgrList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      applicationOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        isDeleted: undefined,
      },
      // 表单参数
      formData: {
        id: undefined,
        name: undefined,
        remark: undefined,
        isDeleted: 0,
        codraftBoList: [],
      },
      bizId: null,
      // 表单校验
      rules: {
        id: [
          { required: true, message: "主键不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ],
        isDeleted: [
          { required: true, message: "是否删除: 0-正常, 1-删除不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建人不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      },
      //详情状态
      isDetail:false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询合稿管理列表 */
    getList() {
      this.loading = true;
      listMgr(this.queryParams).then(response => {
        this.mgrList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.formData = {
        id: undefined,
        name: undefined,
        remark: undefined,
        isDeleted: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.$nextTick(() => {
        this.reset();
      })
      this.isDetail = false
      this.title = "添加合稿管理";
    },
    /** 查看详情按钮操作 */
    handleDetail(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getMgr(id).then(response => {
        this.isDetail = true
        this.loading = false;
        this.formData = response.data;
        this.open = true;
        this.title = "查看详情";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getMgr(id).then(response => {
        this.isDetail = false
        this.loading = false;
        this.formData = response.data;
        this.open = true;
        this.title = "修改合稿管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      let self = this
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.formData.id != null) {
            updateMgr(this.formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addMgr(this.formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除该数据项？').then(() => {
        this.loading = true;
        return delMgr(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('combined/mgr/export', {
        ...this.queryParams
      }, `mgr_${new Date().getTime()}.xlsx`)
    },
    openChangeParent(val){
      this.formData.openMerge = val
    },
    applyChangeParent(val){
      this.formData.applyMerge = val
    },
    /** 获取子组件数据 **/
    combinedChangeFormatData(val){
      this.$set(this.formData,'codraftBoList',val)
    },
    handleApplication(row) {
      this.title = "应用管理";
      this.bizId = row.id
      this.applicationOpen = true;
    },
    treeClose(){
      this.applicationOpen = false;
    }
  }
};
</script>
