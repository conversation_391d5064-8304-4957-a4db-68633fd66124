<template>
  <el-card>
    <div slot="header" class="clearfix"><span>消息提醒设置</span></div>

      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="150px"
        label-position="right"
      >
      <el-row>
        <el-col :span="20">
              <el-form-item label="提醒方式:">
                <el-checkbox-group v-model.trim="formData.field113" size="medium">
                  <el-checkbox style="margin-right: 10px" label="站内消息"
                    >站内消息</el-checkbox
                  >
                  <el-select
                    v-model.trim="formData.field114"
                    placeholder="请选择"
                    clearable
                    style="margin-right:60px;"
                  >
                    <el-option
                      v-for="(item, index) in field124Options"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    ></el-option>
                  </el-select>

                  <el-checkbox style="margin-left: 10px" label="邮件"
                    >邮件</el-checkbox
                  >
                  <el-checkbox label="短信">短信</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
        </el-col>
        <el-col :span="20">
              <el-form-item label="提醒内容">
                <div class="item-table2">
                  <div class="cell"><el-form-item label="文件待复审提醒">
                    <el-switch></el-switch>
                    </el-form-item>
                    </div>
                    <div class="cell">
                   <span class="lab">在文件复审周期日前</span>
                  <el-select
                    v-model.trim="formData.field124"
                    placeholder="请选择下拉选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in field113Options"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    ></el-option>
                  </el-select>
                  天提醒
                  </div>
                </div>

                <div class="item-table2">
                  <div class="cell"><el-form-item label="文件失效提醒">
                    <el-switch></el-switch>
                    </el-form-item>
                    </div>
                    <div class="cell">
                  <span class="lab">在文件生效截止日期前</span>
                  <el-select
                    v-model.trim="formData.field124"
                    placeholder="请选择下拉选择"
                    clearable
                    :style="{ width: '20%' }"
                  >
                    <el-option
                      v-for="(item, index) in field113Options"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    ></el-option>
                  </el-select>
                  天提醒
                    </div>
                </div>
              </el-form-item>

        </el-col>
        <el-col :span="20">

              <el-form-item label=" ">
                <div class="switch-item">
                  <el-form-item label="文件借阅成功提醒" prop="field123">
                    <el-switch v-model.trim="formData.field123"></el-switch>
                  </el-form-item>
                  <el-form-item label="文件补发成功提醒" prop="field123">
                    <el-switch v-model.trim="formData.field123"></el-switch>
                  </el-form-item>
                  <el-form-item label="文件增发成功提醒" prop="field123">
                    <el-switch v-model.trim="formData.field123"></el-switch>
                  </el-form-item>
                  <el-form-item label="变更申请成功提醒" prop="field123">
                    <el-switch v-model.trim="formData.field123"></el-switch>
                  </el-form-item>
                  <el-form-item label="复审结果提醒" prop="field123">
                    <el-switch v-model.trim="formData.field123"></el-switch>
                  </el-form-item>
                </div>
              </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item >
            <el-button type="primary" style="width:120px;" @click="submitForm">提交</el-button>
            <el-button @click="resetForm" style="width:120px;">重置</el-button>
          </el-form-item>
        </el-col>
        </el-row>
      </el-form>
  </el-card>
</template>
<script>
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        field113: [],
        field114: undefined,
        field124: undefined,
        field123: false,
      },
      rules: {
        field113: [
          {
            required: true,
            type: "array",
            message: "请至少选择一个field113",
            trigger: "change",
          },
        ],
        field114: [
          {
            required: true,
            message: "请选择下拉选择",
            trigger: "change",
          },
        ],
        field124: [
          {
            required: true,
            message: "请选择下拉选择",
            trigger: "change",
          },
        ],
      },
      field113Options: [
        {
          label: "15",
          value: 1,
        },
        {
          label: "60",
          value: 2,
        },
        {
          label: "120",
          value: 3,
        },
      ],
      field114Options: [
        {
          label: "选项一",
          value: 1,
        },
        {
          label: "选项二",
          value: 2,
        },
      ],
      field124Options: [
        {
          label: "系统内部消息",
          value: 1,
        },
        {
          label: "As7消息",
          value: 2,
        },
        {
          label: "微信",
          value: 3,
        },
        {
          label: "企业微信",
          value: 4,
        },
        {
          label: "钉钉",
          value: 5,
        },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.remindindex {
  .el-form-item__label {
    width: 120px !important;
  }
  .el-form-item .el-form-item {
    width: 200px;
  }
  .tix {
    color: #5e5e5e;
    font-weight: normal;
    position: absolute;
    left: -35px;
  }
}
</style>
