<template>
  <div class="app-container">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="medium"
    >
      <el-form-item label="文件分类" prop="id" required>
        <el-select
          v-model="form.id"
          placeholder="请选择文件分类"
          style="width: 100%"
          clearable
          filterable
          @change="handleDocClassChange"
        >
          <el-option
            v-for="item in docClassOptions"
            :key="item.id"
            :label="item.className"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="下推公司" prop="subsidiaryCompany" required>
        <el-select
          v-model="form.subsidiaryCompany"
          placeholder="请选择下推公司"
          style="width: 100%"
          clearable
          filterable
          @change="handleCompanyChange"
        >
          <el-option
            v-for="item in dict.type.downpush_company"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="下推文件分类" prop="subsidiaryDocClassId" required>
        <el-select
          v-model="form.subsidiaryDocClassId"
          :placeholder="!form.subsidiaryCompany ? '请先选择下推公司' : '请选择下推文件分类'"
          style="width: 100%"
          clearable
          filterable
          :disabled="!form.subsidiaryCompany"
        >
          <el-option
            v-for="item in subsidiaryDocClassOptions"
            :key="item.id"
            :label="item.className"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="接收人" prop="connectorCode" required>
        <el-select
          v-model="form.connectorCode"
          placeholder="请选择"
          style="width: 100%"
          clearable
          filterable
        >
          <el-option
            v-for="item in connectorOptions"
            :key="item.userName"
            :label="item.nickName"
            :value="item.userName"
          />
        </el-select>
      </el-form-item>

      <div style="text-align: center; margin-top: 30px;">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">
          提交
        </el-button>
        <el-button @click="cancel">
          取消
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import {settingDocClassId, settingDocClassList,settingDocClassUpdata} from "@/api/file_settings/type_settings";
import {getZonciActiveDocClass} from '@/api/setting/docClass'
import {getActiveUsers} from "@/api/system/user";

export default {
  name: 'PushClassAdd',
  dicts: ['downpush_company'],
  props: {
    id: {
      type: String,
      default: ''
    },
    dataType: {
      type: String,
      default: 'stdd'
    }
  },
  data() {
    return {
      submitLoading: false,
      // 标志位：是否已从接口获取详情数据
      hasDetailData: false,
      form: {
        id: '',
        subsidiaryCompany: '',
        subsidiaryDocClassId: '',
        connectorCode: ''
      },
      rules: {
        id: [
          { required: true, message: '请选择文件分类', trigger: 'change' }
        ],
        subsidiaryCompany: [
          { required: true, message: '请选择下推公司', trigger: 'change' }
        ],
        subsidiaryDocClassId: [
          { required: true, message: '请选择下推文件分类', trigger: 'change' }
        ]
      },
      // 文件分类选项
      docClassOptions: [],
      // 下推文件分类选项
      subsidiaryDocClassOptions: [],
      // 接收人选项
      connectorOptions: [

      ]
    }
  },
  created() {
    this.reset()
    this.getDocClassList()
    if (this.id) {
      this.getDetail()
    }
  },
  mounted() {
    // 只在编辑模式下或有特殊需求时设置默认公司
    // 新增模式下不设置任何默认值
    if (this.id) {
      this.$nextTick(() => {
        this.setDefaultCompany()
      })
    }
  },
  watch: {
    // 监听字典数据变化，设置默认下推公司
    'dict.type.downpush_company': {
      handler(newVal) {
        if (newVal && newVal.length === 1 && !this.form.subsidiaryCompany) {
          this.$nextTick(() => {
            this.setDefaultCompany()
          })
        }
      },
      immediate: true
    },
    // 监听下推公司变化，确保联动逻辑
    'form.subsidiaryCompany': {
      handler(newVal, oldVal) {
        if (!newVal && oldVal) {
          // 如果下推公司被清空，同时清空下推文件分类
          this.form.subsidiaryDocClassId = ''
          this.subsidiaryDocClassOptions = []
        }
      }
    },
    // 监听下推文件分类值变化
    'form.subsidiaryDocClassId': {
      handler(newVal) {
        console.log('下推文件分类值变化:', newVal)
      }
    }
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        id: '',
        subsidiaryCompany: '',
        subsidiaryDocClassId: '',
        connectorCode: ''
      }
      // 清空所有选项数据
      this.subsidiaryDocClassOptions = []
    },

    // 获取文件分类列表
    async getDocClassList() {
      try {
        const response = await settingDocClassList({
          dataType: this.dataType,
          classStatus: '1'
        })
        this.docClassOptions = response.rows || []
        console.log('文件分类数据:', this.docClassOptions)
        // 获取文件分类列表后，设置默认选中
        this.$nextTick(() => {
          this.setDefaultDocClass()
        })
      } catch (error) {
        console.error('获取文件分类失败:', error)
      }
    },

    // 设置默认文件分类
    setDefaultDocClass() {
      // 如果已从接口获取详情数据，则不执行默认设置（互斥逻辑）
      if (this.hasDetailData) {
        console.log('已获取详情数据，跳过默认文件分类设置')
        return
      }

      // 如果有传入id且当前没有选中文件分类，则默认选中对应的文件分类
      if (this.id && !this.form.id && this.docClassOptions.length > 0) {
        const matchedClass = this.docClassOptions.find(item => item.id === this.id)
        if (matchedClass) {
          this.form.id = this.id
          console.log('自动选择文件分类:', matchedClass.className, this.id)
          // 自动触发文件分类变化事件
          this.handleDocClassChange(this.id)
        }
      }
    },

    // 设置默认下推公司
    setDefaultCompany() {
      // 如果已从接口获取详情数据，则不执行默认设置（互斥逻辑）
      if (this.hasDetailData) {
        console.log('已获取详情数据，跳过默认下推公司设置')
        return
      }

      const companies = this.dict.type.downpush_company

      if (companies &&
          companies.length === 1 &&
          !this.form.subsidiaryCompany) {
        this.form.subsidiaryCompany = companies[0].value
        // 自动触发公司变化事件
        this.handleCompanyChange(this.form.subsidiaryCompany)
      }
    },

    // 文件分类变化处理
    handleDocClassChange(value) {
      console.log('选择的文件分类:', value)
      // 可以根据选择的文件分类动态加载其他选项
    },

    // 下推公司变化处理
    async handleCompanyChange(value) {


      // 清空下推文件分类选项
      this.subsidiaryDocClassOptions = []
      this.form.subsidiaryDocClassId = ''

      console.log('选择的下推公司:', value)

      if (value) {
        // 根据选择的公司获取对应的数据库名称
        const selectedCompany = this.dict.type.downpush_company.find(item => item.value === value)

        if (selectedCompany && (selectedCompany.remark || selectedCompany.raw?.remark)) {
          try {
            // 解析remark字段中的JSON数据获取dbName
            const remark = selectedCompany.remark || selectedCompany.raw?.remark
            const remarkData = JSON.parse(remark)
            const dbName = remarkData.dbName


            if (dbName) {
              // 调用接口获取下推文件分类列表
              await this.getZonciActiveDocClassList(dbName)
              //   调用接收人
              await this.getUsers(dbName)
            }
          } catch (error) {
            console.error('解析公司remark数据失败:', error)
          }
        }
      }
    },
    async getUsers(dbName) {
      const response = await getActiveUsers(dbName)
      if (response.data) {
        this.connectorOptions = response.data
        console.log('获取到的接收人:', this.connectorOptions)
      }
    },
    // 获取下推文件分类列表
    async getZonciActiveDocClassList(dbName) {
      try {
        const response = await getZonciActiveDocClass(dbName)

        if (response.data) {
          this.subsidiaryDocClassOptions = response.data
        }
      } catch (error) {
        console.error('获取下推文件分类失败:', error)
        this.$modal.msgError('获取下推文件分类失败')
      }
    },

    // 获取详情（编辑时使用）
    async getDetail() {
      if (!this.id) {
        console.warn('获取详情失败：缺少id参数')
        return
      }

      try {
        const response = await settingDocClassId(this.id)

        if (response && response.data) {
          // 设置标志位，表示已从接口获取详情数据
          this.hasDetailData = true

          // 填充表单数据
          const detailData = response.data

          // 设置文件分类ID（接口返回的字段名是 id）
          this.form.docClassId = detailData.id || this.id

          // 根据数据库字段映射推送配置相关数据
          if (detailData.subsidiaryCompany) {
            this.form.subsidiaryCompany = detailData.subsidiaryCompany
          }
          if (detailData.subsidiaryDocClassId) {
            this.form.subsidiaryDocClassId = detailData.subsidiaryDocClassId
          }
          if (detailData.subsidiaryDocClassName) {
            this.form.subsidiaryDocClassName = detailData.subsidiaryDocClassName
          }
          // 处理接收人字段（优先使用 connector，如果没有则使用 connectorId）
          if (detailData.connector) {
            this.form.connector = detailData.connector
          }

          // 触发相关变化事件
          if (this.form.id) {
            this.handleDocClassChange(this.form.id)
          }
          if (this.form.subsidiaryCompany) {
            this.handleCompanyChange(this.form.subsidiaryCompany)
          }
        } else {
          console.warn('接口返回数据为空')
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$modal.msgError('获取详情数据失败')
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true

          this.form.subsidiaryDocClassName = this.subsidiaryDocClassOptions.find(item => item.id === this.form.subsidiaryDocClassId).className
          this.form.connector=this.connectorOptions.find(item=>item.userName===this.form.connectorCode).nickName
          settingDocClassUpdata(this.form).then(res=>{
            if(res.code===200){
              this.submitLoading = false
              this.$modal.msgSuccess('保存成功')
              this.$emit('chinldClose')
            }
          })

        }
      })
    },

    // 取消
    cancel() {
      this.$emit('chinldClose')
    },


  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-select {
  width: 100%;
}
</style>
