<template>
  <div class="number_manageadd">
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="120px"
    >
      <el-form-item :label="getLabelWithColon($t('file_set.version_rule_name'))" prop="ruleName">
        <el-input
          v-model.trim="formData.ruleName"
          :placeholder="$t('file_set.version_fill_rule_name')"
          clearable
          :style="{ width: '100%' }"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t('file_set.version_follow_up_rule'))" prop="nextId">
        <el-select
          v-model.trim="formData.nextId"
          :placeholder="$t('file_set.version_select_num_rule')"
          clearable
          :style="{ width: '100%' }"
        >
          <el-option
            v-for="(item, index) in listCodeRuleOptions"
            :key="index"
            :label="item.ruleName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t('file_set.version_start_val'))" prop="startValue">
        <el-input
          v-model.trim="formData.startValue"
          :placeholder="$t('file_set.version_start_val')"
          clearable
          :style="{ width: '100%' }"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="getLabelWithColon($t('file_set.version_num_rule'))" prop="">
        <div>
          <span style="margin: 0 40px">{{ $t('file_handle.recovery_num') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.version_rule_type') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.version_start_val') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.version_satisfy') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.version_figure') }}</span>
          <span style="margin: 0 60px">{{ $t('file_set.version_sync') }}</span>
        </div>
        <draggable
          :options="{
            group: 'people',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'chosenClass',
            scroll: true,
            scrollSensitivity: 200,
          }"
          v-model.trim="formData.ruleDetailList"
          @change="change"
          @start="start"
          @end="end"
          :move="move"
          style="
            display: inline-block;
            width: 1000px;
            height: 150px;
            background: #eee;
            overflow: auto;
          "
        >
          <div
            v-for="(val, key) in formData.ruleDetailList"
            :key="val.orderBy"
            class="fromdynamicItem"
          >
            <span style="margin: 0 40px">{{ val.orderBy }}</span>
            <el-form-item>
              <el-select
                v-model.trim="val.ruleType"
                :placeholder="$t('file_set.number_select_rule_type')"
                clearable
                :style="{ width: '100%' }"
                @change="handleChange(key)"
              >
                <el-option
                  v-for="(dict, i) in dict.type.version_rule_type"
                  :key="i"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                  :disabled="dict.disabled"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              style="width: 180.33px"
            >
              <el-input
                v-model.trim="val.startValue"
                :placeholder="$t('file_set.version_fill_content')"
              ></el-input>
            </el-form-item>
            <el-form-item
              style="width: 180.33px"
            >
              <el-input
                v-model.trim="val.endValue"
                :placeholder="$t('file_set.version_fill_content')"
              ></el-input>
            </el-form-item>
            <el-form-item
              style="width: 180.33px"
            >
              <el-input
                v-model.trim="val.digit"
                @input="(v)=>val.digit = v.replace(/[^\d]/g, '')"
                :placeholder="$t('file_set.version_fill_content')"
              ></el-input>
            </el-form-item>
            <el-form-item
              style="width: 60px"
            >
              <el-switch
                v-model.trim="val.sync"
                active-color="#13ce66"
                :active-value="true"
                :inactive-value="false"
                size="medium"
                inactive-color="#ff4949"
              >
              </el-switch>
            </el-form-item>
            <el-form-item>
              <i class="el-icon-rank" style="margin: 0 5px"></i>
              <i
                class="el-icon-delete"
                @click="deleteItem(val, key)"
                style="margin: 0 5px"
              ></i>
              <i class="el-icon-plus" @click="addItem"></i>
            </el-form-item>
          </div>
        </draggable>
        <div><el-button @click="addItem" type="primary">{{ $t('file_set.version_add') }}</el-button></div>
      </el-form-item>
      <!--  预览    -->
      <el-form-item size="large" :label="getLabelWithColon($t('file_set.version_preview'))">
        {{versionPreview }}
      </el-form-item>
      <el-form-item size="large">
        <el-button type="primary" @click="submitForm">{{ $t('doc.this_dept_annex') }}</el-button>
        <el-button @click="handleClose">{{ $t('doc.this_dept_close') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import draggable from "vuedraggable";
import { addVersionRule, getVersionRule, listVersionRule, updateVersionRule } from '@/api/setting/versionRule'
import { listVersionRuleDetail } from '@/api/setting/versionRuleDetail'
import {dictLanguage} from "../../../utils/ruoyi";
export default {
  dicts: ["version_rule_type"],
  components: { draggable },
  props: ["id"],
  data() {
    return {
      formData: {
        id: undefined,
        ruleName: undefined,
        nextId:undefined,
        startValue: undefined,
        ruleDetailList: [],
      },
      listCodeRuleOptions: [],
      rules: {
        ruleName: [
          {
            required: true,
            message: this.$t('file_set.version_fill_rule_name')+':',
            trigger: "blur",
          },
          {
            min: 1,
            max: 20,
            message: this.$t('file_set.number_long_rule'),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {},
  created() {
    this.getRuleList()
  },
  computed: {
    versionPreview(){
      if(this.formData&&this.formData.ruleDetailList&&this.formData.ruleDetailList.length>0){
        let arr = []
        this.formData.ruleDetailList.forEach(item=>{
          switch (item.ruleType) {
            case 'ALP':
              let part = '';
              for (let i = 0; i < item.digit; i++) {
                part += String.fromCharCode(Math.floor(Math.random() * (item.endValue.charCodeAt(0) - item.startValue.charCodeAt(0) + 1)) + item.startValue.charCodeAt(0));
              }
              arr.push(part);
              break;
            case 'NUM':
              let partNum = '';
              for (let i = 0; i < item.digit; i++) {
                partNum+=Math.floor(Math.random() * (item.endValue - item.startValue + 1));
              }
              arr.push(partNum);
              break;
            case 'STR':
              arr.push( item.startValue);
              break;
            default:
              console.error('未知的规则类型:', item.ruleType);
          }
        })
        return arr.join('')
      }else{
        return ''
      }
    }
  },
  mounted() {
    if (this.id) {
      getVersionRule(this.id).then(async(response) => {
        let formData = response.data;
        let res = await listVersionRuleDetail({
          ruleId: this.id,
          orderByColumn: 'order_by',
          isAsc: 'asc'
        })
        formData.ruleDetailList = res.rows;
        this.formData = formData
      });
    }
  },
  methods: {
    getRuleList(){
      listVersionRule({neId:this.id}).then(res=>{
        this.listCodeRuleOptions=res.rows
      })
    },
    handleClose() {
      this.$emit("chinldClose", false);
    },
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            updateVersionRule(formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.$emit("chinldClose");
            });
          } else {
            addVersionRule(formData).then((response) => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.$emit("chinldClose");
            });
          }
        }
      });
    },
    addItem() {
      if (this.formData.ruleDetailList.length===0) {
        this.formData.ruleDetailList.push({
          orderBy: 1,
          ruleType: "",
          ruleValue: "",
        });
      } else {
        var maximum = 1;
        this.formData.ruleDetailList.forEach((element) => {
          if (element.orderBy > maximum) {
            maximum = element.orderBy;
          }
        });
        this.formData.ruleDetailList.push({
          orderBy: maximum + 1,
          ruleType: "",
          ruleValue: "",
        });
      }
    },
    deleteItem(item, index) {
      this.formData.ruleDetailList.splice(index, 1);
    },

    handleChange(e) {

    },

    //evt里面有两个值，一个evt.added 和evt.removed  可以分别知道移动元素的ID和删除元素的ID
    change: function (evt) {
      this.formData.ruleDetailList.forEach((element, index) => {
        this.formData.ruleDetailList[index].orderBy = index + 1;
      });
    },
    //start ,end ,add,update, sort, remove 得到的都差不多
    start: function (evt) {
      //console.log(evt);
    },
    end: function (evt) {
      //console.log(evt);
      evt.item; //可以知道拖动的本身
      evt.to; // 可以知道拖动的目标列表
      evt.from; // 可以知道之前的列表
      evt.oldIndex; // 可以知道拖动前的位置
      evt.newIndex; // 可以知道拖动后的位置
    },
    move: function (evt, originalEvent) {
      // console.log(originalEvent); //鼠标位置
    },
  },
};
</script>
<style lang="scss" scoped>
.number_manageadd {
  .el-upload__tip {
    line-height: 1.2;
  }
  .fromdynamicItem {
    display: flex;
    margin: 5px 0px;
  }
}
</style>
