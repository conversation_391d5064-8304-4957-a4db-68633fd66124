<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>水印规则</span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>          
      <el-button type="primary" style="float:right; margin-left: 10px;" size="mini" @click="handleCopyAdd">拷贝新增</el-button>
      <el-button type="primary" style="float:right;" icon="el-icon-plus" size="mini" @click="handleAdd">新增模板</el-button>


      <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="规则名称" align="center" prop="ruleName" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="创建人" align="center" prop="createByName" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-picture" @click="handlePreview(scope.row)">预览</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>

    <!-- 添加或修改水印规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1250px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="水印规则" prop="ruleDetails">
          <el-button class="el-icon-plus" type="primary" @click="addItem()" style="float:right;"></el-button>

          <el-table ref="dragTable" :data="watermarkList" border style="width: 100%" row-key="id">
            <el-table-column type="index" label="序号" width="50" align="center" />
            <el-table-column label="水印类型" width="150">
              <template slot-scope="scope">
                <el-select v-model="scope.row.watermarkSettingCode" placeholder="请选择"
                  @change="changeWatermarkSettingName($event, scope.row)">
                  <el-option v-for="(changeval, changekey) in dict.type.watermark_type" :key="changekey"
                    :label="changeval.label" :value="changeval.value" :disabled="changeval.disabled">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="是否选择" width="120">
              <template slot-scope="scope">
                <el-select v-model="scope.row.watermarkSettingFactorUse" placeholder="请选择">
                  <el-option v-for="(changeval, changekey) in dict.type.yes_no" :key="changekey"
                    :label="changeval.label" :value="parseInt(changeval.value)" :disabled="changeval.disabled">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="样式" width="350">
              <template slot-scope="scope">
                <div style="display: flex; flex-direction: row;">
                  <el-input v-if="scope.row.watermarkSettingType === 'text'" v-model="scope.row.typeSize"
                    style="flex: 1;" placeholder="请输入字号" />
                  <el-input v-else v-model="scope.row.scale" style="flex: 1;" placeholder="请输入规模" />
                  <el-input v-model.number="scope.row.xposition" style="flex: 1;" placeholder="X坐标" />
                  <el-input v-model.number="scope.row.yposition" style="flex: 1;" placeholder="Y坐标" />
                </div>
              </template>
            </el-table-column>

            <el-table-column label="范围" width="250">
              <template slot-scope="scope">
                <div style="display: flex; flex-direction: row;">
                  <el-select v-model="scope.row.appliedRange" placeholder="请选择应用范围:" style="flex: 1;">
                    <el-option v-for="(changeval, changekey) in dict.type.applied_range" :key="changekey"
                      :label="changeval.label" :value="changeval.value" :disabled="changeval.disabled">
                    </el-option>
                  </el-select>
                  <el-select v-model="scope.row.pageOption" placeholder="请选择页面范围" style="flex: 1;">
                    <el-option v-for="(changeval, changekey) in dict.type.watermark_page_option" :key="changekey"
                      :label="changeval.label" :value="changeval.value" :disabled="changeval.disabled">
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <i class="el-icon-rank handle" style="margin: 0 5px; cursor: move"></i>
                <i class="el-icon-delete" v-show="scope.row.defaultFlag != 1"
                  @click="deleteWatermark(scope.row, scope.$index)" style="margin: 0 5px; cursor: pointer"></i>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 水印规则详情对话框 -->
    <el-dialog title="水印规则详情" :visible.sync="detailOpen" width="1000px" append-to-body>
      <div style="overflow-y: auto; max-height: 800px;">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="规则名称">{{ detailForm.ruleName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ detailForm.createTime }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">规则明细</el-divider>
        <el-table :data="detailWatermarkList" border style="width: 100%">
          <el-table-column type="index" label="序号" width="50" align="center" />
          <el-table-column label="水印类型" prop="watermarkSettingName" />
          <el-table-column label="是否选择" prop="watermarkSettingFactorUse">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.yes_no" :value="scope.row.watermarkSettingFactorUse" />
            </template>
          </el-table-column>
          <el-table-column label="样式">
            <template slot-scope="scope">
              {{scope.row.watermarkSettingType === 'text'?"字号":"规模"}}:{{ scope.row.typeSize }} {{ scope.row.scale }} <br/>
              X:{{ scope.row.xposition }}, Y:{{ scope.row.yposition }}
            </template>
          </el-table-column>
          <el-table-column label="范围" >
            <template slot-scope="scope">
              <el-row><el-col :span="12">应用范围: </el-col><el-col :span="12"><dict-tag :options="dict.type.applied_range" :value="scope.row.appliedRange" /></el-col></el-row>
              <el-row><el-col :span="12">页面范围: </el-col><el-col :span="12"><dict-tag :options="dict.type.watermark_page_option" :value="scope.row.pageOption" /></el-col></el-row>
            </template>
          </el-table-column>
        </el-table>

        <el-divider content-position="left">应用列表</el-divider>
        <div style="margin-bottom: 10px;">
          <el-button type="primary" icon="el-icon-plus" size="mini" style="float:right;" @click="handleAddUsage">新增</el-button>
        </div>
        <el-table :data="sortedUsageList" border style="width: 100%">
          <el-table-column label="文件类型" prop="className" />
          <el-table-column label="业务类型">
            <template slot-scope="scope">
              {{ getTypeText(scope.row.bizType) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleRemoveUsage(scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 新增应用对话框 -->
    <el-dialog title="新增应用" :visible.sync="addUsageOpen" width="500px" append-to-body>
      <el-form ref="addUsageForm" :model="addUsageForm" :rules="addUsageRules" label-width="100px">
        <el-form-item label="文件类型" prop="docClass">
          <treeselect
            v-model="addUsageForm.docClass"
            :options="classLevelOptions"
            :normalizer="normalizer"
            :searchable="true"
            :show-count="true"
            :clearable="false"
            placeholder="请选择文件类型"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="bizType">
          <el-select v-model="addUsageForm.bizType" placeholder="请选择业务类型" style="width: 100%">
            <el-option label="文件新增" value="ADD" />
            <el-option label="文件修订" value="UPDATE" />
            <el-option label="文件作废" value="DISUSE" />
            <el-option label="文件打印" value="PRINT" />
            <el-option label="文件留用" value="RETAIN" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否生效" prop="openFlag">
          <el-radio-group v-model="addUsageForm.openFlag">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="应用子分类" prop="applyFlag">
          <el-radio-group v-model="addUsageForm.applyFlag">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddUsage">确 定</el-button>
        <el-button @click="cancelAddUsage">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWatermarkRule, getWatermarkRule, delWatermarkRule, addWatermarkRule, updateWatermarkRule, checkWatermarkRuleUsage, removeWatermarkRuleUsage, addWatermarkRuleUsage } from "@/api/setting/watermarkRule";
import { listDocClass } from "@/api/setting/docClass";
import Sortable from 'sortablejs'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "WatermarkRule",
  components: { Treeselect },
  dicts: ["applied_range", "watermark_page_option", "watermark_type", "yes_no"],
  data() {
    // 自定义水印规则验证
    const validateWatermarkRules = (rule, value, callback) => {
      if (!this.watermarkList || this.watermarkList.length === 0) {
        callback(new Error('请至少添加一条水印规则'));
        return;
      }

      // 用于检查重复的水印类型
      const typeMap = new Map();

      // 检查每一条规则
      for (let i = 0; i < this.watermarkList.length; i++) {
        const rule = this.watermarkList[i];

        // 检查水印类型是否选择
        if (!rule.watermarkSettingCode) {
          callback(new Error(`第${i + 1}行水印类型不能为空`));
          return;
        }

        // 检查水印类型是否重复
        if (typeMap.has(rule.watermarkSettingCode)) {
          callback(new Error(`水印类型"${rule.watermarkSettingName}"重复`));
          return;
        }
        typeMap.set(rule.watermarkSettingCode, true);

        // 检查是否选择
        if (rule.watermarkSettingFactorUse === undefined) {
          callback(new Error(`第${i + 1}行是否选择不能为空`));
          return;
        }

        // 根据水印类型检查样式
        if (rule.watermarkSettingType === 'text') {
          if (!rule.typeSize) {
            callback(new Error(`第${i + 1}行字号不能为空`));
            return;
          }
        } else if (rule.watermarkSettingType === 'image') {
          if (!rule.scale) {
            callback(new Error(`第${i + 1}行规模不能为空`));
            return;
          }
        }

        // 检查位置坐标
        if (rule.xposition === undefined || rule.xposition === '') {
          callback(new Error(`第${i + 1}行X坐标不能为空`));
          return;
        }
        if (rule.yposition === undefined || rule.yposition === '') {
          callback(new Error(`第${i + 1}行Y坐标不能为空`));
          return;
        }

        // 检查应用范围
        if (!rule.appliedRange) {
          callback(new Error(`第${i + 1}行应用范围不能为空`));
          return;
        }

        // 检查页面范围
        if (!rule.pageOption) {
          callback(new Error(`第${i + 1}行页面范围不能为空`));
          return;
        }
      }

      callback();
    };

    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 水印规则表格数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        ruleName: undefined,
        remark: undefined,
        ruleDetails: undefined
      },
      // 水印列表
      watermarkList: [],
      // 表单校验规则
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        ruleDetails: [
          { required: true, validator: validateWatermarkRules, trigger: "blur" }
        ]
      },
      sortable: null,
      detailOpen: false,
      detailForm: {},
      detailWatermarkList: [],
      usageList: [],
      addUsageOpen: false,
      addUsageForm: {
        docClass: undefined,
        bizType: undefined,
        openFlag: 'Y',
        applyFlag: 'Y',
        ruleId: undefined
      },
      addUsageRules: {
        docClass: [
          { required: true, message: '请选择文件类型', trigger: 'change' }
        ],
        bizType: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ]
      },
      docClassOptions: [],
      // 添加树形控件所需的数据
      classLevelOptions: []
    };
  },
  created() {
    this.getList();
    // 获取文件类型列表并转换为树形结构
    listDocClass().then(response => {
      this.docClassOptions = response.rows;
      // 转换为树形结构
      this.classLevelOptions = this.handleTree(response.rows,"id","parentClassId");
      console.log(this.classLevelOptions);
    });
  },
  computed: {
    sortedUsageList() {
      if(!this.usageList) return [];
      // 对usageList进行排序 
      return this.usageList.sort((a, b) => a.className.localeCompare(b.className));
    }
  },
  methods: {
    /** 查询水印规则列表 */
    getList() {
      this.loading = true;
      listWatermarkRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        ruleName: undefined,
        remark: undefined,
        ruleDetails: undefined
      };
      this.watermarkList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加水印规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      getWatermarkRule(row.id).then(response => {
        this.form = response.data;
        if (this.form.ruleDetails) {
          this.watermarkList = JSON.parse(this.form.ruleDetails);
        }
        this.open = true;
        this.title = "修改水印规则";
        this.loading = false;
      });
    },
    /** 修改按钮操作 */
    handleCopyAdd() {
      
      const ids = this.ids;
      if(!ids||ids.length==0) {
        this.$message.error("请先选择要拷贝的水印规则");
        return;
      }
      this.reset();
      getWatermarkRule(ids[0]).then(response => {
        this.form = response.data;
        this.form.id=undefined;
        if (this.form.ruleDetails) {
          this.watermarkList = JSON.parse(this.form.ruleDetails);
        }
        this.open = true;
        this.title = "拷贝新增水印规则";
        this.loading = false;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 手动触发水印规则验证
          this.rules.ruleDetails[0].validator(null, this.form.ruleDetails, (error) => {
            if (error) {
              this.$message.error(error.message);
              return;
            }

            // 验证通过，继续提交
            this.form.ruleDetails = JSON.stringify(this.watermarkList);

            if (this.form.id) {
              // 修改时先检查规则是否被使用
              checkWatermarkRuleUsage(this.form.id).then(response => {
                if (response.data && response.data.length > 0) {
                  // 显示确认对话框
                  this.$confirm('该水印规则已被以下文件类型使用：<br/>' + 
                    response.data.map(item => `${item.className}(${this.getTypeText(item.bizType)})`).join('<br/>') + 
                    '<br/>是否覆盖这些配置？', '提示', {
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    distinguishCancelAndClose: true,
                    closeOnClickModal: false,
                    dangerouslyUseHTMLString: true,
                    showClose: true,
                    type: 'warning',
                    callback: action => {
                      if (action === 'confirm') {
                        // 选择"是"，更新所有使用此规则的配置
                        updateWatermarkRule({...this.form, updateAll: true}).then(response => {
                          this.$modal.msgSuccess("修改成功");
                          this.open = false;
                          this.getList();
                        });
                      } else if (action === 'cancel') {
                        // 选择"否"，只更新规则本身
                        updateWatermarkRule({...this.form, updateAll: false}).then(response => {
                          this.$modal.msgSuccess("修改成功");
                          this.open = false;
                          this.getList();
                        });
                      }
                    }
                  });
                } else {
                  // 规则未被使用，直接更新
                  updateWatermarkRule(this.form).then(response => {
                    this.$modal.msgSuccess("修改成功");
                    this.open = false;
                    this.getList();
                  });
                }
              });
            } else {
              addWatermarkRule(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      // 先检查规则是否被使用
      checkWatermarkRuleUsage(ids).then(response => {
        if (response.data && response.data.length > 0) {
          this.$modal.msgError('该水印规则已被以下文件类型使用，无法删除：\n' + 
            response.data.map(item => `${item.className}(${this.getTypeText(item.bizType)})`).join('\n'));
        } else {
          // 未被使用时才允许删除
          this.$modal.confirm('是否确认删除水印规则编号为"' + ids + '"的数据项？').then(() => {
            return delWatermarkRule(ids);
          }).then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          }).catch(() => { });
        }
      });
    },
    /** 添加水印项目 */
    addItem() {
      this.watermarkList.push({
        watermarkSettingType: "",
        watermarkSettingName: "",
        watermarkSettingCode: "",
        watermarkSettingFactorUse: 2,
        xposition: undefined,
        yposition: undefined,
        appliedRange: undefined,
        pageOption: undefined,
      });
    },
    /** 删除水印 */
    deleteWatermark(row, index) {
      this.watermarkList.splice(index, 1);
    },
    /** 水印类型改变事件 */
    changeWatermarkSettingName(val, item) {
      let selectWatermarkType = this.dict.type.watermark_type.find(item => item.value === val)
      // 选中水印名称赋值
      item.watermarkSettingCode = val
      item.watermarkSettingName = selectWatermarkType.label
      // 设置watermarkSettingType值（水印类型，字典中带image是图片，否则是文本）
      if (val.indexOf('image') >= 0) {
        item.watermarkSettingType = 'image'
        item.typeSize = ''
      } else {
        item.watermarkSettingType = 'text'
        item.scale = ''
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 初始化排序 */
    initSortable() {
      // 添加空值检查
      if (!this.$refs.dragTable || !this.$refs.dragTable.$el) {
        return;
      }
      
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      // 添加元素存在检查
      if (!el) {
        return;
      }
      
      this.sortable = Sortable.create(el, {
        handle: '.handle',
        animation: 300,
        onEnd: evt => {
          const targetRow = this.watermarkList.splice(evt.oldIndex, 1)[0];
          this.watermarkList.splice(evt.newIndex, 0, targetRow);
        }
      });
    },
    // 获取业务类型文本
    getTypeText(type) {
      const typeMap = {
        'ADD': '文件新增',
        'UPDATE': '文件修订',
        'DISUSE': '文件作废',
        'PRINT': '文件打印',
        'RETAIN': '文件留用'
      };
      return typeMap[type] || type;
    },
    /** 查看详情按钮操作 */
    handleDetail(row) {
      this.loading = true;
      Promise.all([
        getWatermarkRule(row.id),
        checkWatermarkRuleUsage(row.id)
      ]).then(([ruleRes, usageRes]) => {
        this.detailForm = ruleRes.data;
        this.detailWatermarkList = JSON.parse(ruleRes.data.ruleDetails || '[]');
        this.usageList = usageRes.data || [];
        this.detailOpen = true;
        this.loading = false;
      });
    },
    /** 移除规则应用 */
    handleRemoveUsage(row) {
      this.$modal.confirm('确定要移除该文件类型的水印规则吗？').then(() => {
        // 这里需要后端提供移除规则应用的接口
        removeWatermarkRuleUsage({
          id: row.id,
          ruleId: this.detailForm.id,
          className: row.docClass,
          bizType: row.bizType
        }).then(() => {
          this.$modal.msgSuccess("移除成功");
          // 重新加载应用列表
          checkWatermarkRuleUsage(this.detailForm.id).then(response => {
            this.usageList = response.data || [];
          });
        });
      });
    },
    /** 新增应用按钮操作 */
    handleAddUsage() {
      this.addUsageForm.ruleId = this.detailForm.id;
      this.addUsageOpen = true;
      // 如果classLevelOptions为空，重新获取文件类型列表
      if (!this.classLevelOptions || this.classLevelOptions.length === 0) {
        listDocClass().then(response => {
          this.docClassOptions = response.rows;
          // 转换为树形结构
          this.classLevelOptions = this.handleTree(response.rows,"id","parentClassId")
        });
      }
    },
    /** 取消新增应用 */
    cancelAddUsage() {
      this.addUsageOpen = false;
      this.resetAddUsageForm();
    },
    /** 重置新增应用表单 */
    resetAddUsageForm() {
      this.addUsageForm = {
        docClass: undefined,
        bizType: undefined,
        openFlag: 'Y',
        applyFlag: 'Y',
        ruleId: undefined
      };
      if (this.$refs.addUsageForm) {
        this.$refs.addUsageForm.resetFields();
      }
    },
    /** 提交新增应用 */
    submitAddUsage() {
      this.$refs.addUsageForm.validate(valid => {
        if (valid) {
          addWatermarkRuleUsage(this.addUsageForm).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.addUsageOpen = false;
            // 重新加载应用列表
            checkWatermarkRuleUsage(this.detailForm.id).then(response => {
              this.usageList = response.data || [];
            });
          });
        }
      });
    },
   
    
    // 添加树节点格式化方法
    normalizer(node) {
      return {
        id: node.id,
        label: node.className,
        children: node.children
      };
    },
    /** 预览水印规则 */
    handlePreview(row) {
      const { id } = row;
      window.open(`${process.env.VUE_APP_BASE_API}/setting/watermarkRule/preview?id=${id}`, '_blank');
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 添加延迟以确保表格完全渲染
      setTimeout(() => {
        this.initSortable();
      }, 100);
    });
  },
  watch: {
    open(val) {
      if (val) {
        this.$nextTick(() => {
          // 添加延迟以确保表格完全渲染
          setTimeout(() => {
            this.initSortable();
          }, 100);
        });
      }
    }
  }
};
</script>

<style scoped>
.el-table {
  margin-top: 15px;
}

.handle {
  color: #909399;
}

.handle:hover {
  color: #409EFF;
}
</style>
