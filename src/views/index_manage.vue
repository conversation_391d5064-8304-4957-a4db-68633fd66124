<template>
  <div class="dashboard-editor-container">
    <!-- 顶部汇总统计 -->
    <el-row :gutter="24" class="panel-group" style="height:35px;">
      <el-col :xs="24" :sm="24" :lg="24">
        <span style="font-size:23px">公司体系文件台账{{panelGroup.docAccount}}个，其中有效文件{{panelGroup.effectiveCount}}个、作废文件{{panelGroup.disUseCount}}个、草稿文件{{panelGroup.draftCount}}个；阅知文件总计{{panelGroup.viewCount}}人次；涉及对体系文件的新增、修订、作废总计{{panelGroup.flowCount}}次。</span>
      </el-col>
    </el-row>
    <!-- 顶部汇总统计 
    <el-row :gutter="24" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            草稿
          </div>
          <count-to :start-val="0" :end-val="panelGroup.draftCount" :duration="2600" class="card-panel-num" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            作废
          </div>
          <count-to :start-val="0" :end-val="panelGroup.disUseCount" :duration="2600" class="card-panel-num" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            有效
          </div>
          <count-to :start-val="0" :end-val="panelGroup.effectiveCount" :duration="2600" class="card-panel-num" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            台账
          </div>
          <count-to :start-val="0" :end-val="panelGroup.docAccount" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('purchases')">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            阅知
          </div>
          <count-to :start-val="0" :end-val="panelGroup.viewCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="shopping" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            流程
          </div>
          <count-to :start-val="0" :end-val="panelGroup.flowCount" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row> -->

    <!-- 中间方格图 -->
    <el-row :gutter="24">
       <!-- 文件类型文件个数统计 -->
       <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <rz-pie-chart2 @queryFileCountByClassType="queryFileCountByClassType" :chartData="classTypeFileChart" />
        </div>
      </el-col>
      <!-- 二级子分类文件个数统计 -->
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
           <rz-pie-chart2 :chartData="subClassTypeFileChart" />
        </div>
      </el-col>
      <!-- 变更统计 -->
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <!-- 
          <bar-chart />
          <rz-category />-->
          <rz-pie-chart ref="changeTypeSumChart"/>
        </div>
      </el-col>
    </el-row>

    <!-- 部门归口文件个数柱状图 -->
    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <rz-bar-chart :chartData="secDeptGkFileChart"/>
    </el-row>

    <!-- 部门阅知人次柱状图 -->
    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <rz-bar-chart :chartData="secDeptViewFileChart"/>
    </el-row>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import PanelGroup from './dashboard/rz/PanelGroup'
// 
import RzPieChart from './dashboard/rz/RzPieChart'
import RzCategory from './dashboard/rz/RzCategory'


import RzBarChart from './dashboard/rz/RzBarChart'
import RzPieChart2 from './dashboard/rz/RzPieChart2'

import { queryDocAccount,queryViewCount,queryFlowCount,queryClassTypeFileCount,queryFileCountByClassType,querySecDeptGkFileCount,querySecDeptViewCount } from "@/api/index/select";

// 变更类型统计占比
import { changeTypeSum } from "@/api/statistics/personal";
export default {
  name: 'Index',
  components: {
    CountTo,
    RzPieChart,
    RzCategory,
    RzBarChart,
    RzPieChart2
  },
  data() {
    return {
      // 顶部面板
      panelGroup: {
        // 总台账、作废、有效、草稿
        docAccount: 0,
        disUseCount: 0,
        effectiveCount: 0,
        draftCount: 0,
        // 阅知、流程
        viewCount: 0,
        flowCount: 0,
      },
      // 文件类型占比个数
      classTypeFileChart: {
        name:"文件类型占比个数",
        data:[],
      },
      // 子文件分类占比个数
      subClassTypeFileChart: {
        name:"子文件分类占比个数",
        data:[],
      },
      // 二级部门归口文件个数
      secDeptGkFileChart: {
        name:"部门归口文件个数",
        data:[],
      },
      // 二级部门阅知文件次数
      secDeptViewFileChart: {
        name:"部门阅知文件次数",
        data:[],
      },
    }
  },
  created() {
    this.loadPageData()
  },
  methods: {
    loadPageData() {
      // 台账总数，按照状态进行统计
      this.queryDocAccount()
      // 阅知人次
      this.queryViewCount()
      // 流程总数
      this.queryFlowCount()
      // 文件类型占比个数
      this.queryClassTypeFileCount()
      // 二级部门归口文件个数
      this.querySecDeptGkFileCount()
      // 二级部门阅知文件次数
      this.querySecDeptViewCount()
      // 变更统计占比
      this.changeTypeSum()
    },
    // 变更统计占比
    changeTypeSum() {
      changeTypeSum({}).then((response) => {
        this.$refs.changeTypeSumChart.initChart(response.data)
      });
    },
    // 台账总数，按照状态进行统计
    queryDocAccount() {
      let _this = this
      _this.loading = true
      let queryParams = {}
      // 台账、有效、草稿
      _this.panelGroup.docAccount = 0
      _this.panelGroup.effectiveCount = 0
      _this.panelGroup.draftCount = 0
      queryDocAccount(queryParams).then((res) => {
        if(res.data.length > 0) {

          res.data.forEach(element => {
            if(element.status == "1") {
              // 有效
              _this.panelGroup.effectiveCount += element.num
            } else if(element.status == "2") {
              // 作废
              _this.panelGroup.disUseCount += element.num
            } else {
              // 草稿
              _this.panelGroup.draftCount += element.num
            }
            // 总台账
            _this.panelGroup.docAccount += element.num
          });
        }
        _this.loading = false
      });
    },
    // 阅知人次
    queryViewCount() {
      let _this = this
      _this.loading = true
      let queryParams = {}
      queryViewCount(queryParams).then((res) => {
        if(res.data) {
          _this.panelGroup.viewCount = res.data.num
        }
        _this.loading = false
      });
    },
    // 流程总数
    queryFlowCount() {
      let _this = this
      _this.loading = true
      let queryParams = {}
      queryFlowCount(queryParams).then((res) => {
        if(res.data) {
          _this.panelGroup.flowCount = res.data.num
        }
        _this.loading = false
      });
    },
    // 文件类型占比个数
    queryClassTypeFileCount() {
      let _this = this
      _this.loading = true
      let queryParams = {}
      _this.classTypeFileChart.data = []
      queryClassTypeFileCount(queryParams).then((res) => {
        if(res.data.length > 0) {
          _this.classTypeFileChart.data = res.data
          // 文件类型下的子分类占比个数
          _this.queryFileCountByClassType(res.data[0].class_id)
        }
        _this.loading = false
        
      });
    },
    // 文件类型下的子分类占比个数
    queryFileCountByClassType(classType) {
      let _this = this
      _this.loading = true
      let queryParams = {}
      _this.subClassTypeFileChart.data = []
      queryFileCountByClassType(classType).then((res) => {
        if(res.data.length > 0) {
          _this.subClassTypeFileChart.data = res.data;
        }
        _this.loading = false
      });
    },
    // 二级部门归口文件个数
    querySecDeptGkFileCount() {
      let _this = this
      _this.loading = true
      let queryParams = {}
      _this.secDeptGkFileChart.data = []
      querySecDeptGkFileCount(queryParams).then((res) => {
        if(res.data.length > 0) {
          _this.secDeptGkFileChart.data = res.data
        }
        _this.loading = false
      });
    },
    // 二级部门阅知文件次数
    querySecDeptViewCount() {
      let _this = this
      _this.loading = true
      let queryParams = {}
      _this.secDeptViewFileChart.data = []
      querySecDeptViewCount(queryParams).then((res) => {
        if(res.data.length > 0) {
          _this.secDeptViewFileChart.data = res.data
        }
        _this.loading = false
      });
    },

  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}

</style>
