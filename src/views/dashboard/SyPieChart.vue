<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "500px",
    },
    height: {
      type: String,
      default: "300px",
    },
    wenjshenxiao: [],
    seriesdata: [],
    legenddata: [],
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      let arr = [];
      let legend = [];
      console.log("this.wenjshenxiao", this.wenjshenxiao);
      this.wenjshenxiao.forEach((element) => {
        arr.push({ value: element.num, name: element.name });
        legend.push(element.name);
      });
      console.log("arr", arr);
      this.seriesdata = arr;
      this.legenddata = legend;
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          right: 10,
          top: 50,
          bottom: 20,
          data: this.legenddata,
        },
        series: [
          {
            name: "变更类型统计占比",
            type: "pie",

            radius: [75, 95],
            center: ["50%", "38%"],
            data: this.seriesdata,
            animationEasing: "cubicInOut",
            animationDuration: 2600,
          },
        ],
      });
    },
  },
};
</script>
