<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "../mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "600px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        legend: {},
        tooltip: {},
        dataset: {
          source: [
            ["product", "签收时长", "文件回收率"],
            ["OA", 43.3, 85.8],
            ["QC", 83.1, 73.4],
            ["SC", 86.4, 65.2],
            ["SB", 72.4, 53.9],
            ["WL", 72.4, 53.9],
            ["HR", 72.4, 53.9],
          ],
        },
        xAxis: { 
              type: "category",
              axisLine:{
                show:false
              },
              axisTick:{       //y轴刻度线
                show:false
              },
              axisLabel: {
                
                lineStyle:{
                  
                    color:'#cccccc' //更改坐标轴颜色
                },
                textStyle: {
                    color: '#333333',  //更改坐标轴文字颜色
                }, 
              },
            },
        yAxis: {
          axisTick:{       //y轴刻度线
            show:false
          },
          splitArea: {
            show: false
          },
          axisLine:{
            show:false
          },
          axisLabel: {
                lineStyle:{
                    color:'#cccccc' //更改坐标轴颜色
                },
                textStyle: {
                    color: '#333333',  //更改坐标轴文字颜色
                }, 
              },
        },
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{
            type: "bar",
            barWidth: 30, //柱图宽度
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#B8F29B'
            }, 
          }, { 
            type: "bar" ,
            label: {
              show: true,
              position: 'top'
            },
            barWidth: 30, //柱图宽度
            itemStyle: {
              color: '#90B3F0'
            },
          }],
      });
    },
  },
};
</script>
