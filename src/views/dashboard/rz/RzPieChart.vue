<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "../mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(data) {
      if(data==null){
        data={
          total:0,
          addNum:0,
          updateNum:0,
          disuseNum:0
        }
      }
      this.chart = echarts.init(this.$el, "macarons");

      let option = {
        graphic: {
          type: 'text',
          left: 'center',
          // top: 'center',
          top: '35%',
          style: {
            text: this.$t(`file_count.personal_total_times`)+'\n\n'+data.total,
            textAlign: 'center',
            fill: '#333',
            width: 30,
            height: 20,
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 50,
          bottom: 20,
          formatter: function (name) {
            let data = option.series[0].data;
            let total = 0;
            let tarValue;
            for (let i = 0; i < data.length; i++) {
              total += data[i].value;
              if (data[i].name == name) {
                tarValue = data[i].value;
              }
            }
            let v = tarValue;
            let p = Math.round((tarValue / total) * 100) + '%';
            if(tarValue===0){
              p='0%'
            }
            if(p.length==3){
              p="  "+p
            }
            if(p.length==2){
              p="    "+p
            }
            return `${name}  |    ${p}                  ${v}`;
          },
          data: [
            {
              name: this.$t(`doc.this_dept_new_add`),
              icon: 'circle',
              itemStyle: {
                color: '#3ba1ff'
              }
            },
            {
              name: this.$t(`doc.this_dept_revision`),
              icon: 'circle',
              itemStyle: {
                color: '#4ecb73'
              }
            },
            {
              name: this.$t(`doc.this_dept_cancel`),
              icon: 'circle',
              itemStyle: {
                color: '#fbd437'
              }
            }
          ]
        },
        series: [
          {
            name: '变更类型统计占比',
            type: 'pie',
            radius: [75, 95],
            center: ['50%', '38%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            animationEasing: 'cubicInOut',
            animationDuration: 2600,
            data: [
              {
                value: data.addNum,
                name: this.$t(`doc.this_dept_new_add`),
                itemStyle: {
                  color: '#3ba1ff'
                }
              },
              {
                value: data.updateNum,
                name: this.$t(`doc.this_dept_revision`),
                itemStyle: {
                  color: '#4ecb73'
                }
              },
              {
                value: data.disuseNum,
                name: this.$t(`doc.this_dept_cancel`),
                itemStyle: {
                  color: '#fbd437'
                }
              }
            ]
          }
        ]
      };
      this.chart.setOption(option);
    },
  },
};
</script>
