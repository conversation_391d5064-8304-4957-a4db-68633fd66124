<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '../mixins/resize'

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object,
      default: {}
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    chartData: {
            // 每个属性值发生变化就会调用这个函数
            handler(newVal, oldVal) {
              this.$nextTick(() => {
                this.initChart()
              })
            },
            // 立即处理 进入页面就触发
            immediate: true,
            // 深度监听 属性的变化
            deep: true
    },
  },
  mounted() {
    
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    queryFileCountByClassType(classType) {
      this.$emit('queryFileCountByClassType', classType)
    },
    initChart() {
      let _this = this
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        /*
        title: {
          text: 'Referer of a Website',
          subtext: 'Fake Data',
          left: 'center'
        }, */
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          // bottom: '10'
        },
        series: [
          {
            // 
            name: this.chartData.name,
            type: 'pie',
            // roseType: 'radius',
            // radius: '50%',
            //radius: [15, 95],
            //center: ['50%', '38%'],
            /*
            data: [
              { value: 320, name: '一级文件' },
              { value: 240, name: '二级文件' },
              { value: 149, name: '三级文件' },
              { value: 100, name: '四级文件' },
              { value: 59, name: '五级文件' }
            ],*/
             data: this.chartData.data,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
      // 点击事件
      this.chart.on("click",function(param) {
     		_this.queryFileCountByClassType(param.data.class_id)
      });
    }
  }
}
</script>
