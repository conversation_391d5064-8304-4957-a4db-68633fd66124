<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '../mixins/resize'

const animationDuration = 6000

//	单一柱状图的颜色
// #389cef=亮蓝色  #7cb5ec=蓝色  #00BB00=绿色
var CONFIG_SINGLE_BAR_COLOR = "#389cef";
//单一柱状图-柱子的宽度
var CONFIG_SINGLE_BAR_WIDTH = "50%";
//	单一柱子，上显示的文字，和字体大小
var CONFIG_SINGLE_BAR_LABEL_COLOR = "#389cef"; //#800080
var CONFIG_SINGLE_BAR_LABEL_FONT_SIZE = 16;

// 单一柱子，X上显示的文字 倾斜度
// 0=横向显示，数字越大，倾斜越大
var CONFIG_SINGLE_BAR_AXISLABEL_TORATE = 30;

// 是否自定义显示X轴上的文字
var CONFIG_SINGLE_BAR_AXISLABEL_FORMATTER_FLAG = false;

//单一柱子，下边距
var CONFIG_SINGLE_BAR_GRID_BOTTOM = 0;

export default {
  mixins: [resize],
  props: {
    chartData: {
      type: Object,
      default: {}
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      xAxisData:[],
    }
  },
  watch: {
    chartData: {
            // 每个属性值发生变化就会调用这个函数
            handler(newVal, oldVal) {
              this.rebuildxAxisData()
            },
            // 立即处理 进入页面就触发
            immediate: true,
            // 深度监听 属性的变化
            deep: true
    },
  },
  mounted() {
    /*
    this.$nextTick(() => {
      this.initChart()
    })*/
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    // 重新构建X轴名称
    rebuildxAxisData() {
      let _this = this
      _this.chartData.data.forEach(element => {
        // X轴节点
        _this.xAxisData.push(element.name)
      });
      // 加载图形
      _this.$nextTick(() => {
        _this.initChart()
      })
    },
    // 初始化图形
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 15,
          left: '10',
          right: '10',
          //bottom: '3%',
          bottom: CONFIG_SINGLE_BAR_GRID_BOTTOM,
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          // ['质量管理中心', '生产部', '研发部', '商务部', '人力资源部', '营销中心', '行政部'],
          data: this.xAxisData,
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
		        interval:0,  
				    rotate:CONFIG_SINGLE_BAR_AXISLABEL_TORATE,
		        textStyle: {
		          // fontSize: 14
		        }
		      },
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        series: [{
          // '归口文件个数',
          name: this.chartData.name,
          type: 'bar',
          stack: 'vistors',
          //barWidth: '30%',
          barWidth: CONFIG_SINGLE_BAR_WIDTH,
          data: this.chartData.data,
          /*
          data: [
            { value: 320, name: '质量管理中心' },
              { value: 240, name: '生产部' },
              { value: 149, name: '研发部' },
              { value: 100, name: '商务部' },
              { value: 59, name: '人力资源部' }
            ], */
          animationDuration,
          itemStyle: {
				    normal: {
				      label: {
				        show: true,
				        position: 'top',
				        textStyle: {
				          // fontSize:CONFIG_SINGLE_BAR_LABEL_FONT_SIZE,
				          color: CONFIG_SINGLE_BAR_LABEL_COLOR
				        }
				      },
				      color: function(params) {
				        return CONFIG_SINGLE_BAR_COLOR
		          }
		      }
		      },
        }]
      })
    }
  }
}
</script>
