<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";
export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "800px",
    },
    height: {
      type: String,
      default: "300px",
    },
    wenjianyi: [],
  },
  data() {
    return {
      chart: null,
      yAxisdata: [],
      seriesdata: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.wenjianyi.forEach((element) => {
        this.yAxisdata.push(element.name);
        this.seriesdata.push(element.num);
      });
  
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {},
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          
          splitArea: {
            show: false
          },
          axisLine:{
            show:false
          },
          axisTick:{
            show:false
          },
          axisLabel: {
            lineStyle:{
                color:'#cccccc' //更改坐标轴颜色
            },
            textStyle: {
              color: '#333333',  //更改坐标轴文字颜色
            },
          }
        },
        yAxis: {
          type: "category",
          data: this.yAxisdata,
          axisLine:{
            show:false
          },
          axisTick:{
            show:false
          },
          axisLabel: {
            lineStyle:{
                color:'#cccccc' //更改坐标轴颜色
            },
            textStyle: {
              color: '#333333',  //更改坐标轴文字颜色
            },
          }
        },
        series: [
          {
            type: "bar",
            data: this.seriesdata,
            barWidth: 30, //柱图宽度
            label: {
              show: true,
              position: 'right'
            },
            itemStyle: {
              color: "#8FB2F0",
            },
          },
        ],
      });
    },
  },
};
</script>
