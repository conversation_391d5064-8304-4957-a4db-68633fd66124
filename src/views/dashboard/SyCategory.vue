<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "800px",
    },
    height: {
      type: String,
      default: "600px",
    },
    wenjtongji: [],
  },
  data() {
    return {
      chart: null,
      xAxisdata: [],
      seriesdata: [],
    };
  },
  watch: {
    wenjtongji(val) {
      this.xAxisdata = [];
      this.seriesdata = [];
      console.log("111");
      this.wenjtongji.forEach((element) => {
        this.xAxisdata.push(element.name);
        this.seriesdata.push(element.num);
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      console.log("xAxisdata", this.xAxisdata);
      this.wenjtongji.forEach((element) => {
        this.xAxisdata.push(element.name);
        this.seriesdata.push(element.num);
      });
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");

      this.chart.setOption({
        xAxis: {
          type: "category",
          
          data: this.xAxisdata,
          axisLine:{
            show:false
          },
          axisTick:{
            show:false
          },
          axisLabel: {
            interval: 0,
            lineStyle:{
                color:'#cccccc' //更改坐标轴颜色
            },
            textStyle: {
              color: '#333333',  //更改坐标轴文字颜色
            },
            // formatter: function (value) {
        
            //   var ret = ""; //拼接加\n返回的类目项
            //   var maxLength = 2; //每项显示文字个数
            //   var valLength = value.length; //X轴类目项的文字个数
            //   var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
            //   if (rowN > 1) {
            //     //如果类目项的文字大于3,
            //     for (var i = 0; i < rowN; i++) {
            //       var temp = ""; //每次截取的字符串
            //       var start = i * maxLength; //开始截取的位置
            //       var end = start + maxLength; //结束截取的位置
            //       //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
            //       temp = value.substring(start, end) + "\n";
            //       ret += temp; //凭借最终的字符串
            //     }
            //     return ret;
            //   } else {
            //     return value;
            //   }
            // },
          },
        },
        yAxis: {
          type: "value",
          splitArea: {
            show: false
          },
          axisTick:{
            show:false
          },
          axisLine:{
            show:false
          },
          axisLabel: {
            lineStyle:{
                color:'#cccccc' //更改坐标轴颜色
            },
            textStyle: {
              color: '#333333',  //更改坐标轴文字颜色
            },
          }
        },

        series: [
          {
            data: this.seriesdata,
            type: "bar",
            barWidth: 30, //柱图宽度
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              color: '#8FB2F0'
            },
            
          },
        ],
      });
    },
  },
};
</script>
