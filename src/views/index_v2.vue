<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :span="13">
        <el-card>
          <div slot="header" class="clearfix"><span>快捷操作</span></div>
          <div class="kjcz-box">
            <div class="list">
              <img src="../assets/images/icon1.png" />
              <p class="title">借阅</p>
            </div>
            <div class="list">
              <img src="../assets/images/icon2.png" />
              <p class="title">公司文件</p>
            </div>
            <div class="list">
              <img src="../assets/images/icon3.png" />
              <p class="title">新增</p>
            </div>
            <div class="list">
              <img src="../assets/images/icon4.png" />
              <p class="title">修订</p>
            </div>
            <div class="list">
              <img src="../assets/images/icon5.png" />
              <p class="title">作废</p>
            </div>
          </div>
        </el-card>
        <el-card>
          <div slot="header" class="clearfix"><span>操作指引</span></div>
          <div class="xszy-box">
            <div class="list">
              <div class="clum">组织维护</div>
              <div class="text">
                <span class="strator4">维护公司部门</span>
                <span class="strator4">维护角色权限</span>
                <span class="strator4">添加用户</span>
              </div>
            </div>
            <div class="list">
              <div class="clum">文件类型维护</div>
              <div class="text">
                <span class="strator2">添加文件类型</span>
                <span class="strator2">设置文件类型</span>
              </div>
            </div>
            <div class="list">
              <div class="clum">文件变更</div>
              <div class="text">
                <span class="strator3">新增/修订/作废文件</span>
                <span class="strator1">文件审核</span>
                <span class="strator1">文件批准</span>
                <span class="strator3">文件培训</span>
                <span class="strator2">合稿/签章/分发/归档</span>
              </div>
            </div>
            <div class="list">
              <div class="clum">文件处理</div>
              <div class="text">
                <span class="strator3">复审</span>
                <span class="strator3">增发</span>
                <span class="strator3">补发</span>
                <span class="strator3">签收</span>
                <span>借阅</span>
                <span>查看</span>
                <span>文件建议</span>
                <span class="strator3">打印</span>
                <span class="strator3">回收</span>
              </div>
            </div>
            <div class="annot">
              <div class="text strator1">领导</div>
              <div class="text strator2">公司文件管理员</div>
              <div class="text strator3">部门文件管理员</div>
              <div class="text strator4">系统管理员</div>
              <div class="text strator5">公司员工个人</div>
            </div>
          </div>
        </el-card>
        <el-card>
          <div slot="header" class="clearfix"><span>最近查看</span></div>
          <div class="file_list">
            <div class="list">
              <div class="cell-ico">
                <img src="../assets/images/icon6.png" />
              </div>
              <div class="cell-text">
                <p class="title">关于生产质量部仪器装配操作规范说明</p>
                <p class="text">
                  <span>v1.0</span><span>人力资源部</span
                  ><span>2021-12-24</span>
                </p>
              </div>
            </div>
            <div class="list">
              <div class="cell-ico">
                <img src="../assets/images/icon6.png" />
              </div>
              <div class="cell-text">
                <p class="title">关于生产质量部仪器装配操作规范说明</p>
                <p class="text">
                  <span>v1.0</span><span>人力资源部</span
                  ><span>2021-12-24</span>
                </p>
              </div>
            </div>
            <div class="list">
              <div class="cell-ico">
                <img src="../assets/images/icon6.png" />
              </div>
              <div class="cell-text">
                <p class="title">关于生产质量部仪器装配操作规范说明</p>
                <p class="text">
                  <span>v1.0</span><span>人力资源部</span
                  ><span>2021-12-24</span>
                </p>
              </div>
            </div>
            <div class="list">
              <div class="cell-ico">
                <img src="../assets/images/icon6.png" />
              </div>
              <div class="cell-text">
                <p class="title">关于生产质量部仪器装配操作规范说明</p>
                <p class="text">
                  <span>v1.0</span><span>人力资源部</span
                  ><span>2021-12-24</span>
                </p>
              </div>
            </div>
            <div class="list">
              <div class="cell-ico">
                <img src="../assets/images/icon6.png" />
              </div>
              <div class="cell-text">
                <p class="title">关于生产质量部仪器装配操作规范说明</p>
                <p class="text">
                  <span>v1.0</span><span>人力资源部</span
                  ><span>2021-12-24</span>
                </p>
              </div>
            </div>
          </div>
          <!-----file_list---->
        </el-card>
      </el-col>
      <el-col :span="11">
        <el-card>
          <el-tabs value="first" class="out-tabs">
            <el-tab-pane name="first">
              <span slot="label">文件统计</span>
              <div class="tj-search">
                <el-form
                  :inline="true"
                  :model="formInline"
                  class="demo-form-inline"
                >
                  <el-form-item label="类型：">
                    <template>
                      <el-radio v-model.trim="radio" label="1">新增</el-radio>
                      <el-radio v-model.trim="radio" label="2">修订</el-radio>
                      <el-radio v-model.trim="radio" label="3">作废</el-radio>
                      <el-radio v-model.trim="radio" label="4">借阅</el-radio>
                    </template>
                  </el-form-item>
                  <el-form-item label="统计时间：">
                    <el-date-picker
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-form>
              </div>
              <div class="tjt-img">
                <p class="totl">总数<span class="sz">200</span>个</p>
                <img style="height: 370px" src="../assets/images/tjt1.png" />
              </div>
            </el-tab-pane>
            <el-tab-pane name="second">
              <span slot="label">文件生效</span>
            </el-tab-pane>
            <el-tab-pane name="third">
              <span slot="label">文件建议光荣榜</span>
            </el-tab-pane> </el-tabs
          ><!-----el-tabs----->
        </el-card>
        <el-card>
          <div slot="header" class="clearfix"><span>消息速递</span></div>
          <div class="file-list">
            <div class="list">
              <div class="cell cell-lx"><el-tag size="small">新增</el-tag></div>
              <div class="cell cell-title">关于生产质量部生产仪器操作手册</div>
              <div class="cell cell-bb">v5.0</div>
              <div class="cell cell-bm">物资采购部</div>
              <div class="cell cell-time">2021-10-11</div>
            </div>
            <div class="list">
              <div class="cell cell-lx">
                <el-tag size="small" type="success">修订</el-tag>
              </div>
              <div class="cell cell-title">关于生产质量部生产仪器操作手册</div>
              <div class="cell cell-bb">v5.0</div>
              <div class="cell cell-bm">物资采购部</div>
              <div class="cell cell-time">2021-10-11</div>
            </div>
            <div class="list">
              <div class="cell cell-lx">
                <el-tag size="small" type="danger">作废</el-tag>
              </div>
              <div class="cell cell-title">关于生产质量部生产仪器操作手册</div>
              <div class="cell cell-bb">v5.0</div>
              <div class="cell cell-bm">物资采购部</div>
              <div class="cell cell-time">2021-10-11</div>
            </div>
            <div class="list">
              <div class="cell cell-lx">
                <el-tag size="small" type="warning">借阅</el-tag>
              </div>
              <div class="cell cell-title">关于生产质量部生产仪器操作手册</div>
              <div class="cell cell-bb">v5.0</div>
              <div class="cell cell-bm">物资采购部</div>
              <div class="cell cell-time">2021-10-11</div>
            </div>
          </div>
          <!-----file-list---->
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.3.0",

      visible: true,
      isCollapse: false,
      radio: "1",
      drawer: false,
      dialogVisible: false, // 控制dialog 打开 flag   false 关闭    true 打开
      tableData: [
        {
          nob: "1",
          html: "RBP",
          project: "视黄醇结合蛋白",
          results: "133.6",
          results: "25-70",
          units: "mg/l",
          detection: "荧光免疫法",
        },
        {
          nob: "2",
          html: "RBP",
          project: "视黄醇结合蛋白",
          results: "133.6",
          results: "25-70",
          units: "mg/l",
          detection: "荧光免疫法",
        },
        {
          nob: "3",
          html: "RBP",
          project: "视黄醇结合蛋白",
          results: "133.6",
          results: "25-70",
          units: "mg/l",
          detection: "荧光免疫法",
        },
        {
          nob: "4",
          html: "RBP",
          project: "视黄醇结合蛋白",
          results: "133.6",
          results: "25-70",
          units: "mg/l",
          detection: "荧光免疫法",
        },
        {
          nob: "5",
          html: "RBP",
          project: "视黄醇结合蛋白",
          results: "133.6",
          results: "25-70",
          units: "mg/l",
          detection: "荧光免疫法",
        },
        {
          nob: "6",
          html: "RBP",
          project: "视黄醇结合蛋白",
          results: "133.6",
          results: "25-70",
          units: "mg/l",
          detection: "荧光免疫法",
        },
      ],
      data: [
        {
          label: "一级 1",
          children: [
            {
              label: "二级 1-1",
              children: [
                {
                  label: "三级 1-1-1",
                },
              ],
            },
          ],
        },
        {
          label: "一级 2",
          children: [
            {
              label: "二级 2-1",
              children: [
                {
                  label: "三级 2-1-1",
                },
              ],
            },
            {
              label: "二级 2-2",
              children: [
                {
                  label: "三级 2-2-1",
                },
              ],
            },
          ],
        },
        {
          label: "一级 3",
          children: [
            {
              label: "二级 3-1",
              children: [
                {
                  label: "三级 3-1-1",
                },
              ],
            },
            {
              label: "二级 3-2",
              children: [
                {
                  label: "三级 3-2-1",
                },
              ],
            },
          ],
        },
      ],
    };
  },
  methods: {
    goTarget(href) {
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="scss">
.home {
  .el-card .el-card__header {
    box-sizing: border-box;
    padding: 15px 20px;
    font-size: 15px;
    color: #225fc7;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
    background: rgba(34 95 199 / 2%);
  }
  .el-card .el-card__body {
    position: relative;
    padding: 15px 20px !important;
  }
  /*kjcz-box 快捷入口*/
  .kjcz-box {
    display: flex;
    margin: 0;
    padding: 0;
  }
  .kjcz-box .list {
    flex: 1;
    margin: 0 20px;
    border: 1px solid #f5f5f5;
    padding: 20px 10px;
    border-radius: 4px;
    cursor: pointer;
  }
  .kjcz-box .list:hover {
    box-shadow: 0 0 10px 0 rgb(71 139 251 / 15%);
  }
  .kjcz-box .list img {
    display: block;
    margin: 0 auto;
    width: auto;
  }
  .kjcz-box .list .title {
    margin: 10px 0 0 0;
    display: block;
    padding: 0;
    text-align: center;
    color: #333;
    font-size: 14px;
  }

  /*xszy-box 操作指引*/

  .xszy-box .list {
    display: table;
    width: 100%;
    background: #f9faff;
    margin: 0 0 10px 0;
    padding: 15px;
    box-sizing: border-box;
    border-radius: 4px;
  }
  .xszy-box .list .clum {
    display: table-cell;
    vertical-align: top;
    color: #333;
    font-size: 14px;
    width: 110px;
  }
  .xszy-box .list .text {
    display: table-cell;
    vertical-align: top;
    color: #000;
    font-size: 14px;
    text-align: left;
    width: auto !important;
    overflow: auto;
  }
  .xszy-box .list .text:before,
  .xszy-box .list .text:after {
    content: "";
    display: table;
  }
  .xszy-box .list .text:after {
    clear: both;
  }
  .xszy-box .list .text span {
    display: inline-block;

    padding: 0 15px;
    position: relative;
  }
  .xszy-box .list .text .strator1 {
    color: #225fc7;
  }
  .xszy-box .list .text .strator2 {
    color: #8a75ff;
  }
  .xszy-box .list .text .strator3 {
    color: #3bc6ae;
  }
  .xszy-box .list .text .strator4 {
    color: #ff9f2b;
  }
  .xszy-box .list .text .strator5 {
    color: #000;
  }
  .xszy-box .list .text span:before {
    content: "";
    position: absolute;
    right: 0;
    top: calc(50% - 7px);
    display: block;
    width: 1px;
    height: 14px;
    background: #ccc;
  }
  .xszy-box .list .text > span:last-child:before {
    content: none;
    padding-right: 0;
  }
  .xszy-box .annot {
    display: block;
    margin: 15px 0 0 0;
    padding: 0;
  }
  .xszy-box .annot .text {
    display: inline-block;
    margin: 0;
    padding: 0 0 0 20px;
    position: relative;
    color: #888;
    font-size: 14px;
    overflow: visible;
    width: auto;
  }
  .xszy-box .annot .text + .text {
    margin-left: 25px;
  }
  .xszy-box .annot .text:before {
    position: absolute;
    display: block;
    left: 0;
    top: calc(50% - 5px);
    display: block;
    content: "";
    width: 10px;
    height: 10px;
    background: #ddd;
  }
  .xszy-box .annot .text.strator1:before {
    background: #225fc7;
  }
  .xszy-box .annot .text.strator2:before {
    background: #8a75ff;
  }
  .xszy-box .annot .text.strator3:before {
    background: #3bc6ae;
  }
  .xszy-box .annot .text.strator4:before {
    background: #ff9f2b;
  }
  .xszy-box .annot .text.strator5:before {
    background: #000;
  }
  .el-col > .el-card + .el-card {
    margin-top: 20px;
  }
  /*file_list*/
  .file_list {
    display: block;
    margin: 0;
    padding: 0;
    height: 172px;
    overflow: auto;
  }
  .file_list:before,
  .file_list:after {
    content: "";
    display: table;
  }
  .file_list:after {
    clear: both;
  }
  .file_list .list {
    float: left;
    width: 50%;
    margin: 0 0 16px 0;
    padding: 0;
    display: table;
  }
  .file_list .list .cell-ico {
    display: table-cell;
    vertical-align: middle;
    width: 55px;
  }
  .file_list .list .cell-ico img {
    display: block;
  }
  .file_list .list .cell-text {
    display: table-cell;
    vertical-align: middle;
  }
  .file_list .list .cell-text .title {
    display: block;
    margin: 0;
    padding: 0 20px 0 0;
    color: #000;
    font-size: 14px;
    line-height: 1.75;
  }
  .file_list .list .cell-text .text {
    display: block;
    margin: 0;
    padding: 0;
    color: #888;
    font-size: 13px;
    width: auto;
  }
  .file_list .list .cell-text .text span {
    display: inline-block;
    margin: 0;
    padding: 0 15px;
    position: relative;
  }
  .file_list .list .cell-text .text span:before {
    content: "";
    position: absolute;
    left: 0;
    top: calc(50% - 5px);
    display: block;
    width: 1px;
    height: 10px;
    background: #ddd;
  }
  .file_list .list .cell-text .text > span:first-child {
    padding-left: 0;
  }
  .file_list .list .cell-text .text > span:first-child:before {
    content: none;
  }
  /*tjt-img*/
  .tjt-img {
    display: block;
    margin: 0;
    padding: 0;
    text-align: center;
  }
  .tjt-img img {
    width: auto;
    max-width: 100%;
    margin: 0 auto;
  }
  .totl {
    display: block;
    text-align: center;
    color: #333;
    font-size: 14px;
  }
  .totl .sz {
    color: #225fc7;
    font-size: 28px;
    padding: 0 5px;
  }
  /*tj-search 统计图搜索*/
  .tj-search .el-form-item {
    margin: 5px 0;
  }
  .tj-search .el-form-item + .el-form-item {
    margin-left: 35px;
  }
  .tj-search .el-form-item .el-radio {
    margin: 0 15px 0 0;
  }
  /*file-list*/
  .file-list {
    height: 172px;
    overflow: auto;
  }
  .file-list > .list:first-child {
    padding-top: 0px;
  }
  .file-list .list {
    display: table;
    width: 100%;
    margin: 0;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #333;
  }
  .file-list .list .cell {
    display: table-cell;
    vertical-align: middle;
  }
  .file-list .list > .cell:last-child {
    text-align: right;
  }
  .file-list .list .cell.cell-icon {
    color: #225fc7;
    font-size: 22px;
    width: 30px;
  }
  .file-list .list .cell.cell-lx {
    color: #225fc7;
    font-size: 22px;
    width: 50px;
  }
  .file-list .list .cell.cell-lx .el-tag {
    border: 0;
    border-radius: 15px 0 15px 0;
    line-height: 24px;
  }
  .file-list .list .cell.cell-title {
    color: #225fc7;
  }
  .file-list .list .cell.cell-bb {
    width: 15%;
    color: #666;
  }
  .file-list .list .cell.cell-bm {
    width: 15%;
  }
  .file-list .list .cell.cell-time {
    width: 15%;
    color: #999;
  }

  /*file_list*/
  .file_list {
    display: block;
    margin: 0;
    padding: 0;
    height: 172px;
    overflow: auto;
  }
  .file_list:before,
  .file_list:after {
    content: "";
    display: table;
  }
  .file_list:after {
    clear: both;
  }
  .file_list .list {
    float: left;
    width: 50%;
    margin: 0 0 16px 0;
    padding: 0;
    display: table;
  }
  .file_list .list .cell-ico {
    display: table-cell;
    vertical-align: middle;
    width: 55px;
  }
  .file_list .list .cell-ico img {
    display: block;
  }
  .file_list .list .cell-text {
    display: table-cell;
    vertical-align: middle;
  }
  .file_list .list .cell-text .title {
    display: block;
    margin: 0;
    padding: 0 20px 0 0;
    color: #000;
    font-size: 14px;
    line-height: 1.75;
  }
  .file_list .list .cell-text .text {
    display: block;
    margin: 0;
    padding: 0;
    color: #888;
    font-size: 13px;
  }
  .file_list .list .cell-text .text span {
    display: inline-block;
    margin: 0;
    padding: 0 15px;
    position: relative;
  }
  .file_list .list .cell-text .text span:before {
    content: "";
    position: absolute;
    left: 0;
    top: calc(50% - 5px);
    display: block;
    width: 1px;
    height: 10px;
    background: #ddd;
  }
  .file_list .list .cell-text .text > span:first-child {
    padding-left: 0;
  }
  .file_list .list .cell-text .text > span:first-child:before {
    content: none;
  }
}
</style>

