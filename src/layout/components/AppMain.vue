<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
    <!-- 全局备案信息 -->
    <IcpInfo />
  </section>
</template>

<script>
import watermark from 'watermark-dom'
import { parseTime } from "@/utils/ruoyi"
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  mounted() {
    // 配置系统界面文字水印
    const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
    const userName = userInfo.userName
    const nickName = userInfo.nickName
    // const loginDate = userInfo.loginDate
    const loginDate = new Date()
    const txt = `${userName},${nickName},${parseTime(loginDate, '{y}-{m}-{d}')}`
    watermark.init({ watermark_txt: txt, watermark_alpha: 0.08, watermark_width: 260, watermark_height: 150, })
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
