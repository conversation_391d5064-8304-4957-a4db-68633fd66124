<script>
export default {
  name: "MenuItem",
  functional: true,
  props: {
    icon: {
      type: String,
      default: "",
    },
    number: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
  },
  methods: {},
  render(h, context) {
    const { icon, number, title } = context.props;
    const vnodes = [];

    if (icon) {
      vnodes.push(<svg-icon icon-class={icon} />);
    }

    if (title) {
      // 指定菜单的角标设置相应的数字
      if (title == "我的消息" || title == 'My message') {
        vnodes.push(title);
        if (number[0] != 0) {
          vnodes.push(<span class="badges">{number[0]}</span>);
        }
      } else if (title == "我的申请") {
        vnodes.push(title);
        if (number[2] != 0) {
          vnodes.push(<span class="badges">{number[2]}</span>);
        }
      }
      else if (title == "我的办理" || title == 'My management') {
        vnodes.push(title);
        if (number[1] != 0) {
          vnodes.push(<span class="badges">{number[1]}</span>);
        }
      } else if (title == "我的事项" || title == 'My business') {
        vnodes.push(<span>{title}</span>);
        if (number[0] + number[1] != 0) {
          vnodes.push(<span class="badges">{number[0] + number[1]}</span>);
        }
      } else {
        vnodes.push(<span slot="title">{title}</span>);
      }
    }
    return vnodes;
  },
};
</script>
