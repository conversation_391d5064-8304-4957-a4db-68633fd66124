<template>
  <div class="navbar" v-loading="loading">
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />
    <div class="big-title">{{ $t('login.tit') }}</div>
    <div class="right-menu">
      <template v-if="device !== 'mobile' && isSearchShow">
        <el-input placeholder="请输入内容" v-model="searchContent" @keyup.enter.native="searchClick">
          <el-button slot="append" icon="el-icon-search" @click="searchClick"></el-button>
        </el-input>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <div style="    display: flex;
    justify-content: center;
}">
            <screenfull id="screenfull" class="right-menu-item hover-effect" />

            <div><svg-icon style="margin: 0 5px" icon-class="touxian" /></div>

            {{ user.nickName }}
            <i class="el-icon-caret-bottom" />
          </div>
        </div>
        <!--修改密码-->
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>修改密码</el-dropdown-item>
          </router-link>

          <!--消息中心-->
          <el-dropdown-item divided @click.native="msgcenter" v-hasPermi="['system:msg:center']">
            <span>消息中心</span>
          </el-dropdown-item>

          <!--调度中心-->
          <el-dropdown-item divided @click.native="xxjobcenter" v-hasPermi="['system:xxl:job']">
            <span>调度中心</span>
          </el-dropdown-item>

          <!--退出登录-->
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { searchFileSwitch } from '@/api/search_results/searchGlobal'
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import {getUserProfile, xxjobLogin} from "@/api/system/user";
import { getLanguage } from '@/utils/auth'
import {fetchLocaleMessages} from '@/main.js'
import { getToken } from "@/utils/auth";

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
  },
  dicts: [],
  created() {
    this.getUser();
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
  },
  data() {
    return {
      loading: false,
      user: {},
      tenantId: "",
      custList: [],
      roleGroup: {},
      postGroup: {},
      custId: "",
      activeTab: "userinfo",
      // date: this.parseTime(new Date()),
      searchContent: "",
      isSearchShow: false,
    };
  },
  mounted() {
    let _this = this; // 声明一个变量指向Vue实例this，保证作用域一致
    _this.getSwitch()
    // this.timer = setInterval(() => {
    //   _this.date = this.parseTime(new Date()); // 修改数据date
    // }, 1000);
  },
  beforeDestroy() {
    // if (this.timer) {
    //   clearInterval(this.timer); // 在Vue实例销毁前，清除我们的定时器
    // }
  },
  methods: {
    //
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    changgeCust(val) {
      cutCustomer(val)
        .then((response) => {
          this.$router.go(0);
        })
        .catch(() => { });
    },
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data.user;
        this.roleGroup = response.data.roleGroup;
        this.postGroup = response.data.postGroup;
      });
    },
    async logout() {
      let _this = this
      _this.$confirm(_this.$t(`home.exit_the_system`), _this.$t(`file_handle.change_tip`), {
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        type: "warning",
      })
        .then(() => {
          _this.loading = true
          _this.$store.dispatch("LogOut").then(() => {
            _this.loading = false
            location.href = (process.env.VUE_APP_CONTEXT_PATH == "/" ? "" : process.env.VUE_APP_CONTEXT_PATH) + "/";
          });
        })
        .catch(() => {
          console.log(process.env.VUE_APP_CONTEXT_PATH);
        });
    },
    searchClick() {
      const timestamp = Date.now(); // 获取当前时间戳
      this.$store.dispatch('setSearchQuery', this.searchContent);
      this.$router.push({ path: '/search/index', query: { timestamp: timestamp } })
    },
    /** 查询列表 */
    getSwitch() {
      this.isSearchShow = false
      searchFileSwitch().then((res) => {
        if (res == 'Y') {
          this.isSearchShow = true
        }
      });
    },
    msgcenter() {
      const token = getToken();
      //console.log("token=" + token);
      window.open(process.env.VUE_APP_MESSAGE_HOST + "?ssotoken=" + token, "_blank");
    },
    xxjobcenter() {
      const token = getToken();
      //console.log("token=" + token);
      window.open(process.env.VUE_APP_XXJOB_HOST + "/ssoLogin?ssotoken=" + token, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
