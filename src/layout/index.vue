<template>
  <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar class="sidebar-container"/>
    <div :class="{hasTagsView:needTagsView}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar v-if="navbar"/>
        <!--如果是爱数集成登录显示此条件切换-->
        <div class="brtext" :style="isAs7">
<!--          <el-link icon="el-icon-search" style="margin-right: 20px" :underline="false" @click="searchHandle()">{{$t('home.search_btn')}}</el-link>-->
          <el-dropdown class="tenant-change" trigger="click">
            <div class="avatar-wrapper">
              {{ tenantName}}
              <i class="el-icon-caret-bottom"></i>
            </div>
            <el-dropdown-menu slot="dropdown" >
              <el-dropdown-item :disabled="disabledChangeTenant(item)" v-for='item in dict.type["tenant_list"]' :key="item.value" @click.native="onClickTenant(item)">
                <span>{{dictLanguage(item)}}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown class="tenant-change" trigger="click">
            <div class="avatar-wrapper">
              {{ languageLabel}}
              <i class="el-icon-caret-bottom"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :disabled="language===item.value" v-for='item in dict.type["language_switch"]' :key="item.value" @click.native="languageChange(item)">
                <span>{{dictLanguage(item)}}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
<!--          <el-link style="margin-right: 20px" type="primary" @click="onClickRms">{{$t('home.rms')}}</el-link>-->
          <span>{{$t('doc.this_dept_ver_id')}}：{{versionInfo}}</span>
        </div>
<!--        <breadcrumb id="breadcrumb-container" ref="breadcrumb" class="breadcrumb-container"/>-->
        <tags-view v-if="needTagsView"/>
      </div>
      <app-main />
      <right-panel>
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import breadcrumb from '@/components/Breadcrumb'

import variables from '@/assets/styles/variables.scss'
import {get3Token, getTenantList} from '@/api/system/user'
import cache from '@/plugins/cache'
import {getAsToken, getLanguage, removeToken} from '@/utils/auth'

export default {
  name: 'Layout',
  dicts:['tenant_list','language_switch'],
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    breadcrumb,
    Sidebar,
    TagsView,

  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    },
    navbar: {
      get() {
        return sessionStorage.getItem('client') === null
      },
    },
    isAs7() {
      return sessionStorage.getItem('client')=='as7'?'margin-top:-55px':''
    },
    tenantName: function () {
      let item = this.dict.type['tenant_list'].find(v => window.location.href.includes(v.raw.remark));
      if(item){
        return this.dictLanguage(item)
      }
      return null;
    },
    languageLabel: function () {
      let item = this.dict.type['language_switch'].find(v => this.language===v.value);
      if(item){
        return this.dictLanguage(item)
      }
      return null;
    }
  },
  data(){
    return {
      language: null, // 语言类型
      versionInfo: null,
    }
  },
  created(){
    this.language = sessionStorage.getItem('language')
    this.getVersionInfo()
  },
  methods: {
    searchHandle(){
      this.$router.push({path: "/search"})
    },
    languageChange(item){
      this.$i18n.locale = item.value
      sessionStorage.setItem('language', item.value)
      sessionStorage.removeItem('languageData')
      window.location.reload()
    },
    disabledChangeTenant(item){
      if(window.location.href.includes(item.raw.remark)){
        return true
      }else{
        return false
      }
    },
    onClickRms(){
      get3Token().then(res=>{
        let token3 = res.msg
        let language = getLanguage()
        let href = process.env.VUE_APP_BASE_RMS
        let fullpath = '/#/index'
        this.$nextTick(()=>{
            console.log("href 重定向>>>>",href+`/ssoredirect?token3=${token3}&fullpath=${encodeURIComponent(fullpath)}&language=${language}`)
            window.open(href+`/ssoredirect?token3=${token3}&language=${language}&fullpath=${encodeURIComponent(fullpath)}`,'_blank',"noopener,noreferrer")

          }
        )
      })
    },
    // 切换租户
    onClickTenant(item){
      let url = item.raw.remark;
      let fullpath = this.$route.fullPath
      sessionStorage.removeItem('language')
      sessionStorage.removeItem('languageData')
      cache.session.set('tenantId',item.value)
      url = `/ssoredirect?fullpath=${encodeURIComponent(fullpath)}&href=${encodeURIComponent(url)}&tenantId=${item.value}`
      console.log(url)
      this.$router.push({path:url})
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    getVersionInfo(){
      this.getConfigKey("sys_version").then(response => {
        this.versionInfo = response.msg
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mixin.scss";
  @import "~@/assets/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    min-height: 100vh;
    width: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }

  .mobile .fixed-header {
    width: 100%;
  }
  .tenant-change{
    margin-right: 10px;
    float: right;
    top:10px;
    right:20px;
  }
</style>
