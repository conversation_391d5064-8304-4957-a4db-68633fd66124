import { processFileLocalUpload } from "@/api/commmon/file";
import { settingDocClassId } from "@/api/file_settings/type_settings";
export default {
  data() {
    return {
      recmoban: [],
    }
  },
  created() {
    settingDocClassId("REC").then((response) => {
      if (response.data&&response.data.fileList != null) {
        this.recmoban = response.data.fileList;
      }
    });
  },
  methods: {
    handleUpdata() {
      if (this.selection.length != 1) {
        this.$modal.msg("请选择一个文件");
      } else {
        if (this.selection[0].isDeleted != 1) {
          this.$modal.msg("关联记录未生效不可修订或者作废");
        } else {
          this.open2 = true;
          this.title = "修订";
          this.ruleForm = {
            docName: this.selection[0].docName,
            versionValue: this.selection[0].versionValue,
            status: 0,
            docId: this.selection[0].docId,
            linkClass: this.selection[0].linkClass,
            linkId: this.selection[0].linkId,
            docClass: this.selection[0].docClass,
          };
        }
      }
    },

    handlezuofei() {
      if (this.selection.length != 1) {
        this.$modal.msg("请选择一个文件");
      } else {
        if (this.selection[0].isDeleted != 1) {
          this.$modal.msg("关联记录未生效不可修订或者作废");
        } else {
          if (this.selection[0].status == 2) {
            this.postList2.forEach((val, i) => {
              if (val.docId == this.selection[0].docId) {
                this.$modal.msg("已在修订中不可作废");
              }
            })
          } else {
            this.postList2.forEach((element, index) => {
              this.selection.forEach((val, i) => {
                if (
                  element.linkId == val.linkId &&
                  element.versionValue == val.versionValue
                ) {
                  this.postList2[index].status = 2;
                  this.selection = []
                  this.isshow = !this.isshow;
                }
              });
            });
          }

        }
      }
    },
    //撤销
    handlecexiao() {
      this.selection.forEach((element, index) => {
        if (element.isDeleted == 1) {
          this.postList2.forEach((val, index) => {
            if (element.linkId == val.linkId && val.isDeleted == 1) {
              this.postList2[index].status = null;
            }
          })
        } else {
          if (element.status == 0 && element.docId != "") {
            this.postList2.forEach((el, i) => {
              if (element.docId == el.docId && el.isDeleted == 1) {
                this.postList2[i].status = null

              }
              if (element.docId == el.docId && el.isDeleted == undefined) {
                this.postList2.splice(i, 1);
              }
            });
          }
        }
      });
    },
    //确定
    handleButton() {
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) return
      })
      if (this.postList2 == null) {
        this.postList2 = [];
      }

      for (let index = 0; index < this.postList2.length; index++) {
        const element = this.postList2[index];

        if (this.ruleForm.docId != "") {
          if (element.docId == this.ruleForm.docId && element.status == this.ruleForm.status) {
            this.postList2[index] = this.ruleForm;
            this.isshow = !this.isshow;
            //this.ruleForm = { docName: "" }
            this.open2 = false;
            return false
          }
        }
        if (this.ruleForm.docName != "" && this.ruleForm.versionValue != "") {

          if (element.docName == this.ruleForm.docName && element.versionValue == this.ruleForm.versionValue) {
            this.$message.error("文件重复");
            return false
          }
        }
      }

      var arr = [];
      if (this.ruleForm.docName != '') {
        arr.push(this.ruleForm);

        for (let index = 0; index < this.postList2.length; index++) {
          const element = this.postList2[index];
          if (this.ruleForm.docId != '') {
            if (element.docId == this.ruleForm.docId) {
              this.postList2[index].status = 2
              this.isshow = !this.isshow;
            }
          }

        }

      }
      this.postList2 = [...this.postList2, ...arr];

      this.open2 = false;
    },
    /** 删除按钮操作 */
    handelshangchu(index) {
      if (this.menuitem == 2) {
        if (this.postList2[index].isDeleted == 1) {
          this.postList2[index].status = 1
          this.postList2.forEach((ele, i) => {
            if (ele.docId == this.postList2[index].docId) {
              if (ele.isDeleted == null) {
                this.postList2.splice(i, 1);
              }
            }
          });
          this.isshow = !this.isshow;
        } else {
          this.postList2.forEach((ele, i) => {
            if (ele.docId == this.postList2[index].docId) {
              this.postList2[i].status = null
              this.isshow = !this.isshow;
            }
          });
          this.postList2.splice(index, 1);
        }


        this.isshow = !this.isshow;
      } else {
        this.postList.splice(index, 1);
      }
    },
    standardDocBeforeUpload(params) {
      const isLt1M = params.file.size / 1024 / 1024 < 10;
      if (!isLt1M) {
        this.$message.error("上传文件不能超过 10MB!");
        return;
      }
      var testmsg = params.file.name.substring(
        params.file.name.lastIndexOf(".") + 1
      );
      if (
        testmsg != "docx" &&
        testmsg != "doc" &&
        testmsg != "ppt" &&
        testmsg != "pptx" &&
        testmsg != "pdf" &&
        testmsg != "xls" &&
        testmsg != "xlsx"
      ) {
        this.$message.error("只能上传doc,docx,xlsx,pdf,ppt,pptx文档");
        this.standardDocfileList = [];
        return;
      }
      this.standardDocfileList = [];
      if (this.title == "新增") {
        this.ruleForm = {
          docName: undefined,
          versionValue: undefined,
          docId: undefined,
          status: 0,
          isDelete: 0,
          linkClass: undefined,
          linkId: undefined,
        };
      }
      let standardDoc = new FormData();
      standardDoc.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      processFileLocalUpload(standardDoc).then((res) => {
        if (res.code == 200) {
          this.ruleForm.docName = res.data.fileName;
          this.ruleForm.fileId = res.data.fileId;
          if (this.title == "新增") {
            // 默认关联记录版本
            this.ruleForm.versionValue = "A0";
            this.ruleForm.docId = "";
            this.ruleForm.linkClass = "";
            this.standardDocfileList = [];
            this.nameFlag = false;
          }
        } else {
        }

      });
      this.disabled = false;
    },
  },

}
