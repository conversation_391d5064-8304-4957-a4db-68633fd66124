import { settingDocClassList } from "@/api/file_settings/type_settings";
import { fileLocalDownload,fileLocalDownloadPdf } from "@/api/commmon/file";
export default {
    data() {
        return {
            viewId: "",
            viewShow: false,
            fileTypeList: [],
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
            },
        };
    },
    created() {
        let query = {
            classStatus: 1,
        };
        let fileTypeList = this.$cache.session.getJSON('fileTypeList')
        if (fileTypeList == undefined) {

            settingDocClassList(query).then((res) => {
                this.fileTypeList = res.rows;
                this.$cache.session.setJSON('fileTypeList', this.fileTypeList)
            });
        } else {
            this.fileTypeList = this.$cache.session.getJSON('fileTypeList')
        }

    },
    methods: {
        close() {
            this.viewShow = false;
        },
        handlePreview(id, source, linkType, mode) {
          console.log('预览文件id', id);
          console.log('source', source);
          console.log('linkType', linkType);
          console.log('mode', mode);
          this.viewId = id;
          this.$refs.viewRef.handleOpenView(id, source, linkType, mode);
          this.viewShow = true;
        },
        //转化文件类型
        handleFileType(obj) {
            if (obj && this.fileTypeList) {
                let arr = this.fileTypeList.filter((x) => x.id === obj)[0];
                if (arr) {
                    return arr.className;
                }
                return obj;
            }
        },
        //下载文件
        handelefileLocalDownload(id, name) {
            fileLocalDownload(id).then((res) => {
                //console.log("file", res);
                this.saveFile(res, name);
            });
        },
        //批量下载文件
        handelefileLocalDownloadAll(list) {
          console.log(list)
          for (let i=0;i<list.length;i++){
            fileLocalDownload(list[i].fileId?list[i].fileId:list[i].url).then((res) => {
              //console.log("file", res);
              this.saveFile(res, list[i].fileName?list[i].fileName:list[i].name);
            });
          }
        },
        handelefileLocalDownloadPdf(id, name) {
          fileLocalDownloadPdf(id).then((res) => {
            //console.log("file", res);
            name = name.substring(0,name.lastIndexOf('.')) + '.pdf'
            this.saveFile(res, name);
          });
        },
        saveFile(data, name) {
            try {
                const blobUrl = window.URL.createObjectURL(data);
                // console.log('bo',blobUrl);
                const a = document.createElement("a");
                a.style.display = "none";
                a.download = name;
                a.href = blobUrl;
                a.click();
            } catch (e) {
                alert("保存文件出错");
            }
        },
    },

}
