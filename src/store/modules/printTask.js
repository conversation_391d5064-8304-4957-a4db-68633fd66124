import { Notification } from 'element-ui';
import { Message } from 'element-ui';

const state = {
  printTasks: [],
  currentTask: null,
  printers: []
}

const mutations = {
  SET_PRINT_TASKS: (state, tasks) => {
    state.printTasks = tasks
  },
  ADD_PRINT_TASK: (state, task) => {
    // 将任务添加到打印任务列表的开头
    state.printTasks.unshift(task)
  },
  UPDATE_PRINT_TASK: (state, task) => {
    const index = state.printTasks.findIndex(t => t.id === task.id)
    if (index !== -1) {
      state.printTasks.splice(index, 1, task)
    }
  },
  SET_CURRENT_TASK: (state, task) => {
    state.currentTask = task
  },
  SET_PRINTERS: (state, printers) => {
    state.printers = printers
  }
}

const actions = {
  // 发送打印任务到WebSocket
  sendPrintTask({ commit, rootState }, task) {
    const wsMessage = {
      type: 'PRINT_TASK',
      data: task
    }
    rootState.websocket.socket.send(JSON.stringify(wsMessage))
    commit('ADD_PRINT_TASK', task)
  },

  // 处理WebSocket返回的打印任务状态更新
  handlePrintTaskUpdate({ commit }, message) {
    commit('UPDATE_PRINT_TASK', message);

    // 如果打印成功，显示通知
    if (message.status === 'completed') {
      Message.success(`文件 "${message.docName}" 打印完成`)
    } else if (message.status === 'failure') {
      Message.error( message.message || `文件 "${message.docName}" 打印失败`)
    }
  },

  // 请求打印机列表
  requestPrinters({ rootState }) {
    const wsMessage = {
      type: 'GET_PRINTERS'
    }
    rootState.websocket.socket.send(JSON.stringify(wsMessage))
  },

  // 处理接收到的打印机列表
  handlePrintersUpdate({ commit }, printers) {
    console.log('handlePrintersUpdate', printers)
    // 确保printers是数组
    if (!Array.isArray(printers)) {
      console.error('打印机列表格式错误:', printers);
      printers = [];
    }
    // 过滤掉无效的打印机
    const validPrinters = printers.filter(printer =>
      printer && typeof printer.name === 'string'
    );
    commit('SET_PRINTERS', validPrinters)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
