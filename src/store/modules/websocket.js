const state = {
  socket: null,
  connected: false,
  heartbeatTimer: null,
  reconnectTimer: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  reconnectInterval: 5000, // 重连间隔5秒
  heartbeatInterval: 30000 // 心跳间隔30秒
}

const mutations = {
  SET_SOCKET: (state, socket) => {
    state.socket = socket
  },
  SET_CONNECTED: (state, status) => {
    state.connected = status
  },
  SET_HEARTBEAT_TIMER: (state, timer) => {
    state.heartbeatTimer = timer
  },
  SET_RECONNECT_TIMER: (state, timer) => {
    state.reconnectTimer = timer
  },
  SET_RECONNECT_ATTEMPTS: (state, attempts) => {
    state.reconnectAttempts = attempts
  }
}

const actions = {
  // 初始化WebSocket连接
  initWebSocket({ commit, dispatch, state }) {
    console.log('正在初始化WebSocket连接...');
    const wsUrl = process.env.VUE_APP_WS_API;
    console.log('WebSocket URL:', wsUrl);

    return new Promise((resolve, reject) => {
      try {
        const socket = new WebSocket(wsUrl);

        socket.onopen = () => {
          console.log('WebSocket连接成功');
          commit('SET_CONNECTED', true);
          commit('SET_RECONNECT_ATTEMPTS', 0);
          // 启动心跳
          dispatch('startHeartbeat');
          resolve();
        };

        socket.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          commit('SET_CONNECTED', false);
          dispatch('handleConnectionError');
          reject(error);
        };

        socket.onclose = (event) => {
          console.log('WebSocket连接关闭:', event);
          commit('SET_CONNECTED', false);
          // 停止心跳
          dispatch('stopHeartbeat');
          // 尝试重连
          dispatch('handleConnectionError');
        };

        // 处理收到消息
        socket.onmessage = (event) => {
          console.log('收到WebSocket消息:', event.data);
          try {
            // 使用$#分割消息
            const messageStrings = event.data.split('$#').filter(str => str.trim());

            // 处理每个消息字符串
            messageStrings.forEach(msgStr => {
              try {
                const message = JSON.parse(msgStr);
                console.log('解析后的消息:', message);

                // 如果是心跳响应，重置心跳计时器
                if (message.type === 'PONG') {
                  dispatch('resetHeartbeat');
                  return;
                }

                switch (message.type) {
                  case 'PRINTERS_LIST':
                    if (!message.data) {
                      console.error('打印机列表数据为空');
                      return;
                    }
                    dispatch('printTask/handlePrintersUpdate', message.data, { root: true });
                    break;
                  case 'PRINT_TASK_STATUS':
                    //打印成功
                    dispatch('printTask/handlePrintTaskUpdate', message.data, { root: true });
                    break;
                  default:
                    console.warn('未知的消息类型:', message.type);
                }
              } catch (e) {
                console.warn('解析单条消息失败:', msgStr, e);
              }
            });
          } catch (error) {
            console.error('处理WebSocket消息失败:', error);
          }
        };

        commit('SET_SOCKET', socket);
      } catch (error) {
        console.error('创建WebSocket实例失败:', error);
        dispatch('handleConnectionError');
        reject(error);
      }
    });
  },

  // 处理连接错误
  handleConnectionError({ commit, dispatch, state }) {
    if (state.reconnectAttempts < state.maxReconnectAttempts) {
      console.log(`准备第 ${state.reconnectAttempts + 1} 次重连...`);
      // 清除之前的重连定时器
      if (state.reconnectTimer) {
        clearTimeout(state.reconnectTimer);
      }
      // 设置新的重连定时器
      const timer = setTimeout(() => {
        commit('SET_RECONNECT_ATTEMPTS', state.reconnectAttempts + 1);
        dispatch('initWebSocket');
      }, state.reconnectInterval);
      commit('SET_RECONNECT_TIMER', timer);
    } else {
      console.error('WebSocket重连次数超过最大限制，停止重连');
    }
  },

  // 启动心跳
  startHeartbeat({ commit, dispatch, state }) {
    dispatch('stopHeartbeat'); // 先清除可能存在的心跳定时器
    const timer = setInterval(() => {
      if (state.connected && state.socket) {
        dispatch('sendMessage', { type: 'PING' });
      }
    }, state.heartbeatInterval);
    commit('SET_HEARTBEAT_TIMER', timer);
  },

  // 停止心跳
  stopHeartbeat({ commit, state }) {
    if (state.heartbeatTimer) {
      clearInterval(state.heartbeatTimer);
      commit('SET_HEARTBEAT_TIMER', null);
    }
  },

  // 重置心跳
  resetHeartbeat({ dispatch }) {
    dispatch('stopHeartbeat');
    dispatch('startHeartbeat');
  },

  // 发送消息到WebSocket服务器
  sendMessage({ state }, message) {
    if (state.socket && state.connected) {
      state.socket.send(JSON.stringify(message));
    }
  },

  // 关闭WebSocket连接
  closeWebSocket({ commit, dispatch, state }) {
    dispatch('stopHeartbeat');
    if (state.socket) {
      state.socket.close();
      commit('SET_SOCKET', null);
      commit('SET_CONNECTED', false);
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
