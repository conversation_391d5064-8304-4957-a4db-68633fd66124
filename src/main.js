import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'rzelement-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' //directive
import dbClick from '@/directive/button/dbClick'
import plugins from './plugins' // plugins
import { download } from '@/utils/request'
import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import '../public/css/poctstyle.css'
import '../public/css/rzcommon.css'
import '../public/css/main.css'
import '../public/css/iconfont.css'
import i18n from './i18n/index.js'

import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  handleTree,
  deepTraversal,
  isEmpty,
  menusLanguage
} from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
// import RightToolbar from "@/components/RightToolbar"
// 富文本组件
// import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
//import ImageUpload from "@/components/ImageUpload"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// 图形监控组件
import MonitorDrawer from "@/components/MonitorDrawer"
// 爱数预览组件
import AsPreView from '@/components/AsAs7/index.vue'
// 备案信息组件
import IcpInfo from '@/components/IcpInfo'
import { setLanguageData } from './utils/auth'
import {dictLanguage} from "./utils/ruoyi";
import {fileTypesLanguage} from "./utils/ruoyi";
import {getLabelWithColon} from "./utils/ruoyi";
// import ViewDoc from '@/components/AsAs7/viewDoc.vue'
import UUID from 'vue-uuid'

import { RecycleScroller } from 'vue-virtual-scroller' // 虚拟滚动插件
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
Vue.component('RecycleScroller', RecycleScroller)
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.isEmpty = isEmpty
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.deepTraversal = deepTraversal
Vue.prototype.menusLanguage = menusLanguage
Vue.prototype.dictLanguage = dictLanguage
Vue.prototype.fileTypesLanguage = fileTypesLanguage
Vue.prototype.getLabelWithColon = getLabelWithColon


// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
//Vue.component('RightToolbar', RightToolbar)
// Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
//Vue.component('ImageUpload', ImageUpload)
Vue.component('MonitorDrawer', MonitorDrawer)
Vue.component('AsPreView', AsPreView)
Vue.component('IcpInfo', IcpInfo)
// Vue.component('ViewDoc', ViewDoc)
Vue.use(UUID)
Vue.use(directive)
Vue.use(dbClick)
Vue.use(plugins)
Vue.use(VueMeta)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})
Vue.config.productionTip = false
Vue.config.silent = true

// 初始化应用
console.log('初始化应用')
store.dispatch('app/initApp')

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
