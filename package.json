{"name": "ruoyi-vue-plus", "version": "3.3.0", "description": "RuoYi-Vue-Plus后台管理系统", "author": "LionLi", "license": "MIT", "sideEffects": true, "scripts": {"dev": "vue-cli-service serve", "build:dev": "vue-cli-service build --mode dev", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:jiuli": "vue-cli-service build --mode jiuli", "build:product": "vue-cli-service build --mode product", "build:env": "vue-cli-service build --mode production", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "dev:node18": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "build:node18": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --report"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "babel-plugin-import": "^1.13.8", "clipboard": "2.0.6", "code-inspector-plugin": "^1.0.4", "core-js": "3.45.0", "echarts": "4.9.0", "el-tree-transfer": "^2.4.7", "element-ui": "2.14.1", "express": "^4.17.1", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "http-proxy-middleware": "^1.0.5", "js-beautify": "1.13.0", "js-cookie": "2.2.1", "jsencrypt": "3.2.1", "lodash": "^4.17.21", "nprogress": "0.2.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^2.5.207", "print-js": "^1.6.0", "quill": "1.3.7", "rzelement-ui": "^1.0.2", "screenfull": "5.0.2", "sortablejs": "^1.10.2", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-i18n": "^8.28.2", "vue-meta": "^2.4.0", "vue-pdf": "^4.2.0", "vue-router": "3.4.9", "vue-template-compiler": "2.6.12", "vue-ueditor-wrap": "^2.5.6", "vue-uuid": "^2.0.2", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "^2.24.3", "vuex": "3.6.0", "watermark-dom": "^2.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "@vue/runtime-dom": "^3.3.7", "babel-eslint": "10.1.0", "chalk": "4.1.0", "compression-webpack-plugin": "^6.1.1", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "prettier": "^2.6.2", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "webpack-bundle-analyzer": "^4.9.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}